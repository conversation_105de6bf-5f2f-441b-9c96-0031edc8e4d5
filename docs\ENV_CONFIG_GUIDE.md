# WisCude 环境变量配置指南

本文档详细说明了 WisCude 后台管理系统的环境变量配置。

## 配置文件位置

- 主配置文件：`.env`（项目根目录）
- 配置模板：`.env.example`（如果存在）

## 配置分类

### 1. 应用基本配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `APP_NAME` | 应用名称 | WisCude 后台管理系统 | 否 |
| `APP_VERSION` | 应用版本 | 1.0.0 | 否 |
| `DEBUG` | 调试模式 | true | 是 |

### 2. 服务器配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `HOST` | 服务器监听地址 | 0.0.0.0 | 是 |
| `PORT` | 服务器端口 | 8000 | 是 |

### 3. 安全配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `SECRET_KEY` | JWT密钥 | - | **是** |
| `ALGORITHM` | JWT算法 | HS256 | 是 |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | 访问令牌过期时间 | 30 | 是 |
| `REFRESH_TOKEN_EXPIRE_DAYS` | 刷新令牌过期时间 | 7 | 是 |

⚠️ **重要提醒**：生产环境必须修改 `SECRET_KEY` 为强随机密钥！

### 4. 密码安全策略

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `PASSWORD_MIN_LENGTH` | 密码最小长度 | 8 | 是 |
| `PASSWORD_REQUIRE_UPPERCASE` | 要求大写字母 | true | 是 |
| `PASSWORD_REQUIRE_LOWERCASE` | 要求小写字母 | true | 是 |
| `PASSWORD_REQUIRE_NUMBERS` | 要求数字 | true | 是 |
| `PASSWORD_REQUIRE_SPECIAL` | 要求特殊字符 | true | 是 |
| `PASSWORD_MAX_ATTEMPTS` | 最大登录尝试次数 | 5 | 是 |
| `PASSWORD_LOCKOUT_MINUTES` | 账户锁定时间 | 15 | 是 |

### 5. 数据库配置

| 配置项 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| `DATABASE_URL` | PostgreSQL连接URL | ********************************/db | **是** |
| `DATABASE_ECHO` | 输出SQL语句 | false | 否 |
| `SQLITE_URL` | SQLite备用数据库 | sqlite:///./wiscude_admin.db | 否 |
| `ANDROID_DB_PATH` | Android数据库路径 | ../Wiscude/app/databases/wiscude.db | 否 |

### 6. Redis配置

| 配置项 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| `REDIS_URL` | Redis连接URL | redis://localhost:6379/0 | 是 |
| `REDIS_PASSWORD` | Redis密码 | - | 否 |

### 7. CORS跨域配置

| 配置项 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| `CORS_ORIGINS` | 允许的前端域名 | http://localhost:5173,http://localhost:5174 | 是 |

### 8. 网络安全配置

| 配置项 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| `ALLOWED_HOSTS` | 允许的主机列表 | localhost,127.0.0.1 | 是 |
| `SECURE_COOKIES` | 启用安全Cookie | false | 是 |
| `SESSION_TIMEOUT_MINUTES` | 会话超时时间 | 60 | 是 |

### 9. 文件上传配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `MAX_FILE_SIZE` | 最大文件大小（字节） | 10485760 | 否 |
| `UPLOAD_DIR` | 上传目录 | uploads | 否 |

### 10. 分页配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `DEFAULT_PAGE_SIZE` | 默认每页条数 | 20 | 否 |
| `MAX_PAGE_SIZE` | 最大每页条数 | 100 | 否 |

### 11. 日志配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `LOG_LEVEL` | 日志级别 | INFO | 是 |
| `LOG_FILE_PATH` | 日志文件路径 | logs/wiscude-admin.log | 是 |
| `LOG_RETENTION_DAYS` | 日志保留天数 | 30 | 否 |
| `LOG_MAX_SIZE_MB` | 日志文件最大大小 | 100 | 否 |

### 12. 数据同步配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `SYNC_INTERVAL_MINUTES` | 同步间隔时间 | 30 | 否 |
| `SYNC_BATCH_SIZE` | 同步批次大小 | 1000 | 否 |

### 13. 邮件配置

| 配置项 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| `SMTP_HOST` | SMTP服务器 | smtp.gmail.com | 否 |
| `SMTP_PORT` | SMTP端口 | 587 | 否 |
| `SMTP_USER` | 邮箱用户名 | <EMAIL> | 否 |
| `SMTP_PASSWORD` | 邮箱密码 | your-app-password | 否 |
| `SMTP_TLS` | 启用TLS | true | 否 |
| `EMAIL_FROM` | 发件人邮箱 | <EMAIL> | 否 |
| `EMAIL_FROM_NAME` | 发件人名称 | WisCude Admin | 否 |

### 14. 监控配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `ENABLE_METRICS` | 启用性能监控 | true | 否 |
| `METRICS_PORT` | 监控服务端口 | 9090 | 否 |
| `METRICS_PATH` | 监控路径 | /metrics | 否 |

### 15. 开发环境配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `ENABLE_DOCS` | 启用API文档 | true | 否 |
| `ENABLE_DEBUG_TOOLBAR` | 启用调试工具 | true | 否 |
| `ENABLE_RELOAD` | 启用热重载 | true | 否 |

## 环境配置示例

### 开发环境

```env
DEBUG=true
DATABASE_URL=postgresql://postgres:password@localhost:5432/wiscude_dev
CORS_ORIGINS=http://localhost:5173,http://localhost:5174
SECURE_COOKIES=false
LOG_LEVEL=DEBUG
ENABLE_DOCS=true
```

### 生产环境

```env
DEBUG=false
SECRET_KEY=your-super-strong-secret-key-here
DATABASE_URL=postgresql://wiscude_user:<EMAIL>:5432/wiscude_prod
CORS_ORIGINS=https://admin.yourdomain.com
ALLOWED_HOSTS=admin.yourdomain.com
SECURE_COOKIES=true
LOG_LEVEL=WARNING
ENABLE_DOCS=false
```

## 常见问题

### Q: 如何生成强密钥？

A: 可以使用以下命令生成：
```bash
# 使用 openssl
openssl rand -hex 32

# 使用 Python
python -c "import secrets; print(secrets.token_hex(32))"
```

### Q: 数据库连接失败怎么办？

A: 检查以下项目：
1. PostgreSQL 服务是否启动
2. 数据库用户名和密码是否正确
3. 数据库是否存在
4. 网络连接是否正常

### Q: 如何配置不同的邮件服务商？

A: 常见邮件服务商配置：

**Gmail:**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_TLS=true
```

**QQ邮箱:**
```env
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_TLS=true
```

**163邮箱:**
```env
SMTP_HOST=smtp.163.com
SMTP_PORT=25
SMTP_TLS=false
```

### Q: 如何启用HTTPS？

A: 在生产环境中：
1. 设置 `SECURE_COOKIES=true`
2. 配置SSL证书路径（如果需要）
3. 更新 `CORS_ORIGINS` 为 https 地址
4. 使用反向代理（如 Nginx）处理SSL

## 安全建议

1. **永远不要**将 `.env` 文件提交到版本控制系统
2. 生产环境必须使用强密钥和密码
3. 定期更换密钥和密码
4. 限制数据库用户权限
5. 启用防火墙和访问控制
6. 定期备份数据库
7. 监控系统日志和性能指标

## 配置验证

启动应用时，系统会自动验证配置的有效性。如果发现配置错误，会在日志中显示详细的错误信息。

建议在部署前使用以下命令验证配置：

```bash
# 检查配置语法
python -m backend.app.core.config

# 测试数据库连接
python -c "from backend.app.core.database import engine; print('Database connection OK')"
```