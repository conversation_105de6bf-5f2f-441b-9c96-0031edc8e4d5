"""
WisCude 后台管理系统 - 核心配置
"""
from pydantic_settings import BaseSettings
from pydantic import Field, field_validator
from typing import Optional, List, Union
import os
from pathlib import Path

class Settings(BaseSettings):
    """应用配置"""

    # 应用基本信息
    APP_NAME: str = "WisCude 后台管理系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True

    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # 数据库配置
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/wiscude_admin",
        description="数据库连接URL"
    )
    DATABASE_ECHO: bool = False

    # Android SQLite 数据库路径
    ANDROID_DB_PATH: str = "../Wiscude/app/databases/wiscude.db"

    # JWT 配置
    SECRET_KEY: str = Field(
        default="wiscude-secret-key-change-in-production-2024",
        description="JWT密钥，生产环境必须更改为随机生成的安全密钥"
    )
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # 密码安全策略
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SPECIAL: bool = True
    PASSWORD_MAX_ATTEMPTS: int = 5
    PASSWORD_LOCKOUT_MINUTES: int = 15

    # Redis 配置
    REDIS_URL: str = "redis://localhost:6379/0"

    # CORS 配置
    CORS_ORIGINS: Union[str, List[str]] = Field(
        default="http://localhost:5173,http://127.0.0.1:5173",
        description="允许的CORS源，生产环境应限制为实际域名"
    )

    # 安全配置
    ALLOWED_HOSTS: Union[str, List[str]] = Field(
        default="localhost,127.0.0.1",
        description="允许的主机列表"
    )
    
    @field_validator('CORS_ORIGINS', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',') if origin.strip()]
        return v
    
    @field_validator('ALLOWED_HOSTS', mode='before')
    @classmethod
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',') if host.strip()]
        return v
    SECURE_COOKIES: bool = Field(
        default=False,
        description="是否启用安全Cookie（HTTPS环境设为True）"
    )
    SESSION_TIMEOUT_MINUTES: int = Field(
        default=60,
        description="会话超时时间（分钟）"
    )

    # 文件上传配置
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIR: str = "uploads"

    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE_PATH: str = "logs/wiscude-admin.log"
    LOG_RETENTION_DAYS: int = 30
    LOG_MAX_SIZE_MB: int = 100

    # 数据同步配置
    SYNC_INTERVAL_MINUTES: int = 30
    SYNC_BATCH_SIZE: int = 1000

    # 邮件配置
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAIL_FROM: Optional[str] = None
    EMAIL_FROM_NAME: Optional[str] = None

    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    METRICS_PATH: str = "/metrics"

    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局设置实例
settings = Settings()

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.UPLOAD_DIR,
        "logs",
        "backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# 数据库配置
class DatabaseConfig:
    """数据库配置类"""
    
    @staticmethod
    def get_database_url() -> str:
        """获取数据库连接URL"""
        return settings.DATABASE_URL
    
    @staticmethod
    def get_android_db_path() -> str:
        """获取Android数据库路径"""
        return settings.ANDROID_DB_PATH
    
    @staticmethod
    def is_android_db_available() -> bool:
        """检查Android数据库是否可用"""
        return Path(settings.ANDROID_DB_PATH).exists()

# JWT 配置
class JWTConfig:
    """JWT配置类"""
    
    @staticmethod
    def get_secret_key() -> str:
        """获取JWT密钥"""
        return settings.SECRET_KEY
    
    @staticmethod
    def get_algorithm() -> str:
        """获取JWT算法"""
        return settings.ALGORITHM
    
    @staticmethod
    def get_access_token_expire_minutes() -> int:
        """获取访问令牌过期时间（分钟）"""
        return settings.ACCESS_TOKEN_EXPIRE_MINUTES

# 导出配置实例
database_config = DatabaseConfig()
jwt_config = JWTConfig()
