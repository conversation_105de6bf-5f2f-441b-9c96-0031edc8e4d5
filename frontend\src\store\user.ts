/**
 * 用户资料管理Store
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { profileApi } from '@/api/profile'
import type { UserProfile, UserPreferences } from '@/api/profile'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const profile = ref<UserProfile | null>(null)
  const loading = ref(false)
  const updating = ref(false)

  // 计算属性
  const isProfileLoaded = computed(() => !!profile.value)
  const userPreferences = computed(() => profile.value?.preferences || {})
  const hasAvatar = computed(() => !!profile.value?.avatar)
  const displayName = computed(() => {
    if (!profile.value) return ''
    return profile.value.full_name || profile.value.username
  })

  // 获取用户资料
  const fetchProfile = async () => {
    loading.value = true
    try {
      const data = await profileApi.getProfile()
      profile.value = data
      return data
    } catch (error: any) {
      ElMessage.error(error.message || '获取用户资料失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新基本信息
  const updateProfile = async (data: Partial<UserProfile>) => {
    updating.value = true
    try {
      const updatedProfile = await profileApi.updateProfile(data)
      profile.value = updatedProfile
      ElMessage.success('个人资料更新成功')
      return updatedProfile
    } catch (error: any) {
      ElMessage.error(error.message || '更新个人资料失败')
      throw error
    } finally {
      updating.value = false
    }
  }

  // 修改密码
  const changePassword = async (data: {
    current_password: string
    new_password: string
  }) => {
    try {
      await profileApi.changePassword(data)
      ElMessage.success('密码修改成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '密码修改失败')
      throw error
    }
  }

  // 更新偏好设置
  const updatePreferences = async (preferences: UserPreferences) => {
    try {
      await profileApi.updatePreferences(preferences)
      if (profile.value) {
        profile.value.preferences = preferences
      }
      ElMessage.success('偏好设置更新成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '更新偏好设置失败')
      throw error
    }
  }

  // 上传头像
  const uploadAvatar = async (file: File) => {
    try {
      const response = await profileApi.uploadAvatar(file)
      if (profile.value) {
        profile.value.avatar = response.avatar_url
      }
      ElMessage.success('头像上传成功')
      return response.avatar_url
    } catch (error: any) {
      ElMessage.error(error.message || '头像上传失败')
      throw error
    }
  }

  // 启用双因素认证
  const enableTwoFactor = async () => {
    try {
      const response = await profileApi.enableTwoFactor()
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '启用双因素认证失败')
      throw error
    }
  }

  // 确认双因素认证
  const confirmTwoFactor = async (data: { token: string; secret: string }) => {
    try {
      await profileApi.confirmTwoFactor(data)
      if (profile.value) {
        profile.value.two_factor_enabled = true
      }
      ElMessage.success('双因素认证启用成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '确认双因素认证失败')
      throw error
    }
  }

  // 禁用双因素认证
  const disableTwoFactor = async (data: { password: string; token?: string }) => {
    try {
      await profileApi.disableTwoFactor(data)
      if (profile.value) {
        profile.value.two_factor_enabled = false
      }
      ElMessage.success('双因素认证已禁用')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '禁用双因素认证失败')
      throw error
    }
  }

  // 获取登录历史
  const getLoginHistory = async (params: { page?: number; page_size?: number }) => {
    try {
      const response = await profileApi.getLoginHistory(params)
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取登录历史失败')
      throw error
    }
  }

  // 获取安全状态
  const getSecurityStatus = async () => {
    try {
      const response = await profileApi.getSecurityStatus()
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取安全状态失败')
      throw error
    }
  }

  // 验证密码
  const verifyPassword = async (password: string) => {
    try {
      const response = await profileApi.verifyPassword(password)
      return response.valid
    } catch (error: any) {
      ElMessage.error(error.message || '密码验证失败')
      throw error
    }
  }

  // 注销其他会话
  const logoutOtherSessions = async () => {
    try {
      const response = await profileApi.logoutOtherSessions()
      ElMessage.success(`已注销 ${response.logged_out_sessions} 个其他会话`)
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '注销其他会话失败')
      throw error
    }
  }

  // 获取活跃会话
  const getActiveSessions = async () => {
    try {
      const response = await profileApi.getActiveSessions()
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取活跃会话失败')
      throw error
    }
  }

  // 导出个人数据
  const exportPersonalData = async () => {
    try {
      const blob = await profileApi.exportPersonalData()
      
      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `personal-data-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      ElMessage.success('个人数据导出成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '导出个人数据失败')
      throw error
    }
  }

  // 清除用户数据
  const clearUserData = () => {
    profile.value = null
  }

  // 应用主题设置
  const applyThemeSettings = () => {
    if (!profile.value?.preferences) return
    
    const { theme, ui_primary_color } = profile.value.preferences as any
    
    // 应用主题
    if (theme) {
      document.documentElement.setAttribute('data-theme', theme)
    }
    
    // 应用主色调
    if (ui_primary_color) {
      document.documentElement.style.setProperty('--el-color-primary', ui_primary_color)
    }
  }

  // 应用语言设置
  const applyLanguageSettings = () => {
    if (!profile.value?.preferences) return
    
    const { language } = profile.value.preferences as any
    
    if (language) {
      // 这里可以集成i18n来切换语言
      document.documentElement.setAttribute('lang', language)
    }
  }

  // 初始化用户设置
  const initializeUserSettings = async () => {
    try {
      await fetchProfile()
      applyThemeSettings()
      applyLanguageSettings()
    } catch (error) {
      console.error('初始化用户设置失败:', error)
    }
  }

  return {
    // 状态
    profile,
    loading,
    updating,
    
    // 计算属性
    isProfileLoaded,
    userPreferences,
    hasAvatar,
    displayName,
    
    // 方法
    fetchProfile,
    updateProfile,
    changePassword,
    updatePreferences,
    uploadAvatar,
    enableTwoFactor,
    confirmTwoFactor,
    disableTwoFactor,
    getLoginHistory,
    getSecurityStatus,
    verifyPassword,
    logoutOtherSessions,
    getActiveSessions,
    exportPersonalData,
    clearUserData,
    applyThemeSettings,
    applyLanguageSettings,
    initializeUserSettings
  }
})

export default useUserStore
