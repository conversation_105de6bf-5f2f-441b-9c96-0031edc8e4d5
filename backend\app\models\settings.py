"""
WisCude 后台管理系统 - 系统设置数据模型
"""
from sqlalchemy import Column, String, Integer, Boolean, Text, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
from enum import Enum
import json

Base = declarative_base()

class SystemSettings(Base):
    """系统设置数据库模型"""
    __tablename__ = "system_settings"
    
    id = Column(String(36), primary_key=True, default="system_config")
    
    # 基本系统配置
    app_name = Column(String(100), default="WisCude 后台管理系统")
    app_version = Column(String(20), default="1.0.0")
    app_description = Column(Text, default="智慧学习数据管理平台")
    timezone = Column(String(50), default="Asia/Shanghai")
    language = Column(String(10), default="zh-CN")
    
    # 数据同步设置
    sync_interval_minutes = Column(Integer, default=30)
    sync_batch_size = Column(Integer, default=1000)
    auto_sync_enabled = Column(Boolean, default=False)
    
    # 安全设置
    secret_key = Column(String(500))  # 加密存储
    jwt_algorithm = Column(String(20), default="HS256")
    access_token_expire_minutes = Column(Integer, default=30)
    refresh_token_expire_days = Column(Integer, default=7)
    session_timeout_minutes = Column(Integer, default=60)
    max_login_attempts = Column(Integer, default=5)
    lockout_duration_minutes = Column(Integer, default=15)
    password_min_length = Column(Integer, default=8)
    password_require_uppercase = Column(Boolean, default=True)
    password_require_lowercase = Column(Boolean, default=True)
    password_require_numbers = Column(Boolean, default=True)
    password_require_special = Column(Boolean, default=True)
    two_factor_auth_enabled = Column(Boolean, default=False)
    
    # 邮件配置
    smtp_host = Column(String(100))
    smtp_port = Column(Integer, default=587)
    smtp_user = Column(String(100))
    smtp_password = Column(String(500))  # 加密存储
    smtp_tls = Column(Boolean, default=True)
    smtp_ssl = Column(Boolean, default=False)
    email_from = Column(String(100))
    email_from_name = Column(String(100))
    email_templates = Column(JSON)
    
    # 日志配置
    log_level = Column(String(20), default="INFO")
    log_file_path = Column(String(500), default="backend/logs/wiscude-admin.log")
    log_retention_days = Column(Integer, default=30)
    log_max_size_mb = Column(Integer, default=100)
    
    # 用户界面设置
    ui_theme = Column(String(20), default="light")
    ui_primary_color = Column(String(20), default="#409EFF")
    ui_layout_mode = Column(String(20), default="classic")
    ui_sidebar_collapsed = Column(Boolean, default=False)
    ui_show_breadcrumb = Column(Boolean, default=True)
    ui_show_tags_view = Column(Boolean, default=True)
    default_page_size = Column(Integer, default=20)
    max_page_size = Column(Integer, default=100)
    dashboard_widgets = Column(JSON)
    
    # AI配置
    ai_enabled = Column(Boolean, default=False)
    ai_configs = Column(JSON, default=list)  # AI模型配置列表
    default_ai_model = Column(String(100))  # 默认AI模型ID

    # 其他配置（JSON格式存储）
    extra_config = Column(JSON, default=dict)

    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = Column(String(36))

# Pydantic 模型用于API
class SystemSettingsBase(BaseModel):
    """系统设置基础模型"""
    # 基本系统配置
    app_name: str = Field(default="WisCude 后台管理系统", max_length=100)
    app_version: str = Field(default="1.0.0", max_length=20)
    app_description: str = Field(default="智慧学习数据管理平台")
    timezone: str = Field(default="Asia/Shanghai", max_length=50)
    language: str = Field(default="zh-CN", max_length=10)
    
    # 数据同步设置
    sync_interval_minutes: int = Field(default=30, ge=1, le=1440)
    sync_batch_size: int = Field(default=1000, ge=100, le=10000)
    auto_sync_enabled: bool = False
    
    # 安全设置
    secret_key: Optional[str] = Field(None, min_length=32)
    jwt_algorithm: str = Field(default="HS256", pattern="^(HS256|HS384|HS512|RS256)$")
    access_token_expire_minutes: int = Field(default=30, ge=5, le=1440)
    refresh_token_expire_days: int = Field(default=7, ge=1, le=30)
    session_timeout_minutes: int = Field(default=60, ge=5, le=480)
    max_login_attempts: int = Field(default=5, ge=3, le=10)
    lockout_duration_minutes: int = Field(default=15, ge=5, le=1440)
    password_min_length: int = Field(default=8, ge=6, le=32)
    password_require_uppercase: bool = True
    password_require_lowercase: bool = True
    password_require_numbers: bool = True
    password_require_special: bool = True
    two_factor_auth_enabled: bool = False
    
    # 邮件配置
    smtp_host: Optional[str] = Field(None, max_length=100)
    smtp_port: int = Field(default=587, ge=1, le=65535)
    smtp_user: Optional[str] = Field(None, max_length=100)
    smtp_password: Optional[str] = Field(None, max_length=500)
    smtp_tls: bool = True
    smtp_ssl: bool = False
    email_from: Optional[str] = Field(None, max_length=100)
    email_from_name: Optional[str] = Field(None, max_length=100)
    email_templates: Optional[Dict[str, Any]] = None
    
    # 日志配置
    log_level: str = Field(default="INFO", pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$")
    log_file_path: str = Field(default="backend/logs/wiscude-admin.log", max_length=500)
    log_retention_days: int = Field(default=30, ge=1, le=365)
    log_max_size_mb: int = Field(default=100, ge=1, le=1000)
    
    # 用户界面设置
    ui_theme: str = Field(default="light", pattern="^(light|dark|auto)$")
    ui_primary_color: str = Field(default="#409EFF", max_length=20)
    ui_layout_mode: str = Field(default="classic", pattern="^(classic|top-nav|mix)$")
    ui_sidebar_collapsed: bool = False
    ui_show_breadcrumb: bool = True
    ui_show_tags_view: bool = True
    default_page_size: int = Field(default=20, ge=10, le=100)
    max_page_size: int = Field(default=100, ge=50, le=500)
    dashboard_widgets: Optional[list] = None
    
    # AI配置
    ai_enabled: bool = False
    ai_configs: List[Dict[str, Any]] = Field(default_factory=list)
    default_ai_model: Optional[str] = None

    # 其他配置
    extra_config: Dict[str, Any] = Field(default_factory=dict)



    @validator('email_from')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('邮箱格式不正确')
        return v

class SystemSettingsCreate(SystemSettingsBase):
    """创建系统设置模型"""
    pass

class SystemSettingsUpdate(SystemSettingsBase):
    """更新系统设置模型"""
    pass

class SystemSettingsResponse(SystemSettingsBase):
    """系统设置响应模型"""
    created_at: datetime
    updated_at: datetime
    updated_by: Optional[str]
    
    class Config:
        from_attributes = True

class SettingsTestRequest(BaseModel):
    """设置测试请求模型"""
    test_type: str = Field(..., pattern="^(database|email)$")
    config: Dict[str, Any]

class SettingsImportRequest(BaseModel):
    """设置导入请求模型"""
    settings: Dict[str, Any]
    overwrite: bool = False

class SettingsExportResponse(BaseModel):
    """设置导出响应模型"""
    settings: Dict[str, Any]
    exported_at: datetime
    version: str

# AI相关模型
class AIProvider(str, Enum):
    """AI服务提供商"""
    QWEN = "qwen"
    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    KIMI = "kimi"
    DOUBAO = "doubao"
    CLAUDE = "claude"
    GEMINI = "gemini"
    BAICHUAN = "baichuan"
    CHATGLM = "chatglm"
    WENXIN = "wenxin"
    SPARK = "spark"
    MINIMAX = "minimax"
    CUSTOM = "custom"

class AIModelParameters(BaseModel):
    """AI模型参数"""
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=2000, ge=1, le=32000)
    top_p: float = Field(default=0.9, ge=0.0, le=1.0)
    frequency_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    presence_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    stream: bool = False
    timeout: int = Field(default=30, ge=5, le=300)
    retry_count: int = Field(default=3, ge=0, le=5)
    custom_params: Optional[Dict[str, Any]] = Field(default_factory=dict)

class AIModelConfig(BaseModel):
    """AI模型配置"""
    id: str
    name: str
    provider: AIProvider
    display_name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(default="", max_length=500)
    api_key: str = Field(..., min_length=1)
    api_endpoint: str = Field(..., pattern=r'^https?://.+')
    model_name: str = Field(..., min_length=1, max_length=100)
    parameters: AIModelParameters = Field(default_factory=AIModelParameters)
    enabled: bool = True
    is_default: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        protected_namespaces = ()

class AITestRequest(BaseModel):
    """AI连接测试请求"""
    model_id: str
    test_message: Optional[str] = "Hello, this is a test message."
    
    class Config:
        protected_namespaces = ()

class AITestResult(BaseModel):
    """AI连接测试结果"""
    success: bool
    message: str
    response_time: Optional[float] = None
    model_info: Optional[Dict[str, Any]] = None
    error_details: Optional[str] = None
    
    class Config:
        protected_namespaces = ()

class AIConfigRequest(BaseModel):
    """AI配置请求"""
    ai_enabled: bool
    default_ai_model: Optional[str] = None
    models: List[AIModelConfig] = Field(default_factory=list)

class AIConfigResponse(BaseModel):
    """AI配置响应"""
    ai_enabled: bool
    default_ai_model: Optional[str]
    models: List[AIModelConfig]

    class Config:
        from_attributes = True
