<template>
  <div class="activities-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>活动竞赛</h1>
          <p>参与精彩活动，展示学习成果，赢取丰厚奖励</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            创建活动
          </el-button>
          <el-button @click="refreshActivities" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 活动状态筛选 -->
    <div class="status-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部活动" name="all">
          <template #label>
            <span class="tab-label">
              全部活动
              <el-badge :value="getTotalCount()" class="tab-badge" />
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="报名中" name="registration">
          <template #label>
            <span class="tab-label">
              报名中
              <el-badge :value="getStatusCount('registration')" type="primary" class="tab-badge" />
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="进行中" name="ongoing">
          <template #label>
            <span class="tab-label">
              进行中
              <el-badge :value="getStatusCount('ongoing')" type="success" class="tab-badge" />
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="已结束" name="finished">
          <template #label>
            <span class="tab-label">
              已结束
              <el-badge :value="getStatusCount('finished')" type="info" class="tab-badge" />
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-section">
      <div class="filter-content">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索活动..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-controls">
          <el-select v-model="selectedType" placeholder="活动类型" @change="handleTypeChange">
            <el-option label="全部类型" value="" />
            <el-option label="学习挑战" value="challenge" />
            <el-option label="知识竞赛" value="quiz" />
            <el-option label="技能比赛" value="skill" />
            <el-option label="团队协作" value="team" />
          </el-select>

          <el-select v-model="selectedDifficulty" placeholder="难度等级" @change="handleDifficultyChange">
            <el-option label="全部难度" value="" />
            <el-option label="初级" value="easy" />
            <el-option label="中级" value="medium" />
            <el-option label="高级" value="hard" />
          </el-select>

          <el-select v-model="sortBy" placeholder="排序方式" @change="handleSortChange">
            <el-option label="最新发布" value="latest" />
            <el-option label="即将开始" value="upcoming" />
            <el-option label="参与人数" value="participants" />
            <el-option label="奖励金额" value="reward" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 活动列表 -->
    <div class="activities-list" v-loading="loading">
      <div class="activities-grid">
        <div
          v-for="activity in filteredActivities"
          :key="activity.id"
          class="activity-card"
          :class="getActivityCardClass(activity.status)"
        >
          <!-- 活动状态标签 -->
          <div class="status-badge">
            <el-tag :type="getStatusTagType(activity.status)" size="small">
              {{ getStatusText(activity.status) }}
            </el-tag>
          </div>

          <!-- 活动封面 -->
          <div class="activity-cover">
            <img :src="activity.cover || '/default-activity.jpg'" :alt="activity.title" />
            <div class="cover-overlay">
              <div class="activity-type">
                <el-tag size="small">{{ getTypeText(activity.type) }}</el-tag>
              </div>
              <div class="difficulty-level">
                <el-rate
                  v-model="activity.difficulty"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}星难度"
                />
              </div>
            </div>
          </div>

          <!-- 活动信息 -->
          <div class="activity-info">
            <h3 class="activity-title">{{ activity.title }}</h3>
            <p class="activity-description">{{ activity.description }}</p>

            <div class="activity-meta">
              <div class="meta-item">
                <el-icon><Calendar /></el-icon>
                <span>{{ formatDateRange(activity.start_time, activity.end_time) }}</span>
              </div>
              <div class="meta-item">
                <el-icon><User /></el-icon>
                <span>{{ activity.participants }}/{{ activity.max_participants }}人</span>
              </div>
              <div class="meta-item">
                <el-icon><Trophy /></el-icon>
                <span>{{ activity.reward }}</span>
              </div>
            </div>
          </div>

          <!-- 活动操作 -->
          <div class="activity-actions">
            <template v-if="activity.status === 'registration'">
              <el-button
                v-if="!activity.isRegistered"
                type="primary"
                @click="registerActivity(activity)"
                :loading="activity.registering"
              >
                立即报名
              </el-button>
              <el-button
                v-else
                type="success"
                disabled
              >
                已报名
              </el-button>
            </template>

            <template v-else-if="activity.status === 'ongoing'">
              <el-button
                v-if="activity.isRegistered"
                type="primary"
                @click="joinActivity(activity)"
              >
                参与活动
              </el-button>
              <el-button
                v-else
                disabled
              >
                未报名
              </el-button>
            </template>

            <template v-else-if="activity.status === 'finished'">
              <el-button @click="viewResults(activity)">
                查看结果
              </el-button>
            </template>

            <el-button text @click="viewDetails(activity)">
              查看详情
            </el-button>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore">
        <el-button @click="loadMore" :loading="loadingMore">
          加载更多
        </el-button>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!loading && filteredActivities.length === 0">
        <el-empty description="暂无活动">
          <el-button type="primary" @click="showCreateDialog = true">
            创建第一个活动
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Search, Calendar, User, Trophy, TrendCharts
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const creating = ref(false)
const showCreateDialog = ref(false)
const activeTab = ref('all')
const searchKeyword = ref('')
const selectedType = ref('')
const selectedDifficulty = ref('')
const sortBy = ref('latest')
const hasMore = ref(true)

// 表单引用
const createFormRef = ref()

// 创建表单
const createForm = reactive({
  title: '',
  type: '',
  difficulty: 3,
  timeRange: [] as string[],
  maxParticipants: 100,
  reward: '',
  description: ''
})

// 表单验证规则
const createRules = {
  title: [
    { required: true, message: '请输入活动标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择活动类型', trigger: 'change' }
  ],
  timeRange: [
    { required: true, message: '请选择活动时间', trigger: 'change' }
  ],
  maxParticipants: [
    { required: true, message: '请输入参与人数', trigger: 'blur' }
  ],
  reward: [
    { required: true, message: '请输入奖励内容', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入活动描述', trigger: 'blur' },
    { min: 20, max: 1000, message: '描述长度在 20 到 1000 个字符', trigger: 'blur' }
  ]
}

// 活动数据
const activities = ref([
  {
    id: '1',
    title: '数学竞赛挑战赛',
    description: '测试你的数学能力，与全国学霸一较高下！涵盖代数、几何、概率等多个领域。',
    type: 'quiz',
    difficulty: 4,
    status: 'registration',
    cover: '',
    start_time: '2024-02-01 09:00',
    end_time: '2024-02-03 18:00',
    participants: 156,
    max_participants: 500,
    reward: '一等奖：iPad Pro + 奖学金5000元',
    progress: 0,
    isRegistered: false,
    registering: false,
    topParticipants: [
      { id: '1', name: '数学天才', avatar: '', score: 95 },
      { id: '2', name: '计算达人', avatar: '', score: 92 },
      { id: '3', name: '逻辑大师', avatar: '', score: 89 }
    ]
  },
  {
    id: '2',
    title: '编程马拉松',
    description: '48小时编程挑战，团队协作开发创新项目，展示你的编程技能和创意思维。',
    type: 'skill',
    difficulty: 5,
    status: 'ongoing',
    cover: '',
    start_time: '2024-01-25 18:00',
    end_time: '2024-01-27 18:00',
    participants: 89,
    max_participants: 120,
    reward: '冠军团队：MacBook Pro + 实习机会',
    progress: 65,
    isRegistered: true,
    registering: false,
    topParticipants: [
      { id: '4', name: '代码忍者', avatar: '', score: 88 },
      { id: '5', name: '算法大神', avatar: '', score: 85 },
      { id: '6', name: '全栈高手', avatar: '', score: 82 }
    ]
  },
  {
    id: '3',
    title: '英语演讲比赛',
    description: '提升英语口语表达能力，主题为"科技改变生活"，展示你的语言魅力。',
    type: 'challenge',
    difficulty: 3,
    status: 'finished',
    cover: '',
    start_time: '2024-01-15 14:00',
    end_time: '2024-01-15 17:00',
    participants: 45,
    max_participants: 50,
    reward: '最佳演讲者：雅思培训课程',
    progress: 100,
    isRegistered: true,
    registering: false,
    topParticipants: [
      { id: '7', name: '演讲之星', avatar: '', score: 96 },
      { id: '8', name: '语言大师', avatar: '', score: 94 },
      { id: '9', name: '表达达人', avatar: '', score: 91 }
    ]
  }
])

// 计算属性
const filteredActivities = computed(() => {
  let filtered = [...activities.value]

  // 状态筛选
  if (activeTab.value !== 'all') {
    filtered = filtered.filter(activity => activity.status === activeTab.value)
  }

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(activity =>
      activity.title.toLowerCase().includes(keyword) ||
      activity.description.toLowerCase().includes(keyword)
    )
  }

  // 类型过滤
  if (selectedType.value) {
    filtered = filtered.filter(activity => activity.type === selectedType.value)
  }

  // 难度过滤
  if (selectedDifficulty.value) {
    const difficultyMap = { easy: [1, 2], medium: [3], hard: [4, 5] }
    const range = difficultyMap[selectedDifficulty.value as keyof typeof difficultyMap]
    filtered = filtered.filter(activity => range.includes(activity.difficulty))
  }

  // 排序
  switch (sortBy.value) {
    case 'upcoming':
      filtered.sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())
      break
    case 'participants':
      filtered.sort((a, b) => b.participants - a.participants)
      break
    case 'reward':
      // 简单按奖励文本长度排序，实际应该按奖励价值
      filtered.sort((a, b) => b.reward.length - a.reward.length)
      break
    case 'latest':
    default:
      // 按创建时间排序（这里用ID模拟）
      filtered.sort((a, b) => parseInt(b.id) - parseInt(a.id))
      break
  }

  return filtered
})

// 方法
const getTotalCount = (): number => {
  return activities.value.length
}

const getStatusCount = (status: string): number => {
  return activities.value.filter(activity => activity.status === status).length
}

const refreshActivities = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleTabChange = (tab: string) => {
  activeTab.value = tab
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleTypeChange = () => {
  // 类型过滤逻辑已在计算属性中处理
}

const handleDifficultyChange = () => {
  // 难度过滤逻辑已在计算属性中处理
}

const handleSortChange = () => {
  // 排序逻辑已在计算属性中处理
}

const getActivityCardClass = (status: string): string => {
  return `status-${status}`
}

const getStatusTagType = (status: string): string => {
  const types = {
    registration: 'primary',
    ongoing: 'success',
    finished: 'info'
  }
  return types[status as keyof typeof types] || 'default'
}

const getStatusText = (status: string): string => {
  const texts = {
    registration: '报名中',
    ongoing: '进行中',
    finished: '已结束'
  }
  return texts[status as keyof typeof texts] || status
}

const getTypeText = (type: string): string => {
  const texts = {
    challenge: '学习挑战',
    quiz: '知识竞赛',
    skill: '技能比赛',
    team: '团队协作'
  }
  return texts[type as keyof typeof texts] || type
}

const getRankClass = (index: number): string => {
  const classes = ['rank-1', 'rank-2', 'rank-3']
  return classes[index] || ''
}

const formatDateRange = (startTime: string, endTime: string): string => {
  const start = new Date(startTime)
  const end = new Date(endTime)

  const formatDate = (date: Date) => {
    return `${date.getMonth() + 1}/${date.getDate()}`
  }

  const formatTime = (date: Date) => {
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  }

  if (start.toDateString() === end.toDateString()) {
    return `${formatDate(start)} ${formatTime(start)}-${formatTime(end)}`
  } else {
    return `${formatDate(start)}-${formatDate(end)}`
  }
}

const registerActivity = async (activity: any) => {
  activity.registering = true
  try {
    // 模拟报名API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    activity.isRegistered = true
    activity.participants += 1
    ElMessage.success('报名成功！')
  } catch (error) {
    ElMessage.error('报名失败，请重试')
  } finally {
    activity.registering = false
  }
}

const joinActivity = (activity: any) => {
  // 跳转到活动参与页面
  console.log('参与活动:', activity)
  ElMessage.info('跳转到活动页面...')
}

const viewResults = (activity: any) => {
  // 查看活动结果
  console.log('查看结果:', activity)
  ElMessage.info('查看活动结果...')
}

const viewDetails = (activity: any) => {
  // 查看活动详情
  console.log('查看详情:', activity)
  ElMessage.info('查看活动详情...')
}

const loadMore = async () => {
  loadingMore.value = true
  try {
    // 模拟加载更多数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    hasMore.value = false // 模拟没有更多数据
  } catch (error) {
    ElMessage.error('加载失败')
  } finally {
    loadingMore.value = false
  }
}

const createActivity = async () => {
  try {
    await createFormRef.value?.validate()
    creating.value = true

    // 模拟创建API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 添加到活动列表
    const newActivity = {
      id: Date.now().toString(),
      title: createForm.title,
      description: createForm.description,
      type: createForm.type,
      difficulty: createForm.difficulty,
      status: 'registration',
      cover: '',
      start_time: createForm.timeRange[0],
      end_time: createForm.timeRange[1],
      participants: 0,
      max_participants: createForm.maxParticipants,
      reward: createForm.reward,
      progress: 0,
      isRegistered: false,
      registering: false,
      topParticipants: []
    }

    activities.value.unshift(newActivity)

    // 重置表单
    createFormRef.value?.resetFields()
    showCreateDialog.value = false

    ElMessage.success('活动创建成功')
  } catch (error) {
    console.error('创建失败:', error)
  } finally {
    creating.value = false
  }
}

const handleCloseCreate = (done: () => void) => {
  if (createForm.title || createForm.description) {
    ElMessageBox.confirm('确定要关闭吗？未保存的内容将丢失。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      done()
    }).catch(() => {
      // 取消关闭
    })
  } else {
    done()
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.activities-page {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 状态标签页 */
  .status-tabs {
    margin-bottom: var(--spacing-6);

    .el-tabs {
      .el-tabs__header {
        margin-bottom: 0;
        border-bottom: 2px solid var(--border-light);
      }

      .el-tabs__nav-wrap {
        .el-tabs__nav {
          .el-tabs__item {
            font-weight: var(--font-weight-medium);

            .tab-label {
              display: flex;
              align-items: center;
              gap: var(--spacing-2);

              .tab-badge {
                .el-badge__content {
                  font-size: var(--font-size-xs);
                  min-width: 16px;
                  height: 16px;
                  line-height: 16px;
                  padding: 0 4px;
                }
              }
            }
          }
        }
      }
    }
  }

  /* 筛选区域 */
  .filter-section {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);

    .filter-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);

      .search-bar {
        .el-input {
          max-width: 400px;
        }
      }

      .filter-controls {
        display: flex;
        gap: var(--spacing-4);
        flex-wrap: wrap;

        .el-select {
          min-width: 140px;
        }
      }
    }
  }

  /* 活动列表 */
  .activities-list {
    .activities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: var(--spacing-6);
      margin-bottom: var(--spacing-8);

      .activity-card {
        background: white;
        border-radius: var(--radius-xl);
        border: 1px solid var(--border-light);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-fast);
        overflow: hidden;
        position: relative;

        &:hover {
          transform: translateY(-4px);
          box-shadow: var(--shadow-lg);
        }

        &.status-registration {
          border-left: 4px solid var(--primary-color);
        }

        &.status-ongoing {
          border-left: 4px solid var(--success-color);
        }

        &.status-finished {
          border-left: 4px solid var(--info-color);
        }

        .status-badge {
          position: absolute;
          top: var(--spacing-3);
          right: var(--spacing-3);
          z-index: 2;
        }

        .activity-cover {
          position: relative;
          height: 200px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
          }

          .cover-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            padding: var(--spacing-4);
            display: flex;
            justify-content: space-between;
            align-items: flex-end;

            .activity-type {
              .el-tag {
                background: rgba(255, 255, 255, 0.9);
                color: var(--text-primary);
                border: none;
              }
            }

            .difficulty-level {
              .el-rate {
                .el-rate__text {
                  color: white;
                  font-size: var(--font-size-xs);
                }
              }
            }
          }
        }

        .activity-info {
          padding: var(--spacing-5);

          .activity-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-3) 0;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .activity-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            line-height: 1.6;
            margin: 0 0 var(--spacing-4) 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .activity-meta {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-4);

            .meta-item {
              display: flex;
              align-items: center;
              gap: var(--spacing-2);
              font-size: var(--font-size-sm);
              color: var(--text-tertiary);

              .el-icon {
                font-size: var(--font-size-base);
                color: var(--text-secondary);
              }
            }
          }
          .activity-progress {
            margin-bottom: var(--spacing-4);

            .progress-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: var(--spacing-2);
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
            }
          }

          .leaderboard-preview {
            margin-bottom: var(--spacing-4);

            .leaderboard-title {
              display: flex;
              align-items: center;
              gap: var(--spacing-2);
              margin-bottom: var(--spacing-3);
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--text-primary);

              .el-icon {
                color: var(--warning-color);
              }
            }

            .top-participants {
              display: flex;
              flex-direction: column;
              gap: var(--spacing-2);

              .participant-item {
                display: flex;
                align-items: center;
                gap: var(--spacing-2);
                padding: var(--spacing-2);
                border-radius: var(--radius-md);
                background: var(--bg-secondary);
                font-size: var(--font-size-xs);

                &.rank-1 {
                  background: linear-gradient(135deg, #ffd700, #ffed4e);
                  color: #8b5a00;
                }

                &.rank-2 {
                  background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
                  color: #666;
                }

                &.rank-3 {
                  background: linear-gradient(135deg, #cd7f32, #daa520);
                  color: #5d4e37;
                }

                .rank {
                  width: 20px;
                  height: 20px;
                  border-radius: 50%;
                  background: var(--text-tertiary);
                  color: white;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: var(--font-size-xs);
                  font-weight: var(--font-weight-bold);
                }

                .name {
                  flex: 1;
                  font-weight: var(--font-weight-medium);
                }

                .score {
                  font-weight: var(--font-weight-semibold);
                }
              }
            }
          }
        }

        .activity-actions {
          padding: var(--spacing-4) var(--spacing-5);
          background: var(--bg-secondary);
          border-top: 1px solid var(--border-light);
          display: flex;
          gap: var(--spacing-3);
          justify-content: space-between;

          .el-button {
            flex: 1;
            border-radius: var(--radius-lg);
            font-weight: var(--font-weight-medium);

            &[text] {
              flex: none;
              min-width: auto;
            }
          }
        }
      }
    }

    /* 加载更多 */
    .load-more {
      text-align: center;
      margin: var(--spacing-8) 0;
    }

    /* 空状态 */
    .empty-state {
      text-align: center;
      padding: var(--spacing-12) var(--spacing-6);
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .activities-page {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .filter-section .filter-content {
      .filter-controls {
        .el-select {
          min-width: 120px;
        }
      }
    }

    .activities-list .activities-grid {
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: var(--spacing-4);
    }
  }
}

@include respond-to('md') {
  .activities-page {
    padding: var(--spacing-4);

    .status-tabs {
      .el-tabs {
        .el-tabs__header {
          .el-tabs__nav-wrap {
            .el-tabs__nav {
              .el-tabs__item {
                .tab-label {
                  flex-direction: column;
                  gap: var(--spacing-1);
                  font-size: var(--font-size-xs);
                }
              }
            }
          }
        }
      }
    }

    .filter-section .filter-content {
      .filter-controls {
        flex-direction: column;
        align-items: stretch;

        .el-select {
          min-width: auto;
        }
      }
    }

    .activities-list .activities-grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);

      .activity-card {
        .activity-cover {
          height: 150px;

          .cover-overlay {
            padding: var(--spacing-3);

            .difficulty-level {
              .el-rate {
                transform: scale(0.8);
                transform-origin: right bottom;
              }
            }
          }
        }

        .activity-info {
          padding: var(--spacing-4);

          .activity-title {
            font-size: var(--font-size-base);
          }

          .activity-meta {
            .meta-item {
              font-size: var(--font-size-xs);
            }
          }

          .leaderboard-preview {
            .top-participants {
              .participant-item {
                padding: var(--spacing-1) var(--spacing-2);
              }
            }
          }
        }

        .activity-actions {
          padding: var(--spacing-3) var(--spacing-4);
          flex-direction: column;

          .el-button {
            &[text] {
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style>