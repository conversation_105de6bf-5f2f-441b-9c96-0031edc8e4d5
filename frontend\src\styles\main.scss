// 智慧教育管理系统 - 主样式文件
// Main Styles for Smart Education Management System

// 导入设计系统
@import './design-system.scss';

// 导入Element Plus主题
@import './element-plus-theme.scss';

// 导入通用组件样式
@import './components.scss';

// ==================== 全局样式重置 ====================
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

// ==================== 滚动条样式 ====================
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);

  &:hover {
    background: var(--border-dark);
  }
}

// ==================== 选择文本样式 ====================
::selection {
  background-color: rgba(79, 70, 229, 0.2);
  color: var(--primary-color);
}

::-moz-selection {
  background-color: rgba(79, 70, 229, 0.2);
  color: var(--primary-color);
}

// ==================== 焦点样式 ====================
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

// ==================== 链接样式 ====================
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);

  &:hover {
    color: var(--primary-light);
    text-decoration: underline;
  }

  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

// ==================== 标题样式 ====================
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--spacing-4);
}

h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-2xl);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5 {
  font-size: var(--font-size-base);
}

h6 {
  font-size: var(--font-size-sm);
}

// ==================== 段落样式 ====================
p {
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
}

// ==================== 列表样式 ====================
ul, ol {
  margin-bottom: var(--spacing-4);
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
}

// ==================== 代码样式 ====================
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--bg-tertiary);
  color: var(--primary-color);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow-x: auto;
  margin-bottom: var(--spacing-4);

  code {
    background: none;
    border: none;
    padding: 0;
  }
}

// ==================== 表格样式 ====================
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-6);
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

th, td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

th {
  background-color: var(--bg-tertiary);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

td {
  color: var(--text-secondary);
}

tr:hover {
  background-color: var(--bg-accent);
}

// ==================== 表单样式 ====================
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

// ==================== 图片样式 ====================
img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
}

// ==================== 分割线样式 ====================
hr {
  border: none;
  height: 1px;
  background-color: var(--border-light);
  margin: var(--spacing-8) 0;
}

// ==================== 工具类 ====================
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-light {
  font-weight: var(--font-weight-light);
}

// ==================== 间距工具类 ====================
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-5 { margin-top: var(--spacing-5); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

// ==================== 显示工具类 ====================
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

// ==================== Flex工具类 ====================
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

// ==================== 宽高工具类 ====================
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

// ==================== 打印样式 ====================
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.5;
  }

  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }

  p, blockquote {
    orphans: 3;
    widows: 3;
  }

  blockquote, pre {
    page-break-inside: avoid;
  }

  .page-container {
    padding: 0;
  }

  .toolbar,
  .pagination-wrapper,
  .el-button,
  .mobile-hidden {
    display: none !important;
  }
}
