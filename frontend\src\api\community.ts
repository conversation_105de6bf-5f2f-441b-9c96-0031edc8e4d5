/**
 * 社区管理API接口
 */
import request from './request'

// ==================== 资源库管理 ====================

/**
 * 获取学习资源列表
 */
export const getLearningResources = (params: {
  page?: number
  size?: number
  keyword?: string
  type?: string
  status?: string
  subject?: string
}) => {
  return request({
    url: '/v1/community/resources',
    method: 'get',
    params
  })
}

/**
 * 获取资源详情
 */
export const getLearningResource = (resourceId: string) => {
  return request({
    url: `/api/v1/community/resources/${resourceId}`,
    method: 'get'
  })
}

/**
 * 审核通过资源
 */
export const approveLearningResource = (resourceId: string) => {
  return request({
    url: `/api/v1/community/resources/${resourceId}/approve`,
    method: 'put'
  })
}

/**
 * 拒绝资源
 */
export const rejectLearningResource = (resourceId: string) => {
  return request({
    url: `/api/v1/community/resources/${resourceId}/reject`,
    method: 'put'
  })
}

/**
 * 删除资源
 */
export const deleteLearningResource = (resourceId: string) => {
  return request({
    url: `/api/v1/community/resources/${resourceId}`,
    method: 'delete'
  })
}

// ==================== 自习室管理 ====================

/**
 * 获取自习室列表
 */
export const getStudyRooms = (params: {
  page?: number
  size?: number
  keyword?: string
  status?: string
  is_public?: boolean
}) => {
  return request({
    url: '/v1/community/study-rooms',
    method: 'get',
    params
  })
}

/**
 * 获取自习室成员列表
 */
export const getStudyRoomMembers = (roomId: string) => {
  return request({
    url: `/api/v1/community/study-rooms/${roomId}/members`,
    method: 'get'
  })
}

/**
 * 删除自习室
 */
export const deleteStudyRoom = (roomId: string) => {
  return request({
    url: `/api/v1/community/study-rooms/${roomId}`,
    method: 'delete'
  })
}

// ==================== 话题讨论管理 ====================

/**
 * 获取话题列表
 */
export const getTopics = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  status?: string
  is_pinned?: boolean
}) => {
  return request({
    url: '/v1/community/topics',
    method: 'get',
    params
  })
}

/**
 * 获取话题详情
 */
export const getTopic = (topicId: string) => {
  return request({
    url: `/api/v1/community/topics/${topicId}`,
    method: 'get'
  })
}

/**
 * 获取话题回复列表
 */
export const getTopicReplies = (topicId: string, params: {
  page?: number
  size?: number
}) => {
  return request({
    url: `/api/v1/community/topics/${topicId}/replies`,
    method: 'get',
    params
  })
}

/**
 * 审核话题
 */
export const reviewTopic = (topicId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/community/topics/${topicId}/review`,
    method: 'put',
    data
  })
}

/**
 * 置顶话题
 */
export const pinTopic = (topicId: string) => {
  return request({
    url: `/api/v1/community/topics/${topicId}/pin`,
    method: 'post'
  })
}

/**
 * 取消置顶话题
 */
export const unpinTopic = (topicId: string) => {
  return request({
    url: `/api/v1/community/topics/${topicId}/unpin`,
    method: 'post'
  })
}

/**
 * 删除话题
 */
export const deleteTopic = (topicId: string) => {
  return request({
    url: `/api/v1/community/topics/${topicId}`,
    method: 'delete'
  })
}

/**
 * 删除话题回复
 */
export const deleteTopicReply = (topicId: string, replyId: string) => {
  return request({
    url: `/api/v1/community/topics/${topicId}/replies/${replyId}`,
    method: 'delete'
  })
}

// ==================== 部落管理 ====================

/**
 * 获取部落列表
 */
export const getTribes = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  status?: string
}) => {
  return request({
    url: '/v1/community/tribes',
    method: 'get',
    params
  })
}

/**
 * 获取部落详情
 */
export const getTribe = (tribeId: string) => {
  return request({
    url: `/api/v1/community/tribes/${tribeId}`,
    method: 'get'
  })
}

/**
 * 获取部落成员列表
 */
export const getTribeMembers = (tribeId: string, params: {
  page?: number
  size?: number
  role?: string
}) => {
  return request({
    url: `/api/v1/community/tribes/${tribeId}/members`,
    method: 'get',
    params
  })
}

/**
 * 审核部落
 */
export const reviewTribe = (tribeId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/community/tribes/${tribeId}/review`,
    method: 'put',
    data
  })
}

/**
 * 删除部落
 */
export const deleteTribe = (tribeId: string) => {
  return request({
    url: `/api/v1/community/tribes/${tribeId}`,
    method: 'delete'
  })
}

/**
 * 移除部落成员
 */
export const removeTribeMember = (tribeId: string, memberId: string) => {
  return request({
    url: `/api/v1/community/tribes/${tribeId}/members/${memberId}`,
    method: 'delete'
  })
}

// ==================== 导师管理 ====================

/**
 * 获取导师列表
 */
export const getMentors = (params: {
  page?: number
  size?: number
  keyword?: string
  status?: string
  is_verified?: boolean
}) => {
  return request({
    url: '/v1/community/mentors',
    method: 'get',
    params
  })
}

/**
 * 获取导师详情
 */
export const getMentor = (mentorId: string) => {
  return request({
    url: `/api/v1/community/mentors/${mentorId}`,
    method: 'get'
  })
}

/**
 * 认证导师
 */
export const verifyMentor = (mentorId: string, data: {
  is_verified: boolean
  verification_comment?: string
}) => {
  return request({
    url: `/api/v1/community/mentors/${mentorId}/verify`,
    method: 'post',
    data
  })
}

/**
 * 获取师徒关系列表
 */
export const getMentorships = (params: {
  page?: number
  size?: number
  mentor_id?: string
  mentee_id?: string
  status?: string
}) => {
  return request({
    url: '/v1/community/mentorships',
    method: 'get',
    params
  })
}

/**
 * 审核师徒关系
 */
export const reviewMentorship = (mentorshipId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/community/mentorships/${mentorshipId}/review`,
    method: 'put',
    data
  })
}

// ==================== 经验分享管理 ====================

/**
 * 获取经验分享列表
 */
export const getExperiences = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  status?: string
}) => {
  return request({
    url: '/v1/community/experiences',
    method: 'get',
    params
  })
}

/**
 * 获取经验分享详情
 */
export const getExperience = (experienceId: string) => {
  return request({
    url: `/api/v1/community/experiences/${experienceId}`,
    method: 'get'
  })
}

/**
 * 审核经验分享
 */
export const reviewExperience = (experienceId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/community/experiences/${experienceId}/review`,
    method: 'put',
    data
  })
}

/**
 * 推荐经验分享
 */
export const recommendExperience = (experienceId: string) => {
  return request({
    url: `/api/v1/community/experiences/${experienceId}/recommend`,
    method: 'post'
  })
}

/**
 * 删除经验分享
 */
export const deleteExperience = (experienceId: string) => {
  return request({
    url: `/api/v1/community/experiences/${experienceId}`,
    method: 'delete'
  })
}

// ==================== 活动管理 ====================

/**
 * 获取活动列表
 */
export const getActivities = (params: {
  page?: number
  size?: number
  keyword?: string
  activity_type?: string
  status?: string
}) => {
  return request({
    url: '/v1/community/activities',
    method: 'get',
    params
  })
}

/**
 * 获取活动详情
 */
export const getActivity = (activityId: string) => {
  return request({
    url: `/api/v1/community/activities/${activityId}`,
    method: 'get'
  })
}

/**
 * 获取活动参与者列表
 */
export const getActivityParticipants = (activityId: string, params: {
  page?: number
  size?: number
}) => {
  return request({
    url: `/api/v1/community/activities/${activityId}/participants`,
    method: 'get',
    params
  })
}

/**
 * 审核活动
 */
export const reviewActivity = (activityId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/community/activities/${activityId}/review`,
    method: 'put',
    data
  })
}

/**
 * 删除活动
 */
export const deleteActivity = (activityId: string) => {
  return request({
    url: `/api/v1/community/activities/${activityId}`,
    method: 'delete'
  })
}

// ==================== 许愿池管理 ====================

/**
 * 获取许愿列表
 */
export const getWishes = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  status?: string
}) => {
  return request({
    url: '/v1/community/wishes',
    method: 'get',
    params
  })
}

/**
 * 获取许愿详情
 */
export const getWish = (wishId: string) => {
  return request({
    url: `/api/v1/community/wishes/${wishId}`,
    method: 'get'
  })
}

/**
 * 获取许愿评论列表
 */
export const getWishComments = (wishId: string, params: {
  page?: number
  size?: number
}) => {
  return request({
    url: `/api/v1/community/wishes/${wishId}/comments`,
    method: 'get',
    params
  })
}

/**
 * 审核许愿
 */
export const reviewWish = (wishId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/community/wishes/${wishId}/review`,
    method: 'put',
    data
  })
}

/**
 * 删除许愿
 */
export const deleteWish = (wishId: string) => {
  return request({
    url: `/api/v1/community/wishes/${wishId}`,
    method: 'delete'
  })
}

/**
 * 删除许愿评论
 */
export const deleteWishComment = (wishId: string, commentId: string) => {
  return request({
    url: `/api/v1/community/wishes/${wishId}/comments/${commentId}`,
    method: 'delete'
  })
}

// ==================== 游戏活动管理 ====================

/**
 * 获取游戏活动列表
 */
export const getGameActivities = (params: {
  page?: number
  size?: number
  keyword?: string
  game_type?: string
  status?: string
}) => {
  return request({
    url: '/v1/community/games',
    method: 'get',
    params
  })
}

/**
 * 获取游戏活动详情
 */
export const getGameActivity = (gameId: string) => {
  return request({
    url: `/api/v1/community/games/${gameId}`,
    method: 'get'
  })
}

/**
 * 获取游戏参与者列表
 */
export const getGameParticipants = (gameId: string, params: {
  page?: number
  size?: number
}) => {
  return request({
    url: `/api/v1/community/games/${gameId}/participants`,
    method: 'get',
    params
  })
}

/**
 * 审核游戏活动
 */
export const reviewGameActivity = (gameId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/community/games/${gameId}/review`,
    method: 'put',
    data
  })
}

/**
 * 删除游戏活动
 */
export const deleteGameActivity = (gameId: string) => {
  return request({
    url: `/api/v1/community/games/${gameId}`,
    method: 'delete'
  })
}

// ==================== 统计分析 ====================

/**
 * 获取社区概览统计
 */
export const getCommunityOverview = () => {
  return request({
    url: '/v1/community/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取资源统计
 */
export const getResourceStatistics = () => {
  return request({
    url: '/v1/community/statistics/resources',
    method: 'get'
  })
}

/**
 * 获取自习室统计
 */
export const getStudyRoomStatistics = () => {
  return request({
    url: '/v1/community/statistics/study-rooms',
    method: 'get'
  })
}

/**
 * 获取话题统计
 */
export const getTopicStatistics = () => {
  return request({
    url: '/v1/community/statistics/topics',
    method: 'get'
  })
}

/**
 * 获取部落统计
 */
export const getTribeStatistics = () => {
  return request({
    url: '/v1/community/statistics/tribes',
    method: 'get'
  })
}

/**
 * 获取导师统计
 */
export const getMentorStatistics = () => {
  return request({
    url: '/v1/community/statistics/mentors',
    method: 'get'
  })
}

/**
 * 获取活动统计
 */
export const getActivityStatistics = () => {
  return request({
    url: '/v1/community/statistics/activities',
    method: 'get'
  })
}

// ==================== 数据类型定义 ====================

export interface LearningResource {
  id: string
  user_id: string
  title: string
  description?: string
  type: string
  subject: string
  content_url?: string
  file_size?: number
  file_format?: string
  tags: string[]
  difficulty_level: string
  target_audience?: string
  is_approved: boolean
  approved_by?: string
  approved_at?: string
  status: string
  download_count: number
  view_count: number
  like_count: number
  rating: number
  rating_count: number
  created_at: string
  updated_at: string
}

export interface StudyRoom {
  id: string
  creator_id: string
  name: string
  description?: string
  room_type: string
  subject?: string
  max_members: number
  current_members: number
  is_public: boolean
  password?: string
  room_settings: any
  status: string
  created_at: string
  updated_at: string
}

export interface Topic {
  id: string
  user_id: string
  title: string
  content: string
  category: string
  tags: string[]
  is_pinned: boolean
  is_locked: boolean
  status: string
  view_count: number
  reply_count: number
  like_count: number
  last_reply_at?: string
  created_at: string
  updated_at: string
}

export interface Tribe {
  id: string
  creator_id: string
  name: string
  description?: string
  avatar?: string
  category: string
  tags: string[]
  is_public: boolean
  member_count: number
  max_members?: number
  status: string
  created_at: string
  updated_at: string
}

export interface MentorProfile {
  id: string
  user_id: string
  bio?: string
  specialties: string[]
  experience_years: number
  education_background: any[]
  certifications: any[]
  mentoring_style?: string
  availability: any
  contact_preferences: any
  is_verified: boolean
  verification_date?: string
  status: string
  rating: number
  rating_count: number
  mentee_count: number
  created_at: string
  updated_at: string
}

export interface Experience {
  id: string
  user_id: string
  title: string
  content: string
  category: string
  tags: string[]
  difficulty_level?: string
  estimated_time?: number
  resources?: any[]
  is_featured: boolean
  status: string
  view_count: number
  like_count: number
  bookmark_count: number
  comment_count: number
  created_at: string
  updated_at: string
}

export interface Activity {
  id: string
  organizer_id: string
  title: string
  description?: string
  activity_type: string
  start_time: string
  end_time: string
  location?: string
  online_link?: string
  max_participants?: number
  current_participants: number
  registration_deadline?: string
  requirements?: string[]
  rewards?: any[]
  status: string
  created_at: string
  updated_at: string
}

export interface Wish {
  id: string
  user_id: string
  title: string
  description: string
  category: string
  tags: string[]
  priority: string
  target_date?: string
  reward_points?: number
  status: string
  supporter_count: number
  comment_count: number
  created_at: string
  updated_at: string
}

export interface GameActivity {
  id: string
  organizer_id: string
  title: string
  description?: string
  game_type: string
  rules: any
  start_time: string
  end_time: string
  max_participants?: number
  current_participants: number
  entry_requirements?: any
  rewards: any[]
  status: string
  created_at: string
  updated_at: string
}

export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}