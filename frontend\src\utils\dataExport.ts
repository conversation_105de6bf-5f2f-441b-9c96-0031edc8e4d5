/**
 * 数据导入导出工具
 * 支持多种格式的数据导入导出功能
 */

import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { ElMessage } from 'element-plus'
import { logger, ModuleType, OperationType } from './logger'

// 导出格式
export enum ExportFormat {
  EXCEL = 'excel',
  CSV = 'csv',
  JSON = 'json',
  PDF = 'pdf'
}

// 导入格式
export enum ImportFormat {
  EXCEL = 'excel',
  CSV = 'csv',
  JSON = 'json'
}

// 导出配置
export interface ExportConfig {
  filename: string
  format: ExportFormat
  sheetName?: string
  headers?: string[]
  data: any[]
  module: ModuleType
  includeTimestamp?: boolean
  customHeaders?: Record<string, string>
}

// 导入配置
export interface ImportConfig {
  format: ImportFormat
  module: ModuleType
  requiredFields?: string[]
  fieldMapping?: Record<string, string>
  validator?: (data: any[]) => { valid: boolean; errors: string[] }
  transformer?: (data: any[]) => any[]
}

// 导入结果
export interface ImportResult {
  success: boolean
  totalRows: number
  successRows: number
  failedRows: number
  errors: string[]
  data?: any[]
}

/**
 * 数据导出类
 */
export class DataExporter {
  /**
   * 导出数据
   */
  static async export(config: ExportConfig): Promise<void> {
    const startTime = performance.now()
    
    try {
      const filename = config.includeTimestamp 
        ? `${config.filename}_${this.getTimestamp()}`
        : config.filename

      switch (config.format) {
        case ExportFormat.EXCEL:
          await this.exportToExcel(config, filename)
          break
        case ExportFormat.CSV:
          await this.exportToCSV(config, filename)
          break
        case ExportFormat.JSON:
          await this.exportToJSON(config, filename)
          break
        case ExportFormat.PDF:
          await this.exportToPDF(config, filename)
          break
        default:
          throw new Error(`不支持的导出格式: ${config.format}`)
      }

      const duration = performance.now() - startTime
      
      logger.logOperation(
        config.module,
        OperationType.EXPORT,
        `导出${config.format.toUpperCase()}文件: ${filename}`,
        {
          success: true,
          duration,
          details: {
            format: config.format,
            rowCount: config.data.length,
            filename
          }
        }
      )

      ElMessage.success(`导出成功: ${filename}`)
    } catch (error) {
      logger.logError(config.module, error as Error, {
        operation: OperationType.EXPORT,
        details: { format: config.format, filename: config.filename }
      })
      
      ElMessage.error(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`)
      throw error
    }
  }

  /**
   * 导出到Excel
   */
  private static async exportToExcel(config: ExportConfig, filename: string): Promise<void> {
    const workbook = XLSX.utils.book_new()
    const sheetName = config.sheetName || 'Sheet1'
    
    // 处理数据
    const processedData = this.processDataForExport(config.data, config.headers, config.customHeaders)
    
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(processedData)
    
    // 设置列宽
    const colWidths = this.calculateColumnWidths(processedData)
    worksheet['!cols'] = colWidths
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
    
    // 生成文件并下载
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    saveAs(blob, `${filename}.xlsx`)
  }

  /**
   * 导出到CSV
   */
  private static async exportToCSV(config: ExportConfig, filename: string): Promise<void> {
    const processedData = this.processDataForExport(config.data, config.headers, config.customHeaders)
    
    if (processedData.length === 0) {
      throw new Error('没有数据可导出')
    }

    // 生成CSV内容
    const headers = Object.keys(processedData[0])
    const csvContent = [
      headers.join(','),
      ...processedData.map(row => 
        headers.map(header => {
          const value = row[header]
          // 处理包含逗号或引号的值
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value
        }).join(',')
      )
    ].join('\n')

    // 添加BOM以支持中文
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' })
    saveAs(blob, `${filename}.csv`)
  }

  /**
   * 导出到JSON
   */
  private static async exportToJSON(config: ExportConfig, filename: string): Promise<void> {
    const processedData = this.processDataForExport(config.data, config.headers, config.customHeaders)
    
    const jsonContent = JSON.stringify({
      exportTime: new Date().toISOString(),
      module: config.module,
      totalRows: processedData.length,
      data: processedData
    }, null, 2)

    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' })
    saveAs(blob, `${filename}.json`)
  }

  /**
   * 导出到PDF
   */
  private static async exportToPDF(config: ExportConfig, filename: string): Promise<void> {
    // 这里需要集成PDF生成库，如jsPDF
    // 由于篇幅限制，这里只是一个占位符
    throw new Error('PDF导出功能正在开发中')
  }

  /**
   * 处理导出数据
   */
  private static processDataForExport(
    data: any[], 
    headers?: string[], 
    customHeaders?: Record<string, string>
  ): any[] {
    if (data.length === 0) return []

    // 如果指定了headers，只导出指定字段
    if (headers) {
      return data.map(item => {
        const processedItem: any = {}
        headers.forEach(header => {
          const displayName = customHeaders?.[header] || header
          processedItem[displayName] = item[header]
        })
        return processedItem
      })
    }

    // 如果有自定义headers，重命名字段
    if (customHeaders) {
      return data.map(item => {
        const processedItem: any = {}
        Object.keys(item).forEach(key => {
          const displayName = customHeaders[key] || key
          processedItem[displayName] = item[key]
        })
        return processedItem
      })
    }

    return data
  }

  /**
   * 计算列宽
   */
  private static calculateColumnWidths(data: any[]): any[] {
    if (data.length === 0) return []

    const headers = Object.keys(data[0])
    return headers.map(header => {
      const maxLength = Math.max(
        header.length,
        ...data.map(row => String(row[header] || '').length)
      )
      return { wch: Math.min(maxLength + 2, 50) }
    })
  }

  /**
   * 获取时间戳
   */
  private static getTimestamp(): string {
    return new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
  }
}

/**
 * 数据导入类
 */
export class DataImporter {
  /**
   * 导入数据
   */
  static async import(file: File, config: ImportConfig): Promise<ImportResult> {
    const startTime = performance.now()
    
    try {
      let data: any[] = []

      switch (config.format) {
        case ImportFormat.EXCEL:
          data = await this.importFromExcel(file)
          break
        case ImportFormat.CSV:
          data = await this.importFromCSV(file)
          break
        case ImportFormat.JSON:
          data = await this.importFromJSON(file)
          break
        default:
          throw new Error(`不支持的导入格式: ${config.format}`)
      }

      // 字段映射
      if (config.fieldMapping) {
        data = this.mapFields(data, config.fieldMapping)
      }

      // 数据转换
      if (config.transformer) {
        data = config.transformer(data)
      }

      // 数据验证
      const validationResult = this.validateData(data, config)
      
      const duration = performance.now() - startTime
      
      logger.logOperation(
        config.module,
        OperationType.IMPORT,
        `导入${config.format.toUpperCase()}文件: ${file.name}`,
        {
          success: validationResult.success,
          duration,
          details: {
            format: config.format,
            totalRows: validationResult.totalRows,
            successRows: validationResult.successRows,
            failedRows: validationResult.failedRows,
            filename: file.name
          }
        }
      )

      if (validationResult.success) {
        ElMessage.success(`导入成功: ${validationResult.successRows}条记录`)
      } else {
        ElMessage.warning(`导入完成: 成功${validationResult.successRows}条，失败${validationResult.failedRows}条`)
      }

      return validationResult
    } catch (error) {
      logger.logError(config.module, error as Error, {
        operation: OperationType.IMPORT,
        details: { format: config.format, filename: file.name }
      })
      
      ElMessage.error(`导入失败: ${error instanceof Error ? error.message : '未知错误'}`)
      throw error
    }
  }

  /**
   * 从Excel导入
   */
  private static async importFromExcel(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          
          // 读取第一个工作表
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          
          // 转换为JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet)
          resolve(jsonData)
        } catch (error) {
          reject(new Error(`Excel文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`))
        }
      }
      
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 从CSV导入
   */
  private static async importFromCSV(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const csvText = e.target?.result as string
          const lines = csvText.split('\n').filter(line => line.trim())
          
          if (lines.length === 0) {
            resolve([])
            return
          }

          // 解析表头
          const headers = this.parseCSVLine(lines[0])
          
          // 解析数据行
          const data = lines.slice(1).map((line, index) => {
            const values = this.parseCSVLine(line)
            const row: any = {}
            
            headers.forEach((header, i) => {
              row[header] = values[i] || ''
            })
            
            return row
          })
          
          resolve(data)
        } catch (error) {
          reject(new Error(`CSV文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`))
        }
      }
      
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  /**
   * 从JSON导入
   */
  private static async importFromJSON(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const jsonText = e.target?.result as string
          const jsonData = JSON.parse(jsonText)
          
          // 如果是导出的JSON格式，提取data字段
          if (jsonData.data && Array.isArray(jsonData.data)) {
            resolve(jsonData.data)
          } else if (Array.isArray(jsonData)) {
            resolve(jsonData)
          } else {
            reject(new Error('JSON格式不正确，应为数组或包含data字段的对象'))
          }
        } catch (error) {
          reject(new Error(`JSON文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`))
        }
      }
      
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  /**
   * 解析CSV行
   */
  private static parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"'
          i++
        } else {
          inQuotes = !inQuotes
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  /**
   * 字段映射
   */
  private static mapFields(data: any[], fieldMapping: Record<string, string>): any[] {
    return data.map(item => {
      const mappedItem: any = {}
      Object.keys(item).forEach(key => {
        const mappedKey = fieldMapping[key] || key
        mappedItem[mappedKey] = item[key]
      })
      return mappedItem
    })
  }

  /**
   * 数据验证
   */
  private static validateData(data: any[], config: ImportConfig): ImportResult {
    const errors: string[] = []
    let successRows = 0
    let failedRows = 0

    // 检查必填字段
    if (config.requiredFields) {
      data.forEach((item, index) => {
        const missingFields = config.requiredFields!.filter(field => !item[field])
        if (missingFields.length > 0) {
          errors.push(`第${index + 1}行缺少必填字段: ${missingFields.join(', ')}`)
          failedRows++
        } else {
          successRows++
        }
      })
    } else {
      successRows = data.length
    }

    // 自定义验证
    if (config.validator) {
      const validationResult = config.validator(data)
      if (!validationResult.valid) {
        errors.push(...validationResult.errors)
        failedRows = data.length - successRows
      }
    }

    return {
      success: errors.length === 0,
      totalRows: data.length,
      successRows,
      failedRows,
      errors,
      data: errors.length === 0 ? data : undefined
    }
  }
}

// 导出工具函数
export const dataExport = {
  excel: (config: Omit<ExportConfig, 'format'>) => 
    DataExporter.export({ ...config, format: ExportFormat.EXCEL }),
  
  csv: (config: Omit<ExportConfig, 'format'>) => 
    DataExporter.export({ ...config, format: ExportFormat.CSV }),
  
  json: (config: Omit<ExportConfig, 'format'>) => 
    DataExporter.export({ ...config, format: ExportFormat.JSON }),
  
  pdf: (config: Omit<ExportConfig, 'format'>) => 
    DataExporter.export({ ...config, format: ExportFormat.PDF })
}

// 导入工具函数
export const dataImport = {
  excel: (file: File, config: Omit<ImportConfig, 'format'>) => 
    DataImporter.import(file, { ...config, format: ImportFormat.EXCEL }),
  
  csv: (file: File, config: Omit<ImportConfig, 'format'>) => 
    DataImporter.import(file, { ...config, format: ImportFormat.CSV }),
  
  json: (file: File, config: Omit<ImportConfig, 'format'>) => 
    DataImporter.import(file, { ...config, format: ImportFormat.JSON })
}
