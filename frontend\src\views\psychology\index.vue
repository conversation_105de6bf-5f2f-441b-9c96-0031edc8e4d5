<template>
  <div class="psychology-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>心理健康中心</h1>
          <p>关注心理健康，提供专业评估与咨询服务</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="startAssessment">
            <el-icon><DocumentChecked /></el-icon>
            心理测评
          </el-button>
          <el-button @click="bookConsultation">
            <el-icon><ChatDotRound /></el-icon>
            预约咨询
          </el-button>
          <el-button @click="viewResources">
            <el-icon><Reading /></el-icon>
            心理资源
          </el-button>
        </div>
      </div>
    </div>

    <!-- 健康状态概览 -->
    <div class="health-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="health-card stress">
            <div class="health-content">
              <div class="health-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="health-info">
                <div class="health-value">{{ healthStatus.stress }}</div>
                <div class="health-label">压力指数</div>
                <div class="health-status" :class="getStressLevel(healthStatus.stress)">
                  {{ getStressText(healthStatus.stress) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="health-card mood">
            <div class="health-content">
              <div class="health-icon">
                <el-icon><Sunny /></el-icon>
              </div>
              <div class="health-info">
                <div class="health-value">{{ healthStatus.mood }}</div>
                <div class="health-label">情绪指数</div>
                <div class="health-status" :class="getMoodLevel(healthStatus.mood)">
                  {{ getMoodText(healthStatus.mood) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="health-card sleep">
            <div class="health-content">
              <div class="health-icon">
                <el-icon><Moon /></el-icon>
              </div>
              <div class="health-info">
                <div class="health-value">{{ healthStatus.sleep }}h</div>
                <div class="health-label">睡眠时长</div>
                <div class="health-status" :class="getSleepLevel(healthStatus.sleep)">
                  {{ getSleepText(healthStatus.sleep) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="health-card anxiety">
            <div class="health-content">
              <div class="health-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="health-info">
                <div class="health-value">{{ healthStatus.anxiety }}</div>
                <div class="health-label">焦虑指数</div>
                <div class="health-status" :class="getAnxietyLevel(healthStatus.anxiety)">
                  {{ getAnxietyText(healthStatus.anxiety) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速服务 -->
    <div class="quick-services">
      <el-row :gutter="24">
        <el-col :xs="24" :md="8">
          <el-card class="service-card" @click="navigateTo('/psychology/assessment')">
            <div class="service-content">
              <div class="service-icon">
                <el-icon><DocumentChecked /></el-icon>
              </div>
              <div class="service-info">
                <h3>心理测评</h3>
                <p>专业心理量表，全面评估心理状态</p>
                <div class="service-stats">
                  <span>已完成 {{ assessmentStats.completed }} 次测评</span>
                </div>
              </div>
              <div class="service-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :md="8">
          <el-card class="service-card" @click="navigateTo('/psychology/consultation')">
            <div class="service-content">
              <div class="service-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="service-info">
                <h3>心理咨询</h3>
                <p>专业心理咨询师，一对一服务</p>
                <div class="service-stats">
                  <span>下次预约：{{ nextAppointment }}</span>
                </div>
              </div>
              <div class="service-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :md="8">
          <el-card class="service-card" @click="navigateTo('/psychology/resources')">
            <div class="service-content">
              <div class="service-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <div class="service-info">
                <h3>心理资源</h3>
                <p>心理健康知识库和自助工具</p>
                <div class="service-stats">
                  <span>{{ resourceStats.total }} 个资源可用</span>
                </div>
              </div>
              <div class="service-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  DocumentChecked, ChatDotRound, Reading, Warning, Sunny, Moon,
  CircleClose, ArrowRight, Refresh
} from '@element-plus/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const trendPeriod = ref('30d')

// 健康状态数据
const healthStatus = reactive({
  stress: 65,
  mood: 78,
  sleep: 7.2,
  anxiety: 42
})

// 评估统计
const assessmentStats = reactive({
  completed: 12
})

// 下次预约
const nextAppointment = ref('明天 14:00')

// 资源统计
const resourceStats = reactive({
  total: 156
})

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const startAssessment = () => {
  router.push('/psychology/assessment')
}

const bookConsultation = () => {
  router.push('/psychology/consultation')
}

const viewResources = () => {
  router.push('/psychology/resources')
}

const getStressLevel = (value: number): string => {
  if (value < 30) return 'low'
  if (value < 70) return 'medium'
  return 'high'
}

const getStressText = (value: number): string => {
  if (value < 30) return '压力较小'
  if (value < 70) return '压力适中'
  return '压力较大'
}

const getMoodLevel = (value: number): string => {
  if (value > 80) return 'excellent'
  if (value > 60) return 'good'
  return 'poor'
}

const getMoodText = (value: number): string => {
  if (value > 80) return '情绪很好'
  if (value > 60) return '情绪良好'
  return '情绪低落'
}

const getSleepLevel = (value: number): string => {
  if (value >= 7 && value <= 9) return 'good'
  if (value >= 6) return 'fair'
  return 'poor'
}

const getSleepText = (value: number): string => {
  if (value >= 7 && value <= 9) return '睡眠充足'
  if (value >= 6) return '睡眠一般'
  return '睡眠不足'
}

const getAnxietyLevel = (value: number): string => {
  if (value < 30) return 'low'
  if (value < 70) return 'medium'
  return 'high'
}

const getAnxietyText = (value: number): string => {
  if (value < 30) return '焦虑较少'
  if (value < 70) return '焦虑适中'
  return '焦虑较多'
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.psychology-overview {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 健康状态概览 */
  .health-overview {
    margin-bottom: var(--spacing-8);

    .health-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.stress {
        border-left: 4px solid #ef4444;
      }

      &.mood {
        border-left: 4px solid #10b981;
      }

      &.sleep {
        border-left: 4px solid #4f46e5;
      }

      &.anxiety {
        border-left: 4px solid #f59e0b;
      }

      .health-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .health-icon {
          width: 60px;
          height: 60px;
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-xl);
        }

        .stress .health-icon {
          background: linear-gradient(135deg, #ef4444, #f87171);
        }

        .mood .health-icon {
          background: linear-gradient(135deg, #10b981, #34d399);
        }

        .sleep .health-icon {
          background: linear-gradient(135deg, #4f46e5, #6366f1);
        }

        .anxiety .health-icon {
          background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }

        .health-info {
          flex: 1;

          .health-value {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .health-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .health-status {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            padding: 2px 8px;
            border-radius: var(--radius-sm);

            &.low {
              background: var(--success-light);
              color: var(--success-color);
            }

            &.medium, &.fair, &.good {
              background: var(--warning-light);
              color: var(--warning-color);
            }

            &.high, &.poor {
              background: var(--error-light);
              color: var(--error-color);
            }

            &.excellent {
              background: var(--success-light);
              color: var(--success-color);
            }
          }
        }
      }
    }
  }

  /* 快速服务 */
  .quick-services {
    margin-bottom: var(--spacing-8);

    .service-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-light);
      }

      .service-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .service-icon {
          width: 50px;
          height: 50px;
          border-radius: var(--radius-lg);
          background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);
        }

        .service-info {
          flex: 1;

          h3 {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-1) 0;
          }

          p {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin: 0 0 var(--spacing-2) 0;
          }

          .service-stats {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }

        .service-arrow {
          color: var(--text-tertiary);
          font-size: var(--font-size-lg);
        }
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .psychology-overview {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .health-overview {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }

    .quick-services {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }
  }
}

@include respond-to('md') {
  .psychology-overview {
    padding: var(--spacing-4);

    .health-overview {
      .health-card .health-content {
        padding: var(--spacing-4);

        .health-icon {
          width: 50px;
          height: 50px;
        }

        .health-info .health-value {
          font-size: var(--font-size-xl);
        }
      }
    }

    .quick-services {
      .service-card .service-content {
        padding: var(--spacing-4);

        .service-icon {
          width: 40px;
          height: 40px;
        }

        .service-info h3 {
          font-size: var(--font-size-base);
        }
      }
    }
  }
}
</style>
