<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>🔧 WisCude 登录问题诊断工具</h2>
        <p><strong>目的：</strong>逐步测试前后端连接和登录功能</p>
        <p><strong>服务器状态：</strong></p>
        <ul>
            <li>前端：http://localhost:5173</li>
            <li>后端：http://localhost:8000</li>
            <li>测试账户：admin / admin123</li>
        </ul>
    </div>

    <div class="test-container">
        <h3>第一步：基础连接测试</h3>
        <button class="test-button" onclick="testBackendDirect()">1. 直接访问后端健康检查</button>
        <button class="test-button" onclick="testFrontendProxy()">2. 通过前端代理访问后端</button>
        <div id="result1" class="result" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h3>第二步：登录API测试</h3>
        <button class="test-button" onclick="testDirectLogin()">3. 直接调用后端登录API</button>
        <button class="test-button" onclick="testProxyLogin()">4. 通过前端代理登录</button>
        <div id="result2" class="result" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h3>第三步：前端应用测试</h3>
        <button class="test-button" onclick="testVueLogin()">5. 测试Vue应用登录</button>
        <button class="test-button" onclick="openMainApp()">6. 打开主应用</button>
        <div id="result3" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(containerId, message, isSuccess = false) {
            const resultDiv = document.getElementById(containerId);
            resultDiv.textContent = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
        }

        // 1. 直接访问后端健康检查
        async function testBackendDirect() {
            try {
                const response = await fetch('http://localhost:8000/api/health', {
                    method: 'GET',
                    mode: 'cors'
                });
                const data = await response.json();
                showResult('result1', `✅ 后端直接访问成功！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`, true);
            } catch (error) {
                showResult('result1', `❌ 后端直接访问失败！\n\n错误信息: ${error.message}\n\n可能原因:\n- 后端服务器未启动\n- CORS配置问题\n- 网络连接问题`);
            }
        }

        // 2. 通过前端代理访问后端
        async function testFrontendProxy() {
            try {
                const response = await fetch('/api/health', {
                    method: 'GET'
                });
                const data = await response.json();
                showResult('result1', `✅ 前端代理访问成功！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`, true);
            } catch (error) {
                showResult('result1', `❌ 前端代理访问失败！\n\n错误信息: ${error.message}\n\n可能原因:\n- Vite代理配置错误\n- 后端服务器未启动\n- 代理路径配置问题`);
            }
        }

        // 3. 直接调用后端登录API
        async function testDirectLogin() {
            try {
                const response = await fetch('http://localhost:8000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=admin&password=admin123',
                    mode: 'cors'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('result2', `✅ 后端直接登录成功！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`, true);
                } else {
                    showResult('result2', `❌ 后端直接登录失败！\n\n状态码: ${response.status}\n错误信息:\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                showResult('result2', `❌ 后端直接登录网络错误！\n\n错误信息: ${error.message}`);
            }
        }

        // 4. 通过前端代理登录
        async function testProxyLogin() {
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=admin&password=admin123'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('result2', `✅ 前端代理登录成功！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`, true);
                    
                    // 保存令牌到localStorage
                    if (data.access_token) {
                        localStorage.setItem('access_token', data.access_token);
                        localStorage.setItem('refresh_token', data.refresh_token || data.access_token);
                        showResult('result2', `✅ 前端代理登录成功！令牌已保存！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`, true);
                    }
                } else {
                    showResult('result2', `❌ 前端代理登录失败！\n\n状态码: ${response.status}\n错误信息:\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                showResult('result2', `❌ 前端代理登录网络错误！\n\n错误信息: ${error.message}`);
            }
        }

        // 5. 测试Vue应用登录
        async function testVueLogin() {
            try {
                // 模拟Vue应用的登录请求
                const params = new URLSearchParams();
                params.append('username', 'admin');
                params.append('password', 'admin123');

                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: params
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('result3', `✅ Vue应用登录测试成功！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`, true);
                } else {
                    showResult('result3', `❌ Vue应用登录测试失败！\n\n状态码: ${response.status}\n错误信息:\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                showResult('result3', `❌ Vue应用登录测试网络错误！\n\n错误信息: ${error.message}`);
            }
        }

        // 6. 打开主应用
        function openMainApp() {
            window.open('/', '_blank');
            showResult('result3', `✅ 主应用已在新标签页中打开！\n\n请检查:\n- 是否能正常加载登录页面\n- 是否能使用 admin/admin123 登录\n- 登录后是否能访问各个功能页面`, true);
        }

        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('简单登录测试页面已加载完成');
            console.log('请按顺序执行测试步骤');
        });
    </script>
</body>
</html>
