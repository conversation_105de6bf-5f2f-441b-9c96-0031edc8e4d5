<template>
  <div class="wishes-management">
    <div class="page-header">
      <h1>心愿墙</h1>
      <p>管理用户心愿发布和实现</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><MagicStick /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.total }}</div>
              <div class="stat-label">心愿总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.active }}</div>
              <div class="stat-label">活跃心愿</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.achieved }}</div>
              <div class="stat-label">已实现</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon info">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(statistics.totalSupports) }}</div>
              <div class="stat-label">总支持数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" :inline="true" class="filter-form">
        <el-form-item label="关键词">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索心愿内容"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="心愿状态">
          <el-select v-model="queryParams.status" placeholder="全部状态" clearable>
            <el-option label="活跃" value="active" />
            <el-option label="已实现" value="achieved" />
            <el-option label="已删除" value="deleted" />
            <el-option label="被举报" value="reported" />
          </el-select>
        </el-form-item>
        <el-form-item label="实现状态">
          <el-select v-model="queryParams.isAchieved" placeholder="全部状态" clearable>
            <el-option label="已实现" :value="true" />
            <el-option label="未实现" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 心愿列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>心愿列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              添加心愿
            </el-button>
            <el-button type="success" @click="handleBatchAchieve" :disabled="!selectedWishes.length">
              <el-icon><Check /></el-icon>
              批量实现
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="wishes"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="心愿信息" min-width="300">
          <template #default="{ row }">
            <div class="wish-info">
              <div class="wish-header">
                <el-avatar :src="row.userAvatar" :size="32">{{ row.userName.charAt(0) }}</el-avatar>
                <div class="wish-user">
                  <div class="user-name">{{ row.userName }}</div>
                  <div class="time-ago">{{ row.timeAgo }}</div>
                </div>
                <el-icon v-if="row.isAchieved" class="achieved-icon"><Check /></el-icon>
              </div>
              <div class="wish-content">{{ row.content }}</div>
              <div class="wish-tags">
                <el-tag v-for="tag in row.tags" :key="tag" size="small">{{ tag }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="互动数据" width="150">
          <template #default="{ row }">
            <div class="stats">
              <div><el-icon><Star /></el-icon> {{ row.supportCount }}</div>
              <div><el-icon><ChatDotRound /></el-icon> {{ row.commentCount }}</div>
              <div><el-icon><View /></el-icon> {{ row.viewCount }}</div>
              <div>热度: {{ row.hotScore.toFixed(1) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusName(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="实现状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isAchieved ? 'success' : 'info'">
              {{ row.isAchieved ? '已实现' : '未实现' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="发布时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleComments(row)">评论</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="achieve" v-if="!row.isAchieved">标记实现</el-dropdown-item>
                  <el-dropdown-item command="unachieve" v-if="row.isAchieved">取消实现</el-dropdown-item>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 心愿详情对话框 -->
    <el-dialog v-model="detailDialog.visible" title="心愿详情" width="600px">
      <div v-if="detailDialog.wish" class="wish-detail">
        <div class="wish-header-detail">
          <el-avatar :src="detailDialog.wish.userAvatar" :size="50">
            {{ detailDialog.wish.userName.charAt(0) }}
          </el-avatar>
          <div class="user-info">
            <div class="user-name">{{ detailDialog.wish.userName }}</div>
            <div class="publish-time">{{ formatDateTime(detailDialog.wish.createdAt) }}</div>
          </div>
          <el-tag v-if="detailDialog.wish.isAchieved" type="success" size="large">
            <el-icon><Check /></el-icon>
            已实现
          </el-tag>
        </div>
        
        <div class="wish-content-detail">
          {{ detailDialog.wish.content }}
        </div>
        
        <div class="wish-tags-detail">
          <el-tag v-for="tag in detailDialog.wish.tags" :key="tag" style="margin-right: 8px;">
            {{ tag }}
          </el-tag>
        </div>
        
        <el-descriptions :column="2" border style="margin-top: 20px;">
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(detailDialog.wish.status)">
              {{ getStatusName(detailDialog.wish.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="实现状态">
            <el-tag :type="detailDialog.wish.isAchieved ? 'success' : 'info'">
              {{ detailDialog.wish.isAchieved ? '已实现' : '未实现' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支持数">{{ detailDialog.wish.supportCount }}</el-descriptions-item>
          <el-descriptions-item label="评论数">{{ detailDialog.wish.commentCount }}</el-descriptions-item>
          <el-descriptions-item label="浏览数">{{ detailDialog.wish.viewCount }}</el-descriptions-item>
          <el-descriptions-item label="热度">{{ detailDialog.wish.hotScore.toFixed(1) }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ formatDateTime(detailDialog.wish.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="实现时间">
            {{ detailDialog.wish.achievedAt ? formatDateTime(detailDialog.wish.achievedAt) : '未实现' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="handleComments(detailDialog.wish)">查看评论</el-button>
        <el-button 
          :type="detailDialog.wish?.isAchieved ? 'warning' : 'success'" 
          @click="handleAchieve(detailDialog.wish)"
        >
          {{ detailDialog.wish?.isAchieved ? '取消实现' : '标记实现' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 评论管理对话框 -->
    <el-dialog v-model="commentsDialog.visible" title="评论管理" width="800px">
      <el-table :data="commentsDialog.comments" v-loading="commentsDialog.loading">
        <el-table-column label="评论者" width="150">
          <template #default="{ row }">
            <div class="commenter-info">
              <el-avatar :src="row.userAvatar" :size="32">{{ row.userName.charAt(0) }}</el-avatar>
              <span>{{ row.userName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="评论内容" min-width="300" />
        <el-table-column prop="likeCount" label="点赞数" width="80" />
        <el-table-column prop="createdAt" label="评论时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="danger" size="small" @click="handleDeleteComment(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="commentsDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  MagicStick, Star, Check, Search, Plus, ChatDotRound, View, ArrowDown
} from '@element-plus/icons-vue'
import type { Wish, WishComment, WishStatus, QueryParams } from '@/types/community'

// 响应式数据
const loading = ref(false)
const wishes = ref<Wish[]>([])
const selectedWishes = ref<Wish[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive<QueryParams & { status?: WishStatus; isAchieved?: boolean }>({
  page: 1,
  size: 20,
  keyword: '',
  status: undefined,
  isAchieved: undefined,
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// 统计数据
const statistics = reactive({
  total: 0,
  active: 0,
  achieved: 0,
  totalSupports: 0
})

// 对话框状态
const detailDialog = reactive({
  visible: false,
  wish: null as Wish | null
})

const commentsDialog = reactive({
  visible: false,
  loading: false,
  comments: [] as WishComment[]
})

// 工具函数
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const getStatusName = (status: WishStatus) => {
  const names = {
    active: '活跃',
    achieved: '已实现',
    deleted: '已删除',
    reported: '被举报'
  }
  return names[status] || status
}

const getStatusTagType = (status: WishStatus) => {
  const types = {
    active: 'success',
    achieved: 'primary',
    deleted: 'info',
    reported: 'danger'
  }
  return types[status] || ''
}

// 事件处理
const handleSearch = () => {
  queryParams.page = 1
  loadWishes()
}

const handleReset = () => {
  Object.assign(queryParams, {
    page: 1,
    size: 20,
    keyword: '',
    status: undefined,
    isAchieved: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
  loadWishes()
}

const handleSelectionChange = (selection: Wish[]) => {
  selectedWishes.value = selection
}

const handleSizeChange = (size: number) => {
  queryParams.size = size
  queryParams.page = 1
  loadWishes()
}

const handleCurrentChange = (page: number) => {
  queryParams.page = page
  loadWishes()
}

const handleAdd = () => {
  ElMessage.info('添加心愿功能开发中')
}

const handleView = (wish: Wish) => {
  detailDialog.wish = wish
  detailDialog.visible = true
}

const handleComments = async (wish: Wish) => {
  commentsDialog.visible = true
  commentsDialog.loading = true
  
  try {
    // TODO: 调用API获取评论列表
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    commentsDialog.comments = [
      {
        id: '1',
        wishId: wish.id,
        userId: '1',
        userName: '张三',
        userAvatar: '',
        content: '加油！相信你一定能实现这个心愿的！',
        likeCount: 5,
        isLiked: false,
        createdAt: '2024-01-15T10:30:00Z',
        timeAgo: '2小时前'
      }
    ]
  } catch (error) {
    ElMessage.error('加载评论列表失败')
  } finally {
    commentsDialog.loading = false
  }
}

const handleAction = async (command: string, wish: Wish) => {
  switch (command) {
    case 'achieve':
      await handleAchieve(wish)
      break
    case 'unachieve':
      await handleAchieve(wish)
      break
    case 'edit':
      ElMessage.info(`编辑心愿: ${wish.content.substring(0, 20)}...`)
      break
    case 'delete':
      await handleDelete(wish)
      break
  }
}

const handleAchieve = async (wish: Wish) => {
  try {
    const action = wish.isAchieved ? '取消实现' : '标记实现'
    await ElMessageBox.confirm(`确定要${action}心愿吗？`, `确认${action}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    // TODO: 调用API
    ElMessage.success(`${action}成功`)
    loadWishes()
    if (detailDialog.visible) {
      detailDialog.visible = false
    }
  } catch {
    // 用户取消
  }
}

const handleDelete = async (wish: Wish) => {
  try {
    await ElMessageBox.confirm(`确定要删除这个心愿吗？此操作不可恢复！`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'danger'
    })
    // TODO: 调用API
    ElMessage.success('删除成功')
    loadWishes()
  } catch {
    // 用户取消
  }
}

const handleBatchAchieve = async () => {
  try {
    await ElMessageBox.confirm(`确定要批量标记选中的 ${selectedWishes.value.length} 个心愿为已实现吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    // TODO: 调用API
    ElMessage.success('批量操作成功')
    loadWishes()
  } catch {
    // 用户取消
  }
}

const handleDeleteComment = async (comment: WishComment) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？此操作不可恢复！', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'danger'
    })
    // TODO: 调用API
    ElMessage.success('删除成功')
    // 重新加载评论列表
  } catch {
    // 用户取消
  }
}

// 数据加载
const loadWishes = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取心愿列表
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    wishes.value = [
      {
        id: '1',
        userId: '1',
        userName: '小明',
        userAvatar: '',
        content: '希望能在期末考试中数学成绩达到90分以上，为了这个目标我会努力学习的！',
        tags: ['学习', '数学', '期末考试'],
        status: 'active' as WishStatus,
        isAchieved: false,
        supportCount: 25,
        commentCount: 8,
        viewCount: 156,
        hotScore: 8.5,
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        timeAgo: '5天前'
      }
    ]
    
    total.value = 300
    
    // 更新统计数据
    statistics.total = 300
    statistics.active = 250
    statistics.achieved = 45
    statistics.totalSupports = 5000
    
  } catch (error) {
    ElMessage.error('加载心愿列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadWishes()
})
</script>

<style scoped>
.wishes-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background-color: #409eff;
  color: white;
  font-size: 20px;
}

.stat-icon.active {
  background-color: #e6a23c;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.info {
  background-color: #909399;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin-bottom: 0;
}

.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.wish-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.wish-header {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.wish-user {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.time-ago {
  font-size: 12px;
  color: #909399;
}

.achieved-icon {
  color: #67c23a;
  font-size: 18px;
  position: absolute;
  right: 0;
  top: 0;
}

.wish-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 8px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.wish-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.stats div {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.wish-detail {
  margin-bottom: 20px;
}

.wish-header-detail {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.user-info {
  flex: 1;
}

.user-info .user-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: white;
}

.publish-time {
  font-size: 14px;
  opacity: 0.9;
}

.wish-content-detail {
  font-size: 16px;
  line-height: 1.6;
  color: #606266;
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.wish-tags-detail {
  margin: 20px 0;
}

.commenter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .wishes-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .filter-form .el-form-item {
    margin-bottom: 10px;
  }

  .header-actions {
    flex-direction: column;
    gap: 4px;
  }

  .wish-header-detail {
    flex-direction: column;
    text-align: center;
  }

  .wish-content {
    font-size: 13px;
  }

  .wish-content-detail {
    font-size: 14px;
    padding: 16px;
  }
}
</style>
