/**
 * 用户状态管理
 * 管理用户登录状态、用户信息和权限数据
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 用户信息接口
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  roles: Role[]
  permissions: Permission[]
  profile?: UserProfile
}

export interface Role {
  id: string
  name: string
  code: string
  permissions: Permission[]
  description?: string
}

export interface Permission {
  id: string
  name: string
  code: string
  module: string
  description?: string
}

export interface UserProfile {
  firstName?: string
  lastName?: string
  phone?: string
  department?: string
  position?: string
  lastLoginAt?: string
  createdAt?: string
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refreshToken'))
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!currentUser.value)
  
  const userRoles = computed(() => currentUser.value?.roles || [])
  
  const userPermissions = computed(() => {
    const directPermissions = currentUser.value?.permissions || []
    const rolePermissions = userRoles.value.flatMap(role => role.permissions || [])
    
    // 去重
    const allPermissions = [...directPermissions, ...rolePermissions]
    const uniquePermissions = allPermissions.filter((permission, index, self) => 
      index === self.findIndex(p => p.code === permission.code)
    )
    
    return uniquePermissions
  })

  const hasRole = computed(() => (roleCode: string) => {
    return userRoles.value.some(role => role.code === roleCode)
  })

  const hasPermission = computed(() => (permissionCode: string) => {
    return userPermissions.value.some(permission => permission.code === permissionCode)
  })

  const hasAnyRole = computed(() => (roleCodes: string[]) => {
    return roleCodes.some(roleCode => hasRole.value(roleCode))
  })

  const hasAnyPermission = computed(() => (permissionCodes: string[]) => {
    return permissionCodes.some(permissionCode => hasPermission.value(permissionCode))
  })

  // 操作方法
  const setToken = (newToken: string, newRefreshToken?: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
    
    if (newRefreshToken) {
      refreshToken.value = newRefreshToken
      localStorage.setItem('refreshToken', newRefreshToken)
    }
  }

  const setUser = (user: User) => {
    currentUser.value = user
  }

  const login = async (credentials: { username: string; password: string }) => {
    isLoading.value = true
    
    try {
      // TODO: 调用登录API
      const response = await fetch('/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      })

      if (!response.ok) {
        throw new Error('登录失败')
      }

      const data = await response.json()
      
      setToken(data.access_token, data.refresh_token)
      setUser(data.user)
      
      return { success: true, user: data.user }
    } catch (error) {
      console.error('Login error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '登录失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      // TODO: 调用登出API
      if (token.value) {
        await fetch('/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token.value}`
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      currentUser.value = null
      token.value = null
      refreshToken.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
    }
  }

  const refreshUserToken = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await fetch('/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refresh_token: refreshToken.value
        })
      })

      if (!response.ok) {
        throw new Error('Token refresh failed')
      }

      const data = await response.json()
      setToken(data.access_token, data.refresh_token)
      
      return data.access_token
    } catch (error) {
      console.error('Token refresh error:', error)
      // 刷新失败，清除所有认证信息
      await logout()
      throw error
    }
  }

  const fetchUserInfo = async () => {
    if (!token.value) {
      throw new Error('No token available')
    }

    try {
      const response = await fetch('/auth/me', {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          // Token可能过期，尝试刷新
          await refreshUserToken()
          return fetchUserInfo()
        }
        throw new Error('Failed to fetch user info')
      }

      const userData = await response.json()
      setUser(userData)
      
      return userData
    } catch (error) {
      console.error('Fetch user info error:', error)
      throw error
    }
  }

  const updateProfile = async (profileData: Partial<UserProfile>) => {
    if (!token.value || !currentUser.value) {
      throw new Error('User not authenticated')
    }

    try {
      const response = await fetch('/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(profileData)
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      const updatedUser = await response.json()
      setUser(updatedUser)
      
      return updatedUser
    } catch (error) {
      console.error('Update profile error:', error)
      throw error
    }
  }

  const changePassword = async (passwordData: { 
    currentPassword: string
    newPassword: string 
  }) => {
    if (!token.value) {
      throw new Error('User not authenticated')
    }

    try {
      const response = await fetch('/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(passwordData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to change password')
      }

      return { success: true }
    } catch (error) {
      console.error('Change password error:', error)
      throw error
    }
  }

  // 初始化用户信息
  const initializeUser = async () => {
    if (token.value && !currentUser.value) {
      try {
        await fetchUserInfo()
      } catch (error) {
        console.error('Failed to initialize user:', error)
        // 如果初始化失败，清除token
        await logout()
      }
    }
  }

  return {
    // 状态
    currentUser,
    token,
    refreshToken,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userRoles,
    userPermissions,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAnyPermission,
    
    // 方法
    setToken,
    setUser,
    login,
    logout,
    refreshUserToken,
    fetchUserInfo,
    updateProfile,
    changePassword,
    initializeUser
  }
})

export default useUserStore
