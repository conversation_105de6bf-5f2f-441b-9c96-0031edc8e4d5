import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import { setupRouteDiagnostics } from './utils/route-diagnostics'

// 导入样式
import 'element-plus/dist/index.css'
import './styles/main.scss'

// 创建应用
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 启用路由诊断（仅在开发环境）
if (import.meta.env.DEV) {
  setupRouteDiagnostics(router)
  console.log('[Main] 路由诊断已启用')
}

// 挂载应用
app.mount('#app')
