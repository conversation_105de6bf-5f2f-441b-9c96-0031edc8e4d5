<template>
  <BaseChart
    :width="width"
    :height="height"
    :options="chartOptions"
    :loading="loading"
    :error="error"
    :theme="theme"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'
import type { EChartsOption } from 'echarts'

// Props定义
interface DatasetItem {
  label: string
  data: number[]
  borderColor: string
  backgroundColor: string
  tension?: number
  fill?: boolean
}

interface Props {
  width?: string
  height?: string
  data: {
    labels: string[]
    datasets: DatasetItem[]
  }
  title?: string
  subtitle?: string
  loading?: boolean
  error?: string
  theme?: string
  showLegend?: boolean
  showGrid?: boolean
  showTooltip?: boolean
  showDataZoom?: boolean
  colors?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  loading: false,
  error: '',
  theme: 'default',
  showLegend: true,
  showGrid: true,
  showTooltip: true,
  showDataZoom: false,
  colors: () => ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
})

// Emits定义
const emit = defineEmits<{
  chartReady: [chart: any]
  chartClick: [params: any]
}>()

// 计算图表配置
const chartOptions = computed<EChartsOption>(() => {
  const option: EChartsOption = {
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      },
      subtextStyle: {
        fontSize: 12,
        color: '#909399'
      }
    } : undefined,

    tooltip: props.showTooltip ? {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: (params: any) => {
        if (!Array.isArray(params)) return ''
        
        let result = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: bold;">${param.value} MB/s</span>
            </div>
          `
        })
        return result
      }
    } : undefined,

    legend: props.showLegend ? {
      top: 'top',
      right: 'right',
      orient: 'vertical',
      textStyle: {
        color: '#606266',
        fontSize: 12
      }
    } : undefined,

    grid: {
      left: '3%',
      right: props.showLegend ? '15%' : '4%',
      top: props.title ? '15%' : '10%',
      bottom: props.showDataZoom ? '15%' : '10%',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      data: props.data.labels,
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#909399',
        fontSize: 11
      },
      splitLine: props.showGrid ? {
        show: true,
        lineStyle: {
          color: '#f5f7fa',
          type: 'dashed'
        }
      } : { show: false }
    },

    yAxis: {
      type: 'value',
      name: '流量 (MB/s)',
      nameTextStyle: {
        color: '#909399',
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#909399',
        fontSize: 11,
        formatter: '{value} MB/s'
      },
      splitLine: props.showGrid ? {
        show: true,
        lineStyle: {
          color: '#f5f7fa',
          type: 'dashed'
        }
      } : { show: false }
    },

    series: props.data.datasets.map((dataset, index) => ({
      name: dataset.label,
      type: 'line',
      data: dataset.data,
      smooth: true,
      areaStyle: {
        color: dataset.backgroundColor
      },
      lineStyle: {
        color: dataset.borderColor,
        width: 2
      },
      itemStyle: {
        color: dataset.borderColor
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          borderWidth: 3
        }
      }
    })),

    dataZoom: props.showDataZoom ? [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        height: 20,
        bottom: 10,
        textStyle: {
          color: '#909399',
          fontSize: 10
        },
        handleStyle: {
          color: '#409eff'
        }
      }
    ] : undefined
  }

  return option
})

// 事件处理
const handleChartReady = (chart: any) => {
  emit('chartReady', chart)
}

const handleChartClick = (params: any) => {
  emit('chartClick', params)
}
</script>

<style scoped>
/* 组件样式 */
</style>
