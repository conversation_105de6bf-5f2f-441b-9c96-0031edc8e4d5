<template>
  <div class="appointments-management">
    <div class="page-header">
      <h1>咨询预约管理</h1>
      <p>管理心理咨询预约、咨询师排班和预约统计分析</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-appointments">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalAppointments }}</div>
              <div class="stat-label">总预约数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending-appointments">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.pendingAppointments }}</div>
              <div class="stat-label">待确认</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed-appointments">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.completedAppointments }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon satisfaction-rate">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ (statistics.satisfactionRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">满意度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="预约列表" name="appointments">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新增预约
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedAppointments.length === 0"
              @click="batchConfirm"
            >
              批量确认
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedAppointments.length === 0"
              @click="batchReschedule"
            >
              批量改期
            </el-button>
            <el-button @click="exportAppointments">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索学生姓名"
              style="width: 200px"
              clearable
              @change="loadAppointments"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="counselorFilter" placeholder="咨询师筛选" style="width: 120px" @change="loadAppointments">
              <el-option label="全部咨询师" value="" />
              <el-option label="张医生" value="zhang" />
              <el-option label="李医生" value="li" />
              <el-option label="王医生" value="wang" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadAppointments">
              <el-option label="全部状态" value="" />
              <el-option label="待确认" value="pending" />
              <el-option label="已确认" value="confirmed" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="default"
              style="width: 240px"
              @change="loadAppointments"
            />
          </div>
        </div>

        <!-- 预约列表 -->
        <el-table
          :data="appointments"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="预约信息" min-width="250">
            <template #default="{ row }">
              <div class="appointment-info">
                <div class="student-info">
                  <span class="student-name">{{ row.student_name }}</span>
                  <span class="student-id">{{ row.student_id }}</span>
                </div>
                <div class="appointment-time">
                  <el-icon><Calendar /></el-icon>
                  {{ formatDateTime(row.appointment_time) }}
                </div>
                <div class="appointment-type">
                  <el-tag size="small" :type="getTypeTagType(row.type)">
                    {{ getTypeName(row.type) }}
                  </el-tag>
                  <span class="duration">{{ row.duration }}分钟</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="咨询师" width="150">
            <template #default="{ row }">
              <div class="counselor-info">
                <div class="counselor-avatar">
                  <img v-if="row.counselor_avatar" :src="row.counselor_avatar" :alt="row.counselor_name" />
                  <div v-else class="avatar-placeholder">{{ row.counselor_name.charAt(0) }}</div>
                </div>
                <div class="counselor-details">
                  <div class="counselor-name">{{ row.counselor_name }}</div>
                  <div class="counselor-title">{{ row.counselor_title }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="咨询主题" width="200">
            <template #default="{ row }">
              <div class="consultation-topic">
                <div class="topic-title">{{ row.topic }}</div>
                <div class="topic-description">{{ row.description }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="联系方式" width="150">
            <template #default="{ row }">
              <div class="contact-info">
                <div class="contact-item">
                  <el-icon><Phone /></el-icon>
                  {{ row.phone }}
                </div>
                <div class="contact-item">
                  <el-icon><Message /></el-icon>
                  {{ row.email }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewAppointment(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="text" size="small" @click="editAppointment(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                v-if="row.status === 'pending'"
                @click="confirmAppointment(row)"
              >
                <el-icon><CircleCheck /></el-icon>
                确认
              </el-button>
              <el-button type="text" size="small" @click="rescheduleAppointment(row)">
                <el-icon><RefreshRight /></el-icon>
                改期
              </el-button>
              <el-button type="text" size="small" @click="contactStudent(row)">
                <el-icon><ChatDotRound /></el-icon>
                联系
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                class="danger" 
                @click="cancelAppointment(row)"
              >
                <el-icon><CircleClose /></el-icon>
                取消
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadAppointments"
            @current-change="loadAppointments"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="排班管理" name="schedule">
        <!-- 咨询师排班日历 -->
        <div class="schedule-section">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>咨询师排班</span>
                <div class="header-actions">
                  <el-select v-model="selectedCounselor" placeholder="选择咨询师" style="width: 150px;">
                    <el-option label="全部咨询师" value="" />
                    <el-option label="张医生" value="zhang" />
                    <el-option label="李医生" value="li" />
                    <el-option label="王医生" value="wang" />
                  </el-select>
                  <el-button type="primary" @click="showScheduleDialog = true">
                    <el-icon><Plus /></el-icon>
                    添加排班
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="schedule-calendar">
              <!-- TODO: 添加日历组件 -->
              <div class="calendar-placeholder">排班日历组件</div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>预约趋势分析</span>
                    <el-select v-model="timeRange" size="small" style="width: 120px;">
                      <el-option label="最近7天" value="7d" />
                      <el-option label="最近30天" value="30d" />
                      <el-option label="最近90天" value="90d" />
                    </el-select>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加预约趋势图表 -->
                  <div class="chart-placeholder">预约趋势图表</div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>咨询类型分布</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加咨询类型分布饼图 -->
                  <div class="chart-placeholder">咨询类型分布饼图</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>咨询师工作负荷分析</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加工作负荷分析图表 -->
                  <div class="chart-placeholder">工作负荷分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑预约对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingAppointment ? '编辑预约' : '新增预约'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="appointmentForm" :rules="appointmentRules" ref="appointmentFormRef" label-width="100px">
        <el-form-item label="学生姓名" prop="student_name">
          <el-input v-model="appointmentForm.student_name" placeholder="请输入学生姓名" />
        </el-form-item>
        <el-form-item label="学生学号" prop="student_id">
          <el-input v-model="appointmentForm.student_id" placeholder="请输入学生学号" />
        </el-form-item>
        <el-form-item label="咨询师" prop="counselor_id">
          <el-select v-model="appointmentForm.counselor_id" placeholder="请选择咨询师">
            <el-option label="张医生" value="zhang" />
            <el-option label="李医生" value="li" />
            <el-option label="王医生" value="wang" />
          </el-select>
        </el-form-item>
        <el-form-item label="预约时间" prop="appointment_time">
          <el-date-picker
            v-model="appointmentForm.appointment_time"
            type="datetime"
            placeholder="选择预约时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="咨询类型" prop="type">
          <el-select v-model="appointmentForm.type" placeholder="请选择咨询类型">
            <el-option label="个人咨询" value="individual" />
            <el-option label="团体咨询" value="group" />
            <el-option label="危机干预" value="crisis" />
            <el-option label="学业指导" value="academic" />
          </el-select>
        </el-form-item>
        <el-form-item label="咨询时长" prop="duration">
          <el-input-number v-model="appointmentForm.duration" :min="30" :max="120" :step="15" placeholder="分钟" />
        </el-form-item>
        <el-form-item label="咨询主题" prop="topic">
          <el-input v-model="appointmentForm.topic" placeholder="请输入咨询主题" />
        </el-form-item>
        <el-form-item label="问题描述" prop="description">
          <el-input v-model="appointmentForm.description" type="textarea" rows="3" placeholder="请描述具体问题" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="appointmentForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱地址" prop="email">
          <el-input v-model="appointmentForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAppointment" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 排班设置对话框 -->
    <el-dialog
      v-model="showScheduleDialog"
      title="添加排班"
      width="500px"
    >
      <el-form :model="scheduleForm" label-width="100px">
        <el-form-item label="咨询师">
          <el-select v-model="scheduleForm.counselor_id" placeholder="请选择咨询师">
            <el-option label="张医生" value="zhang" />
            <el-option label="李医生" value="li" />
            <el-option label="王医生" value="wang" />
          </el-select>
        </el-form-item>
        <el-form-item label="排班日期">
          <el-date-picker
            v-model="scheduleForm.date"
            type="date"
            placeholder="选择排班日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="工作时间">
          <el-time-picker
            v-model="scheduleForm.start_time"
            placeholder="开始时间"
            format="HH:mm"
            value-format="HH:mm"
          />
          <span style="margin: 0 8px;">至</span>
          <el-time-picker
            v-model="scheduleForm.end_time"
            placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm"
          />
        </el-form-item>
        <el-form-item label="可预约数">
          <el-input-number v-model="scheduleForm.max_appointments" :min="1" :max="20" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showScheduleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSchedule">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Calendar, Clock, CircleCheck, Star, Plus, Search, Download, View, Edit, 
  RefreshRight, ChatDotRound, CircleClose, Phone, Message
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('appointments')
const loading = ref(false)
const saving = ref(false)
const selectedAppointments = ref([])
const showCreateDialog = ref(false)
const showScheduleDialog = ref(false)
const editingAppointment = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const counselorFilter = ref('')
const statusFilter = ref('')
const dateRange = ref([])
const timeRange = ref('30d')
const selectedCounselor = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = reactive({
  totalAppointments: 456,
  pendingAppointments: 23,
  completedAppointments: 389,
  satisfactionRate: 0.94
})

// 预约列表
const appointments = ref([
  {
    id: '1',
    student_name: '张小明',
    student_id: 'S2024001',
    counselor_name: '李心理医生',
    counselor_title: '主任医师',
    counselor_avatar: '',
    appointment_time: '2024-01-20 14:00:00',
    type: 'individual',
    duration: 60,
    topic: '学习压力咨询',
    description: '最近学习压力很大，经常失眠',
    phone: '13800138000',
    email: '<EMAIL>',
    status: 'pending',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const appointmentForm = reactive({
  student_name: '',
  student_id: '',
  counselor_id: '',
  appointment_time: '',
  type: 'individual',
  duration: 60,
  topic: '',
  description: '',
  phone: '',
  email: ''
})

const scheduleForm = reactive({
  counselor_id: '',
  date: '',
  start_time: '',
  end_time: '',
  max_appointments: 8
})

// 表单验证规则
const appointmentRules = {
  student_name: [
    { required: true, message: '请输入学生姓名', trigger: 'blur' }
  ],
  counselor_id: [
    { required: true, message: '请选择咨询师', trigger: 'change' }
  ],
  appointment_time: [
    { required: true, message: '请选择预约时间', trigger: 'change' }
  ]
}

// 方法
const loadAppointments = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取预约列表
    console.log('Loading appointments...')
  } catch (error) {
    ElMessage.error('加载预约列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedAppointments.value = selection
}

const getTypeName = (type) => {
  const types = {
    individual: '个人咨询',
    group: '团体咨询',
    crisis: '危机干预',
    academic: '学业指导'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    individual: 'primary',
    group: 'success',
    crisis: 'danger',
    academic: 'warning'
  }
  return types[type] || ''
}

const getStatusName = (status) => {
  const statuses = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    confirmed: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || ''
}

const formatDateTime = (datetime) => {
  return new Date(datetime).toLocaleString()
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewAppointment = (appointment) => {
  // TODO: 查看预约详情
  console.log('Viewing appointment:', appointment)
}

const editAppointment = (appointment) => {
  editingAppointment.value = appointment
  Object.assign(appointmentForm, appointment)
  showCreateDialog.value = true
}

const confirmAppointment = async (appointment) => {
  // TODO: 确认预约
  console.log('Confirming appointment:', appointment)
  ElMessage.success('预约已确认')
}

const rescheduleAppointment = (appointment) => {
  // TODO: 改期预约
  console.log('Rescheduling appointment:', appointment)
}

const contactStudent = (appointment) => {
  // TODO: 联系学生
  console.log('Contacting student:', appointment)
}

const cancelAppointment = async (appointment) => {
  try {
    await ElMessageBox.confirm('确定要取消这个预约吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现取消逻辑
    console.log('Cancelling appointment:', appointment)
    ElMessage.success('预约已取消')
  } catch {
    // 用户取消
  }
}

const resetForm = () => {
  editingAppointment.value = null
  Object.assign(appointmentForm, {
    student_name: '',
    student_id: '',
    counselor_id: '',
    appointment_time: '',
    type: 'individual',
    duration: 60,
    topic: '',
    description: '',
    phone: '',
    email: ''
  })
}

const saveAppointment = async () => {
  saving.value = true
  try {
    // TODO: 实现保存逻辑
    console.log('Saving appointment...', appointmentForm)
    showCreateDialog.value = false
    ElMessage.success(editingAppointment.value ? '预约更新成功' : '预约创建成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveSchedule = async () => {
  try {
    // TODO: 实现保存排班逻辑
    console.log('Saving schedule...', scheduleForm)
    showScheduleDialog.value = false
    ElMessage.success('排班添加成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const batchConfirm = async () => {
  // TODO: 实现批量确认逻辑
  console.log('Batch confirming appointments...', selectedAppointments.value)
}

const batchReschedule = async () => {
  // TODO: 实现批量改期逻辑
  console.log('Batch rescheduling appointments...', selectedAppointments.value)
}

const exportAppointments = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting appointments...')
}

onMounted(() => {
  loadAppointments()
})
</script>

<style scoped>
.appointments-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.total-appointments {
  background-color: #409eff;
}

.stat-icon.pending-appointments {
  background-color: #e6a23c;
}

.stat-icon.completed-appointments {
  background-color: #67c23a;
}

.stat-icon.satisfaction-rate {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.appointment-info {
  padding: 8px 0;
}

.student-info {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 6px;
}

.student-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.student-id {
  font-size: 12px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.appointment-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
}

.appointment-type {
  display: flex;
  gap: 8px;
  align-items: center;
}

.duration {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.counselor-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.counselor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.counselor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
}

.counselor-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.counselor-title {
  font-size: 11px;
  color: #909399;
}

.consultation-topic {
  padding: 4px 0;
}

.topic-title {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.topic-description {
  font-size: 12px;
  color: #606266;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.schedule-section {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.calendar-placeholder {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 16px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.danger {
  color: #f56c6c;
}
</style>
