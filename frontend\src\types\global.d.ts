// 全局类型声明文件

// Element Plus 相关类型声明
declare module 'element-plus/dist/locale/zh-cn.mjs' {
  const zhCn: any
  export default zhCn
}

declare module 'element-plus/dist/locale/en.mjs' {
  const en: any
  export default en
}

// Vue 相关类型扩展
declare module '@vue/runtime-core' {
  export interface GlobalProperties {
    $message: typeof import('element-plus')['ElMessage']
    $notify: typeof import('element-plus')['ElNotification']
    $confirm: typeof import('element-plus')['ElMessageBox']['confirm']
    $alert: typeof import('element-plus')['ElMessageBox']['alert']
    $prompt: typeof import('element-plus')['ElMessageBox']['prompt']
  }
}

// 工具类型
export type Nullable<T> = T | null
export type Optional<T> = T | undefined
export type Recordable<T = any> = Record<string, T>

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationParams {
  page: number
  page_size: number
}

export interface PaginationResponse<T = any> {
  items: T[]
  total: number
  page: number
  page_size: number
  pages: number
}

export {}
