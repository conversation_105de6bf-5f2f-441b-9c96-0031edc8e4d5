"""
WisCude 后台管理系统 - 内容相关模型（社区、课程等）
"""
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, Float, ForeignKey, Enum, JSON
from sqlalchemy.orm import relationship
from app.models.base import BaseModel
import enum

class PostType(str, enum.Enum):
    """帖子类型枚举"""
    QUESTION = "question"
    DISCUSSION = "discussion"
    SHARE = "share"
    ANNOUNCEMENT = "announcement"

class PostStatus(str, enum.Enum):
    """帖子状态枚举"""
    DRAFT = "draft"
    PUBLISHED = "published"
    HIDDEN = "hidden"
    DELETED = "deleted"

class CommunityPost(BaseModel):
    """社区帖子模型"""
    __tablename__ = "community_posts"
    
    android_post_id = Column(String(36), unique=True, index=True, nullable=False)
    author_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # 帖子内容
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    post_type = Column(Enum(PostType), default=PostType.DISCUSSION, nullable=False)
    category = Column(String(50), nullable=True)
    tags = Column(JSON, nullable=True)  # 标签列表
    
    # 媒体内容
    images = Column(JSON, nullable=True)  # 图片URL列表
    attachments = Column(JSON, nullable=True)  # 附件信息
    
    # 互动数据
    likes_count = Column(Integer, default=0, nullable=False)
    comments_count = Column(Integer, default=0, nullable=False)
    views_count = Column(Integer, default=0, nullable=False)
    shares_count = Column(Integer, default=0, nullable=False)
    
    # 状态
    status = Column(Enum(PostStatus), default=PostStatus.PUBLISHED, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    is_pinned = Column(Boolean, default=False, nullable=False)
    
    # 审核信息
    reviewed_by = Column(String(36), nullable=True)
    reviewed_at = Column(DateTime, nullable=True)
    review_notes = Column(Text, nullable=True)
    
    # 关系
    author = relationship("WiscudeUser", back_populates="community_posts")
    comments = relationship("PostComment", back_populates="post")
    
    def __repr__(self):
        return f"<CommunityPost {self.title}>"

class PostComment(BaseModel):
    """帖子评论模型"""
    __tablename__ = "post_comments"
    
    android_comment_id = Column(String(36), unique=True, index=True, nullable=False)
    post_id = Column(String(36), ForeignKey("community_posts.id"), nullable=False)
    author_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    parent_id = Column(String(36), ForeignKey("post_comments.id"), nullable=True)
    
    # 评论内容
    content = Column(Text, nullable=False)
    likes_count = Column(Integer, default=0, nullable=False)
    
    # 状态
    is_deleted = Column(Boolean, default=False, nullable=False)
    
    # 关系
    post = relationship("CommunityPost", back_populates="comments")
    author = relationship("WiscudeUser")
    parent = relationship("PostComment", remote_side="PostComment.id")
    
    def __repr__(self):
        return f"<PostComment {self.id}>"

# Course类已移动到app.models.courses模块中

# CourseEnrollment类已移动到app.models.courses模块中

class SyncLog(BaseModel):
    """数据同步日志模型"""
    __tablename__ = "sync_logs"
    
    # 同步信息
    sync_type = Column(String(50), nullable=False)  # full, incremental
    table_name = Column(String(50), nullable=True)
    status = Column(String(20), nullable=False)  # success, failed, running
    
    # 统计数据
    records_processed = Column(Integer, default=0, nullable=False)
    records_inserted = Column(Integer, default=0, nullable=False)
    records_updated = Column(Integer, default=0, nullable=False)
    records_failed = Column(Integer, default=0, nullable=False)
    
    # 时间信息
    started_at = Column(DateTime, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    
    # 详细信息
    error_message = Column(Text, nullable=True)
    details = Column(JSON, nullable=True)
    
    def __repr__(self):
        return f"<SyncLog {self.sync_type} {self.status}>"
