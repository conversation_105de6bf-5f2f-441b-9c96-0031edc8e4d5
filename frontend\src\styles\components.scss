// 通用组件样式
// Common Component Styles

@import './design-system.scss';

// ==================== 基础卡片样式 ====================
.card-modern {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  transition: var(--transition-normal);

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
}

// ==================== 页面布局组件 ====================
.page-container {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);

  @include respond-to('md') {
    padding: var(--spacing-8);
  }
}

.page-header {
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6) 0;

  h1 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-2) 0;
    line-height: var(--line-height-tight);
  }

  p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin: 0;
    line-height: var(--line-height-normal);
  }

  .page-breadcrumb {
    margin-bottom: var(--spacing-4);
    
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        color: var(--text-tertiary);
        font-weight: var(--font-weight-medium);
        
        &:hover {
          color: var(--primary-color);
        }
      }
      
      &:last-child .el-breadcrumb__inner {
        color: var(--text-primary);
      }
    }
  }
}

// ==================== 统计卡片组件 ====================
.stats-overview {
  margin-bottom: var(--spacing-8);

  .stat-card {
    @extend .card-modern;
    height: 100%;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
    }

    .el-card__body {
      padding: var(--spacing-6);
    }
  }

  .stat-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  .stat-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
      border-radius: inherit;
    }

    &.primary {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    &.success {
      background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
    }

    &.warning {
      background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);
    }

    &.error {
      background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);
    }

    &.info {
      background: linear-gradient(135deg, var(--info-color) 0%, var(--info-light) 100%);
    }

    &.secondary {
      background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
    }
  }

  .stat-info {
    flex: 1;
  }

  .stat-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-1);
  }

  .stat-label {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    margin: 0;
  }

  .stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    margin-top: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    &.positive {
      color: var(--success-color);
    }

    &.negative {
      color: var(--error-color);
    }

    &.neutral {
      color: var(--text-tertiary);
    }
  }
}

// ==================== 工具栏组件 ====================
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
  padding: var(--spacing-5) var(--spacing-6);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);

  @include respond-to('md') {
    flex-direction: row;
  }

  @media (max-width: 767px) {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;

    @media (max-width: 767px) {
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  .toolbar-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-right: var(--spacing-4);
  }
}

// ==================== 模块导航组件 ====================
.module-navigation {
  margin-bottom: var(--spacing-8);

  .module-card {
    @extend .card-modern;
    cursor: pointer;
    height: 100%;
    transition: var(--transition-normal);

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
      
      .module-icon {
        transform: scale(1.1);
      }
    }

    .el-card__body {
      padding: var(--spacing-6);
    }
  }

  .module-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
  }

  .module-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: white;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
      border-radius: inherit;
    }
  }

  .module-info {
    flex: 1;
  }

  .module-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
    line-height: var(--line-height-tight);
  }

  .module-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-3);
    line-height: var(--line-height-normal);
  }

  .module-stats {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
  }

  .stat-item {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    background-color: var(--bg-tertiary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
  }
}

// ==================== 表格增强样式 ====================
.enhanced-table {
  .el-table {
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);

    .el-table__header-wrapper {
      th {
        background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        border-bottom: 2px solid var(--border-medium);
        font-size: var(--font-size-sm);
      }
    }

    .el-table__body-wrapper {
      tr {
        transition: var(--transition-fast);

        &:hover > td {
          background-color: var(--bg-accent) !important;
        }
      }
    }

    .el-table__cell {
      border-bottom: 1px solid var(--border-light);
      padding: var(--spacing-4) var(--spacing-3);
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
  }
}

// ==================== 表单增强样式 ====================
.enhanced-form {
  .el-form-item {
    margin-bottom: var(--spacing-6);

    .el-form-item__label {
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
      font-size: var(--font-size-sm);
    }

    .el-form-item__content {
      .form-tip {
        display: block;
        margin-top: var(--spacing-2);
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
        line-height: var(--line-height-normal);
      }
    }
  }

  .form-section {
    margin-bottom: var(--spacing-8);
    padding: var(--spacing-6);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);

    .section-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-4);
      padding-bottom: var(--spacing-3);
      border-bottom: 2px solid var(--border-light);
    }
  }
}

// ==================== 标签增强样式 ====================
.enhanced-tags {
  .el-tag {
    margin-right: var(--spacing-2);
    margin-bottom: var(--spacing-2);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    border: none;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
    
    &.el-tag--primary {
      background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
      color: var(--primary-color);
      border: 1px solid rgba(79, 70, 229, 0.2);
    }

    &.el-tag--success {
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.1) 100%);
      color: var(--success-color);
      border: 1px solid rgba(16, 185, 129, 0.2);
    }

    &.el-tag--warning {
      background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
      color: var(--warning-color);
      border: 1px solid rgba(245, 158, 11, 0.2);
    }

    &.el-tag--danger {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.1) 100%);
      color: var(--error-color);
      border: 1px solid rgba(239, 68, 68, 0.2);
    }

    &.el-tag--info {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(96, 165, 250, 0.1) 100%);
      color: var(--info-color);
      border: 1px solid rgba(59, 130, 246, 0.2);
    }
  }
}

// ==================== 加载状态组件 ====================
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-12);
  color: var(--text-tertiary);

  .loading-text {
    margin-top: var(--spacing-4);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
  }
}

// ==================== 空状态组件 ====================
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-16);
  color: var(--text-tertiary);

  .empty-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
    opacity: 0.5;
  }

  .empty-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-2);
    color: var(--text-secondary);
  }

  .empty-description {
    font-size: var(--font-size-base);
    text-align: center;
    line-height: var(--line-height-relaxed);
    max-width: 400px;
  }
}

// ==================== 响应式工具类 ====================
.mobile-hidden {
  @media (max-width: 767px) {
    display: none !important;
  }
}

.desktop-hidden {
  @include respond-to('md') {
    display: none !important;
  }
}

.mobile-full-width {
  @media (max-width: 767px) {
    width: 100% !important;
  }
}
