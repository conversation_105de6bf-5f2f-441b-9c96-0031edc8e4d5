<template>
  <div class="crisis-management">
    <div class="page-header">
      <h1>危机预警系统</h1>
      <p>心理危机预警、干预处理和应急响应管理</p>
    </div>

    <!-- 紧急状态概览 -->
    <el-row :gutter="20" class="emergency-overview">
      <el-col :span="6">
        <el-card class="emergency-card high-risk">
          <div class="emergency-content">
            <div class="emergency-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="emergency-info">
              <div class="emergency-value">{{ statistics.highRisk }}</div>
              <div class="emergency-label">高危预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="emergency-card medium-risk">
          <div class="emergency-content">
            <div class="emergency-icon">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="emergency-info">
              <div class="emergency-value">{{ statistics.mediumRisk }}</div>
              <div class="emergency-label">中危预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="emergency-card pending-cases">
          <div class="emergency-content">
            <div class="emergency-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="emergency-info">
              <div class="emergency-value">{{ statistics.pendingCases }}</div>
              <div class="emergency-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="emergency-card resolved-cases">
          <div class="emergency-content">
            <div class="emergency-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="emergency-info">
              <div class="emergency-value">{{ statistics.resolvedCases }}</div>
              <div class="emergency-label">已处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="预警列表" name="alerts">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="danger" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新增预警
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedAlerts.length === 0"
              @click="batchAssign"
            >
              批量分配
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedAlerts.length === 0"
              @click="batchResolve"
            >
              批量处理
            </el-button>
            <el-button @click="exportAlerts">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索学生姓名"
              style="width: 200px"
              clearable
              @change="loadAlerts"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="riskFilter" placeholder="风险等级" style="width: 120px" @change="loadAlerts">
              <el-option label="全部等级" value="" />
              <el-option label="高危" value="high" />
              <el-option label="中危" value="medium" />
              <el-option label="低危" value="low" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="处理状态" style="width: 120px" @change="loadAlerts">
              <el-option label="全部状态" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已处理" value="resolved" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </div>
        </div>

        <!-- 预警列表 -->
        <el-table
          :data="alerts"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="学生信息" min-width="200">
            <template #default="{ row }">
              <div class="student-info">
                <div class="student-avatar">
                  <img v-if="row.student_avatar" :src="row.student_avatar" :alt="row.student_name" />
                  <div v-else class="avatar-placeholder">{{ row.student_name.charAt(0) }}</div>
                </div>
                <div class="student-details">
                  <div class="student-name">{{ row.student_name }}</div>
                  <div class="student-id">{{ row.student_id }}</div>
                  <div class="student-class">{{ row.class_name }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="风险等级" width="120">
            <template #default="{ row }">
              <el-tag :type="getRiskTagType(row.risk_level)" :class="`risk-${row.risk_level}`">
                {{ getRiskName(row.risk_level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="预警类型" width="150">
            <template #default="{ row }">
              <div class="alert-type">
                <el-tag size="small" :type="getTypeTagType(row.alert_type)">
                  {{ getTypeName(row.alert_type) }}
                </el-tag>
                <div class="alert-source">{{ getSourceName(row.source) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="预警内容" min-width="300">
            <template #default="{ row }">
              <div class="alert-content">
                <div class="alert-title">{{ row.title }}</div>
                <div class="alert-description">{{ row.description }}</div>
                <div class="alert-indicators">
                  <el-tag 
                    v-for="indicator in row.indicators" 
                    :key="indicator" 
                    size="small" 
                    class="indicator-tag"
                  >
                    {{ indicator }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="负责人" width="120">
            <template #default="{ row }">
              <div class="assignee-info">
                <div v-if="row.assignee" class="assignee-name">{{ row.assignee }}</div>
                <div v-else class="no-assignee">未分配</div>
                <div class="assign-time">{{ formatTime(row.assigned_at) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="处理状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewAlert(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="text" size="small" @click="editAlert(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="text" size="small" @click="assignAlert(row)">
                <el-icon><User /></el-icon>
                分配
              </el-button>
              <el-button type="text" size="small" @click="processAlert(row)">
                <el-icon><Tools /></el-icon>
                处理
              </el-button>
              <el-button type="text" size="small" @click="contactEmergency(row)">
                <el-icon><Phone /></el-icon>
                紧急联系
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadAlerts"
            @current-change="loadAlerts"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="干预记录" name="interventions">
        <!-- 干预记录列表 -->
        <div class="interventions-section">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>干预处理记录</span>
                <el-button type="primary" @click="showInterventionDialog = true">
                  <el-icon><Plus /></el-icon>
                  新增记录
                </el-button>
              </div>
            </template>
            
            <el-table :data="interventions" v-loading="loading" stripe style="width: 100%">
              <el-table-column label="学生姓名" prop="student_name" width="120" />
              <el-table-column label="干预类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getInterventionTypeTag(row.intervention_type)">
                    {{ getInterventionTypeName(row.intervention_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="干预内容" min-width="300">
                <template #default="{ row }">
                  <div class="intervention-content">
                    <div class="intervention-title">{{ row.title }}</div>
                    <div class="intervention-description">{{ row.description }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="执行人" prop="executor" width="100" />
              <el-table-column label="执行时间" width="150">
                <template #default="{ row }">
                  <span>{{ formatTime(row.executed_at) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="效果评估" width="120">
                <template #default="{ row }">
                  <el-rate v-model="row.effectiveness" disabled size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="{ row }">
                  <el-button type="text" size="small" @click="viewIntervention(row)">查看</el-button>
                  <el-button type="text" size="small" @click="editIntervention(row)">编辑</el-button>
                  <el-button type="text" size="small" class="danger" @click="deleteIntervention(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>预警趋势分析</span>
                    <el-select v-model="timeRange" size="small" style="width: 120px;">
                      <el-option label="最近7天" value="7d" />
                      <el-option label="最近30天" value="30d" />
                      <el-option label="最近90天" value="90d" />
                    </el-select>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加预警趋势图表 -->
                  <div class="chart-placeholder">预警趋势图表</div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>风险等级分布</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加风险等级分布饼图 -->
                  <div class="chart-placeholder">风险等级分布饼图</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>干预效果分析</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加干预效果分析图表 -->
                  <div class="chart-placeholder">干预效果分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑预警对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingAlert ? '编辑预警' : '新增预警'"
      width="700px"
      @close="resetForm"
    >
      <el-form :model="alertForm" :rules="alertRules" ref="alertFormRef" label-width="100px">
        <el-form-item label="学生姓名" prop="student_name">
          <el-input v-model="alertForm.student_name" placeholder="请输入学生姓名" />
        </el-form-item>
        <el-form-item label="学生学号" prop="student_id">
          <el-input v-model="alertForm.student_id" placeholder="请输入学生学号" />
        </el-form-item>
        <el-form-item label="风险等级" prop="risk_level">
          <el-select v-model="alertForm.risk_level" placeholder="请选择风险等级">
            <el-option label="高危" value="high" />
            <el-option label="中危" value="medium" />
            <el-option label="低危" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="预警类型" prop="alert_type">
          <el-select v-model="alertForm.alert_type" placeholder="请选择预警类型">
            <el-option label="自杀倾向" value="suicide" />
            <el-option label="抑郁症状" value="depression" />
            <el-option label="焦虑症状" value="anxiety" />
            <el-option label="行为异常" value="behavior" />
            <el-option label="学业压力" value="academic" />
          </el-select>
        </el-form-item>
        <el-form-item label="预警来源" prop="source">
          <el-select v-model="alertForm.source" placeholder="请选择预警来源">
            <el-option label="系统检测" value="system" />
            <el-option label="教师报告" value="teacher" />
            <el-option label="同学举报" value="peer" />
            <el-option label="家长反映" value="parent" />
            <el-option label="自我求助" value="self" />
          </el-select>
        </el-form-item>
        <el-form-item label="预警标题" prop="title">
          <el-input v-model="alertForm.title" placeholder="请输入预警标题" />
        </el-form-item>
        <el-form-item label="详细描述" prop="description">
          <el-input v-model="alertForm.description" type="textarea" rows="4" placeholder="请详细描述情况" />
        </el-form-item>
        <el-form-item label="风险指标">
          <el-select v-model="alertForm.indicators" multiple placeholder="请选择风险指标">
            <el-option label="情绪低落" value="mood_low" />
            <el-option label="社交回避" value="social_avoidance" />
            <el-option label="睡眠障碍" value="sleep_disorder" />
            <el-option label="食欲异常" value="appetite_abnormal" />
            <el-option label="注意力不集中" value="attention_deficit" />
            <el-option label="自伤行为" value="self_harm" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAlert" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 干预记录对话框 -->
    <el-dialog
      v-model="showInterventionDialog"
      title="新增干预记录"
      width="600px"
    >
      <el-form :model="interventionForm" label-width="100px">
        <el-form-item label="学生姓名">
          <el-input v-model="interventionForm.student_name" placeholder="请输入学生姓名" />
        </el-form-item>
        <el-form-item label="干预类型">
          <el-select v-model="interventionForm.intervention_type" placeholder="请选择干预类型">
            <el-option label="心理咨询" value="counseling" />
            <el-option label="危机干预" value="crisis" />
            <el-option label="药物治疗" value="medication" />
            <el-option label="家庭干预" value="family" />
            <el-option label="环境调整" value="environment" />
          </el-select>
        </el-form-item>
        <el-form-item label="干预标题">
          <el-input v-model="interventionForm.title" placeholder="请输入干预标题" />
        </el-form-item>
        <el-form-item label="干预内容">
          <el-input v-model="interventionForm.description" type="textarea" rows="4" placeholder="请描述干预内容" />
        </el-form-item>
        <el-form-item label="执行人">
          <el-input v-model="interventionForm.executor" placeholder="请输入执行人" />
        </el-form-item>
        <el-form-item label="执行时间">
          <el-date-picker
            v-model="interventionForm.executed_at"
            type="datetime"
            placeholder="选择执行时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="效果评估">
          <el-rate v-model="interventionForm.effectiveness" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showInterventionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveIntervention">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Warning, InfoFilled, Clock, CircleCheck, Plus, Search, Download, View, Edit, 
  User, Tools, Phone
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('alerts')
const loading = ref(false)
const saving = ref(false)
const selectedAlerts = ref([])
const showCreateDialog = ref(false)
const showInterventionDialog = ref(false)
const editingAlert = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const riskFilter = ref('')
const statusFilter = ref('')
const timeRange = ref('30d')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = reactive({
  highRisk: 5,
  mediumRisk: 12,
  pendingCases: 8,
  resolvedCases: 156
})

// 预警列表
const alerts = ref([
  {
    id: '1',
    student_name: '张小明',
    student_id: 'S2024001',
    student_avatar: '',
    class_name: '计算机2024-1班',
    risk_level: 'high',
    alert_type: 'depression',
    source: 'teacher',
    title: '学生情绪异常预警',
    description: '该学生近期情绪低落，经常独自一人，不与同学交流',
    indicators: ['mood_low', 'social_avoidance', 'sleep_disorder'],
    assignee: '李心理医生',
    assigned_at: '2024-01-16 09:00:00',
    status: 'processing',
    created_at: '2024-01-15 14:30:00'
  }
])

// 干预记录
const interventions = ref([
  {
    id: '1',
    student_name: '张小明',
    intervention_type: 'counseling',
    title: '个人心理咨询',
    description: '进行了一对一心理咨询，了解学生情况',
    executor: '李心理医生',
    executed_at: '2024-01-16 14:00:00',
    effectiveness: 4
  }
])

// 表单数据
const alertForm = reactive({
  student_name: '',
  student_id: '',
  risk_level: 'medium',
  alert_type: '',
  source: '',
  title: '',
  description: '',
  indicators: []
})

const interventionForm = reactive({
  student_name: '',
  intervention_type: '',
  title: '',
  description: '',
  executor: '',
  executed_at: '',
  effectiveness: 3
})

// 表单验证规则
const alertRules = {
  student_name: [
    { required: true, message: '请输入学生姓名', trigger: 'blur' }
  ],
  risk_level: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  alert_type: [
    { required: true, message: '请选择预警类型', trigger: 'change' }
  ]
}

// 方法
const loadAlerts = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取预警列表
    console.log('Loading alerts...')
  } catch (error) {
    ElMessage.error('加载预警列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedAlerts.value = selection
}

const getRiskName = (risk) => {
  const risks = {
    high: '高危',
    medium: '中危',
    low: '低危'
  }
  return risks[risk] || risk
}

const getRiskTagType = (risk) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return types[risk] || ''
}

const getTypeName = (type) => {
  const types = {
    suicide: '自杀倾向',
    depression: '抑郁症状',
    anxiety: '焦虑症状',
    behavior: '行为异常',
    academic: '学业压力'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    suicide: 'danger',
    depression: 'warning',
    anxiety: 'primary',
    behavior: 'info',
    academic: 'success'
  }
  return types[type] || ''
}

const getSourceName = (source) => {
  const sources = {
    system: '系统检测',
    teacher: '教师报告',
    peer: '同学举报',
    parent: '家长反映',
    self: '自我求助'
  }
  return sources[source] || source
}

const getStatusName = (status) => {
  const statuses = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已处理',
    closed: '已关闭'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return types[status] || ''
}

const getInterventionTypeName = (type) => {
  const types = {
    counseling: '心理咨询',
    crisis: '危机干预',
    medication: '药物治疗',
    family: '家庭干预',
    environment: '环境调整'
  }
  return types[type] || type
}

const getInterventionTypeTag = (type) => {
  const types = {
    counseling: 'primary',
    crisis: 'danger',
    medication: 'warning',
    family: 'success',
    environment: 'info'
  }
  return types[type] || ''
}

const formatTime = (time) => {
  return time ? new Date(time).toLocaleString() : ''
}

const viewAlert = (alert) => {
  // TODO: 查看预警详情
  console.log('Viewing alert:', alert)
}

const editAlert = (alert) => {
  editingAlert.value = alert
  Object.assign(alertForm, alert)
  showCreateDialog.value = true
}

const assignAlert = (alert) => {
  // TODO: 分配预警处理人
  console.log('Assigning alert:', alert)
}

const processAlert = (alert) => {
  // TODO: 处理预警
  console.log('Processing alert:', alert)
}

const contactEmergency = (alert) => {
  // TODO: 紧急联系
  console.log('Emergency contact for alert:', alert)
}

const resetForm = () => {
  editingAlert.value = null
  Object.assign(alertForm, {
    student_name: '',
    student_id: '',
    risk_level: 'medium',
    alert_type: '',
    source: '',
    title: '',
    description: '',
    indicators: []
  })
}

const saveAlert = async () => {
  saving.value = true
  try {
    // TODO: 实现保存逻辑
    console.log('Saving alert...', alertForm)
    showCreateDialog.value = false
    ElMessage.success(editingAlert.value ? '预警更新成功' : '预警创建成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveIntervention = async () => {
  try {
    // TODO: 实现保存干预记录逻辑
    console.log('Saving intervention...', interventionForm)
    showInterventionDialog.value = false
    ElMessage.success('干预记录保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const viewIntervention = (intervention) => {
  // TODO: 查看干预记录详情
  console.log('Viewing intervention:', intervention)
}

const editIntervention = (intervention) => {
  // TODO: 编辑干预记录
  console.log('Editing intervention:', intervention)
}

const deleteIntervention = async (intervention) => {
  try {
    await ElMessageBox.confirm('确定要删除这条干预记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting intervention:', intervention)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchAssign = async () => {
  // TODO: 实现批量分配逻辑
  console.log('Batch assigning alerts...', selectedAlerts.value)
}

const batchResolve = async () => {
  // TODO: 实现批量处理逻辑
  console.log('Batch resolving alerts...', selectedAlerts.value)
}

const exportAlerts = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting alerts...')
}

onMounted(() => {
  loadAlerts()
})
</script>

<style scoped>
.crisis-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.emergency-overview {
  margin-bottom: 24px;
}

.emergency-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.emergency-card.high-risk {
  border-left: 4px solid #f56c6c;
}

.emergency-card.medium-risk {
  border-left: 4px solid #e6a23c;
}

.emergency-card.pending-cases {
  border-left: 4px solid #409eff;
}

.emergency-card.resolved-cases {
  border-left: 4px solid #67c23a;
}

.emergency-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.emergency-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.high-risk .emergency-icon {
  background-color: #f56c6c;
}

.medium-risk .emergency-icon {
  background-color: #e6a23c;
}

.pending-cases .emergency-icon {
  background-color: #409eff;
}

.resolved-cases .emergency-icon {
  background-color: #67c23a;
}

.emergency-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.emergency-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.student-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.student-details {
  flex: 1;
}

.student-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.student-id {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.student-class {
  font-size: 11px;
  color: #c0c4cc;
}

.risk-high {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.alert-type {
  text-align: center;
}

.alert-source {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
}

.alert-content {
  padding: 4px 0;
}

.alert-title {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.alert-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.alert-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.indicator-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
  font-size: 10px;
}

.assignee-info {
  text-align: center;
}

.assignee-name {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
}

.no-assignee {
  font-size: 12px;
  color: #c0c4cc;
}

.assign-time {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.interventions-section {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.intervention-content {
  padding: 4px 0;
}

.intervention-title {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.intervention-description {
  font-size: 12px;
  color: #606266;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.danger {
  color: #f56c6c;
}
</style>
