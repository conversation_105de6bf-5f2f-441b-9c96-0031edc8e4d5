<template>
  <div class="course-management-dashboard">
    <div class="page-header">
      <h1>课程管理</h1>
      <p>管理在线课程内容、讲师资源、学习进度和评价系统</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon courses">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalCourses }}</div>
              <div class="stat-label">课程总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon instructors">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalInstructors }}</div>
              <div class="stat-label">讲师数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon students">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalStudents }}</div>
              <div class="stat-label">学员总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completion">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ (statistics.completionRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能模块导航 -->
    <el-card class="modules-card">
      <template #header>
        <div class="card-header">
          <span>功能模块</span>
        </div>
      </template>
      
      <el-row :gutter="20" class="modules-grid">
        <el-col :span="6" v-for="module in modules" :key="module.name">
          <div class="module-item" @click="navigateToModule(module.path)">
            <div class="module-icon">
              <el-icon><component :is="iconComponents[module.icon]" /></el-icon>
            </div>
            <div class="module-info">
              <h3>{{ module.title }}</h3>
              <p>{{ module.description }}</p>
              <div class="module-stats">
                <span class="stat-item">
                  <el-icon><DataLine /></el-icon>
                  {{ module.count }}
                </span>
                <span class="stat-item" :class="module.trend">
                  <el-icon><TrendCharts /></el-icon>
                  {{ module.trendText }}
                </span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 课程数据分析 -->
    <el-row :gutter="20" class="analytics-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>课程分类分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div class="category-stats">
              <div v-for="category in courseCategories" :key="category.name" class="category-item">
                <div class="category-info">
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-count">{{ category.count }}门</span>
                </div>
                <div class="category-progress">
                  <el-progress 
                    :percentage="(category.count / statistics.totalCourses * 100)" 
                    :show-text="false"
                    :stroke-width="12"
                    :color="category.color"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习进度统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div class="progress-stats">
              <div class="progress-circle">
                <div class="circle-chart">
                  <div class="circle-value">{{ (statistics.completionRate * 100).toFixed(0) }}%</div>
                  <div class="circle-label">平均完成率</div>
                </div>
              </div>
              <div class="progress-details">
                <div v-for="item in progressDetails" :key="item.name" class="detail-item">
                  <span class="detail-name">{{ item.name }}</span>
                  <div class="detail-bar">
                    <div class="detail-progress" :style="{ width: item.value + '%', backgroundColor: item.color }"></div>
                  </div>
                  <span class="detail-value">{{ item.value }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 热门课程和优秀讲师 -->
    <el-row :gutter="20" class="featured-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>热门课程</span>
              <el-button type="text" @click="$router.push('/course-management/courses')">查看更多</el-button>
            </div>
          </template>
          <div class="featured-list">
            <div v-for="(course, index) in hotCourses" :key="course.id" class="featured-item">
              <div class="featured-rank">{{ index + 1 }}</div>
              <div class="featured-content">
                <div class="featured-title">{{ course.title }}</div>
                <div class="featured-meta">
                  <span class="featured-instructor">{{ course.instructor }}</span>
                  <span class="featured-students">{{ course.students }}人学习</span>
                  <span class="featured-rating">{{ course.rating }}★</span>
                </div>
              </div>
              <div class="featured-status">
                <el-tag size="small" :type="course.status === 'published' ? 'success' : 'warning'">
                  {{ course.status === 'published' ? '已发布' : '草稿' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>优秀讲师</span>
              <el-button type="text" @click="$router.push('/course-management/instructors')">查看更多</el-button>
            </div>
          </template>
          <div class="featured-list">
            <div v-for="instructor in topInstructors" :key="instructor.id" class="featured-item">
              <div class="instructor-avatar">
                <img :src="instructor.avatar || '/default-avatar.png'" :alt="instructor.name" />
              </div>
              <div class="featured-content">
                <div class="featured-title">{{ instructor.name }}</div>
                <div class="featured-meta">
                  <span class="instructor-title">{{ instructor.title }}</span>
                  <span class="instructor-courses">{{ instructor.courses }}门课程</span>
                  <span class="instructor-rating">{{ instructor.rating }}★</span>
                </div>
              </div>
              <div class="featured-status">
                <span class="instructor-badge" :class="instructor.level">
                  {{ getInstructorLevelName(instructor.level) }}
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="recent-activities">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新课程</span>
              <el-button type="text" @click="$router.push('/course-management/courses')">查看更多</el-button>
            </div>
          </template>
          <div class="activity-list">
            <div v-for="item in recentCourses" :key="item.id" class="activity-item">
              <div class="activity-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ item.title }}</div>
                <div class="activity-meta">
                  {{ item.category }} · {{ formatTime(item.created_at) }}
                </div>
              </div>
              <div class="activity-status">
                <el-tag size="small" :type="item.status === 'published' ? 'success' : 'warning'">
                  {{ item.status === 'published' ? '已发布' : '草稿' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学员动态</span>
              <el-button type="text" @click="$router.push('/course-management/students')">查看更多</el-button>
            </div>
          </template>
          <div class="activity-list">
            <div v-for="item in studentActivities" :key="item.id" class="activity-item">
              <div class="activity-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ item.student_name }}</div>
                <div class="activity-meta">
                  完成了《{{ item.course_title }}》· {{ formatTime(item.completed_at) }}
                </div>
              </div>
              <div class="activity-status">
                <span class="completion-badge">{{ item.progress }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>课程评价</span>
              <el-button type="text" @click="$router.push('/course-management/reviews')">查看更多</el-button>
            </div>
          </template>
          <div class="activity-list">
            <div v-for="item in recentReviews" :key="item.id" class="activity-item">
              <div class="activity-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ item.course_title }}</div>
                <div class="activity-meta">
                  {{ item.student_name }} · {{ item.rating }}星评价
                </div>
                <div class="review-content">{{ item.comment }}</div>
              </div>
              <div class="activity-status">
                <el-rate v-model="item.rating" disabled size="small" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态监控 -->
    <el-card class="system-status-card">
      <template #header>
        <div class="card-header">
          <span>系统状态监控</span>
        </div>
      </template>
      
      <el-row :gutter="20" class="status-metrics">
        <el-col :span="6" v-for="metric in systemMetrics" :key="metric.name">
          <div class="status-item">
            <div class="status-icon" :style="{ backgroundColor: metric.color }">
              <el-icon><component :is="iconComponents[metric.icon]" /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-name">{{ metric.name }}</div>
              <div class="status-value">{{ metric.value }}</div>
              <div class="status-trend" :class="metric.trend">
                {{ metric.trendText }}
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Reading, User, UserFilled, CircleCheck, DataLine, TrendCharts, Star
} from '@element-plus/icons-vue'

// 注册图标组件
const iconComponents = {
  Reading,
  User,
  UserFilled,
  TrendCharts,
  DataLine
}

const router = useRouter()

// 统计数据
const statistics = reactive({
  totalCourses: 156,
  totalInstructors: 45,
  totalStudents: 3247,
  completionRate: 0.782
})

// 功能模块
const modules = ref([
  {
    name: 'courses',
    title: '课程管理',
    description: '管理课程内容和结构',
    icon: 'Reading',
    path: '/course-management/courses',
    count: 156,
    trend: 'up',
    trendText: '↑12%'
  },
  {
    name: 'instructors',
    title: '讲师管理',
    description: '管理讲师信息和资质',
    icon: 'User',
    path: '/course-management/instructors',
    count: 45,
    trend: 'up',
    trendText: '↑8%'
  },
  {
    name: 'students',
    title: '学员管理',
    description: '管理学员信息和进度',
    icon: 'UserFilled',
    path: '/course-management/students',
    count: 3247,
    trend: 'up',
    trendText: '↑25%'
  },
  {
    name: 'analytics',
    title: '数据分析',
    description: '课程数据统计分析',
    icon: 'TrendCharts',
    path: '/course-management/analytics',
    count: '78.2%',
    trend: 'up',
    trendText: '↑5%'
  }
])

// 课程分类
const courseCategories = ref([
  { name: '编程开发', count: 45, color: '#409eff' },
  { name: '设计创意', count: 32, color: '#67c23a' },
  { name: '商业管理', count: 28, color: '#e6a23c' },
  { name: '语言学习', count: 25, color: '#f56c6c' },
  { name: '职业技能', count: 26, color: '#909399' }
])

// 进度详情
const progressDetails = ref([
  { name: '已完成', value: 78, color: '#67c23a' },
  { name: '进行中', value: 15, color: '#409eff' },
  { name: '未开始', value: 7, color: '#e6a23c' }
])

// 热门课程
const hotCourses = ref([
  {
    id: 1,
    title: 'Vue.js 3.0 完整教程',
    instructor: '张老师',
    students: 1234,
    rating: 4.8,
    status: 'published'
  },
  {
    id: 2,
    title: 'Python 数据分析实战',
    instructor: '李老师',
    students: 987,
    rating: 4.7,
    status: 'published'
  },
  {
    id: 3,
    title: 'UI/UX 设计进阶',
    instructor: '王老师',
    students: 756,
    rating: 4.9,
    status: 'published'
  }
])

// 优秀讲师
const topInstructors = ref([
  {
    id: 1,
    name: '张教授',
    title: '高级前端工程师',
    avatar: '',
    courses: 12,
    rating: 4.9,
    level: 'expert'
  },
  {
    id: 2,
    name: '李博士',
    title: '数据科学专家',
    avatar: '',
    courses: 8,
    rating: 4.8,
    level: 'senior'
  },
  {
    id: 3,
    name: '王设计师',
    title: 'UI/UX 设计总监',
    avatar: '',
    courses: 15,
    rating: 4.7,
    level: 'expert'
  }
])

// 最新课程
const recentCourses = ref([
  {
    id: 1,
    title: 'React Native 移动开发',
    category: '编程开发',
    status: 'published',
    created_at: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    title: '商业数据分析',
    category: '商业管理',
    status: 'draft',
    created_at: '2024-01-14 15:20:00'
  },
  {
    id: 3,
    title: '平面设计基础',
    category: '设计创意',
    status: 'published',
    created_at: '2024-01-13 09:15:00'
  }
])

// 学员动态
const studentActivities = ref([
  {
    id: 1,
    student_name: '小明',
    course_title: 'Vue.js 3.0 完整教程',
    progress: 100,
    completed_at: '2024-01-15 16:30:00'
  },
  {
    id: 2,
    student_name: '小红',
    course_title: 'Python 数据分析实战',
    progress: 85,
    completed_at: '2024-01-15 14:20:00'
  },
  {
    id: 3,
    student_name: '小李',
    course_title: 'UI/UX 设计进阶',
    progress: 92,
    completed_at: '2024-01-15 11:45:00'
  }
])

// 最新评价
const recentReviews = ref([
  {
    id: 1,
    course_title: 'Vue.js 3.0 完整教程',
    student_name: '学员A',
    rating: 5,
    comment: '课程内容很详细，老师讲解清晰'
  },
  {
    id: 2,
    course_title: 'Python 数据分析实战',
    student_name: '学员B',
    rating: 4,
    comment: '实战案例很有用，学到了很多'
  },
  {
    id: 3,
    course_title: 'UI/UX 设计进阶',
    student_name: '学员C',
    rating: 5,
    comment: '设计思路很棒，受益匪浅'
  }
])

// 系统指标
const systemMetrics = ref([
  {
    name: '服务器状态',
    value: '正常',
    icon: 'CircleCheck',
    color: '#67c23a',
    trend: 'stable',
    trendText: '稳定运行'
  },
  {
    name: '在线用户',
    value: '1,234',
    icon: 'UserFilled',
    color: '#409eff',
    trend: 'up',
    trendText: '↑12%'
  },
  {
    name: '响应时间',
    value: '120ms',
    icon: 'TrendCharts',
    color: '#e6a23c',
    trend: 'down',
    trendText: '↓8%'
  },
  {
    name: '存储使用',
    value: '68%',
    icon: 'DataLine',
    color: '#f56c6c',
    trend: 'up',
    trendText: '↑3%'
  }
])

// 导航到模块
const navigateToModule = (path: string) => {
  router.push(path)
}

// 获取讲师等级名称
const getInstructorLevelName = (level: string) => {
  const levels = {
    junior: '初级',
    intermediate: '中级',
    senior: '高级',
    expert: '专家'
  }
  return levels[level] || level
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleDateString()
}

// 加载数据
const loadData = async () => {
  // TODO: 调用API获取统计数据
  console.log('Loading course management dashboard data...')
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.course-management-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.courses {
  background-color: #409eff;
}

.stat-icon.instructors {
  background-color: #67c23a;
}

.stat-icon.students {
  background-color: #e6a23c;
}

.stat-icon.completion {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.modules-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modules-grid {
  margin-top: 16px;
}

.module-item {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
  height: 140px;
  display: flex;
  flex-direction: column;
}

.module-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.module-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-bottom: 12px;
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.module-info p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  flex: 1;
}

.module-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.stat-item.up {
  color: #67c23a;
}

.analytics-section {
  margin-bottom: 24px;
}

.chart-container {
  padding: 20px 0;
}

.category-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.category-info {
  min-width: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-weight: 500;
  color: #303133;
}

.category-count {
  font-size: 12px;
  color: #909399;
}

.category-progress {
  flex: 1;
}

.progress-stats {
  display: flex;
  align-items: center;
  gap: 40px;
}

.progress-circle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-chart {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.circle-value {
  font-size: 32px;
  font-weight: 600;
  line-height: 1;
}

.circle-label {
  font-size: 12px;
  margin-top: 4px;
}

.progress-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-name {
  min-width: 60px;
  font-size: 14px;
  color: #303133;
}

.detail-bar {
  flex: 1;
  height: 8px;
  background-color: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
}

.detail-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.detail-value {
  min-width: 40px;
  font-size: 12px;
  color: #606266;
  text-align: right;
}

.featured-section {
  margin-bottom: 24px;
}

.featured-list {
  max-height: 300px;
  overflow-y: auto;
}

.featured-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.featured-item:last-child {
  border-bottom: none;
}

.featured-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
}

.instructor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
}

.instructor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-content {
  flex: 1;
}

.featured-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.featured-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.featured-status {
  margin-left: 12px;
}

.instructor-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.instructor-badge.expert {
  background-color: #f56c6c;
}

.instructor-badge.senior {
  background-color: #e6a23c;
}

.instructor-badge.intermediate {
  background-color: #409eff;
}

.instructor-badge.junior {
  background-color: #67c23a;
}

.recent-activities {
  margin-bottom: 24px;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: #f5f7fa;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.activity-meta {
  font-size: 12px;
  color: #909399;
}

.review-content {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-status {
  margin-left: 12px;
}

.completion-badge {
  padding: 2px 6px;
  background-color: #f0f9ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.system-status-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.status-metrics {
  margin-top: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s;
}

.status-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px 0 rgba(64, 158, 255, 0.2);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.status-info {
  flex: 1;
}

.status-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.status-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-trend {
  font-size: 12px;
}

.status-trend.up {
  color: #67c23a;
}

.status-trend.down {
  color: #f56c6c;
}

.status-trend.stable {
  color: #909399;
}
</style>
