"""
用户个人资料相关API端点
"""
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import os
import uuid
import hashlib
from datetime import datetime, timedelta
import logging
import json

from app.core.database import get_db
from app.core.security import get_current_user, verify_password, get_password_hash
from app.models.admin import AdminUser
from app.models.admin import LoginLog
from app.schemas.profile import (
    UserProfileResponse,
    UserProfileUpdate,
    PasswordChangeRequest,
    UserPreferencesUpdate,
    LoginHistoryResponse,
    TwoFactorSetupResponse,
    SecurityStatusResponse,
    TwoFactorConfirmRequest,
    TwoFactorDisableRequest,
    AvatarUploadResponse
)

router = APIRouter(prefix="/profile", tags=["个人资料"])
logger = logging.getLogger(__name__)

@router.get("/", response_model=UserProfileResponse)
async def get_profile(
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前用户的个人资料"""
    try:
        # 获取登录次数
        login_count = db.query(LoginLog).filter(
            LoginLog.user_id == current_user.id,
            LoginLog.status == True
        ).count()
        
        profile_data = {
            "id": current_user.id,
            "username": current_user.username,
            "full_name": current_user.full_name,
            "email": current_user.email,
            "phone": current_user.phone,
            "department": current_user.department,
            "position": current_user.position,
            "bio": current_user.bio,
            "avatar": current_user.avatar,
            "avatar_url": current_user.avatar,
            "role": current_user.role,
            "is_active": current_user.is_active,
            "two_factor_enabled": current_user.two_factor_enabled,
            "last_login": current_user.last_login.isoformat() if current_user.last_login else None,
            "created_at": current_user.created_at.isoformat(),
            "updated_at": current_user.updated_at.isoformat() if current_user.updated_at else None,
            "login_count": login_count,
            "preferences": current_user.preferences or {}
        }
        
        return UserProfileResponse(**profile_data)
    except Exception as e:
        logger.error(f"获取用户资料失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户资料失败"
        )

@router.put("/", response_model=UserProfileResponse)
async def update_profile(
    profile_update: UserProfileUpdate,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新用户基本信息"""
    try:
        # 检查邮箱是否已被其他用户使用
        if profile_update.email and profile_update.email != current_user.email:
            existing_user = db.query(AdminUser).filter(
                AdminUser.email == profile_update.email,
                AdminUser.id != current_user.id
            ).first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该邮箱已被其他用户使用"
                )

        # 检查用户名是否已被其他用户使用
        if profile_update.username and profile_update.username != current_user.username:
            existing_user = db.query(AdminUser).filter(
                AdminUser.username == profile_update.username,
                AdminUser.id != current_user.id
            ).first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该用户名已被其他用户使用"
                )
        
        # 更新用户信息
        update_data = profile_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(current_user, field, value)
        
        current_user.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"用户 {current_user.username} 更新了个人资料")
        
        # 获取登录次数
        login_count = db.query(LoginLog).filter(
            LoginLog.user_id == current_user.id,
            LoginLog.status == True
        ).count()
        
        # 返回更新后的资料
        profile_data = {
            "id": current_user.id,
            "username": current_user.username,
            "full_name": current_user.full_name,
            "email": current_user.email,
            "phone": current_user.phone,
            "department": current_user.department,
            "position": current_user.position,
            "bio": current_user.bio,
            "avatar": current_user.avatar,
            "avatar_url": current_user.avatar,
            "role": current_user.role,
            "is_active": current_user.is_active,
            "two_factor_enabled": current_user.two_factor_enabled,
            "last_login": current_user.last_login.isoformat() if current_user.last_login else None,
            "created_at": current_user.created_at.isoformat(),
            "updated_at": current_user.updated_at.isoformat() if current_user.updated_at else None,
            "login_count": login_count,
            "preferences": current_user.preferences or {}
        }
        
        return UserProfileResponse(**profile_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户资料失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户资料失败"
        )

@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改密码"""
    try:
        # 验证当前密码
        if not verify_password(password_data.current_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前密码不正确"
            )
        
        # 检查新密码是否与当前密码相同
        if verify_password(password_data.new_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="新密码不能与当前密码相同"
            )
        
        # 更新密码
        current_user.hashed_password = get_password_hash(password_data.new_password)
        current_user.password_changed_at = datetime.utcnow()
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        
        logger.info(f"用户 {current_user.username} 修改了密码")
        
        return {"message": "密码修改成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改密码失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="修改密码失败"
        )

@router.put("/preferences")
async def update_preferences(
    preferences: UserPreferencesUpdate,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新用户偏好设置"""
    try:
        # 获取当前偏好设置
        current_preferences = current_user.preferences or {}
        
        # 更新偏好设置
        preferences_data = preferences.dict(exclude_unset=True)
        current_preferences.update(preferences_data)
        
        current_user.preferences = current_preferences
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"用户 {current_user.username} 更新了偏好设置")
        
        return {
            "message": "偏好设置更新成功",
            "preferences": current_user.preferences
        }
    except Exception as e:
        logger.error(f"更新偏好设置失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="偏好设置更新失败"
        )

@router.post("/avatar", response_model=AvatarUploadResponse)
async def upload_avatar(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """上传用户头像"""
    
    # 检查文件类型
    allowed_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的文件类型，请上传 JPEG、PNG、GIF 或 WebP 格式的图片"
        )
    
    # 检查文件大小（5MB限制）
    file_size = 0
    content = await file.read()
    file_size = len(content)
    
    if file_size > 5 * 1024 * 1024:  # 5MB
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件大小不能超过 5MB"
        )
    
    # 生成唯一文件名
    file_extension = file.filename.split(".")[-1].lower()
    unique_filename = f"{uuid.uuid4()}.{file_extension}"
    
    # 创建上传目录
    upload_dir = "uploads/avatars"
    os.makedirs(upload_dir, exist_ok=True)
    
    file_path = os.path.join(upload_dir, unique_filename)
    
    try:
        # 删除旧头像文件
        if current_user.avatar:
            old_file_path = current_user.avatar
            if os.path.exists(old_file_path):
                os.remove(old_file_path)
        
        # 保存新文件
        with open(file_path, "wb") as buffer:
            buffer.write(content)
        
        # 更新数据库
        avatar_url = f"/profile/avatar/{unique_filename}"
        current_user.avatar = file_path
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"用户 {current_user.username} 上传了新头像")
        
        return AvatarUploadResponse(
            message="头像上传成功",
            avatar_url=avatar_url
        )
        
    except Exception as e:
        # 如果保存失败，删除已上传的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        logger.error(f"头像上传失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="头像上传失败"
        )

@router.get("/avatar/{filename}")
async def get_avatar(filename: str):
    """获取用户头像"""
    file_path = os.path.join("uploads/avatars", filename)
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="头像文件不存在"
        )
    
    return FileResponse(file_path)

@router.get("/login-history", response_model=LoginHistoryResponse)
async def get_login_history(
    page: int = 1,
    page_size: int = 10,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户登录历史"""
    try:
        logger.info(f"获取登录历史请求 - 用户ID: {current_user.id}, 页码: {page}, 页大小: {page_size}")
        
        offset = (page - 1) * page_size
        
        # 查询登录历史
        query = db.query(LoginLog).filter(
            LoginLog.user_id == current_user.id
        ).order_by(LoginLog.created_at.desc())
        
        logger.info(f"查询SQL构建完成，开始执行count查询")
        total = query.count()
        logger.info(f"总记录数: {total}")
        
        logger.info(f"开始执行分页查询，offset: {offset}, limit: {page_size}")
        items = query.offset(offset).limit(page_size).all()
        logger.info(f"查询到 {len(items)} 条记录")
        
        # 构建响应数据
        response_items = []
        for i, item in enumerate(items):
            try:
                logger.info(f"处理第 {i+1} 条记录: ID={item.id}, created_at={item.created_at}")
                response_item = {
                    "id": item.id,
                    "login_time": item.created_at.isoformat(),
                    "ip_address": item.ip_address or "未知",
                    "user_agent": item.user_agent or "未知",
                    "location": getattr(item, 'location', '未知'),
                    "status": "success" if item.status else "failed"
                }
                response_items.append(response_item)
                logger.info(f"第 {i+1} 条记录处理完成")
            except Exception as item_error:
                logger.error(f"处理第 {i+1} 条记录时出错: {item_error}")
                logger.error(f"记录详情: {vars(item)}")
                raise
        
        logger.info(f"所有记录处理完成，准备返回响应")
        return LoginHistoryResponse(
            items=response_items,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.error(f"获取登录历史失败: {e}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取登录历史失败"
        )

@router.post("/2fa/enable", response_model=TwoFactorSetupResponse)
async def enable_two_factor(
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """启用双因素认证"""
    
    if current_user.two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="双因素认证已启用"
        )
    
    try:
        import pyotp
        import qrcode
        from io import BytesIO
        import base64
        
        # 生成密钥
        secret = pyotp.random_base32()
        
        # 生成QR码
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=current_user.email,
            issuer_name="WisCude Admin"
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # 转换为base64
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        # 生成备用代码
        backup_codes = [str(uuid.uuid4()).replace('-', '')[:8].upper() for _ in range(10)]
        
        # 临时保存密钥（用户确认后才正式启用）
        current_user.two_factor_secret = secret
        current_user.two_factor_backup_codes = backup_codes
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"用户 {current_user.username} 开始设置双因素认证")
        
        return TwoFactorSetupResponse(
            secret=secret,
            qr_code=f"data:image/png;base64,{qr_code_base64}",
            backup_codes=backup_codes
        )
    except Exception as e:
        logger.error(f"启用双因素认证失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启用双因素认证失败"
        )

@router.post("/2fa/confirm")
async def confirm_two_factor(
    request: TwoFactorConfirmRequest,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """确认双因素认证"""
    
    if current_user.two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="双因素认证已启用"
        )
    
    if not current_user.two_factor_secret:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请先启用双因素认证"
        )
    
    try:
        import pyotp
        
        # 验证TOTP代码
        totp = pyotp.TOTP(current_user.two_factor_secret)
        if not totp.verify(request.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码错误"
            )
        
        # 正式启用双因素认证
        current_user.two_factor_enabled = True
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"用户 {current_user.username} 成功启用双因素认证")
        
        return {
            "message": "双因素认证启用成功",
            "two_factor_enabled": True
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认双因素认证失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="确认双因素认证失败"
        )

@router.post("/2fa/disable")
async def disable_two_factor(
    request: TwoFactorDisableRequest,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """禁用双因素认证"""
    
    if not current_user.two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="双因素认证未启用"
        )
    
    try:
        # 验证密码
        if not verify_password(request.password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码错误"
            )
        
        # 禁用双因素认证
        current_user.two_factor_enabled = False
        current_user.two_factor_secret = None
        current_user.two_factor_backup_codes = None
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"用户 {current_user.username} 禁用了双因素认证")
        
        return {
            "message": "双因素认证已禁用",
            "two_factor_enabled": False
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"禁用双因素认证失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="禁用双因素认证失败"
        )

@router.get("/security-status", response_model=SecurityStatusResponse)
async def get_security_status(
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取账户安全状态"""
    try:
        # 获取失败登录次数（最近24小时）
        failed_attempts = db.query(LoginLog).filter(
            LoginLog.user_id == current_user.id,
            LoginLog.status == False,
            LoginLog.created_at >= datetime.utcnow() - timedelta(hours=24)
        ).count()
        
        # 计算密码强度（简化版）
        password_strength = "medium"  # 实际应用中应该基于密码复杂度计算
        
        # 获取最近登录记录
        recent_logins = db.query(LoginLog).filter(
            LoginLog.user_id == current_user.id,
            LoginLog.status == True
        ).order_by(LoginLog.created_at.desc()).limit(5).all()
        
        return SecurityStatusResponse(
            password_strength=password_strength,
            last_password_change=current_user.password_changed_at.isoformat() if current_user.password_changed_at else None,
            failed_login_attempts=failed_attempts,
            active_sessions=1,  # 简化处理
            two_factor_enabled=current_user.two_factor_enabled,
            recent_logins=[
                {
                    "ip_address": log.ip_address,
                    "user_agent": log.user_agent,
                    "login_time": log.created_at.isoformat(),
                    "location": getattr(log, 'location', '未知')
                }
                for log in recent_logins
            ]
        )
    except Exception as e:
        logger.error(f"获取安全状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取安全状态失败"
        )
