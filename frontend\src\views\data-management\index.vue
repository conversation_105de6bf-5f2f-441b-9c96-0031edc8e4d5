<template>
  <div class="data-analysis-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>数据分析中心</h1>
          <p>深度洞察学习数据，驱动教育决策优化</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="exportReport" :loading="exporting">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
          <el-button @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据筛选器 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <el-form :model="filters" inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item label="用户群体">
            <el-select v-model="filters.userGroup" placeholder="选择用户群体" @change="handleFilterChange">
              <el-option label="全部用户" value="all" />
              <el-option label="新用户" value="new" />
              <el-option label="活跃用户" value="active" />
              <el-option label="付费用户" value="paid" />
            </el-select>
          </el-form-item>
          <el-form-item label="课程类型">
            <el-select v-model="filters.courseType" placeholder="选择课程类型" @change="handleFilterChange">
              <el-option label="全部课程" value="all" />
              <el-option label="数学" value="math" />
              <el-option label="英语" value="english" />
              <el-option label="科学" value="science" />
              <el-option label="编程" value="programming" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="applyFilters">应用筛选</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 核心统计指标 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card primary">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalUsers) }}</div>
                <div class="stat-label">总用户数</div>
                <div class="stat-trend" :class="statistics.userGrowth >= 0 ? 'positive' : 'negative'">
                  <el-icon><ArrowUp v-if="statistics.userGrowth >= 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(statistics.userGrowth) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.activeUsers) }}</div>
                <div class="stat-label">活跃用户</div>
                <div class="stat-trend" :class="statistics.activeGrowth >= 0 ? 'positive' : 'negative'">
                  <el-icon><ArrowUp v-if="statistics.activeGrowth >= 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(statistics.activeGrowth) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.completedCourses) }}</div>
                <div class="stat-label">完成课程数</div>
                <div class="stat-trend" :class="statistics.courseGrowth >= 0 ? 'positive' : 'negative'">
                  <el-icon><ArrowUp v-if="statistics.courseGrowth >= 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(statistics.courseGrowth) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card info">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalLearningHours) }}</div>
                <div class="stat-label">学习时长(小时)</div>
                <div class="stat-trend" :class="statistics.timeGrowth >= 0 ? 'positive' : 'negative'">
                  <el-icon><ArrowUp v-if="statistics.timeGrowth >= 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(statistics.timeGrowth) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据可视化图表区域 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <!-- 用户增长趋势 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>用户增长趋势</span>
                <el-select v-model="userTrendPeriod" size="small" @change="updateUserTrend">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <LineChart
                :data="userTrendData"
                :options="userTrendOptions"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 学习时长分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>学习时长分布</span>
            </template>
            <div class="chart-container">
              <PieChart
                :data="learningTimeData"
                :options="learningTimeOptions"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 课程完成率 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <span>课程完成率统计</span>
            </template>
            <div class="chart-container">
              <BarChart
                :data="courseCompletionData"
                :options="courseCompletionOptions"
                height="350px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 学习活跃度热力图 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>学习活跃度</span>
            </template>
            <div class="chart-container">
              <div class="activity-heatmap">
                <div v-for="(day, index) in activityData" :key="index" class="activity-day">
                  <div class="day-label">{{ day.label }}</div>
                  <div class="activity-bars">
                    <div
                      v-for="(hour, hourIndex) in day.hours"
                      :key="hourIndex"
                      class="activity-bar"
                      :style="{ height: hour.value + '%', backgroundColor: getActivityColor(hour.value) }"
                      :title="`${hour.hour}:00 - ${hour.value}%活跃度`"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时数据更新提示 -->
    <div class="real-time-indicator" v-if="isRealTimeEnabled">
      <el-icon class="pulse"><Connection /></el-icon>
      <span>实时数据更新中...</span>
      <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download, Refresh, User, TrendCharts, Document, Clock,
  ArrowUp, ArrowDown, Connection
} from '@element-plus/icons-vue'
import { LineChart, PieChart, BarChart } from '@/components/charts'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const isRealTimeEnabled = ref(true)
const lastUpdateTime = ref('')
const userTrendPeriod = ref('30d')

// 筛选器
const filters = reactive({
  dateRange: [] as string[],
  userGroup: 'all',
  courseType: 'all'
})

// 核心统计数据
const statistics = reactive({
  totalUsers: 15847,
  activeUsers: 8923,
  completedCourses: 2156,
  totalLearningHours: 45678,
  userGrowth: 12.5,
  activeGrowth: 8.3,
  courseGrowth: 15.7,
  timeGrowth: 22.1
})

// 用户增长趋势数据
const userTrendData = ref({
  labels: [] as string[],
  datasets: [
    {
      label: '新增用户',
      data: [] as number[],
      borderColor: '#4f46e5',
      backgroundColor: 'rgba(79, 70, 229, 0.1)',
      tension: 0.4
    },
    {
      label: '活跃用户',
      data: [] as number[],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4
    }
  ]
})

const userTrendOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false,
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '日期'
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '用户数量'
      }
    }
  }
})

// 学习时长分布数据
const learningTimeData = ref({
  labels: ['0-1小时', '1-3小时', '3-5小时', '5-8小时', '8小时以上'],
  datasets: [{
    data: [25, 35, 20, 15, 5],
    backgroundColor: [
      '#4f46e5',
      '#06b6d4',
      '#10b981',
      '#f59e0b',
      '#ef4444'
    ]
  }]
})

const learningTimeOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    }
  }
})

// 课程完成率数据
const courseCompletionData = ref({
  labels: ['数学', '英语', '科学', '编程', '艺术', '体育'],
  datasets: [{
    label: '完成率 (%)',
    data: [85, 78, 92, 67, 73, 88],
    backgroundColor: 'rgba(79, 70, 229, 0.8)',
    borderColor: '#4f46e5',
    borderWidth: 1
  }]
})

const courseCompletionOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      title: {
        display: true,
        text: '完成率 (%)'
      }
    }
  }
})

// 学习活跃度数据
const activityData = ref([
  {
    label: '周一',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.random() * 100
    }))
  },
  {
    label: '周二',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.random() * 100
    }))
  },
  {
    label: '周三',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.random() * 100
    }))
  },
  {
    label: '周四',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.random() * 100
    }))
  },
  {
    label: '周五',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.random() * 100
    }))
  },
  {
    label: '周六',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.random() * 100
    }))
  },
  {
    label: '周日',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.random() * 100
    }))
  }
])

// 实时更新定时器
let updateTimer: NodeJS.Timeout | null = null

// 计算属性
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getActivityColor = (value: number): string => {
  if (value >= 80) return '#10b981'
  if (value >= 60) return '#f59e0b'
  if (value >= 40) return '#06b6d4'
  if (value >= 20) return '#8b5cf6'
  return '#e5e7eb'
}

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await loadAnalyticsData()
    ElMessage.success('数据刷新成功')
    lastUpdateTime.value = new Date().toLocaleString()
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportReport = async () => {
  exporting.value = true
  try {
    // 准备导出数据
    const reportData = {
      statistics: statistics,
      userTrend: userTrendData.value,
      learningTime: learningTimeData.value,
      courseCompletion: courseCompletionData.value
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 统计数据表
    const statsWs = XLSX.utils.json_to_sheet([{
      '总用户数': statistics.totalUsers,
      '活跃用户': statistics.activeUsers,
      '完成课程数': statistics.completedCourses,
      '学习时长': statistics.totalLearningHours,
      '用户增长率': statistics.userGrowth + '%',
      '活跃增长率': statistics.activeGrowth + '%',
      '课程增长率': statistics.courseGrowth + '%',
      '时长增长率': statistics.timeGrowth + '%'
    }])
    XLSX.utils.book_append_sheet(wb, statsWs, '统计概览')

    // 课程完成率表
    const courseWs = XLSX.utils.json_to_sheet(
      courseCompletionData.value.labels.map((label, index) => ({
        '课程': label,
        '完成率': courseCompletionData.value.datasets[0].data[index] + '%'
      }))
    )
    XLSX.utils.book_append_sheet(wb, courseWs, '课程完成率')

    // 导出文件
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([wbout], { type: 'application/octet-stream' })
    saveAs(blob, `数据分析报告_${new Date().toISOString().split('T')[0]}.xlsx`)

    ElMessage.success('报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  } finally {
    exporting.value = false
  }
}

const handleDateChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    loadAnalyticsData()
  }
}

const handleFilterChange = () => {
  loadAnalyticsData()
}

const applyFilters = () => {
  loadAnalyticsData()
  ElMessage.success('筛选条件已应用')
}

const resetFilters = () => {
  filters.dateRange = []
  filters.userGroup = 'all'
  filters.courseType = 'all'
  loadAnalyticsData()
  ElMessage.success('筛选条件已重置')
}

const updateUserTrend = () => {
  generateUserTrendData()
}

const generateUserTrendData = () => {
  const days = userTrendPeriod.value === '7d' ? 7 : userTrendPeriod.value === '30d' ? 30 : 90
  const labels = []
  const newUsers = []
  const activeUsers = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString())
    newUsers.push(Math.floor(Math.random() * 200) + 50)
    activeUsers.push(Math.floor(Math.random() * 500) + 200)
  }

  userTrendData.value.labels = labels
  userTrendData.value.datasets[0].data = newUsers
  userTrendData.value.datasets[1].data = activeUsers
}

const loadAnalyticsData = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成模拟数据
    generateUserTrendData()

    // 更新统计数据
    statistics.totalUsers = Math.floor(Math.random() * 5000) + 15000
    statistics.activeUsers = Math.floor(Math.random() * 3000) + 8000
    statistics.completedCourses = Math.floor(Math.random() * 1000) + 2000
    statistics.totalLearningHours = Math.floor(Math.random() * 10000) + 40000

    console.log('Analytics data loaded')
  } catch (error) {
    console.error('Failed to load analytics data:', error)
    throw error
  }
}

const startRealTimeUpdates = () => {
  if (updateTimer) return

  updateTimer = setInterval(() => {
    if (isRealTimeEnabled.value) {
      // 模拟实时数据更新
      statistics.totalUsers += Math.floor(Math.random() * 5)
      statistics.activeUsers += Math.floor(Math.random() * 3)
      lastUpdateTime.value = new Date().toLocaleString()
    }
  }, 30000) // 每30秒更新一次
}

const stopRealTimeUpdates = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// 生命周期
onMounted(() => {
  loadAnalyticsData()
  startRealTimeUpdates()
  lastUpdateTime.value = new Date().toLocaleString()
})

onUnmounted(() => {
  stopRealTimeUpdates()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.data-analysis-dashboard {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 筛选器卡片 */
  .filter-card {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);

    .filter-content {
      .el-form {
        .el-form-item {
          margin-bottom: 0;
          margin-right: var(--spacing-6);

          .el-form-item__label {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
          }

          .el-date-editor,
          .el-select {
            border-radius: var(--radius-lg);
          }
        }

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 统计概览 */
  .stats-overview {
    margin-bottom: var(--spacing-8);

    .stat-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.primary {
        border-left: 4px solid var(--primary-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }
      }

      &.success {
        border-left: 4px solid var(--success-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--success-color), var(--success-light));
        }
      }

      &.warning {
        border-left: 4px solid var(--warning-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
        }
      }

      &.info {
        border-left: 4px solid var(--info-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--info-color), var(--info-light));
        }
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-xl);
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }
          }
        }
      }
    }
  }

  /* 图表区域 */
  .charts-section {
    .chart-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      margin-bottom: var(--spacing-6);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);
        padding: var(--spacing-5) var(--spacing-6);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-container {
        padding: var(--spacing-4);
        min-height: 300px;
      }
    }

    /* 学习活跃度热力图 */
    .activity-heatmap {
      padding: var(--spacing-4);

      .activity-day {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-3);

        .day-label {
          width: 40px;
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
          font-weight: var(--font-weight-medium);
        }

        .activity-bars {
          display: flex;
          gap: 2px;
          flex: 1;

          .activity-bar {
            width: 8px;
            min-height: 4px;
            border-radius: 2px;
            cursor: pointer;
            transition: var(--transition-fast);

            &:hover {
              transform: scaleY(1.2);
            }
          }
        }
      }
    }
  }

  /* 实时更新指示器 */
  .real-time-indicator {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    background: var(--bg-primary);
    color: white;
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    z-index: 1000;

    .pulse {
      animation: pulse 2s infinite;
    }

    .last-update {
      font-size: var(--font-size-xs);
      opacity: 0.8;
      margin-left: var(--spacing-2);
    }
  }
}

/* 动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .data-analysis-dashboard {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .filter-card .filter-content .el-form {
      .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-4);
      }
    }

    .charts-section {
      .el-col {
        margin-bottom: var(--spacing-6);
      }
    }
  }
}

@include respond-to('md') {
  .data-analysis-dashboard {
    padding: var(--spacing-4);

    .stats-overview {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }

    .charts-section {
      .activity-heatmap {
        .activity-day {
          .activity-bars {
            .activity-bar {
              width: 6px;
            }
          }
        }
      }
    }

    .real-time-indicator {
      bottom: var(--spacing-4);
      right: var(--spacing-4);
      font-size: var(--font-size-xs);
      padding: var(--spacing-2) var(--spacing-3);

      .last-update {
        display: none;
      }
    }
  }
}
</style>
