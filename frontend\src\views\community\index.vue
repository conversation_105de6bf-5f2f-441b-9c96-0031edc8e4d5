<template>
  <div class="community-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>学习社区</h1>
          <p>连接学习者，分享知识，共同成长</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="createPost">
            <el-icon><EditPen /></el-icon>
            发布动态
          </el-button>
          <el-button @click="viewMessages">
            <el-icon><ChatDotRound /></el-icon>
            消息中心
          </el-button>
          <el-button @click="viewFriends">
            <el-icon><User /></el-icon>
            我的好友
          </el-button>
        </div>
      </div>
    </div>

    <!-- 社区统计概览 -->
    <div class="community-stats">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card members">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ communityStats.totalMembers }}</div>
                <div class="stat-label">社区成员</div>
                <div class="stat-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  +{{ communityStats.newMembers }}新成员
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card posts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ communityStats.totalPosts }}</div>
                <div class="stat-label">社区动态</div>
                <div class="stat-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  +{{ communityStats.todayPosts }}今日发布
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card activities">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ communityStats.activeActivities }}</div>
                <div class="stat-label">进行中活动</div>
                <div class="stat-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  {{ communityStats.participationRate }}%参与率
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card online">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ communityStats.onlineMembers }}</div>
                <div class="stat-label">在线成员</div>
                <div class="stat-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  {{ communityStats.activeRate }}%活跃度
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 热门话题 -->
    <div class="hot-topics">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>热门话题</span>
            <el-button text @click="viewAllTopics">查看全部</el-button>
          </div>
        </template>
        <div class="topics-list">
          <div
            v-for="topic in hotTopics"
            :key="topic.id"
            class="topic-item"
            @click="viewTopic(topic)"
          >
            <div class="topic-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="topic-content">
              <div class="topic-title">{{ topic.title }}</div>
              <div class="topic-meta">
                <span class="topic-category">{{ topic.category }}</span>
                <span class="topic-stats">{{ topic.replies }}回复 · {{ topic.views }}浏览</span>
              </div>
            </div>
            <div class="topic-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>{{ topic.trend }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  EditPen, ChatDotRound, User, UserFilled, Document, Trophy, Connection,
  ArrowUp, TrendCharts, StarFilled, Share, Clock
} from '@element-plus/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const postFilter = ref('all')
const loadingPosts = ref(false)
const hasMorePosts = ref(true)

// 社区统计数据
const communityStats = reactive({
  totalMembers: 2847,
  totalPosts: 1256,
  activeActivities: 8,
  onlineMembers: 342,
  newMembers: 23,
  todayPosts: 45,
  participationRate: 78,
  activeRate: 85
})

// 热门话题数据
const hotTopics = ref([
  {
    id: '1',
    title: '如何高效学习数学？',
    category: '学习方法',
    replies: 156,
    views: 2340,
    trend: '热门'
  },
  {
    id: '2',
    title: '英语口语练习技巧分享',
    category: '英语学习',
    replies: 89,
    views: 1567,
    trend: '上升'
  },
  {
    id: '3',
    title: '编程学习路线推荐',
    category: '技术交流',
    replies: 234,
    views: 3456,
    trend: '火爆'
  },
  {
    id: '4',
    title: '考试复习计划制定',
    category: '考试准备',
    replies: 67,
    views: 987,
    trend: '新增'
  }
])

// 方法
const createPost = () => {
  router.push('/community/create-post')
}

const viewMessages = () => {
  router.push('/community/messages')
}

const viewFriends = () => {
  router.push('/community/friends')
}

const viewAllTopics = () => {
  router.push('/community/topics')
}

const viewTopic = (topic: any) => {
  router.push(`/community/topics/${topic.id}`)
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.community-overview {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 社区统计 */
  .community-stats {
    margin-bottom: var(--spacing-8);

    .stat-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.members {
        border-left: 4px solid #4f46e5;
      }

      &.posts {
        border-left: 4px solid #10b981;
      }

      &.activities {
        border-left: 4px solid #f59e0b;
      }

      &.online {
        border-left: 4px solid #ef4444;
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-xl);
        }

        .members .stat-icon {
          background: linear-gradient(135deg, #4f46e5, #6366f1);
        }

        .posts .stat-icon {
          background: linear-gradient(135deg, #10b981, #34d399);
        }

        .activities .stat-icon {
          background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }

        .online .stat-icon {
          background: linear-gradient(135deg, #ef4444, #f87171);
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .stat-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }
          }
        }
      }
    }
  }

  /* 热门话题 */
  .hot-topics {
    margin-bottom: var(--spacing-8);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .topics-list {
      .topic-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: var(--transition-fast);
        cursor: pointer;

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        .topic-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--radius-lg);
          background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-base);
        }

        .topic-content {
          flex: 1;

          .topic-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .topic-meta {
            display: flex;
            gap: var(--spacing-3);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);

            .topic-category {
              background: var(--bg-secondary);
              padding: 2px 6px;
              border-radius: var(--radius-sm);
            }
          }
        }

        .topic-trend {
          display: flex;
          align-items: center;
          gap: var(--spacing-1);
          font-size: var(--font-size-sm);
          color: var(--warning-color);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .community-overview {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .community-stats {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }
  }
}

@include respond-to('md') {
  .community-overview {
    padding: var(--spacing-4);

    .community-stats {
      .stat-card .stat-content {
        padding: var(--spacing-4);

        .stat-icon {
          width: 50px;
          height: 50px;
        }

        .stat-info .stat-value {
          font-size: var(--font-size-xl);
        }
      }
    }

    .hot-topics {
      .topics-list .topic-item {
        padding: var(--spacing-3);

        .topic-icon {
          width: 32px;
          height: 32px;
        }

        .topic-content .topic-title {
          font-size: var(--font-size-sm);
        }
      }
    }
  }
}
</style>