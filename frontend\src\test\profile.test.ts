/**
 * 个人资料功能测试
 */
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import Profile from '@/views/Profile.vue'
import { useUserStore } from '@/store/user'
import type { UserProfile } from '@/api/profile'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn().mockResolvedValue('confirm'),
    alert: vi.fn().mockResolvedValue('confirm')
  }
}))

// Mock API
vi.mock('@/api/profile', () => ({
  profileApi: {
    getProfile: vi.fn(),
    updateProfile: vi.fn(),
    changePassword: vi.fn(),
    updatePreferences: vi.fn(),
    uploadAvatar: vi.fn(),
    getLoginHistory: vi.fn(),
    enableTwoFactor: vi.fn(),
    disableTwoFactor: vi.fn()
  }
}))

const mockUserProfile: UserProfile = {
  id: 1,
  username: 'testuser',
  full_name: '测试用户',
  email: '<EMAIL>',
  phone: '13800138000',
  department: '技术部',
  position: '开发工程师',
  bio: '这是一个测试用户',
  avatar: '/api/profile/avatar/test.jpg',
  role: 'user',
  is_active: true,
  two_factor_enabled: false,
  last_login: '2024-01-20T10:30:00Z',
  created_at: '2024-01-01T00:00:00Z',
  login_count: 25,
  preferences: {
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    theme: 'light',
    email_notifications: ['system_updates', 'security_alerts'],
    default_page_size: 20
  }
}

describe('Profile Page', () => {
  let wrapper: any
  let userStore: any
  let authStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    userStore = useUserStore()
    authStore = useAuthStore()

    // 默认设置为已登录状态
    authStore.user = {
      id: 1,
      username: 'testuser',
      full_name: '测试用户',
      email: '<EMAIL>',
      role: 'admin'
    }
    authStore.isAuthenticated = true
    
    // Mock store methods
    userStore.fetchProfile = vi.fn().mockResolvedValue(mockUserProfile)
    userStore.updateProfile = vi.fn().mockResolvedValue(mockUserProfile)
    userStore.changePassword = vi.fn().mockResolvedValue(true)
    userStore.updatePreferences = vi.fn().mockResolvedValue(true)
    userStore.getLoginHistory = vi.fn().mockResolvedValue({
      items: [],
      total: 0,
      page: 1,
      page_size: 10
    })

    wrapper = mount(Profile, {
      global: {
        stubs: {
          'el-card': true,
          'el-tabs': true,
          'el-tab-pane': true,
          'el-form': true,
          'el-form-item': true,
          'el-input': true,
          'el-button': true,
          'el-upload': true,
          'el-table': true,
          'el-pagination': true,
          'el-tag': true,
          'el-icon': true,
          'el-select': true,
          'el-option': true,
          'el-checkbox-group': true,
          'el-checkbox': true,
          'el-radio-group': true,
          'el-radio': true,
          'el-input-number': true,
          'el-divider': true
        }
      }
    })
  })

  it('应该正确渲染个人资料页面', () => {
    expect(wrapper.find('.profile-page').exists()).toBe(true)
    expect(wrapper.find('h1').text()).toBe('个人资料')
  })

  it('应该显示用户基本信息', async () => {
    // 设置用户资料数据
    await wrapper.setData({
      userInfo: mockUserProfile
    })

    expect(wrapper.text()).toContain('testuser')
    expect(wrapper.text()).toContain('<EMAIL>')
    expect(wrapper.text()).toContain('技术部')
  })

  it('应该有三个主要标签页', () => {
    expect(wrapper.text()).toContain('基本信息')
    expect(wrapper.text()).toContain('安全设置')
    expect(wrapper.text()).toContain('偏好设置')
  })

  it('基本信息表单应该包含所有必要字段', () => {
    expect(wrapper.text()).toContain('用户名')
    expect(wrapper.text()).toContain('真实姓名')
    expect(wrapper.text()).toContain('邮箱地址')
    expect(wrapper.text()).toContain('手机号码')
    expect(wrapper.text()).toContain('部门')
    expect(wrapper.text()).toContain('职位')
    expect(wrapper.text()).toContain('个人简介')
  })

  it('安全设置应该包含密码修改功能', () => {
    expect(wrapper.text()).toContain('修改密码')
    expect(wrapper.text()).toContain('当前密码')
    expect(wrapper.text()).toContain('新密码')
    expect(wrapper.text()).toContain('确认密码')
  })

  it('应该显示双因素认证设置', () => {
    expect(wrapper.text()).toContain('双因素认证')
    expect(wrapper.text()).toContain('双因素认证状态')
  })

  it('应该显示登录历史', () => {
    expect(wrapper.text()).toContain('登录历史')
    expect(wrapper.text()).toContain('登录时间')
    expect(wrapper.text()).toContain('IP地址')
    expect(wrapper.text()).toContain('设备信息')
  })

  it('偏好设置应该包含所有配置选项', () => {
    expect(wrapper.text()).toContain('界面语言')
    expect(wrapper.text()).toContain('时区设置')
    expect(wrapper.text()).toContain('主题设置')
    expect(wrapper.text()).toContain('邮件通知')
    expect(wrapper.text()).toContain('默认页面大小')
  })

  it('应该显示用户统计信息', async () => {
    await wrapper.setData({
      userInfo: mockUserProfile
    })

    expect(wrapper.text()).toContain('注册时间')
    expect(wrapper.text()).toContain('登录次数')
    expect(wrapper.text()).toContain('账户状态')
  })
})

describe('Profile Store', () => {
  let userStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    userStore = useUserStore()
  })

  it('应该正确初始化状态', () => {
    expect(userStore.profile).toBeNull()
    expect(userStore.loading).toBe(false)
    expect(userStore.updating).toBe(false)
  })

  it('计算属性应该正确工作', () => {
    expect(userStore.isProfileLoaded).toBe(false)
    expect(userStore.displayName).toBe('')
    expect(userStore.hasAvatar).toBe(false)

    userStore.profile = mockUserProfile
    expect(userStore.isProfileLoaded).toBe(true)
    expect(userStore.displayName).toBe('测试用户')
    expect(userStore.hasAvatar).toBe(true)
  })

  it('应该能够获取用户资料', async () => {
    const mockFetch = vi.fn().mockResolvedValue(mockUserProfile)
    userStore.fetchProfile = mockFetch

    const result = await userStore.fetchProfile()
    expect(mockFetch).toHaveBeenCalled()
    expect(result).toEqual(mockUserProfile)
  })

  it('应该能够更新用户资料', async () => {
    const updateData = { full_name: '新名称' }
    const mockUpdate = vi.fn().mockResolvedValue({ ...mockUserProfile, ...updateData })
    userStore.updateProfile = mockUpdate

    const result = await userStore.updateProfile(updateData)
    expect(mockUpdate).toHaveBeenCalledWith(updateData)
    expect(result.full_name).toBe('新名称')
  })

  it('应该能够修改密码', async () => {
    const passwordData = {
      current_password: 'oldpass',
      new_password: 'newpass'
    }
    const mockChangePassword = vi.fn().mockResolvedValue(true)
    userStore.changePassword = mockChangePassword

    const result = await userStore.changePassword(passwordData)
    expect(mockChangePassword).toHaveBeenCalledWith(passwordData)
    expect(result).toBe(true)
  })

  it('应该能够更新偏好设置', async () => {
    const preferences = {
      language: 'en-US',
      timezone: 'America/New_York',
      theme: 'dark',
      email_notifications: ['security_alerts'],
      default_page_size: 50
    }
    const mockUpdatePreferences = vi.fn().mockResolvedValue(true)
    userStore.updatePreferences = mockUpdatePreferences

    const result = await userStore.updatePreferences(preferences)
    expect(mockUpdatePreferences).toHaveBeenCalledWith(preferences)
    expect(result).toBe(true)
  })
})

describe('Profile Form Validation', () => {
  it('应该验证邮箱格式', () => {
    const validEmail = '<EMAIL>'
    const invalidEmail = 'invalid-email'
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    expect(emailRegex.test(validEmail)).toBe(true)
    expect(emailRegex.test(invalidEmail)).toBe(false)
  })

  it('应该验证手机号格式', () => {
    const validPhone = '13800138000'
    const invalidPhone = '12345'
    
    const phoneRegex = /^1[3-9]\d{9}$/
    expect(phoneRegex.test(validPhone)).toBe(true)
    expect(phoneRegex.test(invalidPhone)).toBe(false)
  })

  it('应该验证用户名长度', () => {
    const validUsername = 'testuser'
    const shortUsername = 'ab'
    const longUsername = 'a'.repeat(21)
    
    expect(validUsername.length).toBeGreaterThanOrEqual(3)
    expect(validUsername.length).toBeLessThanOrEqual(20)
    expect(shortUsername.length).toBeLessThan(3)
    expect(longUsername.length).toBeGreaterThan(20)
  })

  it('应该验证密码强度', () => {
    const weakPassword = '123456'
    const strongPassword = 'StrongPass123!'
    
    // 简单的密码强度检查
    const hasUpperCase = /[A-Z]/.test(strongPassword)
    const hasLowerCase = /[a-z]/.test(strongPassword)
    const hasNumbers = /\d/.test(strongPassword)
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(strongPassword)
    const isLongEnough = strongPassword.length >= 8
    
    expect(hasUpperCase).toBe(true)
    expect(hasLowerCase).toBe(true)
    expect(hasNumbers).toBe(true)
    expect(hasSpecialChar).toBe(true)
    expect(isLongEnough).toBe(true)
    
    // 弱密码检查
    expect(weakPassword.length).toBeLessThan(8)
    expect(/[A-Z]/.test(weakPassword)).toBe(false)
  })
})
