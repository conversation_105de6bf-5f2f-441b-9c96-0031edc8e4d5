<template>
  <div class="advertising-dashboard page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>广告管理概览</h1>
      <p>管理轮播图、弹窗等广告内容的展示和统计分析</p>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon primary">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalBanners) }}</div>
                <div class="stat-label">轮播图总数</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+5.2%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalPopups) }}</div>
                <div class="stat-label">弹窗总数</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+3.8%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon warning">
                <el-icon><View /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalViews) }}</div>
                <div class="stat-label">总展示次数</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+12.7%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon info">
                <el-icon><Mouse /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalClicks) }}</div>
                <div class="stat-label">总点击次数</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+6.8%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 功能模块导航 -->
    <el-card class="modules-card">
      <template #header>
        <div class="card-header">
          <span>功能模块</span>
        </div>
      </template>
      
      <el-row :gutter="20" class="modules-grid">
        <el-col :span="8" v-for="module in modules" :key="module.name">
          <div class="module-item" @click="navigateToModule(module.path)">
            <div class="module-icon">
              <el-icon><component :is="iconComponents[module.icon]" /></el-icon>
            </div>
            <div class="module-info">
              <h3>{{ module.title }}</h3>
              <p>{{ module.description }}</p>
              <div class="module-stats">
                <span class="stat-item">
                  <el-icon><DataLine /></el-icon>
                  {{ module.count }}
                </span>
                <span class="stat-item" :class="module.trend">
                  <el-icon><TrendCharts /></el-icon>
                  {{ module.trendText }}
                </span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="recent-activities">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近轮播图</span>
              <el-button type="link" @click="$router.push('/advertising/banners')">查看更多</el-button>
            </div>
          </template>
          <div class="activity-list">
            <div v-for="item in recentBanners" :key="item.id" class="activity-item">
              <div class="activity-icon">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ item.title }}</div>
                <div class="activity-meta">{{ item.position }} · {{ item.time }}</div>
              </div>
              <div class="activity-status">
                <el-tag :type="item.status === 'active' ? 'success' : 'warning'">
                  {{ item.status === 'active' ? '已发布' : '草稿' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>热门弹窗</span>
              <el-button type="link" @click="$router.push('/advertising/popups')">查看更多</el-button>
            </div>
          </template>
          <div class="activity-list">
            <div v-for="item in hotPopups" :key="item.id" class="activity-item">
              <div class="activity-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ item.title }}</div>
                <div class="activity-meta">{{ item.displays }}次展示 · {{ item.clicks }}次点击</div>
              </div>
              <div class="activity-status">
                <el-icon class="hot-icon"><Star /></el-icon>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Picture, Monitor, View, Mouse, DataLine, TrendCharts,
  Star, ArrowUp
} from '@element-plus/icons-vue'

// 注册图标组件
const iconComponents = {
  Picture,
  Monitor
}

const router = useRouter()

// 统计数据
const statistics = reactive({
  totalBanners: 45,
  totalPopups: 23,
  totalViews: 125000,
  totalClicks: 8900
})

// 功能模块
const modules = ref([
  {
    name: 'banners',
    title: '轮播图管理',
    description: '管理首页轮播图和广告横幅',
    icon: 'Picture',
    path: '/advertising/banners',
    count: 45,
    trend: 'up',
    trendText: '↑12%'
  },
  {
    name: 'popups',
    title: '弹窗管理',
    description: '管理应用内弹窗广告',
    icon: 'Monitor',
    path: '/advertising/popups',
    count: 23,
    trend: 'up',
    trendText: '↑8%'
  }
])

// 最近轮播图
const recentBanners = ref([
  {
    id: 1,
    title: '新学期课程推广',
    position: '首页轮播',
    time: '2小时前',
    status: 'active'
  },
  {
    id: 2,
    title: '英语学习活动',
    position: '课程页面',
    time: '4小时前',
    status: 'draft'
  },
  {
    id: 3,
    title: '心理健康讲座',
    position: '社区页面',
    time: '6小时前',
    status: 'active'
  }
])

// 热门弹窗
const hotPopups = ref([
  {
    id: 1,
    title: '新用户注册优惠',
    displays: 1234,
    clicks: 456
  },
  {
    id: 2,
    title: '课程限时折扣',
    displays: 987,
    clicks: 234
  },
  {
    id: 3,
    title: '学习打卡活动',
    displays: 765,
    clicks: 123
  }
])

// 导航到模块
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const navigateToModule = (path: string) => {
  router.push(path)
}

// 加载数据
const loadData = async () => {
  // TODO: 调用API获取统计数据
  console.log('Loading advertising dashboard data...')
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.advertising-dashboard {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
}

.stat-icon.popups {
  background-color: #67c23a;
}

.stat-icon.views {
  background-color: #e6a23c;
}

.stat-icon.clicks {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.modules-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modules-grid {
  margin-top: 16px;
}

.module-item {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
  height: 140px;
  display: flex;
  flex-direction: column;
}

.module-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.module-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-bottom: 12px;
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.module-info p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  flex: 1;
}

.module-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.stat-item.up {
  color: #67c23a;
}

.recent-activities {
  margin-bottom: 24px;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: #f5f7fa;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.activity-meta {
  font-size: 12px;
  color: #909399;
}

.activity-status {
  margin-left: 12px;
}

.hot-icon {
  color: #f56c6c;
}
</style>
