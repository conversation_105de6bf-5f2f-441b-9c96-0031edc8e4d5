<template>
  <div class="articles-management">
    <div class="page-header">
      <h1>健康文章管理</h1>
      <p>管理心理健康科普文章、专业资讯和用户互动内容</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="文章列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建文章
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedArticles.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedArticles.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
            <el-button @click="exportArticles">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文章标题"
              style="width: 200px"
              clearable
              @change="loadArticles"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="categoryFilter" placeholder="分类筛选" style="width: 120px" @change="loadArticles">
              <el-option label="全部分类" value="" />
              <el-option label="心理科普" value="psychology" />
              <el-option label="情绪管理" value="emotion" />
              <el-option label="压力缓解" value="stress" />
              <el-option label="人际关系" value="relationship" />
              <el-option label="学习心理" value="learning" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadArticles">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>

        <!-- 文章列表 -->
        <el-table
          :data="articles"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="文章信息" min-width="400">
            <template #default="{ row }">
              <div class="article-info">
                <div class="article-cover">
                  <img :src="row.cover_image || '/default-article.png'" :alt="row.title" />
                </div>
                <div class="article-details">
                  <div class="article-title">{{ row.title }}</div>
                  <div class="article-summary">{{ row.summary }}</div>
                  <div class="article-meta">
                    <el-tag size="small" :type="getCategoryTagType(row.category)">
                      {{ getCategoryName(row.category) }}
                    </el-tag>
                    <span class="reading-time">{{ row.reading_time }}分钟阅读</span>
                    <span class="word-count">{{ row.word_count }}字</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="作者信息" width="150">
            <template #default="{ row }">
              <div class="author-info">
                <div class="author-avatar">
                  <img :src="row.author.avatar || '/default-avatar.png'" :alt="row.author.name" />
                </div>
                <div class="author-details">
                  <div class="author-name">{{ row.author.name }}</div>
                  <div class="author-title">{{ row.author.title }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="互动数据" width="120">
            <template #default="{ row }">
              <div class="interaction-stats">
                <div class="stat-item">
                  <span class="stat-label">阅读:</span>
                  <span class="stat-value">{{ row.view_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">点赞:</span>
                  <span class="stat-value like-count">{{ row.like_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">收藏:</span>
                  <span class="stat-value favorite-count">{{ row.favorite_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">评论:</span>
                  <span class="stat-value comment-count">{{ row.comment_count }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="质量评分" width="100">
            <template #default="{ row }">
              <div class="quality-score">
                <div class="score-value" :class="getScoreClass(row.quality_score)">
                  {{ row.quality_score }}
                </div>
                <div class="score-label">质量分</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="发布时间" width="120">
            <template #default="{ row }">
              <span class="publish-time">{{ formatTime(row.published_at || row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="viewArticle(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="link" size="small" @click="editArticle(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="link" size="small" @click="viewComments(row)">
                <el-icon><ChatDotRound /></el-icon>
                评论
              </el-button>
              <el-button
                type="link"
                size="small"
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="toggleArticleStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '归档' : '发布' }}
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deleteArticle(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadArticles"
            @current-change="loadArticles"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="分类分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">文章分类分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="阅读量趋势">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">阅读量趋势图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="用户互动分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">用户互动分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑文章对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingArticle ? '编辑文章' : '新建文章'"
      width="1000px"
      @close="resetForm"
    >
      <el-form :model="articleForm" :rules="articleRules" ref="articleFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="文章标题" prop="title">
              <el-input v-model="articleForm.title" placeholder="请输入文章标题" />
            </el-form-item>
            <el-form-item label="文章摘要" prop="summary">
              <el-input v-model="articleForm.summary" type="textarea" rows="3" placeholder="请输入文章摘要" />
            </el-form-item>
            <el-form-item label="文章分类" prop="category">
              <el-select v-model="articleForm.category" placeholder="请选择文章分类">
                <el-option label="心理科普" value="psychology" />
                <el-option label="情绪管理" value="emotion" />
                <el-option label="压力缓解" value="stress" />
                <el-option label="人际关系" value="relationship" />
                <el-option label="学习心理" value="learning" />
                <el-option label="职场心理" value="workplace" />
              </el-select>
            </el-form-item>
            <el-form-item label="阅读时长" prop="reading_time">
              <el-input-number v-model="articleForm.reading_time" :min="1" :max="60" placeholder="分钟" />
            </el-form-item>
            <el-form-item label="文章标签" prop="tags">
              <el-select 
                v-model="articleForm.tags" 
                multiple 
                filterable 
                allow-create 
                placeholder="请选择或输入标签"
              >
                <el-option label="热门" value="热门" />
                <el-option label="推荐" value="推荐" />
                <el-option label="专业" value="专业" />
                <el-option label="实用" value="实用" />
                <el-option label="科普" value="科普" />
              </el-select>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="作者信息" name="author">
            <el-form-item label="选择作者" prop="author_id">
              <el-select v-model="articleForm.author_id" placeholder="请选择作者" filterable>
                <el-option 
                  v-for="author in authors" 
                  :key="author.id" 
                  :label="author.name" 
                  :value="author.id"
                >
                  <div class="author-option">
                    <span class="author-name">{{ author.name }}</span>
                    <span class="author-title">{{ author.title }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="作者简介">
              <el-input v-model="articleForm.author_bio" type="textarea" rows="3" placeholder="作者简介" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="文章封面" name="cover">
            <el-form-item label="封面图片">
              <div class="cover-upload">
                <el-upload
                  class="upload-demo"
                  :show-file-list="false"
                  :before-upload="beforeCoverUpload"
                  :http-request="uploadCover"
                  accept="image/*"
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    上传封面图片
                  </el-button>
                </el-upload>
                <div v-if="articleForm.cover_image" class="cover-preview">
                  <img :src="articleForm.cover_image" alt="文章封面" />
                  <el-button type="text" size="small" class="remove-btn" @click="removeCover">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="封面说明">
              <el-input v-model="articleForm.cover_description" placeholder="封面图片说明" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="文章内容" name="content">
            <el-form-item label="文章正文" prop="content">
              <div class="content-editor">
                <!-- TODO: 集成富文本编辑器 -->
                <el-input 
                  v-model="articleForm.content" 
                  type="textarea" 
                  rows="20" 
                  placeholder="请输入文章内容，支持Markdown格式"
                />
              </div>
            </el-form-item>
            <el-form-item label="SEO关键词">
              <el-input v-model="articleForm.seo_keywords" placeholder="用逗号分隔多个关键词" />
            </el-form-item>
            <el-form-item label="SEO描述">
              <el-input v-model="articleForm.seo_description" type="textarea" rows="2" placeholder="SEO描述" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveArticle" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 文章详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="文章详情"
      width="900px"
    >
      <div class="article-detail" v-if="viewingArticle">
        <div class="detail-header">
          <div class="article-cover-large">
            <img :src="viewingArticle.cover_image || '/default-article.png'" :alt="viewingArticle.title" />
          </div>
          <div class="article-info-large">
            <h2 class="article-title-large">{{ viewingArticle.title }}</h2>
            <div class="article-meta-large">
              <el-tag :type="getCategoryTagType(viewingArticle.category)">
                {{ getCategoryName(viewingArticle.category) }}
              </el-tag>
              <span class="reading-time-large">{{ viewingArticle.reading_time }}分钟阅读</span>
              <span class="word-count-large">{{ viewingArticle.word_count }}字</span>
            </div>
            <div class="article-stats-large">
              <span class="stat-item">{{ viewingArticle.view_count }}次阅读</span>
              <span class="stat-item">{{ viewingArticle.like_count }}个赞</span>
              <span class="stat-item">{{ viewingArticle.favorite_count }}次收藏</span>
              <span class="stat-item">{{ viewingArticle.comment_count }}条评论</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>文章摘要</h3>
          <div class="summary-detail">{{ viewingArticle.summary }}</div>
        </div>
        
        <div class="detail-section">
          <h3>作者信息</h3>
          <div class="author-detail">
            <div class="author-avatar-large">
              <img :src="viewingArticle.author.avatar || '/default-avatar.png'" :alt="viewingArticle.author.name" />
            </div>
            <div class="author-info-large">
              <div class="author-name-large">{{ viewingArticle.author.name }}</div>
              <div class="author-title-large">{{ viewingArticle.author.title }}</div>
              <div class="author-bio">{{ viewingArticle.author_bio }}</div>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>文章内容</h3>
          <div class="content-detail" v-html="viewingArticle.content"></div>
        </div>
        
        <div class="detail-section" v-if="viewingArticle.tags && viewingArticle.tags.length">
          <h3>文章标签</h3>
          <div class="tags-detail">
            <el-tag v-for="tag in viewingArticle.tags" :key="tag" size="small" class="tag-detail">
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, Download, ChatDotRound
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedArticles = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const editingArticle = ref(null)
const viewingArticle = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const categoryFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 文章列表
const articles = ref([
  {
    id: '1',
    title: '如何应对考试焦虑：心理学专家的实用建议',
    summary: '考试焦虑是学生群体中常见的心理问题，本文从心理学角度分析考试焦虑的成因，并提供科学有效的应对策略',
    category: 'stress',
    reading_time: 8,
    word_count: 2500,
    cover_image: '',
    author: {
      id: '1',
      name: '张心理师',
      title: '临床心理学博士',
      avatar: ''
    },
    author_bio: '从事心理咨询工作10年，专注于青少年心理健康',
    content: '考试焦虑是一种常见的心理现象...',
    view_count: 1234,
    like_count: 89,
    favorite_count: 156,
    comment_count: 23,
    quality_score: 8.5,
    tags: ['热门', '实用'],
    seo_keywords: '考试焦虑,心理健康,应对策略',
    seo_description: '专业心理学专家教你如何科学应对考试焦虑',
    status: 'published',
    published_at: '2024-01-15 10:30:00',
    created_at: '2024-01-14 15:20:00'
  }
])

// 作者列表
const authors = ref([
  { id: '1', name: '张心理师', title: '临床心理学博士' },
  { id: '2', name: '李咨询师', title: '心理咨询师' },
  { id: '3', name: '王教授', title: '心理学教授' }
])

// 表单数据
const articleForm = reactive({
  title: '',
  summary: '',
  category: '',
  reading_time: 5,
  cover_image: '',
  cover_description: '',
  author_id: '',
  author_bio: '',
  content: '',
  tags: [],
  seo_keywords: '',
  seo_description: ''
})

// 表单验证规则
const articleRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入文章摘要', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择文章分类', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ],
  author_id: [
    { required: true, message: '请选择作者', trigger: 'change' }
  ]
}

// 方法
const loadArticles = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取文章列表
    console.log('Loading articles...')
  } catch (error) {
    ElMessage.error('加载文章列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedArticles.value = selection
}

const getCategoryName = (category) => {
  const categories = {
    psychology: '心理科普',
    emotion: '情绪管理',
    stress: '压力缓解',
    relationship: '人际关系',
    learning: '学习心理',
    workplace: '职场心理'
  }
  return categories[category] || category
}

const getCategoryTagType = (category) => {
  const types = {
    psychology: 'primary',
    emotion: 'success',
    stress: 'warning',
    relationship: 'danger',
    learning: 'info'
  }
  return types[category] || ''
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const getScoreClass = (score) => {
  if (score >= 8) return 'excellent'
  if (score >= 6) return 'good'
  if (score >= 4) return 'average'
  return 'poor'
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewArticle = (article) => {
  viewingArticle.value = article
  showViewDialog.value = true
}

const editArticle = (article) => {
  editingArticle.value = article
  Object.assign(articleForm, article)
  showCreateDialog.value = true
}

const viewComments = (article) => {
  // TODO: 跳转到评论管理页面
  console.log('Viewing comments for article:', article)
}

const resetForm = () => {
  editingArticle.value = null
  Object.assign(articleForm, {
    title: '',
    summary: '',
    category: '',
    reading_time: 5,
    cover_image: '',
    cover_description: '',
    author_id: '',
    author_bio: '',
    content: '',
    tags: [],
    seo_keywords: '',
    seo_description: ''
  })
  formActiveTab.value = 'basic'
}

const beforeCoverUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const uploadCover = async (options) => {
  // TODO: 实现封面上传逻辑
  console.log('Uploading cover...', options.file)
  // 模拟上传成功
  articleForm.cover_image = URL.createObjectURL(options.file)
  ElMessage.success('封面上传成功')
}

const removeCover = () => {
  articleForm.cover_image = ''
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', articleForm)
    showCreateDialog.value = false
    ElMessage.success('文章已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveArticle = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing article...', articleForm)
    showCreateDialog.value = false
    ElMessage.success(editingArticle.value ? '文章更新成功' : '文章创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleArticleStatus = async (article) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling article status...', article)
}

const deleteArticle = async (article) => {
  try {
    await ElMessageBox.confirm('确定要删除这篇文章吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting article...', article)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing articles...', selectedArticles.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving articles...', selectedArticles.value)
}

const exportArticles = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting articles...')
}

onMounted(() => {
  loadArticles()
})
</script>

<style scoped>
.articles-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.article-info {
  display: flex;
  gap: 12px;
  padding: 8px 0;
}

.article-cover {
  width: 100px;
  height: 75px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.article-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-details {
  flex: 1;
}

.article-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-summary {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.article-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.reading-time {
  font-size: 11px;
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.word-count {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-details {
  flex: 1;
}

.author-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.author-title {
  font-size: 11px;
  color: #909399;
}

.interaction-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.like-count {
  color: #f56c6c !important;
}

.favorite-count {
  color: #e6a23c !important;
}

.comment-count {
  color: #409eff !important;
}

.quality-score {
  text-align: center;
}

.score-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.score-value.excellent {
  color: #67c23a;
}

.score-value.good {
  color: #409eff;
}

.score-value.average {
  color: #e6a23c;
}

.score-value.poor {
  color: #f56c6c;
}

.score-label {
  font-size: 11px;
  color: #909399;
}

.publish-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.author-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-name {
  font-weight: 500;
  color: #303133;
}

.author-title {
  font-size: 12px;
  color: #909399;
}

.cover-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cover-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.cover-preview img {
  width: 200px;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
}

.remove-btn {
  color: #f56c6c;
}

.content-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.article-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.article-cover-large {
  width: 200px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.article-cover-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-info-large {
  flex: 1;
}

.article-title-large {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.article-meta-large {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.reading-time-large {
  font-size: 12px;
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.word-count-large {
  font-size: 12px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.article-stats-large {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #606266;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.summary-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
}

.author-detail {
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.author-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.author-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info-large {
  flex: 1;
}

.author-name-large {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.author-title-large {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.author-bio {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

.content-detail {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.8;
  border-left: 4px solid #409eff;
}

.tags-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-detail {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
