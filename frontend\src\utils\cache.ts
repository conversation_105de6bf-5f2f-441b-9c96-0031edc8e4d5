/**
 * 前端缓存管理工具
 */

interface CacheItem<T> {
  data: T
  timestamp: number
  expiry: number
}

class CacheManager {
  private cache = new Map<string, CacheItem<any>>()
  private defaultTTL = 5 * 60 * 1000 // 5分钟默认过期时间

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 过期时间（毫秒），默认5分钟
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    const now = Date.now()
    this.cache.set(key, {
      data,
      timestamp: now,
      expiry: now + ttl
    })
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    const now = Date.now()
    if (now > item.expiry) {
      this.cache.delete(key)
      return null
    }

    return item.data as T
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { total: number; expired: number; size: number } {
    const now = Date.now()
    let expired = 0
    let totalSize = 0

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        expired++
      }
      // 估算数据大小
      totalSize += JSON.stringify(item.data).length
    }

    return {
      total: this.cache.size,
      expired,
      size: totalSize
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param key 缓存键
   */
  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false
    
    const now = Date.now()
    if (now > item.expiry) {
      this.cache.delete(key)
      return false
    }
    
    return true
  }
}

// 创建全局缓存实例
export const cache = new CacheManager()

// 定期清理过期缓存
setInterval(() => {
  cache.cleanup()
}, 60000) // 每分钟清理一次

/**
 * 缓存装饰器工厂
 * @param ttl 过期时间（毫秒）
 * @param keyGenerator 缓存键生成函数
 */
export function cached<T extends (...args: any[]) => Promise<any>>(
  ttl: number = 5 * 60 * 1000,
  keyGenerator?: (...args: Parameters<T>) => string
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: Parameters<T>) {
      // 生成缓存键
      const cacheKey = keyGenerator 
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyKey}:${JSON.stringify(args)}`

      // 尝试从缓存获取
      const cachedResult = cache.get<Awaited<ReturnType<T>>>(cacheKey)
      if (cachedResult !== null) {
        return cachedResult
      }

      // 执行原方法
      const result = await originalMethod.apply(this, args)
      
      // 缓存结果
      cache.set(cacheKey, result, ttl)
      
      return result
    }

    return descriptor
  }
}

/**
 * 本地存储缓存（持久化）
 */
export class PersistentCache {
  private prefix: string

  constructor(prefix: string = 'wiscude_cache_') {
    this.prefix = prefix
  }

  /**
   * 设置持久化缓存
   */
  set<T>(key: string, data: T, ttl: number = 24 * 60 * 60 * 1000): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    }

    try {
      localStorage.setItem(this.prefix + key, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to set persistent cache:', error)
    }
  }

  /**
   * 获取持久化缓存
   */
  get<T>(key: string): T | null {
    try {
      const itemStr = localStorage.getItem(this.prefix + key)
      if (!itemStr) return null

      const item: CacheItem<T> = JSON.parse(itemStr)
      const now = Date.now()

      if (now > item.expiry) {
        localStorage.removeItem(this.prefix + key)
        return null
      }

      return item.data
    } catch (error) {
      console.warn('Failed to get persistent cache:', error)
      return null
    }
  }

  /**
   * 删除持久化缓存
   */
  delete(key: string): void {
    localStorage.removeItem(this.prefix + key)
  }

  /**
   * 清空所有持久化缓存
   */
  clear(): void {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key)
      }
    })
  }
}

// 创建持久化缓存实例
export const persistentCache = new PersistentCache()

export default cache
