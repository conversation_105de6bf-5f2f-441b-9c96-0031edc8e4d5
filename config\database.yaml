# WisCude 数据库配置文件
# 统一管理数据库相关配置

# 主数据库配置 (PostgreSQL)
database:
  # 连接配置
  url: "postgresql://postgres:123456@localhost:5432/wiscude_admin"
  echo: false
  
  # 连接池配置
  pool:
    size: 10
    max_overflow: 20
    timeout: 30
    recycle: 3600
    pre_ping: true
  
  # 连接参数
  connect_args:
    connect_timeout: 10
    application_name: "WisCude-Backend"
    sslmode: "prefer"
  
  # 重试配置
  retry:
    attempts: 3
    delay_min: 4
    delay_max: 10
    exponential_base: 2

# Android SQLite 数据库配置
android_database:
  path: "../Wiscude/app/databases/wiscude.db"
  backup_path: "backups/android_db_backup.db"
  
  # 同步配置
  sync:
    enabled: true
    interval_minutes: 30
    batch_size: 1000
    tables:
      - "users"
      - "study_sessions"
      - "daily_checkins"
      - "community_posts"
      - "courses"
      - "ai_learning_records"
  
  # 备份配置
  backup:
    enabled: true
    interval_hours: 24
    keep_days: 7
    compress: true

# 数据库迁移配置
migration:
  directory: "migrations"
  auto_upgrade: false
  backup_before_upgrade: true
  
# 测试数据库配置
test_database:
  url: "sqlite:///./test_wiscude.db"
  echo: true
  create_tables: true
  drop_tables_after_test: true

# 数据库监控配置
monitoring:
  slow_query_threshold: 1.0  # 秒
  log_slow_queries: true
  track_connection_pool: true
  health_check_interval: 30

# 数据库安全配置
security:
  encrypt_sensitive_data: true
  audit_log_enabled: true
  max_connections_per_user: 10
  idle_timeout_minutes: 30

# 环境特定配置
environments:
  development:
    database:
      echo: true
      pool:
        size: 5
        max_overflow: 10
    
  testing:
    database:
      url: "sqlite:///./test_wiscude.db"
      echo: false
      pool:
        size: 1
        max_overflow: 0
    
  production:
    database:
      echo: false
      pool:
        size: 20
        max_overflow: 40
      connect_args:
        sslmode: "require"
        sslcert: "/path/to/client-cert.pem"
        sslkey: "/path/to/client-key.pem"
        sslrootcert: "/path/to/ca-cert.pem"
