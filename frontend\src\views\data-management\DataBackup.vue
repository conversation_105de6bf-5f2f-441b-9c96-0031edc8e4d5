<template>
  <div class="data-backup-management">
    <div class="page-header">
      <h1>数据备份与清理</h1>
      <p>管理数据备份、清理和恢复操作</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="createBackupNow">
          <el-icon><FolderAdd /></el-icon>
          立即备份
        </el-button>
        <el-button type="success" @click="showRestoreDialog = true">
          <el-icon><RefreshLeft /></el-icon>
          恢复数据
        </el-button>
        <el-button type="warning" @click="cleanupData">
          <el-icon><Delete /></el-icon>
          清理数据
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button @click="exportBackupList">
          <el-icon><Download /></el-icon>
          导出列表
        </el-button>
      </div>
    </div>

    <!-- 备份配置 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>备份配置</span>
          <el-button type="text" @click="saveBackupConfig">保存配置</el-button>
        </div>
      </template>
      
      <el-form :model="backupConfig" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="自动备份">
              <el-switch v-model="backupConfig.autoBackup" />
            </el-form-item>
            <el-form-item label="备份频率">
              <el-select v-model="backupConfig.frequency" style="width: 100%">
                <el-option label="每日" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
              </el-select>
            </el-form-item>
            <el-form-item label="保留天数">
              <el-input-number v-model="backupConfig.retentionDays" :min="1" :max="365" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备份内容">
              <el-checkbox-group v-model="backupConfig.content">
                <el-checkbox label="database">数据库</el-checkbox>
                <el-checkbox label="files">文件系统</el-checkbox>
                <el-checkbox label="logs">日志文件</el-checkbox>
                <el-checkbox label="config">配置文件</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="备份路径">
              <el-input v-model="backupConfig.path" placeholder="备份存储路径" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 备份列表 -->
    <el-card class="backup-list-card">
      <template #header>
        <div class="card-header">
          <span>备份历史</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索备份"
              style="width: 200px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </template>

      <el-table :data="backupList" v-loading="loading" stripe style="width: 100%">
        <el-table-column label="备份名称" min-width="200">
          <template #default="{ row }">
            <div class="backup-name-info">
              <div class="backup-name">{{ row.name }}</div>
              <div class="backup-description">{{ row.description }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备份类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="文件大小" width="120">
          <template #default="{ row }">
            <span class="file-size">{{ row.size }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备份状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="150">
          <template #default="{ row }">
            <span class="create-time">{{ formatTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="downloadBackup(row)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button type="text" size="small" @click="restoreBackup(row)">
              <el-icon><RefreshLeft /></el-icon>
              恢复
            </el-button>
            <el-button type="text" size="small" @click="viewBackupDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button type="text" size="small" class="danger" @click="deleteBackup(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadBackupList"
          @current-change="loadBackupList"
        />
      </div>
    </el-card>

    <!-- 恢复数据对话框 -->
    <el-dialog
      v-model="showRestoreDialog"
      title="恢复数据"
      width="600px"
    >
      <el-form :model="restoreForm" label-width="100px">
        <el-form-item label="选择备份">
          <el-select v-model="restoreForm.backupId" placeholder="请选择要恢复的备份">
            <el-option 
              v-for="backup in backupList" 
              :key="backup.id" 
              :label="backup.name" 
              :value="backup.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="恢复内容">
          <el-checkbox-group v-model="restoreForm.content">
            <el-checkbox label="database">数据库</el-checkbox>
            <el-checkbox label="files">文件系统</el-checkbox>
            <el-checkbox label="config">配置文件</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="恢复模式">
          <el-radio-group v-model="restoreForm.mode">
            <el-radio label="full">完全恢复</el-radio>
            <el-radio label="incremental">增量恢复</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRestoreDialog = false">取消</el-button>
        <el-button type="primary" @click="executeRestore" :loading="restoring">开始恢复</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  FolderAdd, RefreshLeft, Delete, Download, Search, View
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const restoring = ref(false)
const showRestoreDialog = ref(false)
const searchKeyword = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 备份配置
const backupConfig = reactive({
  autoBackup: true,
  frequency: 'weekly',
  retentionDays: 30,
  content: ['database', 'files'],
  path: '/data/backups'
})

// 备份列表
const backupList = ref([
  {
    id: '1',
    name: '系统完整备份_20240120',
    description: '包含数据库和文件系统的完整备份',
    type: 'full',
    size: '2.5GB',
    status: 'completed',
    created_at: '2024-01-20 14:30:00'
  },
  {
    id: '2',
    name: '数据库备份_20240119',
    description: '仅包含数据库的增量备份',
    type: 'database',
    size: '856MB',
    status: 'completed',
    created_at: '2024-01-19 10:15:00'
  }
])

// 恢复表单
const restoreForm = reactive({
  backupId: '',
  content: ['database'],
  mode: 'full'
})

// 方法
const loadBackupList = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取备份列表
    console.log('Loading backup list...')
  } catch (error) {
    ElMessage.error('加载备份列表失败')
  } finally {
    loading.value = false
  }
}

const getTypeName = (type) => {
  const types = {
    full: '完整备份',
    database: '数据库',
    files: '文件系统',
    incremental: '增量备份'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    full: 'primary',
    database: 'success',
    files: 'warning',
    incremental: 'info'
  }
  return types[type] || ''
}

const getStatusName = (status) => {
  const statuses = {
    completed: '已完成',
    running: '进行中',
    failed: '失败',
    cancelled: '已取消'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    completed: 'success',
    running: 'primary',
    failed: 'danger',
    cancelled: 'warning'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const createBackupNow = async () => {
  try {
    await ElMessageBox.confirm('确定要立即创建备份吗？', '确认备份', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    // TODO: 调用API创建备份
    ElMessage.success('备份任务已启动')
  } catch {
    // 用户取消
  }
}

const saveBackupConfig = async () => {
  try {
    // TODO: 调用API保存备份配置
    console.log('Saving backup config:', backupConfig)
    ElMessage.success('备份配置已保存')
  } catch (error) {
    ElMessage.error('保存配置失败')
  }
}

const cleanupData = async () => {
  try {
    await ElMessageBox.confirm('确定要清理过期数据吗？此操作不可恢复！', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 调用API清理数据
    ElMessage.success('数据清理完成')
  } catch {
    // 用户取消
  }
}

const downloadBackup = (backup) => {
  // TODO: 实现备份下载
  console.log('Downloading backup:', backup)
}

const restoreBackup = (backup) => {
  restoreForm.backupId = backup.id
  showRestoreDialog.value = true
}

const viewBackupDetails = (backup) => {
  // TODO: 查看备份详情
  console.log('Viewing backup details:', backup)
}

const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm('确定要删除这个备份吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 调用API删除备份
    ElMessage.success('备份已删除')
  } catch {
    // 用户取消
  }
}

const executeRestore = async () => {
  restoring.value = true
  try {
    // TODO: 调用API执行恢复
    console.log('Executing restore:', restoreForm)
    showRestoreDialog.value = false
    ElMessage.success('数据恢复已启动')
  } catch (error) {
    ElMessage.error('恢复失败')
  } finally {
    restoring.value = false
  }
}

const exportBackupList = () => {
  // TODO: 导出备份列表
  console.log('Exporting backup list...')
}

onMounted(() => {
  loadBackupList()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';
@import '@/styles/components.scss';

.data-backup-management {
  @extend .page-container;

  /* 配置卡片样式 */
  .config-card {
    @extend .card-modern;
    margin-bottom: var(--spacing-6);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .el-form {
      @extend .enhanced-form;
    }
  }

  /* 备份列表卡片样式 */
  .backup-list-card {
    @extend .card-modern;
    margin-bottom: var(--spacing-6);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        align-items: center;

        .el-input {
          .el-input__wrapper {
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-fast);

            &:hover {
              box-shadow: var(--shadow-md);
            }

            &.is-focus {
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }
        }
      }
    }

    .el-table {
      @extend .enhanced-table;

      .backup-name-info {
        padding: var(--spacing-2) 0;

        .backup-name {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
          margin-bottom: var(--spacing-1);
          line-height: var(--line-height-tight);
        }

        .backup-description {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
          line-height: var(--line-height-normal);
        }
      }

      .file-size {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        background-color: var(--bg-tertiary);
        padding: var(--spacing-1) var(--spacing-2);
        border-radius: var(--radius-md);
      }

      .create-time {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
        font-family: var(--font-family-mono);
      }

      .el-button {
        &.danger {
          color: var(--error-color);

          &:hover {
            color: var(--error-light);
            background-color: rgba(239, 68, 68, 0.1);
          }
        }
      }

      .el-tag {
        @extend .enhanced-tags;
      }
    }

    .pagination-wrapper {
      @extend .pagination-wrapper;
    }
  }

  /* 恢复对话框样式 */
  .el-dialog {
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);

    .el-dialog__header {
      background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
      border-bottom: 1px solid var(--border-light);
      border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
      padding: var(--spacing-6);

      .el-dialog__title {
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        font-size: var(--font-size-lg);
      }
    }

    .el-dialog__body {
      padding: var(--spacing-6);

      .el-form {
        @extend .enhanced-form;
      }
    }

    .el-dialog__footer {
      border-top: 1px solid var(--border-light);
      padding: var(--spacing-5) var(--spacing-6);
      background-color: var(--bg-secondary);
      border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);

      .el-button {
        margin-left: var(--spacing-3);
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('md') {
  .data-backup-management {
    .toolbar {
      flex-direction: row;
    }

    .config-card,
    .backup-list-card {
      margin-bottom: var(--spacing-8);
    }
  }
}

@media (max-width: 767px) {
  .data-backup-management {
    .toolbar {
      flex-direction: column;
      gap: var(--spacing-4);
      align-items: stretch;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
      }
    }

    .backup-list-card {
      .header-actions {
        flex-direction: column;
        align-items: stretch;

        .el-input {
          margin-bottom: var(--spacing-3);
        }
      }
    }
  }
}
</style>
