"""
WisCude 后台管理系统 - 数据库连接配置
"""
from sqlalchemy import create_engine, MetaData, text, event
from sqlalchemy.engine import Engine
from sqlalchemy.pool import QueuePool, StaticPool
try:
    from sqlalchemy.ext.declarative import declarative_base
except ImportError:
    from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker, Session
import sqlite3
import asyncio
import time
from typing import Generator, AsyncGenerator, Optional
from contextlib import contextmanager
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import psycopg2
from psycopg2 import OperationalError as PgOperationalError
try:
    from .config import settings
except ImportError:
    # 简化配置用于调试
    class SimpleSettings:
        DATABASE_URL = "sqlite:///./test.db"
        DATABASE_ECHO = False
        ANDROID_DB_PATH = "../Wiscude/app/databases/wiscude.db"
    settings = SimpleSettings()
import logging

logger = logging.getLogger(__name__)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()

class EnhancedDatabaseManager:
    """增强的数据库管理器 - 提供连接池、健康检查、重试机制"""

    def __init__(self):
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        self._is_initialized = False
        self._last_health_check = 0
        self._health_check_interval = 30  # 30秒检查一次

    def _create_engine(self) -> Engine:
        """创建数据库引擎，包含连接池配置"""
        if settings.DATABASE_URL.startswith("postgresql"):
            # PostgreSQL 配置 - 优化连接池
            return create_engine(
                settings.DATABASE_URL,
                echo=settings.DATABASE_ECHO,
                poolclass=QueuePool,
                pool_size=10,  # 连接池大小
                max_overflow=20,  # 最大溢出连接数
                pool_pre_ping=True,  # 连接前ping检查
                pool_recycle=3600,  # 连接回收时间（1小时）
                pool_timeout=30,  # 获取连接超时时间
                connect_args={
                    "connect_timeout": 10,
                    "application_name": "WisCude-Backend"
                }
            )
        else:
            # SQLite 配置
            return create_engine(
                settings.DATABASE_URL,
                echo=settings.DATABASE_ECHO,
                poolclass=StaticPool,
                connect_args={"check_same_thread": False}
            )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((PgOperationalError, Exception))
    )
    def init_database(self) -> bool:
        """初始化数据库连接，包含重试机制"""
        try:
            logger.info("正在初始化数据库连接...")

            # 创建引擎
            self.engine = self._create_engine()

            # 测试连接
            with self.engine.connect() as conn:
                if settings.DATABASE_URL.startswith("postgresql"):
                    conn.execute(text("SELECT 1"))
                else:
                    conn.execute(text("SELECT 1"))
                logger.info("数据库连接测试成功")

            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            # 添加连接事件监听器
            self._setup_connection_events()

            self._is_initialized = True
            logger.info("数据库初始化完成")
            return True

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    def _setup_connection_events(self):
        """设置连接事件监听器"""
        @event.listens_for(self.engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            if "sqlite" in str(self.engine.url):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.close()

        @event.listens_for(self.engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            logger.debug("数据库连接已检出")

        @event.listens_for(self.engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            logger.debug("数据库连接已归还")

    def health_check(self) -> dict:
        """数据库健康检查"""
        current_time = time.time()

        # 如果距离上次检查时间不足间隔，返回缓存结果
        if (current_time - self._last_health_check) < self._health_check_interval:
            return {"status": "healthy", "cached": True}

        try:
            if not self._is_initialized:
                return {"status": "not_initialized", "error": "Database not initialized"}

            # 执行健康检查查询
            with self.engine.connect() as conn:
                start_time = time.time()
                conn.execute(text("SELECT 1"))
                response_time = (time.time() - start_time) * 1000  # 毫秒

                # 获取连接池状态（兼容不同类型的连接池）
                pool_status = {}
                try:
                    # 尝试获取连接池信息（适用于PostgreSQL等）
                    if hasattr(self.engine.pool, 'size'):
                        pool_status = {
                            "size": self.engine.pool.size(),
                            "checked_in": self.engine.pool.checkedin(),
                            "checked_out": self.engine.pool.checkedout(),
                            "overflow": self.engine.pool.overflow(),
                        }
                    else:
                        # SQLite等使用StaticPool的情况
                        pool_status = {
                            "type": type(self.engine.pool).__name__,
                            "status": "available"
                        }
                except Exception as pool_error:
                    pool_status = {
                        "error": f"无法获取连接池状态: {str(pool_error)}",
                        "type": type(self.engine.pool).__name__
                    }

                self._last_health_check = current_time

                return {
                    "status": "healthy",
                    "response_time_ms": round(response_time, 2),
                    "pool_status": pool_status,
                    "database_url": str(self.engine.url).split('@')[0] + '@***',  # 隐藏敏感信息
                    "timestamp": current_time
                }

        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": current_time
            }

    def get_db(self) -> Generator:
        """获取数据库会话"""
        if not self._is_initialized:
            self.init_database()

        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

    def create_tables(self):
        """创建所有表"""
        if not self._is_initialized:
            self.init_database()
        Base.metadata.create_all(bind=self.engine)

    def drop_tables(self):
        """删除所有表"""
        if not self._is_initialized:
            self.init_database()
        Base.metadata.drop_all(bind=self.engine)

# 创建全局增强数据库管理器实例
enhanced_db_manager = EnhancedDatabaseManager()

# 保持向后兼容性
engine = None
SessionLocal = None

def get_engine():
    """获取数据库引擎"""
    if not enhanced_db_manager._is_initialized:
        enhanced_db_manager.init_database()
    return enhanced_db_manager.engine

def get_session_local():
    """获取会话工厂"""
    if not enhanced_db_manager._is_initialized:
        enhanced_db_manager.init_database()
    return enhanced_db_manager.SessionLocal

def get_db() -> Generator:
    """获取数据库会话 - 向后兼容"""
    if not enhanced_db_manager._is_initialized:
        enhanced_db_manager.init_database()

    db = enhanced_db_manager.SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """创建所有表 - 向后兼容"""
    enhanced_db_manager.create_tables()

def drop_tables():
    """删除所有表 - 向后兼容"""
    enhanced_db_manager.drop_tables()

class AndroidDatabaseManager:
    """Android SQLite 数据库管理器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._connection = None
    
    def connect(self) -> sqlite3.Connection:
        """连接到Android SQLite数据库"""
        try:
            self._connection = sqlite3.connect(self.db_path)
            self._connection.row_factory = sqlite3.Row  # 启用列名访问
            logger.info(f"成功连接到Android数据库: {self.db_path}")
            return self._connection
        except sqlite3.Error as e:
            logger.error(f"连接Android数据库失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None
            logger.info("Android数据库连接已关闭")
    
    def execute_query(self, query: str, params: tuple = None) -> list:
        """执行查询"""
        if not self._connection:
            self.connect()
        
        try:
            cursor = self._connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
        except sqlite3.Error as e:
            logger.error(f"执行查询失败: {e}")
            raise
    
    def get_table_names(self) -> list:
        """获取所有表名"""
        query = "SELECT name FROM sqlite_master WHERE type='table'"
        results = self.execute_query(query)
        return [row['name'] for row in results]
    
    def get_table_schema(self, table_name: str) -> list:
        """获取表结构"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)
    
    def get_table_data(self, table_name: str, limit: int = None) -> list:
        """获取表数据"""
        query = f"SELECT * FROM {table_name}"
        if limit:
            query += f" LIMIT {limit}"
        return self.execute_query(query)
    
    def get_row_count(self, table_name: str) -> int:
        """获取表行数"""
        query = f"SELECT COUNT(*) as count FROM {table_name}"
        result = self.execute_query(query)
        return result[0]['count'] if result else 0
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.disconnect()

class DatabaseManager:
    """原有数据库管理器 - 保持向后兼容"""

    @staticmethod
    def check_connection() -> bool:
        """检查数据库连接"""
        try:
            health_status = enhanced_db_manager.health_check()
            return health_status["status"] == "healthy"
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            return False

    @staticmethod
    def get_android_db_manager() -> AndroidDatabaseManager:
        """获取Android数据库管理器"""
        return AndroidDatabaseManager(settings.ANDROID_DB_PATH)

    @staticmethod
    def init_database():
        """初始化数据库"""
        try:
            enhanced_db_manager.init_database()
            enhanced_db_manager.create_tables()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

# 创建全局数据库管理器实例（向后兼容）
db_manager = DatabaseManager()
