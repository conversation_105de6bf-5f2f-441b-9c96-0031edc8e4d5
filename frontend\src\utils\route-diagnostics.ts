// 路由诊断工具
import type { Router, RouteLocationNormalized } from 'vue-router'

export class RouteDiagnostics {
  private router: Router
  private navigationHistory: Array<{
    from: string
    to: string
    timestamp: number
    success: boolean
    error?: any
  }> = []

  constructor(router: Router) {
    this.router = router
    this.setupDiagnostics()
  }

  private setupDiagnostics() {
    // 监听路由导航
    this.router.beforeEach((to, from, next) => {
      console.log(`[RouteDiagnostics] 开始导航: ${from.path} -> ${to.path}`)
      next()
    })

    this.router.afterEach((to, from, failure) => {
      const success = !failure
      const record = {
        from: from.path,
        to: to.path,
        timestamp: Date.now(),
        success,
        error: failure
      }

      this.navigationHistory.push(record)

      // 保持历史记录在合理范围内
      if (this.navigationHistory.length > 50) {
        this.navigationHistory.shift()
      }

      if (success) {
        console.log(`[RouteDiagnostics] 导航成功: ${from.path} -> ${to.path}`)
      } else {
        // 过滤掉冗余导航警告，这些不是真正的错误
        const isRedundantNavigation = failure && failure.message &&
          failure.message.includes('Avoided redundant navigation')

        if (!isRedundantNavigation) {
          console.error(`[RouteDiagnostics] 导航失败: ${from.path} -> ${to.path}`, failure)
        } else {
          console.log(`[RouteDiagnostics] 冗余导航已忽略: ${from.path} -> ${to.path}`)
        }
      }
    })

    this.router.onError((error) => {
      console.error('[RouteDiagnostics] 路由错误:', error)
    })
  }

  // 获取导航历史
  getNavigationHistory() {
    return [...this.navigationHistory]
  }

  // 获取最近的失败导航
  getRecentFailures(count = 5) {
    return this.navigationHistory
      .filter(record => !record.success)
      .slice(-count)
  }

  // 检查路由是否存在
  checkRouteExists(path: string): boolean {
    try {
      const resolved = this.router.resolve(path)
      return resolved.matched.length > 0
    } catch (error) {
      console.error(`[RouteDiagnostics] 检查路由失败: ${path}`, error)
      return false
    }
  }

  // 分析导航模式
  analyzeNavigationPattern() {
    const recent = this.navigationHistory.slice(-10)
    const failureRate = recent.filter(r => !r.success).length / recent.length
    
    console.log('[RouteDiagnostics] 导航分析:', {
      totalNavigations: this.navigationHistory.length,
      recentNavigations: recent.length,
      recentFailureRate: `${(failureRate * 100).toFixed(1)}%`,
      recentHistory: recent.map(r => `${r.from} -> ${r.to} (${r.success ? '✓' : '✗'})`)
    })

    return {
      totalNavigations: this.navigationHistory.length,
      recentFailureRate: failureRate,
      recentHistory: recent
    }
  }

  // 清除历史记录
  clearHistory() {
    this.navigationHistory = []
    console.log('[RouteDiagnostics] 历史记录已清除')
  }
}

// 创建全局实例
let diagnosticsInstance: RouteDiagnostics | null = null

export function setupRouteDiagnostics(router: Router) {
  if (!diagnosticsInstance) {
    diagnosticsInstance = new RouteDiagnostics(router)
  }
  return diagnosticsInstance
}

export function getRouteDiagnostics() {
  return diagnosticsInstance
}
