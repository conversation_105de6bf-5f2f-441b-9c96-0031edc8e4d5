<template>
  <div class="login-container" :style="backgroundStyle">
    <div class="login-box">
      <!-- 左侧装饰图片区域 -->
      <div class="login-image-section">
        <div class="image-container">
          <img
            v-if="loginSettings.decorativeImage"
            :src="loginSettings.decorativeImage"
            alt="装饰图片"
            class="decorative-image"
          />
          <div v-else class="default-decoration">
            <el-icon size="80" color="#409EFF">
              <Odometer />
            </el-icon>
            <h2>WisCude</h2>
            <p>智慧学习数据管理平台</p>
          </div>
        </div>

        <!-- 动态欢迎语 -->
        <div class="welcome-messages">
          <transition name="fade" mode="out-in">
            <div :key="currentWelcomeIndex" class="welcome-text">
              {{ welcomeMessages[currentWelcomeIndex] }}
            </div>
          </transition>
        </div>
      </div>

      <!-- 右侧登录表单区域 -->
      <div class="login-form-section">
        <div class="login-header">
          <h1>欢迎登录</h1>
          <p>WisCude 管理系统</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              size="large"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="authStore.loading"
              @click="handleLogin"
            >
              {{ authStore.loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="background">
      <div class="shape shape1"></div>
      <div class="shape shape2"></div>
      <div class="shape shape3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginRequest } from '@/types/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 登录表单
const loginForm = reactive<LoginRequest>({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

// 登录页面设置
const loginSettings = reactive({
  decorativeImage: localStorage.getItem('login-decorative-image') || '',
  backgroundImage: localStorage.getItem('login-background-image') || ''
})

// 欢迎语列表
const welcomeMessages = [
  '欢迎来到WisCude智慧学习平台，让每一次学习都充满意义 ✨',
  '知识的海洋等待着您的探索，开启今天的学习之旅吧 📚',
  '在这里，每个学习者都能找到属于自己的成长路径 🌱',
  '用智慧点亮未来，用学习改变世界 💡',
  '教育的力量在于启发，学习的快乐在于发现 🎓'
]

// 当前欢迎语索引
const currentWelcomeIndex = ref(0)
let welcomeTimer: NodeJS.Timeout | null = null

// 背景样式
const backgroundStyle = computed(() => {
  if (loginSettings.backgroundImage) {
    return {
      backgroundImage: `url(${loginSettings.backgroundImage})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  }
  return {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  }
})

// 启动欢迎语轮播
const startWelcomeCarousel = () => {
  welcomeTimer = setInterval(() => {
    currentWelcomeIndex.value = (currentWelcomeIndex.value + 1) % welcomeMessages.length
  }, 4000)
}

// 停止欢迎语轮播
const stopWelcomeCarousel = () => {
  if (welcomeTimer) {
    clearInterval(welcomeTimer)
    welcomeTimer = null
  }
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    const success = await authStore.login(loginForm)
    if (success) {
      // 登录成功，跳转到目标页面
      const redirect = route.query.redirect as string || '/'
      router.push(redirect)
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 监听localStorage变化
const handleStorageChange = (e: StorageEvent) => {
  if (e.key === 'login-decorative-image') {
    loginSettings.decorativeImage = e.newValue || ''
  } else if (e.key === 'login-background-image') {
    loginSettings.backgroundImage = e.newValue || ''
  }
}

onMounted(() => {
  startWelcomeCarousel()
  window.addEventListener('storage', handleStorageChange)
})

onUnmounted(() => {
  stopWelcomeCarousel()
  window.removeEventListener('storage', handleStorageChange)
})
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: background 0.3s ease;
}

.login-box {
  position: relative;
  z-index: 10;
  width: 700px;
  min-height: 450px;
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

// 左侧装饰图片区域
.login-image-section {
  flex: 0 0 40%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px 30px;
  color: white;

  .image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .decorative-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
      max-height: 250px;
    }

    .default-decoration {
      text-align: center;

      h2 {
        font-size: 32px;
        font-weight: 700;
        margin: 20px 0 10px;
        color: white;
      }

      p {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
      }
    }
  }

  .welcome-messages {
    margin-top: 30px;

    .welcome-text {
      font-size: 14px;
      line-height: 1.6;
      text-align: center;
      opacity: 0.95;
      padding: 15px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(5px);
    }
  }
}

// 右侧登录表单区域
.login-form-section {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  p {
    color: #7f8c8d;
    font-size: 14px;
    margin: 0;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .login-button {
    width: 100%;
    height: 45px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
  }
}

// 欢迎语淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
  }

  .shape1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  .shape2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
  }

  .shape3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-box {
    width: 95%;
    flex-direction: column;
    min-height: auto;
  }

  .login-image-section {
    flex: none;
    min-height: 200px;
    padding: 30px 20px;

    .image-container {
      .default-decoration {
        h2 {
          font-size: 24px;
        }

        p {
          font-size: 14px;
        }
      }
    }

    .welcome-messages {
      margin-top: 20px;

      .welcome-text {
        font-size: 13px;
        padding: 12px;
      }
    }
  }

  .login-form-section {
    padding: 30px 20px;
  }

  .login-header h1 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .login-box {
    width: 90%;
    margin: 20px;
  }

  .login-image-section {
    min-height: 150px;
    padding: 20px 15px;
  }

  .login-form-section {
    padding: 25px 15px;
  }

  .login-header {
    margin-bottom: 30px;

    h1 {
      font-size: 22px;
    }
  }
}
</style>
