"""
后端缓存管理工具
"""
import time
import json
import hashlib
from typing import Any, Optional, Dict, Tuple
from functools import wraps
from datetime import datetime, timedelta


class MemoryCache:
    """内存缓存管理器"""
    
    def __init__(self):
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._default_ttl = 300  # 5分钟默认过期时间
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = {
            'args': args,
            'kwargs': kwargs
        }
        key_str = f"{prefix}:{json.dumps(key_data, sort_keys=True)}"
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存"""
        if ttl is None:
            ttl = self._default_ttl
        
        expiry_time = time.time() + ttl
        self._cache[key] = (value, expiry_time)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        if key not in self._cache:
            return None
        
        value, expiry_time = self._cache[key]
        
        # 检查是否过期
        if time.time() > expiry_time:
            del self._cache[key]
            return None
        
        return value
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
    
    def cleanup(self) -> int:
        """清理过期缓存，返回清理的数量"""
        current_time = time.time()
        expired_keys = []
        
        for key, (_, expiry_time) in self._cache.items():
            if current_time > expiry_time:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
        
        return len(expired_keys)
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        total = len(self._cache)
        expired = 0
        
        for _, expiry_time in self._cache.values():
            if current_time > expiry_time:
                expired += 1
        
        return {
            'total': total,
            'active': total - expired,
            'expired': expired,
            'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
        }


# 全局缓存实例
cache = MemoryCache()


def cached(ttl: int = 300, key_prefix: str = "default"):
    """缓存装饰器
    
    Args:
        ttl: 缓存过期时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = cache._generate_key(f"{key_prefix}:{func.__name__}", *args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 缓存结果
            cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


def cache_key(*args, **kwargs) -> str:
    """生成缓存键的辅助函数"""
    return cache._generate_key("manual", *args, **kwargs)


class QueryCache:
    """数据库查询缓存"""
    
    def __init__(self, cache_instance: MemoryCache = None):
        self.cache = cache_instance or cache
    
    def get_or_set(self, key: str, query_func, ttl: int = 300):
        """获取缓存或执行查询并缓存结果"""
        cached_result = self.cache.get(key)
        if cached_result is not None:
            return cached_result
        
        result = query_func()
        self.cache.set(key, result, ttl)
        return result
    
    def invalidate_pattern(self, pattern: str):
        """根据模式删除缓存"""
        keys_to_delete = []
        for key in self.cache._cache.keys():
            if pattern in key:
                keys_to_delete.append(key)
        
        for key in keys_to_delete:
            self.cache.delete(key)


# 全局查询缓存实例
query_cache = QueryCache()


def cache_response(ttl: int = 300, key_func=None):
    """API响应缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache._generate_key(f"api:{func.__name__}", *args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


# 定期清理过期缓存的任务
import threading
import time

def cleanup_expired_cache():
    """定期清理过期缓存的后台任务"""
    while True:
        try:
            cleaned = cache.cleanup()
            if cleaned > 0:
                print(f"清理了 {cleaned} 个过期缓存项")
            time.sleep(60)  # 每分钟清理一次
        except Exception as e:
            print(f"缓存清理出错: {e}")
            time.sleep(60)

# 启动后台清理任务
cleanup_thread = threading.Thread(target=cleanup_expired_cache, daemon=True)
cleanup_thread.start()
