<template>
  <div class="radar-chart" :style="{ width, height }">
    <div ref="chartRef" :style="{ width: '100%', height: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props定义
interface Props {
  data: {
    labels: string[]
    datasets: Array<{
      label: string
      data: number[]
      borderColor?: string
      backgroundColor?: string
      pointBackgroundColor?: string
    }>
  }
  options?: any
  width?: string
  height?: string
  theme?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  theme: 'default',
  options: () => ({})
})

// 响应式引用
const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return

  await nextTick()
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新实例
  chartInstance = echarts.init(chartRef.value, props.theme)
  
  // 设置配置
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !props.data) return

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName}<br/>${params.name}: ${params.value}`
      }
    },
    legend: {
      data: props.data.datasets.map(dataset => dataset.label),
      orient: 'horizontal',
      bottom: 10,
      ...props.options.legend
    },
    radar: {
      indicator: props.data.labels.map(label => ({
        name: label,
        max: 100
      })),
      center: ['50%', '50%'],
      radius: '60%',
      startAngle: 90,
      splitNumber: 4,
      shape: 'polygon',
      name: {
        formatter: '{value}',
        textStyle: {
          color: '#666',
          fontSize: 12
        }
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(114, 172, 209, 0.2)', 'rgba(114, 172, 209, 0.4)', 'rgba(114, 172, 209, 0.6)', 'rgba(114, 172, 209, 0.8)', 'rgba(114, 172, 209, 1)'].reverse()
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(211, 253, 250, 0.8)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(211, 253, 250, 0.8)'
        }
      },
      ...props.options.radar
    },
    series: [{
      type: 'radar',
      data: props.data.datasets.map(dataset => ({
        name: dataset.label,
        value: dataset.data,
        itemStyle: {
          color: dataset.borderColor || '#4f46e5'
        },
        areaStyle: {
          color: dataset.backgroundColor || 'rgba(79, 70, 229, 0.2)'
        },
        lineStyle: {
          color: dataset.borderColor || '#4f46e5',
          width: 2
        },
        symbol: 'circle',
        symbolSize: 4
      }))
    }],
    ...props.options
  }

  chartInstance.setOption(option, true)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(() => props.options, updateChart, { deep: true })

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  getChartInstance: () => chartInstance,
  resize: handleResize,
  updateChart
})
</script>

<style scoped>
.radar-chart {
  position: relative;
}
</style>
