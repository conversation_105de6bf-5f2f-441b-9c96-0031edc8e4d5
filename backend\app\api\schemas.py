"""
WisCude 后台管理系统 - API 模型
"""
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, date
from enum import Enum

# 基础模型
class BaseSchema(BaseModel):
    """基础模型"""
    class Config:
        from_attributes = True
        protected_namespaces = ()

# 用户模型
class UserRole(str, Enum):
    """用户角色枚举"""
    SUPERADMIN = "superadmin"
    ADMIN = "admin"
    EDITOR = "editor"
    VIEWER = "viewer"

class Gender(str, Enum):
    """性别枚举"""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"
    UNKNOWN = "unknown"

# 认证模型
class Token(BaseSchema):
    """令牌模型"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class TokenPayload(BaseSchema):
    """令牌载荷"""
    sub: Optional[str] = None
    exp: Optional[int] = None
    type: Optional[str] = None

class LoginRequest(BaseSchema):
    """登录请求"""
    username: str
    password: str

class RefreshTokenRequest(BaseSchema):
    """刷新令牌请求"""
    refresh_token: str

# 管理员用户模型
class AdminUserBase(BaseSchema):
    """管理员用户基础模型"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    role: Optional[UserRole] = UserRole.VIEWER
    is_active: bool = True
    is_superuser: bool = False
    avatar_url: Optional[str] = None
    bio: Optional[str] = None

class AdminUserCreate(AdminUserBase):
    """创建管理员用户"""
    password: str = Field(..., min_length=6)

class AdminUserUpdate(BaseSchema):
    """更新管理员用户"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None
    avatar_url: Optional[str] = None
    bio: Optional[str] = None

class AdminUserResponse(AdminUserBase):
    """管理员用户响应"""
    id: str
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None

class ChangePasswordRequest(BaseSchema):
    """修改密码请求"""
    current_password: str
    new_password: str = Field(..., min_length=6)

# WisCude 用户模型
class WiscudeUserBase(BaseSchema):
    """WisCude用户基础模型"""
    username: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[Gender] = Gender.UNKNOWN
    birth_date: Optional[date] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    is_active: bool = True
    is_premium: bool = False

class WiscudeUserCreate(WiscudeUserBase):
    """创建WisCude用户"""
    android_user_id: str

class WiscudeUserUpdate(BaseSchema):
    """更新WisCude用户"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[Gender] = None
    birth_date: Optional[date] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    is_active: Optional[bool] = None
    is_premium: Optional[bool] = None
    premium_expires_at: Optional[datetime] = None
    level: Optional[int] = None
    experience_points: Optional[int] = None

class WiscudeUserResponse(WiscudeUserBase):
    """WisCude用户响应"""
    id: str
    android_user_id: str
    registration_date: datetime
    last_login: Optional[datetime] = None
    total_study_time: int
    total_focus_sessions: int
    total_check_ins: int
    level: int
    experience_points: int
    premium_expires_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

# 学习会话模型
class StudySessionBase(BaseSchema):
    """学习会话基础模型"""
    session_type: str
    title: Optional[str] = None
    subject: Optional[str] = None
    duration: int
    start_time: datetime
    end_time: Optional[datetime] = None
    interruptions: int = 0
    productivity_score: Optional[float] = None
    notes: Optional[str] = None
    is_completed: bool = False
    completion_percentage: float = 0

class StudySessionCreate(StudySessionBase):
    """创建学习会话"""
    android_session_id: str
    user_id: str

class StudySessionUpdate(BaseSchema):
    """更新学习会话"""
    session_type: Optional[str] = None
    title: Optional[str] = None
    subject: Optional[str] = None
    duration: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    interruptions: Optional[int] = None
    productivity_score: Optional[float] = None
    notes: Optional[str] = None
    is_completed: Optional[bool] = None
    completion_percentage: Optional[float] = None

class StudySessionResponse(StudySessionBase):
    """学习会话响应"""
    id: str
    android_session_id: str
    user_id: str
    created_at: datetime
    updated_at: datetime

# 数据同步模型
class SyncRequest(BaseSchema):
    """同步请求"""
    sync_type: str = "full"  # full, incremental
    tables: Optional[List[str]] = None

class SyncResponse(BaseSchema):
    """同步响应"""
    status: str
    stats: Dict[str, Dict[str, int]]
    total: Dict[str, int]
    duration: Optional[int] = None
    error: Optional[str] = None

# 分页模型
class PaginationParams(BaseSchema):
    """分页参数"""
    page: int = 1
    page_size: int = 20
    
    @validator("page")
    def page_must_be_positive(cls, v):
        if v < 1:
            raise ValueError("页码必须大于0")
        return v
    
    @validator("page_size")
    def page_size_must_be_positive(cls, v):
        if v < 1:
            raise ValueError("每页数量必须大于0")
        if v > 100:
            raise ValueError("每页数量不能超过100")
        return v

class PaginatedResponse(BaseSchema):
    """分页响应"""
    items: List[Any]
    total: int
    page: int
    page_size: int
    pages: int
