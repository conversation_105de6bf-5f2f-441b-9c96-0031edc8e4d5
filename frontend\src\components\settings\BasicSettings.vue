<template>
  <el-card class="settings-card">
    <template #header>
      <div class="card-header">
        <el-icon><Setting /></el-icon>
        <span>基本设置</span>
      </div>
    </template>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      class="settings-form"
    >
      <el-form-item label="系统名称" prop="app_name" required>
        <el-input
          v-model="formData.app_name"
          placeholder="请输入系统名称"
          clearable
        />
        <div class="field-description">
          显示在系统标题栏和登录页面的系统名称
        </div>
      </el-form-item>
      
      <el-form-item label="系统版本" prop="app_version" required>
        <el-input
          v-model="formData.app_version"
          placeholder="请输入版本号，如：1.0.0"
          clearable
        />
        <div class="field-description">
          系统版本号，用于版本管理和更新提示
        </div>
      </el-form-item>
      
      <el-form-item label="系统描述" prop="app_description">
        <el-input
          v-model="formData.app_description"
          type="textarea"
          :rows="3"
          placeholder="请输入系统描述"
        />
        <div class="field-description">
          系统的详细描述信息，显示在关于页面
        </div>
      </el-form-item>
      
      <el-form-item label="时区设置" prop="timezone" required>
        <el-select
          v-model="formData.timezone"
          placeholder="请选择时区"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="option in timezoneOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <div class="field-description">
          系统默认时区，影响日志记录和数据显示的时间
        </div>
      </el-form-item>
      
      <el-form-item label="默认语言" prop="language" required>
        <el-select
          v-model="formData.language"
          placeholder="请选择语言"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="option in languageOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <div class="field-description">
          系统界面的默认显示语言
        </div>
      </el-form-item>
      
      <el-form-item label="数据同步间隔" prop="sync_interval_minutes">
        <el-input-number
          v-model="formData.sync_interval_minutes"
          :min="5"
          :max="1440"
          :step="5"
          controls-position="right"
          style="width: 200px"
        />
        <span class="unit-text">分钟</span>
        <div class="field-description">
          自动数据同步的时间间隔，设置为0表示禁用自动同步
        </div>
      </el-form-item>
      
      <el-form-item label="同步批处理大小" prop="sync_batch_size">
        <el-input-number
          v-model="formData.sync_batch_size"
          :min="100"
          :max="5000"
          :step="100"
          controls-position="right"
          style="width: 200px"
        />
        <span class="unit-text">条记录</span>
        <div class="field-description">
          每次同步处理的数据条数，较大的值可能影响系统性能
        </div>
      </el-form-item>
      
      <el-form-item label="启用自动同步" prop="auto_sync_enabled">
        <el-switch
          v-model="formData.auto_sync_enabled"
          active-text="开启"
          inactive-text="关闭"
        />
        <div class="field-description">
          是否启用定时自动数据同步功能
        </div>
      </el-form-item>
      
      <el-form-item class="form-actions">
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存设置
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button
          type="info"
          @click="handlePreview"
        >
          预览效果
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting } from '@element-plus/icons-vue'
import type { SystemSettings } from '@/types/settings'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  settings: SystemSettings
}

interface Emits {
  (e: 'update', settings: Partial<SystemSettings>): void
  (e: 'save', settings: Partial<SystemSettings>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const saving = ref(false)

const formData = reactive<Partial<SystemSettings>>({
  app_name: '',
  app_version: '',
  app_description: '',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN',
  sync_interval_minutes: 30,
  sync_batch_size: 1000,
  auto_sync_enabled: false
})

const timezoneOptions = [
  { label: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
  { label: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
  { label: '纽约时间 (UTC-5)', value: 'America/New_York' },
  { label: '伦敦时间 (UTC+0)', value: 'Europe/London' },
  { label: 'UTC 标准时间', value: 'UTC' }
]

const languageOptions = [
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' }
]

const formRules: FormRules = {
  app_name: [
    { required: true, message: '请输入系统名称', trigger: 'blur' },
    { min: 2, max: 50, message: '系统名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  app_version: [
    { required: true, message: '请输入系统版本', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式应为 x.y.z', trigger: 'blur' }
  ],
  timezone: [
    { required: true, message: '请选择时区', trigger: 'change' }
  ],
  language: [
    { required: true, message: '请选择语言', trigger: 'change' }
  ]
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  Object.assign(formData, newSettings)
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newData) => {
  emit('update', newData)
}, { deep: true })

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 确认保存
    await ElMessageBox.confirm(
      '确定要保存基本设置吗？部分设置可能需要重启系统后生效。',
      '确认保存',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    saving.value = true
    emit('save', formData)
    
    setTimeout(() => {
      saving.value = false
    }, 1000)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('表单验证失败，请检查输入')
    }
  }
}

const handleReset = () => {
  if (!formRef.value) return
  
  ElMessageBox.confirm('确定要重置所有设置吗？', '确认重置', {
    type: 'warning'
  }).then(() => {
    formRef.value?.resetFields()
    Object.assign(formData, props.settings)
    ElMessage.success('设置已重置')
  }).catch(() => {
    // 用户取消
  })
}

const handlePreview = () => {
  ElMessageBox.alert(
    `系统名称：${formData.app_name}\n版本：${formData.app_version}\n描述：${formData.app_description}\n时区：${formData.timezone}\n语言：${formData.language}`,
    '设置预览',
    {
      confirmButtonText: '确定'
    }
  )
}

onMounted(() => {
  Object.assign(formData, props.settings)
})
</script>

<style lang="scss" scoped>
.settings-card {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .settings-form {
    .field-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
      line-height: 1.4;
    }
    
    .unit-text {
      margin-left: 8px;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
    
    .form-actions {
      margin-top: 32px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
</style>
