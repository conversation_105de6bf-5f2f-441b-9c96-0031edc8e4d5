<template>
  <div class="experiences-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>学霸经验分享</h1>
          <p>汇聚优秀学习经验，助力学习成长</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showPublishDialog = true">
            <el-icon><EditPen /></el-icon>
            发布经验
          </el-button>
          <el-button @click="refreshExperiences" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-section">
      <div class="filter-content">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索经验分享..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-controls">
          <el-select v-model="selectedCategory" placeholder="选择分类" @change="handleCategoryChange">
            <el-option label="全部分类" value="" />
            <el-option label="学习方法" value="method" />
            <el-option label="时间管理" value="time" />
            <el-option label="考试技巧" value="exam" />
            <el-option label="心理调节" value="psychology" />
            <el-option label="工具推荐" value="tools" />
          </el-select>

          <el-select v-model="selectedLevel" placeholder="选择等级" @change="handleLevelChange">
            <el-option label="全部等级" value="" />
            <el-option label="学习新手" value="beginner" />
            <el-option label="进阶学者" value="intermediate" />
            <el-option label="学霸大神" value="expert" />
          </el-select>

          <el-select v-model="sortBy" placeholder="排序方式" @change="handleSortChange">
            <el-option label="最新发布" value="latest" />
            <el-option label="最多点赞" value="likes" />
            <el-option label="最多评论" value="comments" />
            <el-option label="最多收藏" value="favorites" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 经验列表 -->
    <div class="experiences-list" v-loading="loading">
      <div class="experiences-grid">
        <div
          v-for="experience in filteredExperiences"
          :key="experience.id"
          class="experience-card"
          @click="viewExperience(experience)"
        >
          <div class="card-header">
            <div class="author-info">
              <el-avatar :src="experience.author.avatar" :size="40">
                {{ experience.author.name.charAt(0) }}
              </el-avatar>
              <div class="author-details">
                <div class="author-name">{{ experience.author.name }}</div>
                <div class="author-level">
                  <el-tag :type="getLevelTagType(experience.author.level)" size="small">
                    {{ getLevelName(experience.author.level) }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="publish-time">{{ formatTime(experience.created_at) }}</div>
          </div>

          <div class="card-content">
            <h3 class="experience-title">{{ experience.title }}</h3>
            <p class="experience-summary">{{ experience.summary }}</p>

            <div class="experience-tags">
              <el-tag
                v-for="tag in experience.tags"
                :key="tag"
                size="small"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>

          <div class="card-footer">
            <div class="stats">
              <span class="stat-item">
                <el-icon><View /></el-icon>
                {{ experience.views }}
              </span>
              <span class="stat-item">
                <el-icon><ChatDotRound /></el-icon>
                {{ experience.comments }}
              </span>
              <span class="stat-item">
                <el-icon><Star /></el-icon>
                {{ experience.favorites }}
              </span>
            </div>

            <div class="actions">
              <el-button
                text
                :type="experience.isLiked ? 'primary' : 'default'"
                @click.stop="toggleLike(experience)"
              >
                <el-icon><StarFilled /></el-icon>
                {{ experience.likes }}
              </el-button>
              <el-button
                text
                :type="experience.isFavorited ? 'warning' : 'default'"
                @click.stop="toggleFavorite(experience)"
              >
                <el-icon><Star /></el-icon>
                收藏
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore">
        <el-button @click="loadMore" :loading="loadingMore">
          加载更多
        </el-button>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!loading && filteredExperiences.length === 0">
        <el-empty description="暂无经验分享">
          <el-button type="primary" @click="showPublishDialog = true">
            发布第一个经验
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 发布经验对话框 -->
    <el-dialog
      v-model="showPublishDialog"
      title="发布学习经验"
      width="800px"
      :before-close="handleClosePublish"
    >
      <el-form :model="publishForm" :rules="publishRules" ref="publishFormRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="publishForm.title"
            placeholder="请输入经验标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="分类" prop="category">
          <el-select v-model="publishForm.category" placeholder="选择分类">
            <el-option label="学习方法" value="method" />
            <el-option label="时间管理" value="time" />
            <el-option label="考试技巧" value="exam" />
            <el-option label="心理调节" value="psychology" />
            <el-option label="工具推荐" value="tools" />
          </el-select>
        </el-form-item>

        <el-form-item label="摘要" prop="summary">
          <el-input
            v-model="publishForm.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入经验摘要"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <div class="rich-editor">
            <el-input
              v-model="publishForm.content"
              type="textarea"
              :rows="10"
              placeholder="请详细描述你的学习经验..."
              maxlength="5000"
              show-word-limit
            />
          </div>
        </el-form-item>

        <el-form-item label="标签">
          <div class="tags-input">
            <el-tag
              v-for="tag in publishForm.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="showTagInput"
              ref="tagInputRef"
              v-model="newTag"
              size="small"
              @keyup.enter="addTag"
              @blur="addTag"
              placeholder="添加标签"
              style="width: 100px;"
            />
            <el-button v-else size="small" @click="showNewTagInput">
              + 添加标签
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showPublishDialog = false">取消</el-button>
        <el-button type="primary" @click="publishExperience" :loading="publishing">
          发布经验
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  EditPen, Refresh, Search, View, ChatDotRound, Star, StarFilled
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const publishing = ref(false)
const showPublishDialog = ref(false)
const showTagInput = ref(false)
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedLevel = ref('')
const sortBy = ref('latest')
const newTag = ref('')
const hasMore = ref(true)

// 表单引用
const publishFormRef = ref()
const tagInputRef = ref()

// 发布表单
const publishForm = reactive({
  title: '',
  category: '',
  summary: '',
  content: '',
  tags: [] as string[]
})

// 表单验证规则
const publishRules = {
  title: [
    { required: true, message: '请输入经验标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  summary: [
    { required: true, message: '请输入经验摘要', trigger: 'blur' },
    { min: 10, max: 200, message: '摘要长度在 10 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入经验内容', trigger: 'blur' },
    { min: 50, max: 5000, message: '内容长度在 50 到 5000 个字符', trigger: 'blur' }
  ]
}

// 经验数据
const experiences = ref([
  {
    id: '1',
    title: '高效学习法：番茄工作法的实践心得',
    summary: '通过番茄工作法，我的学习效率提升了300%，分享我的实践经验和改进技巧。',
    content: '详细的学习经验内容...',
    category: 'method',
    tags: ['番茄工作法', '时间管理', '效率提升'],
    author: {
      id: '1',
      name: '学霸小王',
      avatar: '',
      level: 'expert'
    },
    views: 1234,
    likes: 89,
    comments: 23,
    favorites: 45,
    isLiked: false,
    isFavorited: false,
    created_at: '2024-01-20 14:30:00'
  },
  {
    id: '2',
    title: '考试前一周的复习策略',
    summary: '分享我在期末考试前一周的高效复习方法，帮助大家在短时间内最大化复习效果。',
    content: '详细的复习策略...',
    category: 'exam',
    tags: ['考试技巧', '复习方法', '时间规划'],
    author: {
      id: '2',
      name: '考试达人',
      avatar: '',
      level: 'intermediate'
    },
    views: 856,
    likes: 67,
    comments: 18,
    favorites: 32,
    isLiked: true,
    isFavorited: false,
    created_at: '2024-01-19 10:15:00'
  }
])

// 计算属性
const filteredExperiences = computed(() => {
  let filtered = [...experiences.value]

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(exp =>
      exp.title.toLowerCase().includes(keyword) ||
      exp.summary.toLowerCase().includes(keyword) ||
      exp.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }

  // 分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter(exp => exp.category === selectedCategory.value)
  }

  // 等级过滤
  if (selectedLevel.value) {
    filtered = filtered.filter(exp => exp.author.level === selectedLevel.value)
  }

  // 排序
  switch (sortBy.value) {
    case 'likes':
      filtered.sort((a, b) => b.likes - a.likes)
      break
    case 'comments':
      filtered.sort((a, b) => b.comments - a.comments)
      break
    case 'favorites':
      filtered.sort((a, b) => b.favorites - a.favorites)
      break
    case 'latest':
    default:
      filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      break
  }

  return filtered
})

// 方法
const refreshExperiences = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleCategoryChange = () => {
  // 分类过滤逻辑已在计算属性中处理
}

const handleLevelChange = () => {
  // 等级过滤逻辑已在计算属性中处理
}

const handleSortChange = () => {
  // 排序逻辑已在计算属性中处理
}

const viewExperience = (experience: any) => {
  // 查看经验详情
  console.log('查看经验:', experience)
  // TODO: 跳转到详情页面或打开详情对话框
}

const toggleLike = async (experience: any) => {
  try {
    experience.isLiked = !experience.isLiked
    experience.likes += experience.isLiked ? 1 : -1
    ElMessage.success(experience.isLiked ? '点赞成功' : '取消点赞')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const toggleFavorite = async (experience: any) => {
  try {
    experience.isFavorited = !experience.isFavorited
    experience.favorites += experience.isFavorited ? 1 : -1
    ElMessage.success(experience.isFavorited ? '收藏成功' : '取消收藏')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const loadMore = async () => {
  loadingMore.value = true
  try {
    // 模拟加载更多数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 这里可以添加更多数据到experiences数组
    hasMore.value = false // 模拟没有更多数据
  } catch (error) {
    ElMessage.error('加载失败')
  } finally {
    loadingMore.value = false
  }
}

const showNewTagInput = () => {
  showTagInput.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !publishForm.tags.includes(tag) && publishForm.tags.length < 5) {
    publishForm.tags.push(tag)
  }
  newTag.value = ''
  showTagInput.value = false
}

const removeTag = (tag: string) => {
  const index = publishForm.tags.indexOf(tag)
  if (index > -1) {
    publishForm.tags.splice(index, 1)
  }
}

const publishExperience = async () => {
  try {
    await publishFormRef.value?.validate()
    publishing.value = true

    // 模拟发布API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 添加到经验列表
    const newExperience = {
      id: Date.now().toString(),
      title: publishForm.title,
      summary: publishForm.summary,
      content: publishForm.content,
      category: publishForm.category,
      tags: [...publishForm.tags],
      author: {
        id: 'current-user',
        name: '当前用户',
        avatar: '',
        level: 'intermediate'
      },
      views: 0,
      likes: 0,
      comments: 0,
      favorites: 0,
      isLiked: false,
      isFavorited: false,
      created_at: new Date().toISOString()
    }

    experiences.value.unshift(newExperience)

    // 重置表单
    publishFormRef.value?.resetFields()
    publishForm.tags = []
    showPublishDialog.value = false

    ElMessage.success('发布成功')
  } catch (error) {
    console.error('发布失败:', error)
  } finally {
    publishing.value = false
  }
}

const handleClosePublish = (done: () => void) => {
  if (publishForm.title || publishForm.summary || publishForm.content) {
    ElMessageBox.confirm('确定要关闭吗？未保存的内容将丢失。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      done()
    }).catch(() => {
      // 取消关闭
    })
  } else {
    done()
  }
}

const getLevelName = (level: string): string => {
  const levels = {
    beginner: '学习新手',
    intermediate: '进阶学者',
    expert: '学霸大神'
  }
  return levels[level as keyof typeof levels] || level
}

const getLevelTagType = (level: string): string => {
  const types = {
    beginner: 'info',
    intermediate: 'warning',
    expert: 'danger'
  }
  return types[level as keyof typeof types] || 'info'
}

const formatTime = (time: string): string => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.experiences-page {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 筛选区域 */
  .filter-section {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);

    .filter-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);

      .search-bar {
        .el-input {
          max-width: 400px;
        }
      }

      .filter-controls {
        display: flex;
        gap: var(--spacing-4);
        flex-wrap: wrap;

        .el-select {
          min-width: 140px;
        }
      }
    }
  }

  /* 经验列表 */
  .experiences-list {
    .experiences-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: var(--spacing-6);
      margin-bottom: var(--spacing-8);

      .experience-card {
        background: white;
        border-radius: var(--radius-xl);
        border: 1px solid var(--border-light);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-fast);
        cursor: pointer;
        overflow: hidden;

        &:hover {
          transform: translateY(-4px);
          box-shadow: var(--shadow-lg);
          border-color: var(--primary-light);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-5);
          border-bottom: 1px solid var(--border-light);

          .author-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);

            .author-details {
              .author-name {
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
                color: var(--text-primary);
                margin-bottom: var(--spacing-1);
              }

              .author-level {
                .el-tag {
                  font-size: var(--font-size-xs);
                }
              }
            }
          }

          .publish-time {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }

        .card-content {
          padding: var(--spacing-5);

          .experience-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-3) 0;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .experience-summary {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            line-height: 1.6;
            margin: 0 0 var(--spacing-4) 0;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .experience-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-2);

            .tag-item {
              font-size: var(--font-size-xs);
              border-radius: var(--radius-md);
            }
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-4) var(--spacing-5);
          background-color: var(--bg-secondary);
          border-top: 1px solid var(--border-light);

          .stats {
            display: flex;
            gap: var(--spacing-4);

            .stat-item {
              display: flex;
              align-items: center;
              gap: var(--spacing-1);
              font-size: var(--font-size-xs);
              color: var(--text-tertiary);

              .el-icon {
                font-size: var(--font-size-sm);
              }
            }
          }

          .actions {
            display: flex;
            gap: var(--spacing-2);

            .el-button {
              font-size: var(--font-size-xs);
              padding: var(--spacing-1) var(--spacing-2);
            }
          }
        }
      }
    }

    /* 加载更多 */
    .load-more {
      text-align: center;
      margin: var(--spacing-8) 0;
    }

    /* 空状态 */
    .empty-state {
      text-align: center;
      padding: var(--spacing-12) var(--spacing-6);
    }
  }

  /* 发布对话框样式 */
  .el-dialog {
    .rich-editor {
      .el-textarea {
        .el-textarea__inner {
          min-height: 200px;
          font-family: var(--font-family-mono);
          line-height: 1.6;
        }
      }
    }

    .tags-input {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
      align-items: center;

      .tag-item {
        margin: 0;
      }

      .el-button {
        height: 24px;
        font-size: var(--font-size-xs);
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .experiences-page {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .filter-section .filter-content {
      .filter-controls {
        .el-select {
          min-width: 120px;
        }
      }
    }

    .experiences-list .experiences-grid {
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: var(--spacing-4);
    }
  }
}

@include respond-to('md') {
  .experiences-page {
    padding: var(--spacing-4);

    .filter-section .filter-content {
      .filter-controls {
        flex-direction: column;
        align-items: stretch;

        .el-select {
          min-width: auto;
        }
      }
    }

    .experiences-list .experiences-grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);

      .experience-card {
        .card-header {
          padding: var(--spacing-4);

          .author-info {
            gap: var(--spacing-2);
          }
        }

        .card-content {
          padding: var(--spacing-4);

          .experience-title {
            font-size: var(--font-size-base);
          }
        }

        .card-footer {
          padding: var(--spacing-3) var(--spacing-4);
          flex-direction: column;
          gap: var(--spacing-3);
          align-items: stretch;

          .stats {
            justify-content: center;
          }

          .actions {
            justify-content: center;
          }
        }
      }
    }
  }

  .el-dialog {
    width: 95% !important;
    margin: var(--spacing-4) auto;

    .el-dialog__body {
      padding: var(--spacing-4);
    }
  }
}
</style>