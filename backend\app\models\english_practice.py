"""
英语练习管理模块数据模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, JSON, Float, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base

class VocabularyWord(Base):
    """词汇库模型"""
    __tablename__ = "vocabulary_words"
    
    id = Column(String(36), primary_key=True)
    word = Column(String(100), nullable=False, unique=True, comment="单词")
    pronunciation = Column(String(200), comment="音标")
    audio_url = Column(String(500), comment="发音音频链接")
    
    # 词汇信息
    word_type = Column(String(20), comment="词性: noun, verb, adjective, adverb, etc.")
    difficulty_level = Column(Integer, default=1, comment="难度等级 1-6")
    frequency_level = Column(String(20), comment="词频等级: high, medium, low")
    
    # 释义和例句
    definitions = Column(JSON, comment="释义列表")
    examples = Column(JSON, comment="例句列表")
    synonyms = Column(JSON, comment="同义词")
    antonyms = Column(JSON, comment="反义词")
    
    # 分类标签
    categories = Column(JSON, comment="分类标签")
    topics = Column(JSON, comment="主题标签")
    grade_levels = Column(JSON, comment="适用年级")
    
    # 学习统计
    learn_count = Column(Integer, default=0, comment="学习次数")
    correct_count = Column(Integer, default=0, comment="答对次数")
    wrong_count = Column(Integer, default=0, comment="答错次数")
    mastery_rate = Column(Float, default=0.0, comment="掌握率")
    
    # 状态
    status = Column(String(20), default="active", comment="状态: active, inactive, archived")
    is_featured = Column(Boolean, default=False, comment="是否为重点词汇")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class ListeningMaterial(Base):
    """听力材料模型"""
    __tablename__ = "listening_materials"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="听力材料标题")
    description = Column(Text, comment="材料描述")
    
    # 音频信息
    audio_url = Column(String(500), nullable=False, comment="音频文件链接")
    duration = Column(Integer, comment="音频时长（秒）")
    audio_quality = Column(String(20), default="standard", comment="音频质量: low, standard, high")
    
    # 文本内容
    transcript = Column(Text, comment="听力文本")
    has_subtitle = Column(Boolean, default=False, comment="是否有字幕")
    subtitle_url = Column(String(500), comment="字幕文件链接")
    
    # 分类信息
    category = Column(String(50), comment="分类: conversation, lecture, news, story, etc.")
    topic = Column(String(100), comment="主题")
    difficulty_level = Column(Integer, default=1, comment="难度等级 1-6")
    speed_level = Column(String(20), default="normal", comment="语速: slow, normal, fast")
    accent = Column(String(50), default="american", comment="口音: american, british, australian, etc.")
    
    # 适用信息
    grade_levels = Column(JSON, comment="适用年级")
    skill_focus = Column(JSON, comment="技能重点")
    
    # 练习配置
    has_questions = Column(Boolean, default=False, comment="是否有配套题目")
    question_count = Column(Integer, default=0, comment="题目数量")
    practice_types = Column(JSON, comment="练习类型")
    
    # 使用统计
    play_count = Column(Integer, default=0, comment="播放次数")
    completion_rate = Column(Float, default=0.0, comment="完成率")
    average_score = Column(Float, default=0.0, comment="平均分")
    
    # 状态
    status = Column(String(20), default="draft", comment="状态: draft, published, archived")
    is_featured = Column(Boolean, default=False, comment="是否为推荐材料")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class SpeakingScenario(Base):
    """口语练习场景模型"""
    __tablename__ = "speaking_scenarios"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="场景标题")
    description = Column(Text, comment="场景描述")
    
    # 场景信息
    scenario_type = Column(String(50), comment="场景类型: daily, business, academic, travel, etc.")
    situation = Column(Text, comment="情境设定")
    role_description = Column(Text, comment="角色描述")
    
    # 练习内容
    prompts = Column(JSON, comment="练习提示")
    sample_responses = Column(JSON, comment="示例回答")
    key_phrases = Column(JSON, comment="关键短语")
    vocabulary_focus = Column(JSON, comment="重点词汇")
    
    # 评测标准
    evaluation_criteria = Column(JSON, comment="评测标准")
    pronunciation_weight = Column(Float, default=0.3, comment="发音权重")
    fluency_weight = Column(Float, default=0.3, comment="流利度权重")
    accuracy_weight = Column(Float, default=0.2, comment="准确性权重")
    content_weight = Column(Float, default=0.2, comment="内容权重")
    
    # 分类信息
    difficulty_level = Column(Integer, default=1, comment="难度等级 1-6")
    grade_levels = Column(JSON, comment="适用年级")
    time_limit = Column(Integer, comment="练习时间限制（秒）")
    
    # 使用统计
    practice_count = Column(Integer, default=0, comment="练习次数")
    average_score = Column(Float, default=0.0, comment="平均分")
    completion_rate = Column(Float, default=0.0, comment="完成率")
    
    # 状态
    status = Column(String(20), default="draft", comment="状态: draft, published, archived")
    is_featured = Column(Boolean, default=False, comment="是否为推荐场景")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class EnglishPracticeRecord(Base):
    """英语练习记录模型"""
    __tablename__ = "english_practice_records"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    
    # 练习信息
    practice_type = Column(String(20), nullable=False, comment="练习类型: vocabulary, listening, speaking")
    content_id = Column(String(36), comment="内容ID")
    content_title = Column(String(200), comment="内容标题")
    
    # 练习结果
    score = Column(Float, default=0.0, comment="得分")
    max_score = Column(Float, default=100.0, comment="满分")
    completion_rate = Column(Float, default=0.0, comment="完成率")
    time_spent = Column(Integer, comment="练习用时（秒）")
    
    # 详细成绩（口语练习）
    pronunciation_score = Column(Float, comment="发音分数")
    fluency_score = Column(Float, comment="流利度分数")
    accuracy_score = Column(Float, comment="准确性分数")
    content_score = Column(Float, comment="内容分数")
    
    # 练习数据
    practice_data = Column(JSON, comment="练习详细数据")
    mistakes = Column(JSON, comment="错误记录")
    improvements = Column(JSON, comment="改进建议")
    
    # 时间信息
    practice_date = Column(DateTime, default=datetime.utcnow, comment="练习日期")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")


class UserEnglishProgress(Base):
    """用户英语学习进度模型"""
    __tablename__ = "user_english_progress"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, unique=True, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    
    # 整体进度
    current_level = Column(Integer, default=1, comment="当前等级")
    total_experience = Column(Integer, default=0, comment="总经验值")
    vocabulary_mastered = Column(Integer, default=0, comment="已掌握词汇数")
    
    # 各项能力评估
    vocabulary_level = Column(Integer, default=1, comment="词汇水平")
    listening_level = Column(Integer, default=1, comment="听力水平")
    speaking_level = Column(Integer, default=1, comment="口语水平")
    
    # 学习统计
    total_study_days = Column(Integer, default=0, comment="总学习天数")
    continuous_days = Column(Integer, default=0, comment="连续学习天数")
    total_study_time = Column(Integer, default=0, comment="总学习时间（分钟）")
    
    # 练习统计
    vocabulary_practices = Column(Integer, default=0, comment="词汇练习次数")
    listening_practices = Column(Integer, default=0, comment="听力练习次数")
    speaking_practices = Column(Integer, default=0, comment="口语练习次数")
    
    # 成绩统计
    average_vocabulary_score = Column(Float, default=0.0, comment="词汇平均分")
    average_listening_score = Column(Float, default=0.0, comment="听力平均分")
    average_speaking_score = Column(Float, default=0.0, comment="口语平均分")
    
    # 学习目标
    daily_goal_minutes = Column(Integer, default=30, comment="每日学习目标（分钟）")
    weekly_goal_practices = Column(Integer, default=10, comment="每周练习目标次数")
    target_level = Column(Integer, default=6, comment="目标等级")
    
    # 时间信息
    last_study_date = Column(DateTime, comment="最后学习日期")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
