<template>
  <div class="statistics-page">
    <div class="page-header">
      <h1>答题统计</h1>
      <p>分析学生答题数据、题目难度分布和学习效果评估</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-answers">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalAnswers }}</div>
              <div class="stat-label">总答题数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon correct-rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ (statistics.correctRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">正确率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active-students">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.activeStudents }}</div>
              <div class="stat-label">活跃学生</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon avg-score">
              <el-icon><Medal /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.avgScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>答题趋势分析</span>
              <el-select v-model="timeRange" size="small" style="width: 120px;">
                <el-option label="最近7天" value="7d" />
                <el-option label="最近30天" value="30d" />
                <el-option label="最近90天" value="90d" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加答题趋势图表 -->
            <div class="chart-placeholder">答题趋势图表</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学科分布统计</span>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加学科分布饼图 -->
            <div class="chart-placeholder">学科分布饼图</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>难度分布分析</span>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加难度分布柱状图 -->
            <div class="chart-placeholder">难度分布柱状图</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>正确率分析</span>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加正确率分析图表 -->
            <div class="chart-placeholder">正确率分析图表</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="table-section">
      <template #header>
        <div class="card-header">
          <span>题目统计详情</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索题目"
              style="width: 200px"
              clearable
              @change="loadStatistics"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="subjectFilter" placeholder="学科筛选" style="width: 120px" @change="loadStatistics">
              <el-option label="全部学科" value="" />
              <el-option label="语文" value="chinese" />
              <el-option label="数学" value="math" />
              <el-option label="英语" value="english" />
              <el-option label="物理" value="physics" />
              <el-option label="化学" value="chemistry" />
            </el-select>
            <el-button @click="exportStatistics">
              <el-icon><Download /></el-icon>
              导出统计
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="questionStats"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column label="题目信息" min-width="300">
          <template #default="{ row }">
            <div class="question-info">
              <div class="question-content">{{ row.content }}</div>
              <div class="question-meta">
                <el-tag size="small" :type="getSubjectTagType(row.subject)">
                  {{ getSubjectName(row.subject) }}
                </el-tag>
                <span class="difficulty-badge" :class="`difficulty-${row.difficulty}`">
                  {{ getDifficultyName(row.difficulty) }}
                </span>
                <span class="question-type">{{ getTypeName(row.type) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="答题统计" width="150">
          <template #default="{ row }">
            <div class="answer-stats">
              <div class="stat-item">
                <span class="stat-label">总答题:</span>
                <span class="stat-value">{{ row.total_answers }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">正确:</span>
                <span class="stat-value correct">{{ row.correct_answers }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">错误:</span>
                <span class="stat-value incorrect">{{ row.incorrect_answers }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="正确率" width="120">
          <template #default="{ row }">
            <div class="accuracy-info">
              <div class="accuracy-value" :class="getAccuracyClass(row.accuracy)">
                {{ (row.accuracy * 100).toFixed(1) }}%
              </div>
              <el-progress 
                :percentage="row.accuracy * 100" 
                :status="getProgressStatus(row.accuracy)"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平均用时" width="100">
          <template #default="{ row }">
            <span class="avg-time">{{ formatTime(row.avg_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="难度系数" width="100">
          <template #default="{ row }">
            <span class="difficulty-coefficient" :class="getDifficultyClass(row.difficulty_coefficient)">
              {{ row.difficulty_coefficient.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="最近更新" width="120">
          <template #default="{ row }">
            <span class="update-time">{{ formatDate(row.updated_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="link" size="small" @click="viewDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button type="link" size="small" @click="viewAnalysis(row)">
              <el-icon><DataAnalysis /></el-icon>
              分析
            </el-button>
            <el-button type="link" size="small" @click="adjustDifficulty(row)">
              <el-icon><Setting /></el-icon>
              调整
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadStatistics"
          @current-change="loadStatistics"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis, TrendCharts, User, Medal, Search, Download, View, Setting, ArrowUp
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const timeRange = ref('30d')
const searchKeyword = ref('')
const subjectFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = reactive({
  totalAnswers: 15678,
  correctRate: 0.742,
  activeStudents: 1234,
  avgScore: 78.5
})

// 题目统计列表
const questionStats = ref([
  {
    id: '1',
    content: '下列哪个选项是正确的数学表达式？',
    subject: 'math',
    difficulty: 'medium',
    type: 'single_choice',
    total_answers: 156,
    correct_answers: 118,
    incorrect_answers: 38,
    accuracy: 0.756,
    avg_time: 45,
    difficulty_coefficient: 0.76,
    updated_at: '2024-01-15 10:30:00'
  }
])

// 方法
const loadStatistics = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取统计数据
    console.log('Loading statistics...')
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const getSubjectName = (subject) => {
  const subjects = {
    chinese: '语文',
    math: '数学',
    english: '英语',
    physics: '物理',
    chemistry: '化学'
  }
  return subjects[subject] || subject
}

const getSubjectTagType = (subject) => {
  const types = {
    chinese: 'primary',
    math: 'success',
    english: 'warning',
    physics: 'danger',
    chemistry: 'info'
  }
  return types[subject] || ''
}

const getDifficultyName = (difficulty) => {
  const difficulties = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return difficulties[difficulty] || difficulty
}

const getTypeName = (type) => {
  const types = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_blank: '填空题',
    essay: '问答题'
  }
  return types[type] || type
}

const getAccuracyClass = (accuracy) => {
  if (accuracy >= 0.8) return 'excellent'
  if (accuracy >= 0.6) return 'good'
  if (accuracy >= 0.4) return 'average'
  return 'poor'
}

const getProgressStatus = (accuracy) => {
  if (accuracy >= 0.8) return 'success'
  if (accuracy >= 0.6) return ''
  if (accuracy >= 0.4) return 'warning'
  return 'exception'
}

const getDifficultyClass = (coefficient) => {
  if (coefficient >= 0.8) return 'easy'
  if (coefficient >= 0.5) return 'medium'
  return 'hard'
}

const formatTime = (seconds) => {
  if (seconds < 60) return `${seconds}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const viewDetails = (question) => {
  // TODO: 查看题目详情
  console.log('Viewing question details:', question)
}

const viewAnalysis = (question) => {
  // TODO: 查看详细分析
  console.log('Viewing question analysis:', question)
}

const adjustDifficulty = (question) => {
  // TODO: 调整题目难度
  console.log('Adjusting question difficulty:', question)
}

const exportStatistics = () => {
  // TODO: 导出统计数据
  console.log('Exporting statistics...')
}

// 工具函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.statistics-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.total-answers {
  background-color: #409eff;
}

.stat-icon.correct-rate {
  background-color: #67c23a;
}

.stat-icon.active-students {
  background-color: #e6a23c;
}

.stat-icon.avg-score {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.table-section {
  margin-bottom: 24px;
}

.question-info {
  padding: 8px 0;
}

.question-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.question-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.difficulty-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.difficulty-badge.difficulty-easy {
  background-color: #67c23a;
}

.difficulty-badge.difficulty-medium {
  background-color: #e6a23c;
}

.difficulty-badge.difficulty-hard {
  background-color: #f56c6c;
}

.question-type {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.answer-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.stat-value.correct {
  color: #67c23a !important;
}

.stat-value.incorrect {
  color: #f56c6c !important;
}

.accuracy-info {
  text-align: center;
}

.accuracy-value {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.accuracy-value.excellent {
  color: #67c23a;
}

.accuracy-value.good {
  color: #409eff;
}

.accuracy-value.average {
  color: #e6a23c;
}

.accuracy-value.poor {
  color: #f56c6c;
}

.avg-time {
  font-size: 12px;
  color: #606266;
}

.difficulty-coefficient {
  font-weight: 500;
}

.difficulty-coefficient.easy {
  color: #67c23a;
}

.difficulty-coefficient.medium {
  color: #e6a23c;
}

.difficulty-coefficient.hard {
  color: #f56c6c;
}

.update-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
