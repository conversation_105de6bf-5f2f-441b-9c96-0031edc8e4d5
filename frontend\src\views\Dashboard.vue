<template>
  <div class="dashboard page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>智慧教育管理仪表板</h1>
      <p>欢迎回来！这里是您的系统概览和关键数据统计</p>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon primary">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalUsers) }}</div>
                <div class="stat-label">总用户数</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+12.5%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.activeUsers) }}</div>
                <div class="stat-label">活跃用户</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+8.3%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon warning">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.studyHours) }}</div>
                <div class="stat-label">学习时长(小时)</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+15.7%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon info">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.completionRate }}%</div>
                <div class="stat-label">完成率</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+3.2%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据图表和活动 -->
    <el-row :gutter="24" class="mb-6">
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3>用户增长趋势</h3>
                <p>过去30天的用户注册和活跃情况</p>
              </div>
              <div class="header-right">
                <el-button type="primary" size="small" @click="viewUserGrowthDetails">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder" v-if="chartLoading">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <p>图表加载中...</p>
            </div>
            <div v-else class="chart-content">
              <!-- 这里可以集成 ECharts 或其他图表库 -->
              <div class="mock-chart">
                <div class="chart-bars">
                  <div class="bar" v-for="i in 12" :key="i" :style="{ height: Math.random() * 100 + 20 + 'px' }"></div>
                </div>
                <div class="chart-labels">
                  <span v-for="i in 12" :key="i">{{ i }}月</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="8">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <h3>最近活动</h3>
              <el-button text size="small" @click="viewAllActivities">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="activity-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-avatar">
                <el-avatar :size="40" :src="activity.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
              </div>
              <div class="activity-content">
                <div class="activity-text">{{ activity.text }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
              <div class="activity-type">
                <el-tag :type="getActivityTagType(activity.type)" size="small">
                  {{ activity.type }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="activity-footer" v-if="recentActivities.length === 0">
            <div class="empty-state">
              <el-icon class="empty-icon"><Bell /></el-icon>
              <p class="empty-text">暂无最近活动</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据统计 -->
    <el-row :gutter="24">
      <el-col :xs="24" :lg="12">
        <el-card class="data-stats-card">
          <template #header>
            <div class="card-header">
              <h3>今日数据概览</h3>
              <el-button text size="small" @click="refreshTodayStats">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="data-stats">
            <div class="data-item" v-for="item in todayStats" :key="item.key">
              <div class="data-icon" :class="item.iconClass">
                <el-icon>
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <div class="data-content">
                <div class="data-label">{{ item.label }}</div>
                <div class="data-value">{{ formatNumber(item.value) }}</div>
                <div class="data-change" :class="item.changeType">
                  <el-icon v-if="item.changeType === 'positive'"><ArrowUp /></el-icon>
                  <el-icon v-else-if="item.changeType === 'negative'"><ArrowDown /></el-icon>
                  <span>{{ item.change }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="12">
        <el-card class="system-status-card">
          <template #header>
            <div class="card-header">
              <h3>系统状态监控</h3>
              <el-button text size="small" @click="checkSystemStatus">
                <el-icon><Monitor /></el-icon>
                检查状态
              </el-button>
            </div>
          </template>
          <div class="system-status">
            <div class="status-item" v-for="status in systemStatus" :key="status.key">
              <div class="status-info">
                <div class="status-label">{{ status.label }}</div>
                <div class="status-description">{{ status.description }}</div>
              </div>
              <div class="status-indicator">
                <el-tag :type="status.type" size="large">
                  <el-icon v-if="status.type === 'success'"><CircleCheck /></el-icon>
                  <el-icon v-else-if="status.type === 'warning'"><Warning /></el-icon>
                  <el-icon v-else-if="status.type === 'danger'"><CircleClose /></el-icon>
                  {{ status.status }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User, UserFilled, Clock, TrendCharts, ArrowUp, ArrowDown,
  View, Loading, Bell, Refresh, Monitor, CircleCheck, Warning, CircleClose
} from '@element-plus/icons-vue'

// 响应式数据
const chartLoading = ref(true)

// 统计数据
const statistics = reactive({
  totalUsers: 1234,
  activeUsers: 987,
  studyHours: 12345,
  completionRate: 89.5
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    text: '用户张三完成了《高等数学》第一章学习',
    time: '2024-01-20 15:30:00',
    type: '学习',
    avatar: ''
  },
  {
    id: 2,
    text: '管理员发布了新的课程《Python编程基础》',
    time: '2024-01-20 14:45:00',
    type: '发布',
    avatar: ''
  },
  {
    id: 3,
    text: '用户李四在社区发表了学习心得',
    time: '2024-01-20 13:20:00',
    type: '分享',
    avatar: ''
  },
  {
    id: 4,
    text: '系统完成了每日数据备份',
    time: '2024-01-20 12:00:00',
    type: '系统',
    avatar: ''
  },
  {
    id: 5,
    text: '用户王五完成了心理测评',
    time: '2024-01-20 11:15:00',
    type: '测评',
    avatar: ''
  }
])

// 今日统计数据
const todayStats = ref([
  {
    key: 'newUsers',
    label: '今日新增用户',
    value: 23,
    change: '+15.2%',
    changeType: 'positive',
    icon: 'UserFilled',
    iconClass: 'primary'
  },
  {
    key: 'studySessions',
    label: '今日学习会话',
    value: 156,
    change: '+8.7%',
    changeType: 'positive',
    icon: 'Clock',
    iconClass: 'success'
  },
  {
    key: 'checkIns',
    label: '今日打卡次数',
    value: 89,
    change: '-2.1%',
    changeType: 'negative',
    icon: 'TrendCharts',
    iconClass: 'warning'
  },
  {
    key: 'communityPosts',
    label: '社区新帖子',
    value: 12,
    change: '+25.0%',
    changeType: 'positive',
    icon: 'Bell',
    iconClass: 'info'
  }
])

// 系统状态数据
const systemStatus = ref([
  {
    key: 'database',
    label: '主数据库连接',
    description: '系统主数据库运行正常',
    status: '正常',
    type: 'success'
  },
  {
    key: 'androidDb',
    label: 'Android数据库',
    description: '移动端数据同步延迟',
    status: '待检查',
    type: 'warning'
  },
  {
    key: 'lastSync',
    label: '最后同步时间',
    description: '2024-01-20 10:30:00',
    status: '2小时前',
    type: 'info'
  },
  {
    key: 'uptime',
    label: '系统运行时间',
    description: '系统稳定运行中',
    status: '2天5小时',
    type: 'success'
  }
])

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (time: string): string => {
  const now = new Date()
  const targetTime = new Date(time)
  const diff = now.getTime() - targetTime.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

const getActivityTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    '学习': 'primary',
    '发布': 'success',
    '分享': 'warning',
    '系统': 'info',
    '测评': 'danger'
  }
  return typeMap[type] || 'info'
}

const viewUserGrowthDetails = () => {
  // 跳转到用户管理页面或打开详情对话框
  ElMessage.info('跳转到用户增长详情页面')
}

const viewAllActivities = () => {
  // 跳转到活动日志页面
  ElMessage.info('跳转到活动日志页面')
}

const refreshTodayStats = async () => {
  try {
    // 模拟API调用
    ElMessage.success('今日数据已刷新')
    // TODO: 调用实际API更新数据
  } catch (error) {
    ElMessage.error('刷新数据失败')
  }
}

const checkSystemStatus = async () => {
  try {
    // 模拟系统状态检查
    ElMessage.success('系统状态检查完成')
    // TODO: 调用实际API检查系统状态
  } catch (error) {
    ElMessage.error('系统状态检查失败')
  }
}

const loadDashboardData = async () => {
  try {
    chartLoading.value = true
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1500))

    // TODO: 调用实际API加载仪表板数据
    // const response = await api.getDashboardData()
    // statistics.value = response.statistics
    // recentActivities.value = response.activities

    chartLoading.value = false
  } catch (error) {
    chartLoading.value = false
    ElMessage.error('加载仪表板数据失败')
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})


</script>

<style lang="scss" scoped>
@import "@/styles/design-system.scss";

.dashboard {
  // 图表卡片样式
  .chart-card {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    transition: var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        h3 {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-1) 0;
        }

        p {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-right {
        flex-shrink: 0;
      }
    }

    .chart-container {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;

      .chart-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: var(--text-tertiary);

        .loading-icon {
          font-size: var(--font-size-3xl);
          margin-bottom: var(--spacing-3);
          animation: spin 1s linear infinite;
        }

        p {
          margin: 0;
          font-size: var(--font-size-base);
        }
      }

      .chart-content {
        width: 100%;
        height: 100%;

        .mock-chart {
          height: 100%;
          display: flex;
          flex-direction: column;

          .chart-bars {
            flex: 1;
            display: flex;
            align-items: end;
            gap: var(--spacing-2);
            padding: var(--spacing-4) 0;

            .bar {
              flex: 1;
              background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
              border-radius: var(--radius-sm) var(--radius-sm) 0 0;
              min-height: 20px;
              transition: var(--transition-normal);

              &:hover {
                transform: scaleY(1.1);
                background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-lighter) 100%);
              }
            }
          }

          .chart-labels {
            display: flex;
            justify-content: space-between;
            padding-top: var(--spacing-2);
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }
      }
    }
  }

  // 活动卡片样式
  .activity-card {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    transition: var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0;
      }
    }

    .activity-list {
      max-height: 300px;
      overflow-y: auto;

      .activity-item {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-3);
        padding: var(--spacing-4) 0;
        border-bottom: 1px solid var(--border-light);
        transition: var(--transition-fast);

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: var(--bg-accent);
          margin: 0 calc(-1 * var(--spacing-4));
          padding-left: var(--spacing-4);
          padding-right: var(--spacing-4);
          border-radius: var(--radius-lg);
        }

        .activity-avatar {
          flex-shrink: 0;
        }

        .activity-content {
          flex: 1;
          min-width: 0;

          .activity-text {
            font-size: var(--font-size-sm);
            color: var(--text-primary);
            line-height: var(--line-height-normal);
            margin-bottom: var(--spacing-1);
          }

          .activity-time {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }

        .activity-type {
          flex-shrink: 0;
        }
      }
    }

    .activity-footer {
      .empty-state {
        text-align: center;
        padding: var(--spacing-8);

        .empty-icon {
          font-size: var(--font-size-3xl);
          color: var(--text-quaternary);
          margin-bottom: var(--spacing-3);
        }

        .empty-text {
          font-size: var(--font-size-sm);
          color: var(--text-tertiary);
          margin: 0;
        }
      }
    }
  }

  // 数据统计卡片样式
  .data-stats-card {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    transition: var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .data-stats {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);

      .data-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-4);
        background-color: var(--bg-secondary);
        border-radius: var(--radius-lg);
        transition: var(--transition-fast);

        &:hover {
          background-color: var(--bg-accent);
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }

        .data-icon {
          width: 48px;
          height: 48px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);

          &.primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
          }

          &.success {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
          }

          &.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);
          }

          &.info {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--info-light) 100%);
          }
        }

        .data-content {
          flex: 1;

          .data-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-1);
          }

          .data-value {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .data-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }

            &.neutral {
              color: var(--text-tertiary);
            }
          }
        }
      }
    }
  }

  // 系统状态卡片样式
  .system-status-card {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    transition: var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .system-status {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);

      .status-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-4);
        background-color: var(--bg-secondary);
        border-radius: var(--radius-lg);
        transition: var(--transition-fast);

        &:hover {
          background-color: var(--bg-accent);
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }

        .status-info {
          flex: 1;

          .status-label {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
            display: block;
          }

          .status-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin: 0;
          }
        }

        .status-indicator {
          flex-shrink: 0;

          .el-tag {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-weight: var(--font-weight-medium);
            padding: var(--spacing-2) var(--spacing-3);
            border-radius: var(--radius-lg);
            border: none;

            &.el-tag--success {
              background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.1) 100%);
              color: var(--success-color);
              border: 1px solid rgba(16, 185, 129, 0.2);
            }

            &.el-tag--warning {
              background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
              color: var(--warning-color);
              border: 1px solid rgba(245, 158, 11, 0.2);
            }

            &.el-tag--danger {
              background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.1) 100%);
              color: var(--error-color);
              border: 1px solid rgba(239, 68, 68, 0.2);
            }

            &.el-tag--info {
              background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(96, 165, 250, 0.1) 100%);
              color: var(--info-color);
              border: 1px solid rgba(59, 130, 246, 0.2);
            }
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@include respond-to('md') {
  .dashboard {
    .stats-overview {
      .el-col {
        margin-bottom: var(--spacing-6);
      }
    }

    .chart-card,
    .activity-card,
    .data-stats-card,
    .system-status-card {
      margin-bottom: var(--spacing-6);
    }
  }
}
</style>
