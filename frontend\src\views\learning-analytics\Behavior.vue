<template>
  <div class="behavior-analysis">
    <div class="page-header">
      <h1>学习行为分析</h1>
      <p>深度分析用户学习行为模式，识别学习习惯和优化建议</p>
    </div>

    <!-- 分析控制面板 -->
    <el-card class="control-panel">
      <template #header>
        <div class="panel-header">
          <span>分析控制面板</span>
        </div>
      </template>
      
      <el-row :gutter="20" class="control-row">
        <el-col :span="6">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="updateAnalysis"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="用户群体">
            <el-select v-model="userGroup" placeholder="选择群体" @change="updateAnalysis">
              <el-option label="全部用户" value="all" />
              <el-option label="新用户" value="new" />
              <el-option label="活跃用户" value="active" />
              <el-option label="流失用户" value="churned" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="学科筛选">
            <el-select v-model="subjectFilter" placeholder="选择学科" @change="updateAnalysis">
              <el-option label="全部学科" value="all" />
              <el-option label="数学" value="math" />
              <el-option label="语文" value="chinese" />
              <el-option label="英语" value="english" />
              <el-option label="物理" value="physics" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="分析维度">
            <el-select v-model="analysisDimension" placeholder="选择维度" @change="updateAnalysis">
              <el-option label="时间分布" value="time" />
              <el-option label="内容偏好" value="content" />
              <el-option label="学习路径" value="path" />
              <el-option label="互动行为" value="interaction" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="">
            <el-button type="primary" @click="generateReport" :loading="generating">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
            <el-button @click="exportData" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 行为概览指标 -->
    <el-row :gutter="20" class="behavior-metrics">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon sessions">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ behaviorMetrics.avgSessionDuration }}</div>
              <div class="metric-label">平均会话时长</div>
              <div class="metric-change positive">+12.5%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon frequency">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ behaviorMetrics.avgFrequency }}</div>
              <div class="metric-label">平均学习频次</div>
              <div class="metric-change positive">+8.3%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon engagement">
              <el-icon><Mouse /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ behaviorMetrics.engagementRate }}%</div>
              <div class="metric-label">参与度</div>
              <div class="metric-change negative">-2.1%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon retention">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ behaviorMetrics.retentionRate }}%</div>
              <div class="metric-label">留存率</div>
              <div class="metric-change positive">+5.7%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 行为分析图表 -->
    <el-row :gutter="20" class="behavior-charts">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习时间分布</span>
              <el-select v-model="timeViewType" size="small" style="width: 100px;">
                <el-option label="按小时" value="hour" />
                <el-option label="按天" value="day" />
                <el-option label="按周" value="week" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <div class="time-distribution">
              <div v-for="(item, index) in timeDistribution" :key="index" class="time-item">
                <div class="time-label">{{ item.label }}</div>
                <div class="time-bar">
                  <div 
                    class="time-progress" 
                    :style="{ width: item.percentage + '%', backgroundColor: item.color }"
                  ></div>
                </div>
                <div class="time-value">{{ item.value }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习路径分析</span>
            </div>
          </template>
          <div class="chart-container">
            <div class="learning-path">
              <div class="path-flow">
                <div v-for="(step, index) in learningPath" :key="index" class="path-step">
                  <div class="step-node" :class="step.type">
                    <div class="step-icon">
                      <el-icon><component :is="getStepIcon(step.type)" /></el-icon>
                    </div>
                    <div class="step-info">
                      <div class="step-title">{{ step.title }}</div>
                      <div class="step-stats">{{ step.users }}人 ({{ step.percentage }}%)</div>
                    </div>
                  </div>
                  <div v-if="index < learningPath.length - 1" class="step-connector">
                    <div class="connector-line"></div>
                    <div class="connector-rate">{{ step.conversionRate }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 行为模式分析 -->
    <el-row :gutter="20" class="behavior-patterns">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习偏好分析</span>
            </div>
          </template>
          <div class="preferences-analysis">
            <div v-for="preference in learningPreferences" :key="preference.type" class="preference-item">
              <div class="preference-header">
                <span class="preference-name">{{ preference.name }}</span>
                <span class="preference-percentage">{{ preference.percentage }}%</span>
              </div>
              <div class="preference-bar">
                <div 
                  class="preference-progress" 
                  :style="{ width: preference.percentage + '%', backgroundColor: preference.color }"
                ></div>
              </div>
              <div class="preference-details">
                <span v-for="detail in preference.details" :key="detail" class="preference-tag">
                  {{ detail }}
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>互动行为分析</span>
            </div>
          </template>
          <div class="interaction-analysis">
            <div v-for="interaction in interactionBehaviors" :key="interaction.type" class="interaction-item">
              <div class="interaction-icon" :style="{ backgroundColor: interaction.color }">
                <el-icon><component :is="getInteractionIcon(interaction.type)" /></el-icon>
              </div>
              <div class="interaction-content">
                <div class="interaction-name">{{ interaction.name }}</div>
                <div class="interaction-stats">
                  <span class="interaction-count">{{ interaction.count }}次</span>
                  <span class="interaction-trend" :class="interaction.trend">
                    {{ interaction.trendText }}
                  </span>
                </div>
                <div class="interaction-description">{{ interaction.description }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>异常行为检测</span>
            </div>
          </template>
          <div class="anomaly-detection">
            <div v-for="anomaly in anomalies" :key="anomaly.id" class="anomaly-item">
              <div class="anomaly-level" :class="`level-${anomaly.level}`">
                {{ getAnomalyLevelName(anomaly.level) }}
              </div>
              <div class="anomaly-content">
                <div class="anomaly-title">{{ anomaly.title }}</div>
                <div class="anomaly-description">{{ anomaly.description }}</div>
                <div class="anomaly-time">{{ formatTime(anomaly.detected_at) }}</div>
              </div>
              <div class="anomaly-actions">
                <el-button type="text" size="small" @click="investigateAnomaly(anomaly)">
                  调查
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户行为聚类 -->
    <el-card class="clustering-card">
      <template #header>
        <div class="card-header">
          <span>用户行为聚类</span>
          <el-button type="primary" size="small" @click="runClustering" :loading="clustering">
            <el-icon><Operation /></el-icon>
            重新聚类
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20" class="clustering-results">
        <el-col :span="6" v-for="cluster in userClusters" :key="cluster.id">
          <div class="cluster-item">
            <div class="cluster-header">
              <div class="cluster-icon" :style="{ backgroundColor: cluster.color }">
                <el-icon><component :is="getClusterIcon(cluster.type)" /></el-icon>
              </div>
              <div class="cluster-info">
                <h4>{{ cluster.name }}</h4>
                <p>{{ cluster.userCount }}用户 ({{ cluster.percentage }}%)</p>
              </div>
            </div>
            <div class="cluster-characteristics">
              <div class="characteristic-title">主要特征：</div>
              <ul class="characteristic-list">
                <li v-for="char in cluster.characteristics" :key="char">{{ char }}</li>
              </ul>
            </div>
            <div class="cluster-recommendations">
              <div class="recommendation-title">优化建议：</div>
              <div class="recommendation-text">{{ cluster.recommendation }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 行为预测模型 -->
    <el-card class="prediction-card">
      <template #header>
        <div class="card-header">
          <span>行为预测模型</span>
          <el-select v-model="predictionModel" size="small" style="width: 150px;">
            <el-option label="流失预测" value="churn" />
            <el-option label="成绩预测" value="performance" />
            <el-option label="参与度预测" value="engagement" />
          </el-select>
        </div>
      </template>
      
      <div class="prediction-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="model-metrics">
              <h4>模型性能指标</h4>
              <div class="metrics-grid">
                <div class="metric-item">
                  <span class="metric-name">准确率</span>
                  <span class="metric-value">{{ modelMetrics.accuracy }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-name">精确率</span>
                  <span class="metric-value">{{ modelMetrics.precision }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-name">召回率</span>
                  <span class="metric-value">{{ modelMetrics.recall }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-name">F1分数</span>
                  <span class="metric-value">{{ modelMetrics.f1Score }}%</span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="prediction-results">
              <h4>预测结果</h4>
              <div class="results-list">
                <div v-for="result in predictionResults" :key="result.id" class="result-item">
                  <div class="result-user">用户ID: {{ result.userId }}</div>
                  <div class="result-prediction">
                    <span class="prediction-label">{{ result.label }}</span>
                    <span class="prediction-probability">{{ (result.probability * 100).toFixed(1) }}%</span>
                  </div>
                  <div class="result-factors">
                    主要因素: {{ result.factors.join(', ') }}
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document, Download, Clock, Calendar, Mouse, UserFilled, Operation,
  Reading, VideoPlay, ChatDotRound, Star, Warning, TrendCharts
} from '@element-plus/icons-vue'

// 响应式数据
const dateRange = ref(['2024-01-01', '2024-01-31'])
const userGroup = ref('all')
const subjectFilter = ref('all')
const analysisDimension = ref('time')
const timeViewType = ref('hour')
const predictionModel = ref('churn')
const generating = ref(false)
const exporting = ref(false)
const clustering = ref(false)

// 行为指标
const behaviorMetrics = reactive({
  avgSessionDuration: '45.2分钟',
  avgFrequency: '4.8次/周',
  engagementRate: 78.5,
  retentionRate: 85.3
})

// 时间分布数据
const timeDistribution = ref([
  { label: '08:00-10:00', percentage: 25, value: '1,234人', color: '#409eff' },
  { label: '10:00-12:00', percentage: 35, value: '1,723人', color: '#67c23a' },
  { label: '14:00-16:00', percentage: 30, value: '1,478人', color: '#e6a23c' },
  { label: '16:00-18:00', percentage: 20, value: '987人', color: '#f56c6c' },
  { label: '19:00-21:00', percentage: 40, value: '1,967人', color: '#909399' }
])

// 学习路径数据
const learningPath = ref([
  { title: '登录系统', users: 2847, percentage: 100, type: 'start', conversionRate: 89 },
  { title: '浏览内容', users: 2534, percentage: 89, type: 'browse', conversionRate: 76 },
  { title: '开始学习', users: 1926, percentage: 68, type: 'learn', conversionRate: 82 },
  { title: '完成练习', users: 1579, percentage: 55, type: 'practice', conversionRate: 71 },
  { title: '提交作业', users: 1121, percentage: 39, type: 'submit', conversionRate: 0 }
])

// 学习偏好数据
const learningPreferences = ref([
  {
    type: 'visual',
    name: '视觉学习',
    percentage: 45,
    color: '#409eff',
    details: ['图表', '视频', '图像']
  },
  {
    type: 'auditory',
    name: '听觉学习',
    percentage: 30,
    color: '#67c23a',
    details: ['音频', '讲解', '讨论']
  },
  {
    type: 'kinesthetic',
    name: '动手学习',
    percentage: 25,
    color: '#e6a23c',
    details: ['实验', '操作', '练习']
  }
])

// 互动行为数据
const interactionBehaviors = ref([
  {
    type: 'click',
    name: '点击行为',
    count: 15672,
    trend: 'up',
    trendText: '+12.5%',
    color: '#409eff',
    description: '用户点击内容的频率'
  },
  {
    type: 'scroll',
    name: '滚动行为',
    count: 8934,
    trend: 'up',
    trendText: '+8.3%',
    color: '#67c23a',
    description: '页面滚动深度和速度'
  },
  {
    type: 'search',
    name: '搜索行为',
    count: 3456,
    trend: 'down',
    trendText: '-5.2%',
    color: '#e6a23c',
    description: '搜索功能使用情况'
  },
  {
    type: 'share',
    name: '分享行为',
    count: 1234,
    trend: 'up',
    trendText: '+18.7%',
    color: '#f56c6c',
    description: '内容分享和推荐'
  }
])

// 异常行为数据
const anomalies = ref([
  {
    id: 1,
    level: 'high',
    title: '异常登录模式',
    description: '用户U123456在凌晨时段频繁登录',
    detected_at: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    level: 'medium',
    title: '学习时长异常',
    description: '平均学习时长较上周下降50%',
    detected_at: '2024-01-15 13:45:00'
  }
])

// 用户聚类数据
const userClusters = ref([
  {
    id: 1,
    name: '勤奋学习者',
    userCount: 856,
    percentage: 30,
    type: 'diligent',
    color: '#67c23a',
    characteristics: ['学习时间长', '完成率高', '互动积极'],
    recommendation: '提供更多挑战性内容，设置进阶目标'
  },
  {
    id: 2,
    name: '随意浏览者',
    userCount: 1139,
    percentage: 40,
    type: 'casual',
    color: '#409eff',
    characteristics: ['浏览时间短', '跳跃性强', '兴趣广泛'],
    recommendation: '优化内容推荐，提高内容吸引力'
  },
  {
    id: 3,
    name: '目标导向者',
    userCount: 569,
    percentage: 20,
    type: 'goal',
    color: '#e6a23c',
    characteristics: ['目标明确', '效率较高', '重复学习'],
    recommendation: '提供个性化学习路径，优化学习计划'
  },
  {
    id: 4,
    name: '潜在流失者',
    userCount: 285,
    percentage: 10,
    type: 'risk',
    color: '#f56c6c',
    characteristics: ['活跃度下降', '完成率低', '登录频次减少'],
    recommendation: '及时干预，提供激励措施和个性化关怀'
  }
])

// 模型性能指标
const modelMetrics = reactive({
  accuracy: 87.5,
  precision: 84.2,
  recall: 89.1,
  f1Score: 86.6
})

// 预测结果
const predictionResults = ref([
  {
    id: 1,
    userId: 'U123456',
    label: '高风险流失',
    probability: 0.85,
    factors: ['登录频次下降', '学习时长减少', '互动减少']
  },
  {
    id: 2,
    userId: 'U789012',
    label: '中风险流失',
    probability: 0.62,
    factors: ['完成率下降', '访问间隔增长']
  },
  {
    id: 3,
    userId: 'U345678',
    label: '低风险流失',
    probability: 0.23,
    factors: ['学习模式稳定', '参与度正常']
  }
])

// 方法
const updateAnalysis = () => {
  // TODO: 更新分析数据
  console.log('Updating behavior analysis...')
}

const generateReport = async () => {
  generating.value = true
  try {
    // TODO: 生成分析报告
    console.log('Generating behavior report...')
    ElMessage.success('报告生成成功')
  } catch (error) {
    ElMessage.error('报告生成失败')
  } finally {
    generating.value = false
  }
}

const exportData = async () => {
  exporting.value = true
  try {
    // TODO: 导出数据
    console.log('Exporting behavior data...')
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  } finally {
    exporting.value = false
  }
}

const runClustering = async () => {
  clustering.value = true
  try {
    // TODO: 运行聚类算法
    console.log('Running user clustering...')
    ElMessage.success('聚类分析完成')
  } catch (error) {
    ElMessage.error('聚类分析失败')
  } finally {
    clustering.value = false
  }
}

const investigateAnomaly = (anomaly: any) => {
  // TODO: 调查异常行为
  console.log('Investigating anomaly...', anomaly)
}

const getStepIcon = (type: string) => {
  const icons = {
    start: 'UserFilled',
    browse: 'Reading',
    learn: 'VideoPlay',
    practice: 'Mouse',
    submit: 'Document'
  }
  return icons[type] || 'Document'
}

const getInteractionIcon = (type: string) => {
  const icons = {
    click: 'Mouse',
    scroll: 'TrendCharts',
    search: 'Search',
    share: 'Star'
  }
  return icons[type] || 'Mouse'
}

const getClusterIcon = (type: string) => {
  const icons = {
    diligent: 'Star',
    casual: 'Reading',
    goal: 'TrendCharts',
    risk: 'Warning'
  }
  return icons[type] || 'UserFilled'
}

const getAnomalyLevelName = (level: string) => {
  const levels = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return levels[level] || level
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 加载数据
const loadData = async () => {
  // TODO: 调用API获取行为分析数据
  console.log('Loading behavior analysis data...')
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.behavior-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.control-panel {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.panel-header {
  font-weight: 600;
  color: #303133;
}

.control-row {
  margin-top: 16px;
}

.behavior-metrics {
  margin-bottom: 24px;
}

.metric-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.metric-icon.sessions {
  background-color: #409eff;
}

.metric-icon.frequency {
  background-color: #67c23a;
}

.metric-icon.engagement {
  background-color: #e6a23c;
}

.metric-icon.retention {
  background-color: #f56c6c;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin: 4px 0;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #67c23a;
}

.metric-change.negative {
  color: #f56c6c;
}

.behavior-charts {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  padding: 20px 0;
}

.time-distribution {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.time-label {
  min-width: 100px;
  font-weight: 500;
  color: #303133;
}

.time-bar {
  flex: 1;
  height: 12px;
  background-color: #f0f2f5;
  border-radius: 6px;
  overflow: hidden;
}

.time-progress {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s;
}

.time-value {
  min-width: 80px;
  font-size: 12px;
  color: #606266;
  text-align: right;
}

.learning-path {
  padding: 20px 0;
}

.path-flow {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.path-step {
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-node {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border-left: 4px solid #409eff;
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-info {
  flex: 1;
}

.step-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.step-stats {
  font-size: 12px;
  color: #606266;
}

.step-connector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.connector-line {
  width: 2px;
  height: 20px;
  background-color: #e4e7ed;
}

.connector-rate {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.behavior-patterns {
  margin-bottom: 24px;
}

.preferences-analysis {
  padding: 10px 0;
}

.preference-item {
  margin-bottom: 20px;
}

.preference-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.preference-name {
  font-weight: 500;
  color: #303133;
}

.preference-percentage {
  font-size: 12px;
  color: #909399;
}

.preference-bar {
  height: 8px;
  background-color: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.preference-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.preference-details {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.preference-tag {
  font-size: 11px;
  color: #606266;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.interaction-analysis {
  padding: 10px 0;
}

.interaction-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.interaction-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.interaction-content {
  flex: 1;
}

.interaction-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.interaction-stats {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.interaction-count {
  font-size: 12px;
  color: #606266;
}

.interaction-trend {
  font-size: 12px;
  font-weight: 500;
}

.interaction-trend.up {
  color: #67c23a;
}

.interaction-trend.down {
  color: #f56c6c;
}

.interaction-description {
  font-size: 11px;
  color: #909399;
}

.anomaly-detection {
  padding: 10px 0;
}

.anomaly-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background-color: #fef0f0;
  border-radius: 6px;
  border-left: 4px solid #f56c6c;
}

.anomaly-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  min-width: 40px;
  text-align: center;
}

.anomaly-level.level-high {
  background-color: #f56c6c;
}

.anomaly-level.level-medium {
  background-color: #e6a23c;
}

.anomaly-level.level-low {
  background-color: #67c23a;
}

.anomaly-content {
  flex: 1;
}

.anomaly-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.anomaly-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.anomaly-time {
  font-size: 11px;
  color: #909399;
}

.clustering-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.clustering-results {
  margin-top: 16px;
}

.cluster-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  height: 100%;
}

.cluster-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.cluster-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.cluster-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.cluster-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.cluster-characteristics {
  margin-bottom: 16px;
}

.characteristic-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.characteristic-list {
  margin: 0;
  padding-left: 16px;
  font-size: 12px;
  color: #606266;
}

.cluster-recommendations {
  margin-bottom: 0;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.recommendation-text {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.prediction-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.prediction-content {
  padding: 20px 0;
}

.model-metrics h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.metric-name {
  font-size: 14px;
  color: #606266;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.prediction-results h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.result-item {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.result-user {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.result-prediction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.prediction-label {
  font-size: 12px;
  color: #606266;
}

.prediction-probability {
  font-size: 12px;
  font-weight: 600;
  color: #f56c6c;
}

.result-factors {
  font-size: 11px;
  color: #909399;
}
</style>
