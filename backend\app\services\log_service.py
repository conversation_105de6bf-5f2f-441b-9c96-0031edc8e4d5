"""
日志服务模块
提供日志创建、查询、管理等功能
"""
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, distinct
import logging

from app.models.admin import SystemLog, LoginLog
from app.models.settings import SystemSettings

logger = logging.getLogger(__name__)

class LogService:
    """日志服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_system_log(
        self,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        action: str = "",
        module: str = "SYSTEM",
        level: str = "INFO",
        message: str = "",
        details: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> SystemLog:
        """创建系统日志记录"""
        try:
            log = SystemLog(
                user_id=user_id,
                username=username,
                action=action,
                module=module,
                level=level,
                message=message,
                details=details,
                ip_address=ip_address
            )
            
            self.db.add(log)
            self.db.commit()
            self.db.refresh(log)
            
            return log
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建系统日志失败: {e}")
            raise
    
    def get_system_logs(
        self,
        page: int = 1,
        page_size: int = 20,
        level: Optional[str] = None,
        module: Optional[str] = None,
        user_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        keyword: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取系统日志列表"""
        try:
            # 构建查询
            query = self.db.query(SystemLog)
            
            # 应用过滤条件
            if level:
                query = query.filter(SystemLog.level == level)
            
            if module:
                query = query.filter(SystemLog.module == module)
            
            if user_id:
                query = query.filter(SystemLog.user_id == user_id)
            
            if start_date:
                query = query.filter(SystemLog.created_at >= start_date)
            
            if end_date:
                query = query.filter(SystemLog.created_at <= end_date)
            
            if keyword:
                search_filter = or_(
                    SystemLog.message.contains(keyword),
                    SystemLog.action.contains(keyword),
                    SystemLog.username.contains(keyword) if SystemLog.username else False
                )
                query = query.filter(search_filter)
            
            # 排序
            query = query.order_by(desc(SystemLog.created_at))
            
            # 计算总数
            total = query.count()
            
            # 分页
            offset = (page - 1) * page_size
            logs = query.offset(offset).limit(page_size).all()
            
            # 计算总页数
            pages = (total + page_size - 1) // page_size
            
            return {
                "items": logs,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": pages
            }
        except Exception as e:
            logger.error(f"获取系统日志失败: {e}")
            raise
    
    def get_system_log_by_id(self, log_id: str) -> Optional[SystemLog]:
        """根据ID获取系统日志"""
        try:
            return self.db.query(SystemLog).filter(SystemLog.id == log_id).first()
        except Exception as e:
            logger.error(f"获取系统日志详情失败: {e}")
            raise
    
    def delete_system_log(self, log_id: str) -> bool:
        """删除系统日志"""
        try:
            log = self.db.query(SystemLog).filter(SystemLog.id == log_id).first()
            if log:
                self.db.delete(log)
                self.db.commit()
                return True
            return False
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除系统日志失败: {e}")
            raise
    
    def batch_delete_system_logs(self, log_ids: List[str]) -> int:
        """批量删除系统日志"""
        try:
            logs = self.db.query(SystemLog).filter(SystemLog.id.in_(log_ids)).all()
            deleted_count = len(logs)
            
            for log in logs:
                self.db.delete(log)
            
            self.db.commit()
            return deleted_count
        except Exception as e:
            self.db.rollback()
            logger.error(f"批量删除系统日志失败: {e}")
            raise
    
    def cleanup_old_logs(self, days: int = 30, level: Optional[str] = None) -> int:
        """清理过期日志"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            query = self.db.query(SystemLog).filter(SystemLog.created_at < cutoff_date)
            
            if level:
                query = query.filter(SystemLog.level == level)
            
            count = query.count()
            query.delete(synchronize_session=False)
            self.db.commit()
            
            return count
        except Exception as e:
            self.db.rollback()
            logger.error(f"清理过期日志失败: {e}")
            raise
    
    def get_log_modules(self) -> List[str]:
        """获取所有日志模块"""
        try:
            modules = self.db.query(distinct(SystemLog.module)).filter(
                SystemLog.module.isnot(None)
            ).all()
            
            return [module[0] for module in modules if module[0]]
        except Exception as e:
            logger.error(f"获取日志模块失败: {e}")
            raise
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            from sqlalchemy import func
            
            # 总日志数
            total_logs = self.db.query(SystemLog).count()
            
            # 按级别统计
            level_stats = self.db.query(
                SystemLog.level,
                func.count(SystemLog.id).label('count')
            ).group_by(SystemLog.level).all()
            
            # 按模块统计
            module_stats = self.db.query(
                SystemLog.module,
                func.count(SystemLog.id).label('count')
            ).group_by(SystemLog.module).limit(10).all()
            
            # 最近24小时日志数
            yesterday = datetime.now() - timedelta(hours=24)
            recent_logs = self.db.query(SystemLog).filter(
                SystemLog.created_at >= yesterday
            ).count()
            
            return {
                "total_logs": total_logs,
                "recent_logs_24h": recent_logs,
                "level_distribution": {
                    level: count for level, count in level_stats
                },
                "module_distribution": {
                    module: count for module, count in module_stats
                }
            }
        except Exception as e:
            logger.error(f"获取日志统计失败: {e}")
            raise
    
    def get_realtime_logs(
        self,
        level: Optional[str] = None,
        module: Optional[str] = None,
        limit: int = 50
    ) -> List[SystemLog]:
        """获取实时日志"""
        try:
            query = self.db.query(SystemLog)
            
            if level and level != 'ALL':
                query = query.filter(SystemLog.level == level)
            
            if module:
                query = query.filter(SystemLog.module == module)
            
            return query.order_by(desc(SystemLog.created_at)).limit(limit).all()
        except Exception as e:
            logger.error(f"获取实时日志失败: {e}")
            raise

def get_log_service(db: Session) -> LogService:
    """获取日志服务实例"""
    return LogService(db)
