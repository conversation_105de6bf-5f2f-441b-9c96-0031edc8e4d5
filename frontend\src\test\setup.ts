/**
 * 测试环境设置
 */
import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// 模拟 Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElNotification: vi.fn(),
  ElLoading: {
    service: vi.fn(() => ({
      close: vi.fn()
    }))
  },
  ElMessageBox: {
    confirm: vi.fn(),
    alert: vi.fn(),
    prompt: vi.fn()
  }
}))

// 模拟 Vue Router
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }),
  useRoute: () => ({
    path: '/',
    name: 'Home',
    params: {},
    query: {},
    meta: {}
  }),
  createRouter: vi.fn(),
  createWebHistory: vi.fn(),
  RouterView: {
    name: 'RouterView',
    render: () => null
  },
  RouterLink: {
    name: 'RouterLink',
    props: ['to'],
    render: (props: any) => props.to
  }
}))

// 模拟 Pinia
vi.mock('pinia', () => ({
  createPinia: vi.fn(),
  defineStore: vi.fn(),
  storeToRefs: vi.fn()
}))

// 模拟 localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
}

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// 模拟 window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn()
  },
  writable: true
})

// 模拟 fetch
global.fetch = vi.fn()

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// 模拟 MutationObserver
global.MutationObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  takeRecords: vi.fn()
}))

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
})

// 模拟 getComputedStyle
Object.defineProperty(window, 'getComputedStyle', {
  value: vi.fn().mockImplementation(() => ({
    getPropertyValue: vi.fn().mockReturnValue(''),
    setProperty: vi.fn()
  }))
})

// 模拟 scrollTo
Object.defineProperty(window, 'scrollTo', {
  value: vi.fn()
})

// 模拟 requestAnimationFrame
Object.defineProperty(window, 'requestAnimationFrame', {
  value: vi.fn().mockImplementation(cb => setTimeout(cb, 16))
})

Object.defineProperty(window, 'cancelAnimationFrame', {
  value: vi.fn().mockImplementation(id => clearTimeout(id))
})

// 模拟 console 方法（在测试中静默）
const consoleMethods = ['log', 'warn', 'error', 'info', 'debug']
consoleMethods.forEach(method => {
  vi.spyOn(console, method as keyof Console).mockImplementation(() => {})
})

// 全局测试配置
config.global.stubs = {
  // 存根组件
  'el-icon': true,
  'el-button': true,
  'el-input': true,
  'el-form': true,
  'el-form-item': true,
  'el-card': true,
  'el-table': true,
  'el-table-column': true,
  'el-pagination': true,
  'el-dialog': true,
  'el-drawer': true,
  'el-menu': true,
  'el-menu-item': true,
  'el-submenu': true,
  'router-link': true,
  'router-view': true
}

// 全局混入
config.global.mixins = [
  {
    methods: {
      $t: (key: string) => key, // 模拟国际化
      $tc: (key: string) => key,
      $te: () => true
    }
  }
]

// 测试工具函数
export const createMockStore = (initialState = {}) => {
  return {
    state: { ...initialState },
    commit: vi.fn(),
    dispatch: vi.fn(),
    getters: {}
  }
}

export const createMockRouter = (currentRoute = {}) => {
  return {
    currentRoute: { value: { path: '/', ...currentRoute } },
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }
}

// 清理函数
export const cleanup = () => {
  vi.clearAllMocks()
  localStorageMock.clear()
  sessionStorageMock.clear()
}

// 在每个测试后清理
afterEach(() => {
  cleanup()
})

// 全局错误处理
process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Rejection:', reason)
})

// 设置测试超时
vi.setConfig({
  testTimeout: 10000,
  hookTimeout: 10000
})
