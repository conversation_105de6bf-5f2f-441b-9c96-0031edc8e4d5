"""
管理员用户相关的数据模型
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field
from app.models.admin import UserRole

class AdminUserBase(BaseModel):
    """管理员用户基础模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="真实姓名")
    role: UserRole = Field(UserRole.VIEWER, description="用户角色")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否超级用户")
    phone: Optional[str] = Field(None, max_length=20, description="手机号码")
    department: Optional[str] = Field(None, max_length=100, description="部门")
    position: Optional[str] = Field(None, max_length=100, description="职位")
    bio: Optional[str] = Field(None, description="个人简介")
    avatar_url: Optional[str] = Field(None, description="头像URL")

class AdminUserCreate(AdminUserBase):
    """创建管理员用户"""
    password: str = Field(..., min_length=6, description="密码")

class AdminUserUpdate(BaseModel):
    """更新管理员用户"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="真实姓名")
    role: Optional[UserRole] = Field(None, description="用户角色")
    is_active: Optional[bool] = Field(None, description="是否激活")
    is_superuser: Optional[bool] = Field(None, description="是否超级用户")
    phone: Optional[str] = Field(None, max_length=20, description="手机号码")
    department: Optional[str] = Field(None, max_length=100, description="部门")
    position: Optional[str] = Field(None, max_length=100, description="职位")
    bio: Optional[str] = Field(None, description="个人简介")
    avatar_url: Optional[str] = Field(None, description="头像URL")

class AdminUserResponse(BaseModel):
    """管理员用户响应"""
    id: str
    username: str
    email: str
    full_name: Optional[str] = None
    role: UserRole
    is_active: bool
    is_superuser: bool
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    last_login: Optional[datetime] = None
    login_count: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class AdminUserListResponse(BaseModel):
    """管理员用户列表响应"""
    items: List[AdminUserResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")

class AdminUserPasswordChange(BaseModel):
    """管理员用户密码修改"""
    admin_id: str
    new_password: str = Field(..., min_length=6, description="新密码")

class AdminUserStats(BaseModel):
    """管理员用户统计"""
    total_admins: int
    active_admins: int
    inactive_admins: int
    superuser_count: int
    role_distribution: dict
    recent_logins: int
