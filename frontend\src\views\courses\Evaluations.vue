<template>
  <div class="course-evaluations">
    <div class="page-header">
      <h1>课程评价管理</h1>
      <p>管理课程评分和用户反馈</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-reviews">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalReviews }}</div>
              <div class="stat-label">总评价数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon avg-rating">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.avgRating }}</div>
              <div class="stat-label">平均评分</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon positive-rate">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ (statistics.positiveRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">好评率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending-reviews">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.pendingReviews }}</div>
              <div class="stat-label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="评价列表" name="reviews">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button 
              type="success" 
              :disabled="selectedReviews.length === 0"
              @click="batchApprove"
            >
              批量通过
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedReviews.length === 0"
              @click="batchReject"
            >
              批量拒绝
            </el-button>
            <el-button @click="exportReviews">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户或课程"
              style="width: 200px"
              clearable
              @change="loadReviews"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="ratingFilter" placeholder="评分筛选" style="width: 120px" @change="loadReviews">
              <el-option label="全部评分" value="" />
              <el-option label="5星" value="5" />
              <el-option label="4星" value="4" />
              <el-option label="3星" value="3" />
              <el-option label="2星" value="2" />
              <el-option label="1星" value="1" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadReviews">
              <el-option label="全部状态" value="" />
              <el-option label="已通过" value="approved" />
              <el-option label="待审核" value="pending" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </div>
        </div>

        <!-- 评价列表 -->
        <el-table
          :data="reviews"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="用户信息" width="150">
            <template #default="{ row }">
              <div class="user-info">
                <div class="user-avatar">
                  <img v-if="row.user_avatar" :src="row.user_avatar" :alt="row.user_name" />
                  <div v-else class="avatar-placeholder">{{ row.user_name.charAt(0) }}</div>
                </div>
                <div class="user-name">{{ row.user_name }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="课程信息" min-width="200">
            <template #default="{ row }">
              <div class="course-info">
                <div class="course-title">{{ row.course_title }}</div>
                <div class="course-instructor">讲师: {{ row.instructor }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="评分" width="120">
            <template #default="{ row }">
              <div class="rating-info">
                <el-rate v-model="row.rating" disabled size="small" />
                <span class="rating-text">{{ row.rating }}分</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="评价内容" min-width="300">
            <template #default="{ row }">
              <div class="review-content">
                <div class="review-text">{{ row.content }}</div>
                <div class="review-tags" v-if="row.tags && row.tags.length">
                  <el-tag 
                    v-for="tag in row.tags" 
                    :key="tag" 
                    size="small" 
                    class="review-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="评价时间" width="120">
            <template #default="{ row }">
              <span class="review-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="viewReview(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button
                type="link"
                size="small"
                v-if="row.status === 'pending'"
                @click="approveReview(row)"
              >
                <el-icon><Check /></el-icon>
                通过
              </el-button>
              <el-button
                type="link"
                size="small"
                v-if="row.status === 'pending'"
                @click="rejectReview(row)"
              >
                <el-icon><Close /></el-icon>
                拒绝
              </el-button>
              <el-button type="link" size="small" @click="replyReview(row)">
                <el-icon><ChatDotRound /></el-icon>
                回复
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deleteReview(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadReviews"
            @current-change="loadReviews"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="analytics">
        <!-- 统计图表区域 -->
        <div class="analytics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>评分分布</span>
                    <el-select v-model="timeRange" size="small" style="width: 120px;">
                      <el-option label="最近7天" value="7d" />
                      <el-option label="最近30天" value="30d" />
                      <el-option label="最近90天" value="90d" />
                    </el-select>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加评分分布图表 -->
                  <div class="chart-placeholder">评分分布图表</div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>评价趋势</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加评价趋势图表 -->
                  <div class="chart-placeholder">评价趋势图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>课程评价排行</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加课程评价排行图表 -->
                  <div class="chart-placeholder">课程评价排行图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 查看评价详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="评价详情"
      width="600px"
    >
      <div v-if="selectedReview" class="review-detail">
        <div class="detail-section">
          <h4>用户信息</h4>
          <div class="user-detail">
            <div class="user-avatar-large">
              <img v-if="selectedReview.user_avatar" :src="selectedReview.user_avatar" :alt="selectedReview.user_name" />
              <div v-else class="avatar-placeholder-large">{{ selectedReview.user_name.charAt(0) }}</div>
            </div>
            <div class="user-info-detail">
              <div class="user-name-large">{{ selectedReview.user_name }}</div>
              <div class="user-id">用户ID: {{ selectedReview.user_id }}</div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>课程信息</h4>
          <div class="course-detail">
            <div class="course-title-large">{{ selectedReview.course_title }}</div>
            <div class="course-instructor-large">讲师: {{ selectedReview.instructor }}</div>
          </div>
        </div>

        <div class="detail-section">
          <h4>评价内容</h4>
          <div class="rating-detail">
            <el-rate v-model="selectedReview.rating" disabled />
            <span class="rating-text-large">{{ selectedReview.rating }}分</span>
          </div>
          <div class="content-detail">{{ selectedReview.content }}</div>
          <div class="tags-detail" v-if="selectedReview.tags && selectedReview.tags.length">
            <el-tag 
              v-for="tag in selectedReview.tags" 
              :key="tag" 
              size="small" 
              class="detail-tag"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>

        <div class="detail-section">
          <h4>评价时间</h4>
          <div class="time-detail">{{ formatTime(selectedReview.created_at) }}</div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="replyReview(selectedReview)">回复评价</el-button>
      </template>
    </el-dialog>

    <!-- 回复评价对话框 -->
    <el-dialog
      v-model="showReplyDialog"
      title="回复评价"
      width="500px"
    >
      <el-form :model="replyForm" label-width="80px">
        <el-form-item label="回复内容">
          <el-input v-model="replyForm.content" type="textarea" rows="4" placeholder="请输入回复内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showReplyDialog = false">取消</el-button>
        <el-button type="primary" @click="submitReply">发送回复</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Star, TrendCharts, Clock, Download, Search, View, Check, Close,
  ChatDotRound, Delete
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('reviews')
const loading = ref(false)
const selectedReviews = ref([])
const showDetailDialog = ref(false)
const showReplyDialog = ref(false)
const selectedReview = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const ratingFilter = ref('')
const statusFilter = ref('')
const timeRange = ref('30d')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = reactive({
  totalReviews: 1234,
  avgRating: 4.6,
  positiveRate: 0.89,
  pendingReviews: 23
})

// 评价列表
const reviews = ref([
  {
    id: '1',
    user_name: '张小明',
    user_id: 'U001',
    user_avatar: '',
    course_title: 'Vue.js 3.0 完整教程',
    instructor: '张老师',
    rating: 5,
    content: '课程内容非常详细，讲师讲解清晰，学到了很多实用的知识。强烈推荐！',
    tags: ['内容丰富', '讲解清晰', '实用性强'],
    status: 'approved',
    created_at: '2024-01-20 14:30:00'
  },
  {
    id: '2',
    user_name: '李小红',
    user_id: 'U002',
    user_avatar: '',
    course_title: 'Python 数据分析实战',
    instructor: '李老师',
    rating: 4,
    content: '课程质量不错，但是有些地方讲得太快了，希望能慢一点。',
    tags: ['质量不错', '节奏偏快'],
    status: 'pending',
    created_at: '2024-01-19 16:45:00'
  }
])

// 回复表单
const replyForm = reactive({
  content: ''
})

// 方法
const loadReviews = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取评价列表
    console.log('Loading reviews...')
  } catch (error) {
    ElMessage.error('加载评价列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedReviews.value = selection
}

const getStatusName = (status) => {
  const statuses = {
    approved: '已通过',
    pending: '待审核',
    rejected: '已拒绝'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    approved: 'success',
    pending: 'warning',
    rejected: 'danger'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const viewReview = (review) => {
  selectedReview.value = review
  showDetailDialog.value = true
}

const approveReview = async (review) => {
  // TODO: 通过评价
  console.log('Approving review:', review)
  ElMessage.success('评价已通过')
}

const rejectReview = async (review) => {
  // TODO: 拒绝评价
  console.log('Rejecting review:', review)
  ElMessage.success('评价已拒绝')
}

const replyReview = (review) => {
  selectedReview.value = review
  showReplyDialog.value = true
}

const deleteReview = async (review) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评价吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting review:', review)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const submitReply = async () => {
  try {
    // TODO: 实现回复逻辑
    console.log('Submitting reply:', replyForm.content)
    showReplyDialog.value = false
    replyForm.content = ''
    ElMessage.success('回复发送成功')
  } catch (error) {
    ElMessage.error('回复发送失败')
  }
}

const batchApprove = async () => {
  // TODO: 实现批量通过逻辑
  console.log('Batch approving reviews...', selectedReviews.value)
}

const batchReject = async () => {
  // TODO: 实现批量拒绝逻辑
  console.log('Batch rejecting reviews...', selectedReviews.value)
}

const exportReviews = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting reviews...')
}

onMounted(() => {
  loadReviews()
})
</script>

<style scoped>
.course-evaluations {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.total-reviews {
  background-color: #409eff;
}

.stat-icon.avg-rating {
  background-color: #67c23a;
}

.stat-icon.positive-rate {
  background-color: #e6a23c;
}

.stat-icon.pending-reviews {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.user-name {
  font-size: 12px;
  color: #303133;
  text-align: center;
}

.course-info {
  padding: 4px 0;
}

.course-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.course-instructor {
  font-size: 12px;
  color: #606266;
}

.rating-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.rating-text {
  font-size: 12px;
  color: #606266;
}

.review-content {
  padding: 4px 0;
}

.review-text {
  font-size: 13px;
  color: #303133;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.review-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.review-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
  font-size: 11px;
}

.review-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.analytics-section {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.review-detail {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.user-detail {
  display: flex;
  gap: 16px;
  align-items: center;
}

.user-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-large {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 24px;
}

.user-name-large {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.user-id {
  font-size: 14px;
  color: #909399;
}

.course-title-large {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.course-instructor-large {
  font-size: 14px;
  color: #606266;
}

.rating-detail {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.rating-text-large {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

.content-detail {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  margin-bottom: 12px;
}

.tags-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.detail-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.time-detail {
  font-size: 14px;
  color: #606266;
}

.danger {
  color: #f56c6c;
}
</style>
