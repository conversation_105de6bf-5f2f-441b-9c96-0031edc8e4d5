<template>
  <div class="papers-management">
    <div class="page-header">
      <h1>试卷管理</h1>
      <p>管理试卷组卷、发布设置、考试安排和成绩统计</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="试卷列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              创建试卷
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedPapers.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedPapers.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
            <el-button @click="exportPapers">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索试卷名称"
              style="width: 200px"
              clearable
              @change="loadPapers"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="subjectFilter" placeholder="学科筛选" style="width: 120px" @change="loadPapers">
              <el-option label="全部学科" value="" />
              <el-option label="语文" value="chinese" />
              <el-option label="数学" value="math" />
              <el-option label="英语" value="english" />
              <el-option label="物理" value="physics" />
              <el-option label="化学" value="chemistry" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadPapers">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>

        <!-- 试卷列表 -->
        <el-table
          :data="papers"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="试卷信息" min-width="300">
            <template #default="{ row }">
              <div class="paper-info">
                <div class="paper-title">{{ row.title }}</div>
                <div class="paper-description">{{ row.description }}</div>
                <div class="paper-meta">
                  <el-tag size="small" :type="getSubjectTagType(row.subject)">
                    {{ getSubjectName(row.subject) }}
                  </el-tag>
                  <span class="difficulty-badge" :class="`difficulty-${row.difficulty}`">
                    {{ getDifficultyName(row.difficulty) }}
                  </span>
                  <span class="duration-info">{{ row.duration }}分钟</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="题目统计" width="150">
            <template #default="{ row }">
              <div class="question-stats">
                <div class="stat-item">
                  <span class="stat-label">总题数:</span>
                  <span class="stat-value">{{ row.total_questions }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">总分:</span>
                  <span class="stat-value total-score">{{ row.total_score }}分</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">及格分:</span>
                  <span class="stat-value pass-score">{{ row.pass_score }}分</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="考试数据" width="120">
            <template #default="{ row }">
              <div class="exam-stats">
                <div class="stat-item">
                  <span class="stat-label">参考:</span>
                  <span class="stat-value">{{ row.exam_count }}次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">平均分:</span>
                  <span class="stat-value avg-score">{{ row.avg_score }}分</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">及格率:</span>
                  <span class="stat-value pass-rate">{{ (row.pass_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="viewPaper(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="link" size="small" @click="editPaper(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="link" size="small" @click="previewPaper(row)">
                <el-icon><Document /></el-icon>
                预览
              </el-button>
              <el-button type="link" size="small" @click="viewResults(row)">
                <el-icon><DataAnalysis /></el-icon>
                成绩
              </el-button>
              <el-button
                type="link"
                size="small"
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="togglePaperStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '归档' : '发布' }}
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deletePaper(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadPapers"
            @current-change="loadPapers"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="学科分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">试卷学科分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="难度分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">试卷难度分布图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="考试成绩分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">考试成绩分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑试卷对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingPaper ? '编辑试卷' : '创建试卷'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="paperForm" :rules="paperRules" ref="paperFormRef" label-width="100px">
        <el-form-item label="试卷名称" prop="title">
          <el-input v-model="paperForm.title" placeholder="请输入试卷名称" />
        </el-form-item>
        <el-form-item label="试卷描述" prop="description">
          <el-input v-model="paperForm.description" type="textarea" rows="3" placeholder="请输入试卷描述" />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select v-model="paperForm.subject" placeholder="请选择学科">
            <el-option label="语文" value="chinese" />
            <el-option label="数学" value="math" />
            <el-option label="英语" value="english" />
            <el-option label="物理" value="physics" />
            <el-option label="化学" value="chemistry" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级" prop="difficulty">
          <el-select v-model="paperForm.difficulty" placeholder="请选择难度等级">
            <el-option label="简单" value="easy" />
            <el-option label="中等" value="medium" />
            <el-option label="困难" value="hard" />
          </el-select>
        </el-form-item>
        <el-form-item label="考试时长" prop="duration">
          <el-input-number v-model="paperForm.duration" :min="10" :max="300" placeholder="分钟" />
        </el-form-item>
        <el-form-item label="总分" prop="total_score">
          <el-input-number v-model="paperForm.total_score" :min="1" :max="1000" placeholder="分" />
        </el-form-item>
        <el-form-item label="及格分" prop="pass_score">
          <el-input-number v-model="paperForm.pass_score" :min="1" :max="1000" placeholder="分" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="savePaper" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, Download, Document, DataAnalysis
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const loading = ref(false)
const saving = ref(false)
const selectedPapers = ref([])
const showCreateDialog = ref(false)
const editingPaper = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const subjectFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 试卷列表
const papers = ref([
  {
    id: '1',
    title: '高中数学期末考试',
    description: '高中数学第一学期期末综合测试',
    subject: 'math',
    difficulty: 'medium',
    duration: 120,
    total_questions: 25,
    total_score: 150,
    pass_score: 90,
    exam_count: 156,
    avg_score: 112.5,
    pass_rate: 0.78,
    status: 'published',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const paperForm = reactive({
  title: '',
  description: '',
  subject: '',
  difficulty: 'medium',
  duration: 90,
  total_score: 100,
  pass_score: 60
})

// 表单验证规则
const paperRules = {
  title: [
    { required: true, message: '请输入试卷名称', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请选择学科', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ]
}

// 方法
const loadPapers = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取试卷列表
    console.log('Loading papers...')
  } catch (error) {
    ElMessage.error('加载试卷列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedPapers.value = selection
}

const getSubjectName = (subject) => {
  const subjects = {
    chinese: '语文',
    math: '数学',
    english: '英语',
    physics: '物理',
    chemistry: '化学'
  }
  return subjects[subject] || subject
}

const getSubjectTagType = (subject) => {
  const types = {
    chinese: 'primary',
    math: 'success',
    english: 'warning',
    physics: 'danger',
    chemistry: 'info'
  }
  return types[subject] || ''
}

const getDifficultyName = (difficulty) => {
  const difficulties = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return difficulties[difficulty] || difficulty
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewPaper = (paper) => {
  // TODO: 查看试卷详情
  console.log('Viewing paper:', paper)
}

const editPaper = (paper) => {
  editingPaper.value = paper
  Object.assign(paperForm, paper)
  showCreateDialog.value = true
}

const previewPaper = (paper) => {
  // TODO: 预览试卷
  console.log('Previewing paper:', paper)
}

const viewResults = (paper) => {
  // TODO: 查看考试成绩
  console.log('Viewing results for paper:', paper)
}

const resetForm = () => {
  editingPaper.value = null
  Object.assign(paperForm, {
    title: '',
    description: '',
    subject: '',
    difficulty: 'medium',
    duration: 90,
    total_score: 100,
    pass_score: 60
  })
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', paperForm)
    showCreateDialog.value = false
    ElMessage.success('试卷已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const savePaper = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing paper...', paperForm)
    showCreateDialog.value = false
    ElMessage.success(editingPaper.value ? '试卷更新成功' : '试卷创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const togglePaperStatus = async (paper) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling paper status...', paper)
}

const deletePaper = async (paper) => {
  try {
    await ElMessageBox.confirm('确定要删除这个试卷吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting paper...', paper)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing papers...', selectedPapers.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving papers...', selectedPapers.value)
}

const exportPapers = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting papers...')
}

onMounted(() => {
  loadPapers()
})
</script>

<style scoped>
.papers-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.paper-info {
  padding: 8px 0;
}

.paper-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.paper-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.paper-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.difficulty-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.difficulty-badge.difficulty-easy {
  background-color: #67c23a;
}

.difficulty-badge.difficulty-medium {
  background-color: #e6a23c;
}

.difficulty-badge.difficulty-hard {
  background-color: #f56c6c;
}

.duration-info {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.question-stats,
.exam-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.total-score {
  color: #409eff !important;
}

.pass-score {
  color: #67c23a !important;
}

.avg-score {
  color: #e6a23c !important;
}

.pass-rate {
  color: #f56c6c !important;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
