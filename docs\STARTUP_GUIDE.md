# WisCude 启动指南

## 快速启动

### 启动后端服务
```bash
python start_backend.py
```

### 启动前端服务
```bash
python start_frontend.py
```

### 数据库迁移（可选）
如果需要从SQLite迁移到PostgreSQL：
```bash
python sql/database_migrate.py
```

## 服务访问地址

- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **前端应用**: http://localhost:5173

## 注意事项

1. 确保已安装Python 3.8+和Node.js 16+
2. 首次运行会自动安装依赖
3. 确保PostgreSQL服务已启动（如果使用PostgreSQL）
4. 检查`.env`文件中的配置是否正确

## 数据库设置

详细的PostgreSQL设置说明请参考 `docs/POSTGRESQL_SETUP.md` 文件。