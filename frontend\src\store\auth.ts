import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { AdminUser, LoginRequest } from '@/types/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<AdminUser | null>(null)
  const accessToken = ref<string>('')
  const refreshToken = ref<string>('')
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin' || user.value?.role === 'superadmin')
  const isSuperAdmin = computed(() => user.value?.role === 'superadmin' || user.value?.is_superuser)

  // 登录
  const login = async (loginData: LoginRequest) => {
    loading.value = true
    try {
      const response = await authApi.login(loginData)
      
      // 保存令牌
      accessToken.value = response.access_token
      refreshToken.value = response.refresh_token
      
      // 保存到本地存储
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      
      // 获取用户信息
      await getCurrentUser()
      
      ElMessage.success('登录成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除状态
      user.value = null
      accessToken.value = ''
      refreshToken.value = ''
      
      // 清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      
      ElMessage.success('已退出登录')
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      console.log('正在获取当前用户信息...')
      const userData = await authApi.getCurrentUser()
      console.log('获取用户信息成功:', userData)
      user.value = userData
    } catch (error: any) {
      console.error('获取用户信息失败:', error)

      // 如果是401错误，清除用户信息但不抛出错误
      if (error?.response?.status === 401) {
        console.log('用户认证失败，清除用户信息')
        user.value = null
      }

      throw error
    }
  }

  // 刷新令牌
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        console.error('刷新令牌失败: 没有刷新令牌')
        throw new Error('没有刷新令牌')
      }

      console.log('正在刷新访问令牌...')
      const response = await authApi.refreshToken(refreshToken.value)
      console.log('访问令牌刷新成功')

      // 更新令牌
      accessToken.value = response.access_token
      refreshToken.value = response.refresh_token

      // 更新本地存储
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)

      return response.access_token
    } catch (error: any) {
      console.error('刷新令牌失败:', error)

      // 如果是401错误，说明刷新令牌也过期了
      if (error?.response?.status === 401) {
        console.log('刷新令牌已过期，清除登录状态')
        // 刷新失败，清除登录状态（但不调用logout API）
        user.value = null
        accessToken.value = ''
        refreshToken.value = ''
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
      }

      throw error
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    // 如果已经认证且有用户信息，直接返回true
    if (isAuthenticated.value) {
      console.log('用户已认证，直接返回true')
      return true
    }

    const token = localStorage.getItem('access_token')
    const refresh = localStorage.getItem('refresh_token')

    console.log('检查认证状态:', { hasToken: !!token, hasRefresh: !!refresh })

    if (!token || !refresh) {
      console.log('没有找到认证令牌')
      return false
    }

    // 设置令牌但不立即标记为已认证
    accessToken.value = token
    refreshToken.value = refresh

    try {
      // 验证令牌有效性并获取用户信息
      console.log('验证令牌有效性...')
      await authApi.checkToken()
      if (!user.value) {
        await getCurrentUser()
      }
      console.log('认证检查成功')
      return true
    } catch (error: any) {
      console.log('令牌验证失败，尝试刷新:', error?.response?.status)

      // 令牌无效，尝试刷新
      try {
        await refreshAccessToken()
        if (!user.value) {
          await getCurrentUser()
        }
        console.log('令牌刷新后认证检查成功')
        return true
      } catch (refreshError: any) {
        console.error('认证检查失败:', refreshError)
        // 清除状态但不调用logout API（避免循环）
        user.value = null
        accessToken.value = ''
        refreshToken.value = ''
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        return false
      }
    }
  }

  // 修改密码
  const changePassword = async (currentPassword: string, newPassword: string) => {
    loading.value = true
    try {
      await authApi.changePassword({
        current_password: currentPassword,
        new_password: newPassword
      })
      ElMessage.success('密码修改成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '密码修改失败')
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    user: readonly(user),
    loading: readonly(loading),
    accessToken: readonly(accessToken),
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isSuperAdmin,
    
    // 方法
    login,
    logout,
    getCurrentUser,
    refreshAccessToken,
    checkAuth,
    changePassword
  }
})
