-- 删除PostgreSQL数据库wiscude_admin中的所有表、序列、枚举类型和函数
-- 警告：此操作不可逆，请确保已备份重要数据

-- 删除所有表（包括依赖关系）
DO $$ DECLARE
    r RECORD;
BEGIN
    -- 删除所有表
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
END $$;

-- 删除所有序列
DO $$ DECLARE
    r RECORD;
BEGIN
    -- 删除所有序列
    FOR r IN (SELECT sequencename FROM pg_sequences WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP SEQUENCE IF EXISTS ' || quote_ident(r.sequencename) || ' CASCADE';
    END LOOP;
END $$;

-- 删除所有枚举类型
DO $$ DECLARE
    r RECORD;
BEGIN
    -- 删除所有枚举类型
    FOR r IN (SELECT typname FROM pg_type WHERE typtype = 'e' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) LOOP
        EXECUTE 'DROP TYPE IF EXISTS ' || quote_ident(r.typname) || ' CASCADE';
    END LOOP;
END $$;

-- 删除所有函数
DO $$ DECLARE
    r RECORD;
BEGIN
    -- 删除所有函数
    FOR r IN (SELECT proname, oidvectortypes(proargtypes) as argtypes 
             FROM pg_proc 
             WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS ' || quote_ident(r.proname) || '(' || r.argtypes || ') CASCADE';
    END LOOP;
END $$;

-- 显示完成信息
SELECT 'PostgreSQL数据库清理完成！所有表、序列、枚举类型和函数已删除。' AS result;