"""
WebSocket API端点
提供实时日志流等WebSocket功能
"""
import json
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from sqlalchemy.orm import Session
import logging

from app.core.database import get_db
from app.core.security import get_current_user_from_token
from app.services.log_service import get_log_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ws", tags=["WebSocket"])

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[Dict[str, Any]] = []
    
    async def connect(self, websocket: WebSocket, user_id: str, filters: Dict[str, Any] = None):
        """接受WebSocket连接"""
        await websocket.accept()
        connection_info = {
            "websocket": websocket,
            "user_id": user_id,
            "filters": filters or {},
            "connected_at": datetime.utcnow()
        }
        self.active_connections.append(connection_info)
        logger.info(f"用户 {user_id} 建立WebSocket连接")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        for connection in self.active_connections:
            if connection["websocket"] == websocket:
                self.active_connections.remove(connection)
                logger.info(f"用户 {connection['user_id']} 断开WebSocket连接")
                break
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
    
    async def broadcast(self, message: str, filters: Dict[str, Any] = None):
        """广播消息到所有连接"""
        disconnected = []
        for connection in self.active_connections:
            try:
                # 检查过滤条件
                if filters and not self._match_filters(connection["filters"], filters):
                    continue
                
                await connection["websocket"].send_text(message)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)
        
        # 清理断开的连接
        for conn in disconnected:
            self.active_connections.remove(conn)
    
    def _match_filters(self, connection_filters: Dict[str, Any], message_filters: Dict[str, Any]) -> bool:
        """检查过滤条件是否匹配"""
        for key, value in message_filters.items():
            if key in connection_filters:
                if connection_filters[key] != value and connection_filters[key] != 'ALL':
                    return False
        return True
    
    def get_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)

# 全局连接管理器
manager = ConnectionManager()

@router.websocket("/logs")
async def websocket_logs_endpoint(
    websocket: WebSocket,
    token: Optional[str] = None,
    level: Optional[str] = None,
    module: Optional[str] = None
):
    """实时日志流WebSocket端点"""
    try:
        # 验证用户身份
        if not token:
            await websocket.close(code=4001, reason="Missing authentication token")
            return
        
        # 这里应该验证token并获取用户信息
        # 为了简化，我们假设token有效
        user_id = "system"  # 实际应该从token中解析
        
        # 建立连接
        filters = {
            "level": level or "ALL",
            "module": module or "ALL"
        }
        await manager.connect(websocket, user_id, filters)
        
        try:
            while True:
                # 接收客户端消息（用于更新过滤条件等）
                data = await websocket.receive_text()
                try:
                    message = json.loads(data)
                    if message.get("type") == "update_filters":
                        # 更新过滤条件
                        for connection in manager.active_connections:
                            if connection["websocket"] == websocket:
                                connection["filters"].update(message.get("filters", {}))
                                break
                        
                        await websocket.send_text(json.dumps({
                            "type": "filter_updated",
                            "filters": message.get("filters", {}),
                            "timestamp": datetime.utcnow().isoformat()
                        }))
                except json.JSONDecodeError:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }))
        except WebSocketDisconnect:
            manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
        try:
            await websocket.close(code=4000, reason="Internal server error")
        except:
            pass

async def broadcast_log_message(log_data: Dict[str, Any]):
    """广播日志消息到所有WebSocket连接"""
    try:
        message = json.dumps({
            "type": "log_message",
            "data": log_data,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # 根据日志级别和模块进行过滤广播
        filters = {
            "level": log_data.get("level"),
            "module": log_data.get("module")
        }
        
        await manager.broadcast(message, filters)
    except Exception as e:
        logger.error(f"广播日志消息失败: {e}")

@router.get("/connections/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计"""
    return {
        "active_connections": manager.get_connection_count(),
        "timestamp": datetime.utcnow().isoformat()
    }

# 日志广播任务
async def log_broadcast_task():
    """定期广播最新日志的后台任务"""
    while True:
        try:
            # 这里可以从数据库获取最新的日志并广播
            # 为了演示，我们每30秒发送一次心跳
            await asyncio.sleep(30)
            
            heartbeat_message = json.dumps({
                "type": "heartbeat",
                "timestamp": datetime.utcnow().isoformat(),
                "connections": manager.get_connection_count()
            })
            
            await manager.broadcast(heartbeat_message)
        except Exception as e:
            logger.error(f"日志广播任务错误: {e}")
            await asyncio.sleep(5)

# 后台任务将在应用启动时创建
# 这里不直接创建任务，避免在模块导入时出错
