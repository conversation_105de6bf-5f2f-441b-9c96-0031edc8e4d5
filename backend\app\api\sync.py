"""
WisCude 后台管理系统 - 数据同步API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc
from app.core.database import get_db, db_manager
from app.core.security import get_current_user, get_current_active_superuser
from app.core.config import settings
from app.models import SyncLog, AdminUser
from app.services.sync_service import create_sync_service
from app.api.schemas import SyncRequest, SyncResponse, PaginatedResponse
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/sync", tags=["数据同步"])

@router.get("/status", summary="获取同步状态")
async def get_sync_status(
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """获取数据同步状态"""
    # 检查Android数据库连接
    android_db_path = settings.ANDROID_DB_PATH
    android_db_exists = Path(android_db_path).exists()
    android_db_readable = False
    android_db_size = 0
    android_db_modified = None
    
    if android_db_exists:
        try:
            android_db_readable = os.access(android_db_path, os.R_OK)
            android_db_size = os.path.getsize(android_db_path)
            android_db_modified = os.path.getmtime(android_db_path)
        except Exception as e:
            logger.error(f"检查Android数据库状态失败: {e}")
    
    # 获取最近的同步记录
    last_sync = db.query(SyncLog).order_by(desc(SyncLog.created_at)).first()
    
    # 检查是否有正在进行的同步
    running_sync = db.query(SyncLog).filter(
        SyncLog.status == "running"
    ).first()
    
    return {
        "android_database": {
            "path": android_db_path,
            "exists": android_db_exists,
            "readable": android_db_readable,
            "size": android_db_size,
            "last_modified": android_db_modified,
            "status": "available" if android_db_exists and android_db_readable else "unavailable"
        },
        "last_sync": {
            "id": last_sync.id if last_sync else None,
            "sync_type": last_sync.sync_type if last_sync else None,
            "status": last_sync.status if last_sync else None,
            "started_at": last_sync.started_at if last_sync else None,
            "completed_at": last_sync.completed_at if last_sync else None,
            "duration_seconds": last_sync.duration_seconds if last_sync else None,
            "records_processed": last_sync.records_processed if last_sync else 0,
            "records_inserted": last_sync.records_inserted if last_sync else 0,
            "records_updated": last_sync.records_updated if last_sync else 0,
            "records_failed": last_sync.records_failed if last_sync else 0
        },
        "is_syncing": running_sync is not None,
        "current_sync_id": running_sync.id if running_sync else None
    }

@router.post("/start", response_model=SyncResponse, summary="开始数据同步")
async def start_sync(
    background_tasks: BackgroundTasks,
    sync_request: SyncRequest = SyncRequest(),
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_active_superuser)
):
    """开始数据同步"""
    # 检查是否有正在进行的同步
    running_sync = db.query(SyncLog).filter(
        SyncLog.status == "running"
    ).first()
    
    if running_sync:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="已有同步任务正在进行中"
        )
    
    # 检查Android数据库是否可用
    if not db_manager.get_android_db_manager().is_android_db_available():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Android数据库不可用"
        )
    
    # 创建同步服务并开始同步
    try:
        sync_service = await create_sync_service(db)
        
        # 在后台执行同步
        background_tasks.add_task(
            perform_sync_task,
            sync_service,
            sync_request.sync_type,
            current_user.id
        )
        
        logger.info(f"管理员 {current_user.username} 启动了数据同步")
        
        return {
            "status": "started",
            "message": "数据同步已开始，请稍后查看同步状态",
            "sync_type": sync_request.sync_type
        }
        
    except Exception as e:
        logger.error(f"启动数据同步失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动数据同步失败: {str(e)}"
        )

async def perform_sync_task(sync_service, sync_type: str, user_id: str):
    """执行同步任务"""
    try:
        if sync_type == "full":
            result = await sync_service.sync_all_data()
        else:
            # TODO: 实现增量同步
            result = await sync_service.sync_all_data()
        
        logger.info(f"数据同步完成: {result}")
        
    except Exception as e:
        logger.error(f"数据同步任务失败: {e}")

@router.get("/logs", response_model=PaginatedResponse, summary="获取同步日志")
async def get_sync_logs(
    page: int = 1,
    page_size: int = 20,
    status: Optional[str] = None,
    sync_type: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """获取同步日志"""
    # 构建查询
    query = db.query(SyncLog)
    
    # 状态过滤
    if status:
        query = query.filter(SyncLog.status == status)
    
    # 同步类型过滤
    if sync_type:
        query = query.filter(SyncLog.sync_type == sync_type)
    
    # 排序
    query = query.order_by(desc(SyncLog.created_at))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    logs = query.offset(offset).limit(page_size).all()
    
    # 计算总页数
    pages = (total + page_size - 1) // page_size
    
    return {
        "items": logs,
        "total": total,
        "page": page,
        "page_size": page_size,
        "pages": pages
    }

@router.get("/logs/{log_id}", summary="获取同步日志详情")
async def get_sync_log(
    log_id: str,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """获取同步日志详情"""
    log = db.query(SyncLog).filter(SyncLog.id == log_id).first()
    if not log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="同步日志不存在"
        )
    
    return log

@router.get("/tables", summary="获取Android数据库表信息")
async def get_android_tables(
    current_user: AdminUser = Depends(get_current_user)
):
    """获取Android数据库表信息"""
    try:
        android_db = db_manager.get_android_db_manager()
        
        with android_db as conn:
            tables = conn.get_table_names()
            
            table_info = []
            for table_name in tables:
                try:
                    row_count = conn.get_row_count(table_name)
                    schema = conn.get_table_schema(table_name)
                    
                    table_info.append({
                        "name": table_name,
                        "row_count": row_count,
                        "columns": [
                            {
                                "name": col["name"],
                                "type": col["type"],
                                "not_null": bool(col["notnull"]),
                                "primary_key": bool(col["pk"])
                            }
                            for col in schema
                        ]
                    })
                except Exception as e:
                    logger.error(f"获取表 {table_name} 信息失败: {e}")
                    table_info.append({
                        "name": table_name,
                        "row_count": 0,
                        "columns": [],
                        "error": str(e)
                    })
            
            return {
                "tables": table_info,
                "total_tables": len(tables)
            }
            
    except Exception as e:
        logger.error(f"获取Android数据库表信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据库表信息失败: {str(e)}"
        )

@router.delete("/logs/{log_id}", summary="删除同步日志")
async def delete_sync_log(
    log_id: str,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_active_superuser)
):
    """删除同步日志"""
    log = db.query(SyncLog).filter(SyncLog.id == log_id).first()
    if not log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="同步日志不存在"
        )
    
    db.delete(log)
    db.commit()
    
    logger.info(f"管理员 {current_user.username} 删除了同步日志 {log_id}")
    
    return {"message": "同步日志已删除"}
