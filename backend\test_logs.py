#!/usr/bin/env python3
"""
测试日志管理功能的脚本
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.admin import SystemLog
from app.services.log_service import LogService

# 日志级别和模块
LOG_LEVELS = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
LOG_MODULES = ['SYSTEM', 'USER', 'AUTH', 'API', 'DATABASE', 'CACHE', 'EMAIL', 'FILE', 'SYNC']

# 示例日志消息
LOG_MESSAGES = [
    "用户登录成功",
    "数据库连接建立",
    "API请求处理完成",
    "缓存更新成功",
    "文件上传完成",
    "权限验证失败",
    "数据库查询超时",
    "内存使用率过高",
    "磁盘空间不足",
    "网络连接异常",
    "邮件发送成功",
    "数据同步完成",
    "配置文件更新",
    "定时任务执行",
    "系统启动完成"
]

LOG_ACTIONS = [
    "LOGIN",
    "LOGOUT",
    "CREATE_USER",
    "UPDATE_USER",
    "DELETE_USER",
    "UPLOAD_FILE",
    "DOWNLOAD_FILE",
    "SEND_EMAIL",
    "SYNC_DATA",
    "UPDATE_CONFIG",
    "SYSTEM_START",
    "SYSTEM_STOP",
    "DATABASE_BACKUP",
    "CACHE_CLEAR",
    "API_CALL"
]

def create_sample_logs(db: Session, count: int = 100):
    """创建示例日志数据"""
    print(f"创建 {count} 条示例日志...")
    
    log_service = LogService(db)
    
    for i in range(count):
        # 随机生成时间（过去7天内）
        random_time = datetime.now() - timedelta(
            days=random.randint(0, 7),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59),
            seconds=random.randint(0, 59)
        )
        
        # 创建日志记录
        log = SystemLog(
            user_id=f"user-{random.randint(1, 20)}" if random.random() > 0.3 else None,
            username=f"testuser{random.randint(1, 20)}" if random.random() > 0.3 else None,
            action=random.choice(LOG_ACTIONS),
            module=random.choice(LOG_MODULES),
            level=random.choice(LOG_LEVELS),
            message=random.choice(LOG_MESSAGES),
            details=f"详细信息 {i+1}" if random.random() > 0.5 else None,
            ip_address=f"192.168.1.{random.randint(1, 255)}"
        )
        
        # 手动设置创建时间
        log.created_at = random_time
        
        db.add(log)
        
        if (i + 1) % 20 == 0:
            print(f"已创建 {i + 1} 条日志...")
    
    db.commit()
    print(f"成功创建 {count} 条示例日志！")

def test_log_service(db: Session):
    """测试日志服务功能"""
    print("\n测试日志服务功能...")
    
    log_service = LogService(db)
    
    # 测试获取日志列表
    print("1. 测试获取日志列表...")
    result = log_service.get_system_logs(page=1, page_size=10)
    print(f"   总日志数: {result['total']}")
    print(f"   当前页日志数: {len(result['items'])}")
    
    # 测试按级别过滤
    print("2. 测试按级别过滤...")
    error_logs = log_service.get_system_logs(level='ERROR', page_size=5)
    print(f"   ERROR级别日志数: {error_logs['total']}")
    
    # 测试按模块过滤
    print("3. 测试按模块过滤...")
    system_logs = log_service.get_system_logs(module='SYSTEM', page_size=5)
    print(f"   SYSTEM模块日志数: {system_logs['total']}")
    
    # 测试关键词搜索
    print("4. 测试关键词搜索...")
    search_logs = log_service.get_system_logs(keyword='用户', page_size=5)
    print(f"   包含'用户'的日志数: {search_logs['total']}")
    
    # 测试获取日志模块
    print("5. 测试获取日志模块...")
    modules = log_service.get_log_modules()
    print(f"   日志模块: {modules}")
    
    # 测试获取日志统计
    print("6. 测试获取日志统计...")
    stats = log_service.get_log_statistics()
    print(f"   统计信息: {stats}")
    
    # 测试创建新日志
    print("7. 测试创建新日志...")
    new_log = log_service.create_system_log(
        user_id="test-user",
        username="testuser",
        action="TEST_ACTION",
        module="TEST",
        level="INFO",
        message="这是一条测试日志",
        details="测试详细信息",
        ip_address="127.0.0.1"
    )
    print(f"   新日志ID: {new_log.id}")
    
    print("日志服务测试完成！")

def cleanup_test_logs(db: Session):
    """清理测试日志"""
    print("\n清理测试日志...")
    
    # 删除所有测试相关的日志
    deleted_count = db.query(SystemLog).filter(
        SystemLog.module == 'TEST'
    ).delete()
    
    db.commit()
    print(f"清理了 {deleted_count} 条测试日志")

def main():
    """主函数"""
    print("=== 日志管理功能测试 ===")
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 检查现有日志数量
        existing_count = db.query(SystemLog).count()
        print(f"现有日志数量: {existing_count}")
        
        # 如果日志数量少于50条，创建示例数据
        if existing_count < 50:
            create_sample_logs(db, 100)
        
        # 测试日志服务
        test_log_service(db)
        
        # 清理测试日志
        cleanup_test_logs(db)
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
