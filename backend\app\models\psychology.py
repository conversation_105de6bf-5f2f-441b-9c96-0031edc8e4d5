"""
心理资源库管理模块数据模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, JSON, Float, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base

class PsychologyAssessment(Base):
    """心理测评量表模型"""
    __tablename__ = "psychology_assessments"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="量表标题")
    description = Column(Text, comment="量表描述")
    assessment_type = Column(String(50), comment="测评类型: personality, emotion, stress, anxiety, depression, etc.")
    
    # 量表配置
    questions = Column(JSON, comment="题目列表")
    question_count = Column(Integer, default=0, comment="题目数量")
    scoring_rules = Column(JSON, comment="评分规则")
    result_interpretation = Column(JSON, comment="结果解读")
    
    # 适用信息
    target_age_min = Column(Integer, comment="最小适用年龄")
    target_age_max = Column(Integer, comment="最大适用年龄")
    target_groups = Column(JSON, comment="目标群体")
    
    # 测评设置
    time_limit = Column(Integer, comment="测评时间限制（分钟）")
    is_anonymous = Column(Boolean, default=True, comment="是否支持匿名测评")
    requires_login = Column(Boolean, default=False, comment="是否需要登录")
    
    # 专业信息
    author = Column(String(100), comment="量表作者")
    source = Column(String(200), comment="量表来源")
    reliability = Column(Float, comment="信度系数")
    validity = Column(Float, comment="效度系数")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    completion_rate = Column(Float, default=0.0, comment="完成率")
    average_time = Column(Float, default=0.0, comment="平均完成时间")
    
    # 状态
    status = Column(String(20), default="draft", comment="状态: draft, published, archived")
    is_featured = Column(Boolean, default=False, comment="是否为推荐量表")
    review_status = Column(String(20), default="pending", comment="审核状态")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class PsychologyArticle(Base):
    """心理健康文章模型"""
    __tablename__ = "psychology_articles"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="文章标题")
    subtitle = Column(String(300), comment="副标题")
    content = Column(Text, nullable=False, comment="文章内容")
    summary = Column(Text, comment="文章摘要")
    
    # 分类信息
    category = Column(String(50), comment="文章分类")
    tags = Column(JSON, comment="标签列表")
    topics = Column(JSON, comment="主题标签")
    target_audience = Column(JSON, comment="目标读者群体")
    
    # 媒体资源
    cover_image = Column(String(500), comment="封面图片")
    images = Column(JSON, comment="文章图片")
    videos = Column(JSON, comment="视频资源")
    audio_url = Column(String(500), comment="音频朗读")
    
    # 作者信息
    author_id = Column(String(36), comment="作者ID")
    author_name = Column(String(100), comment="作者姓名")
    author_title = Column(String(100), comment="作者职称")
    author_bio = Column(Text, comment="作者简介")
    
    # 专业信息
    professional_level = Column(String(20), default="general", comment="专业程度: general, intermediate, advanced")
    evidence_based = Column(Boolean, default=False, comment="是否基于循证")
    references = Column(JSON, comment="参考文献")
    
    # 推荐机制
    recommendation_score = Column(Float, default=0.0, comment="推荐分数")
    quality_score = Column(Float, default=0.0, comment="质量评分")
    is_featured = Column(Boolean, default=False, comment="是否为精选文章")
    is_trending = Column(Boolean, default=False, comment="是否为热门文章")
    
    # 互动统计
    view_count = Column(Integer, default=0, comment="阅读次数")
    like_count = Column(Integer, default=0, comment="点赞数")
    share_count = Column(Integer, default=0, comment="分享次数")
    comment_count = Column(Integer, default=0, comment="评论数")
    
    # 状态
    status = Column(String(20), default="draft", comment="状态: draft, published, archived")
    review_status = Column(String(20), default="pending", comment="审核状态: pending, approved, rejected")
    reviewer_id = Column(String(36), comment="审核者ID")
    review_comments = Column(Text, comment="审核意见")
    
    # 发布信息
    publish_time = Column(DateTime, comment="发布时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class PsychologyCounselor(Base):
    """心理咨询师模型"""
    __tablename__ = "psychology_counselors"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), unique=True, comment="关联用户ID")
    name = Column(String(100), nullable=False, comment="姓名")
    avatar = Column(String(500), comment="头像")
    
    # 专业信息
    title = Column(String(100), comment="职称")
    qualifications = Column(JSON, comment="资质证书")
    specialties = Column(JSON, comment="专业领域")
    experience_years = Column(Integer, comment="从业年限")
    education_background = Column(Text, comment="教育背景")
    
    # 服务信息
    service_types = Column(JSON, comment="服务类型")
    consultation_methods = Column(JSON, comment="咨询方式: online, offline, phone")
    languages = Column(JSON, comment="服务语言")
    
    # 时间安排
    available_times = Column(JSON, comment="可预约时间")
    working_hours = Column(JSON, comment="工作时间")
    time_zone = Column(String(50), comment="时区")
    
    # 费用设置
    hourly_rate = Column(Float, comment="小时费率")
    session_duration = Column(Integer, default=50, comment="单次咨询时长（分钟）")
    payment_methods = Column(JSON, comment="支付方式")
    
    # 评价统计
    rating = Column(Float, default=0.0, comment="评分")
    rating_count = Column(Integer, default=0, comment="评价数量")
    consultation_count = Column(Integer, default=0, comment="咨询次数")
    
    # 状态
    status = Column(String(20), default="active", comment="状态: active, inactive, suspended")
    is_verified = Column(Boolean, default=False, comment="是否已认证")
    is_available = Column(Boolean, default=True, comment="是否可预约")
    
    # 个人介绍
    introduction = Column(Text, comment="个人介绍")
    approach = Column(Text, comment="咨询理念和方法")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class PsychologyAppointment(Base):
    """心理咨询预约模型"""
    __tablename__ = "psychology_appointments"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    counselor_id = Column(String(36), ForeignKey('psychology_counselors.id'), comment="咨询师ID")
    
    # 预约信息
    appointment_time = Column(DateTime, nullable=False, comment="预约时间")
    duration = Column(Integer, default=50, comment="咨询时长（分钟）")
    consultation_type = Column(String(20), comment="咨询类型: individual, group, family")
    consultation_method = Column(String(20), comment="咨询方式: online, offline, phone")
    
    # 问题描述
    issue_category = Column(String(50), comment="问题分类")
    issue_description = Column(Text, comment="问题描述")
    urgency_level = Column(String(20), default="normal", comment="紧急程度: low, normal, high, urgent")
    
    # 状态管理
    status = Column(String(20), default="pending", comment="状态: pending, confirmed, completed, cancelled, no_show")
    payment_status = Column(String(20), default="unpaid", comment="支付状态: unpaid, paid, refunded")
    
    # 费用信息
    fee = Column(Float, comment="咨询费用")
    payment_method = Column(String(20), comment="支付方式")
    payment_time = Column(DateTime, comment="支付时间")
    
    # 咨询记录
    session_notes = Column(Text, comment="咨询记录")
    counselor_feedback = Column(Text, comment="咨询师反馈")
    user_feedback = Column(Text, comment="用户反馈")
    rating = Column(Integer, comment="用户评分 1-5")
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class PsychologyCrisisAlert(Base):
    """心理危机预警模型"""
    __tablename__ = "psychology_crisis_alerts"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), comment="用户姓名")
    
    # 预警信息
    alert_type = Column(String(20), nullable=False, comment="预警类型: keyword, behavior, assessment")
    risk_level = Column(String(20), nullable=False, comment="风险等级: low, medium, high, critical")
    trigger_content = Column(Text, comment="触发内容")
    trigger_source = Column(String(50), comment="触发来源")
    
    # 关键词监控
    matched_keywords = Column(JSON, comment="匹配的关键词")
    keyword_score = Column(Float, comment="关键词风险评分")
    
    # 行为分析
    behavior_patterns = Column(JSON, comment="行为模式")
    behavior_score = Column(Float, comment="行为风险评分")
    
    # 综合评估
    total_risk_score = Column(Float, comment="总风险评分")
    assessment_result = Column(JSON, comment="评估结果")
    
    # 处理状态
    status = Column(String(20), default="pending", comment="处理状态: pending, processing, resolved, false_positive")
    handler_id = Column(String(36), comment="处理人ID")
    handler_name = Column(String(100), comment="处理人姓名")
    handle_time = Column(DateTime, comment="处理时间")
    handle_notes = Column(Text, comment="处理记录")
    
    # 应急处理
    emergency_contact_made = Column(Boolean, default=False, comment="是否已联系紧急联系人")
    professional_referral = Column(Boolean, default=False, comment="是否已转介专业机构")
    follow_up_required = Column(Boolean, default=False, comment="是否需要跟进")
    
    # 时间信息
    alert_time = Column(DateTime, default=datetime.utcnow, comment="预警时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
