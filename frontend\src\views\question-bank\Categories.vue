<template>
  <div class="categories-management">
    <div class="page-header">
      <h1>题目分类管理</h1>
      <p>管理题目的学科分类、难度等级和知识点标签</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建分类
        </el-button>
        <el-button 
          type="success" 
          :disabled="selectedCategories.length === 0"
          @click="batchEnable"
        >
          批量启用
        </el-button>
        <el-button 
          type="warning" 
          :disabled="selectedCategories.length === 0"
          @click="batchDisable"
        >
          批量禁用
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索分类名称"
          style="width: 200px"
          clearable
          @change="loadCategories"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="subjectFilter" placeholder="学科筛选" style="width: 120px" @change="loadCategories">
          <el-option label="全部" value="" />
          <el-option label="数学" value="math" />
          <el-option label="语文" value="chinese" />
          <el-option label="英语" value="english" />
          <el-option label="物理" value="physics" />
          <el-option label="化学" value="chemistry" />
        </el-select>
      </div>
    </div>

    <!-- 分类树形表格 -->
    <el-table
      :data="categories"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="分类信息" min-width="300">
        <template #default="{ row }">
          <div class="category-info">
            <div class="category-name">
              <span class="name-text">{{ row.name }}</span>
              <el-tag size="small" class="code-tag">{{ row.code }}</el-tag>
            </div>
            <div class="category-description">{{ row.description }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="学科" width="100">
        <template #default="{ row }">
          <el-tag :type="getSubjectTagType(row.subject)">
            {{ getSubjectName(row.subject) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="层级" width="80">
        <template #default="{ row }">
          <span class="level-badge" :class="`level-${row.level}`">L{{ row.level }}</span>
        </template>
      </el-table-column>
      <el-table-column label="适用年级" width="150">
        <template #default="{ row }">
          <div class="grade-tags">
            <el-tag 
              v-for="grade in row.grade_levels" 
              :key="grade" 
              size="small" 
              class="grade-tag"
            >
              {{ grade }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="难度等级" width="150">
        <template #default="{ row }">
          <div class="difficulty-tags">
            <span 
              v-for="difficulty in row.difficulty_levels" 
              :key="difficulty" 
              class="difficulty-badge"
              :class="`difficulty-${difficulty}`"
            >
              {{ getDifficultyName(difficulty) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="题目数量" width="100">
        <template #default="{ row }">
          <span class="question-count">{{ row.question_count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="80">
        <template #default="{ row }">
          <span class="sort-order">{{ row.sort_order }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="link" size="small" @click="editCategory(row)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button type="link" size="small" @click="addSubCategory(row)">
            <el-icon><Plus /></el-icon>
            添加子分类
          </el-button>
          <el-button
            type="link"
            size="small"
            :class="row.is_active ? 'warning' : 'success'"
            @click="toggleCategoryStatus(row)"
          >
            <el-icon><Switch /></el-icon>
            {{ row.is_active ? '禁用' : '启用' }}
          </el-button>
          <el-button type="link" size="small" class="danger" @click="deleteCategory(row)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadCategories"
        @current-change="loadCategories"
      />
    </div>

    <!-- 创建/编辑分类对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingCategory ? '编辑分类' : '新建分类'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="categoryForm" :rules="categoryRules" ref="categoryFormRef" label-width="100px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类代码" prop="code">
          <el-input v-model="categoryForm.code" placeholder="请输入分类代码（英文）" />
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input v-model="categoryForm.description" type="textarea" rows="3" placeholder="请输入分类描述" />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select v-model="categoryForm.subject" placeholder="请选择学科">
            <el-option label="数学" value="math" />
            <el-option label="语文" value="chinese" />
            <el-option label="英语" value="english" />
            <el-option label="物理" value="physics" />
            <el-option label="化学" value="chemistry" />
            <el-option label="生物" value="biology" />
            <el-option label="历史" value="history" />
            <el-option label="地理" value="geography" />
          </el-select>
        </el-form-item>
        <el-form-item label="父分类" prop="parent_id">
          <el-tree-select
            v-model="categoryForm.parent_id"
            :data="parentCategoryOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            placeholder="请选择父分类（可选）"
            clearable
            check-strictly
          />
        </el-form-item>
        <el-form-item label="适用年级" prop="grade_levels">
          <el-checkbox-group v-model="categoryForm.grade_levels">
            <el-checkbox label="小学一年级">小学一年级</el-checkbox>
            <el-checkbox label="小学二年级">小学二年级</el-checkbox>
            <el-checkbox label="小学三年级">小学三年级</el-checkbox>
            <el-checkbox label="小学四年级">小学四年级</el-checkbox>
            <el-checkbox label="小学五年级">小学五年级</el-checkbox>
            <el-checkbox label="小学六年级">小学六年级</el-checkbox>
            <el-checkbox label="初中一年级">初中一年级</el-checkbox>
            <el-checkbox label="初中二年级">初中二年级</el-checkbox>
            <el-checkbox label="初中三年级">初中三年级</el-checkbox>
            <el-checkbox label="高中一年级">高中一年级</el-checkbox>
            <el-checkbox label="高中二年级">高中二年级</el-checkbox>
            <el-checkbox label="高中三年级">高中三年级</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="难度等级" prop="difficulty_levels">
          <el-checkbox-group v-model="categoryForm.difficulty_levels">
            <el-checkbox :label="1">简单</el-checkbox>
            <el-checkbox :label="2">较易</el-checkbox>
            <el-checkbox :label="3">中等</el-checkbox>
            <el-checkbox :label="4">较难</el-checkbox>
            <el-checkbox :label="5">困难</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="排序顺序" prop="sort_order">
          <el-input-number v-model="categoryForm.sort_order" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="categoryForm.is_active" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCategory" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const selectedCategories = ref([])
const showCreateDialog = ref(false)
const editingCategory = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const subjectFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 分类列表
const categories = ref([
  {
    id: '1',
    name: '数学基础',
    code: 'math_basic',
    description: '数学基础知识分类',
    subject: 'math',
    parent_id: null,
    level: 1,
    sort_order: 1,
    grade_levels: ['小学一年级', '小学二年级', '小学三年级'],
    difficulty_levels: [1, 2, 3],
    question_count: 156,
    is_active: true,
    children: [
      {
        id: '2',
        name: '加减法',
        code: 'math_add_sub',
        description: '加减法运算',
        subject: 'math',
        parent_id: '1',
        level: 2,
        sort_order: 1,
        grade_levels: ['小学一年级', '小学二年级'],
        difficulty_levels: [1, 2],
        question_count: 78,
        is_active: true
      }
    ]
  }
])

// 父分类选项
const parentCategoryOptions = ref([])

// 表单数据
const categoryForm = reactive({
  name: '',
  code: '',
  description: '',
  subject: '',
  parent_id: null,
  grade_levels: [],
  difficulty_levels: [],
  sort_order: 0,
  is_active: true
})

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '分类代码只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请选择学科', trigger: 'change' }
  ]
}

// 方法
const loadCategories = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取分类列表
    console.log('Loading categories...')
  } catch (error) {
    ElMessage.error('加载分类列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedCategories.value = selection
}

const getSubjectName = (subject) => {
  const subjects = {
    math: '数学',
    chinese: '语文',
    english: '英语',
    physics: '物理',
    chemistry: '化学',
    biology: '生物',
    history: '历史',
    geography: '地理'
  }
  return subjects[subject] || subject
}

const getSubjectTagType = (subject) => {
  const types = {
    math: 'primary',
    chinese: 'success',
    english: 'warning',
    physics: 'danger',
    chemistry: 'info'
  }
  return types[subject] || ''
}

const getDifficultyName = (level) => {
  const names = {
    1: '简单',
    2: '较易',
    3: '中等',
    4: '较难',
    5: '困难'
  }
  return names[level] || `难度${level}`
}

const editCategory = (category) => {
  editingCategory.value = category
  Object.assign(categoryForm, category)
  showCreateDialog.value = true
}

const addSubCategory = (parentCategory) => {
  editingCategory.value = null
  resetForm()
  categoryForm.parent_id = parentCategory.id
  categoryForm.subject = parentCategory.subject
  showCreateDialog.value = true
}

const resetForm = () => {
  editingCategory.value = null
  Object.assign(categoryForm, {
    name: '',
    code: '',
    description: '',
    subject: '',
    parent_id: null,
    grade_levels: [],
    difficulty_levels: [],
    sort_order: 0,
    is_active: true
  })
}

const saveCategory = async () => {
  // TODO: 实现保存逻辑
  console.log('Saving category...', categoryForm)
  showCreateDialog.value = false
  ElMessage.success(editingCategory.value ? '分类更新成功' : '分类创建成功')
}

const toggleCategoryStatus = async (category) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling category status...', category)
}

const deleteCategory = async (category) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分类吗？删除后该分类下的所有题目将被移动到未分类。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting category...', category)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchEnable = async () => {
  // TODO: 实现批量启用逻辑
  console.log('Batch enabling categories...', selectedCategories.value)
}

const batchDisable = async () => {
  // TODO: 实现批量禁用逻辑
  console.log('Batch disabling categories...', selectedCategories.value)
}

onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.categories-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.category-info {
  padding: 4px 0;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.name-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.code-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.category-description {
  font-size: 12px;
  color: #909399;
}

.level-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.level-badge.level-1 {
  background-color: #409eff;
}

.level-badge.level-2 {
  background-color: #67c23a;
}

.level-badge.level-3 {
  background-color: #e6a23c;
}

.grade-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.grade-tag {
  font-size: 11px;
}

.difficulty-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.difficulty-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.difficulty-badge.difficulty-1 {
  background-color: #67c23a;
}

.difficulty-badge.difficulty-2 {
  background-color: #95d475;
}

.difficulty-badge.difficulty-3 {
  background-color: #e6a23c;
}

.difficulty-badge.difficulty-4 {
  background-color: #f78989;
}

.difficulty-badge.difficulty-5 {
  background-color: #f56c6c;
}

.question-count {
  font-weight: 600;
  color: #409eff;
}

.sort-order {
  font-weight: 600;
  color: #606266;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
