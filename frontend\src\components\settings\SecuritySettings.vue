<template>
  <el-card class="settings-card">
    <template #header>
      <div class="card-header">
        <el-icon><Lock /></el-icon>
        <span>安全设置</span>
        <el-tag type="warning" size="small">敏感配置</el-tag>
      </div>
    </template>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="160px"
      class="settings-form"
    >
      <!-- 令牌设置 -->
      <div class="settings-section">
        <h4 class="section-title">令牌配置</h4>
        
        <el-form-item label="访问令牌过期时间" prop="access_token_expire_minutes">
          <el-input-number
            v-model="formData.access_token_expire_minutes"
            :min="5"
            :max="1440"
            :step="5"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">分钟</span>
          <div class="field-description">
            用户登录后访问令牌的有效期，过短会频繁要求重新登录
          </div>
        </el-form-item>
        
        <el-form-item label="刷新令牌过期时间" prop="refresh_token_expire_days">
          <el-input-number
            v-model="formData.refresh_token_expire_days"
            :min="1"
            :max="30"
            :step="1"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">天</span>
          <div class="field-description">
            刷新令牌的有效期，用于自动续期访问令牌
          </div>
        </el-form-item>
        
        <el-form-item label="会话超时时间" prop="session_timeout_minutes">
          <el-input-number
            v-model="formData.session_timeout_minutes"
            :min="5"
            :max="480"
            :step="5"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">分钟</span>
          <div class="field-description">
            用户无操作后自动退出的时间
          </div>
        </el-form-item>
      </div>
      
      <!-- 登录安全 -->
      <div class="settings-section">
        <h4 class="section-title">登录安全</h4>
        
        <el-form-item label="最大登录尝试次数" prop="max_login_attempts">
          <el-input-number
            v-model="formData.max_login_attempts"
            :min="3"
            :max="10"
            :step="1"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">次</span>
          <div class="field-description">
            账户锁定前允许的最大失败登录次数
          </div>
        </el-form-item>
        
        <el-form-item label="账户锁定时长" prop="lockout_duration_minutes">
          <el-input-number
            v-model="formData.lockout_duration_minutes"
            :min="5"
            :max="1440"
            :step="5"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">分钟</span>
          <div class="field-description">
            账户被锁定后的解锁时间
          </div>
        </el-form-item>
      </div>
      
      <!-- 密码策略 -->
      <div class="settings-section">
        <h4 class="section-title">密码策略</h4>
        
        <el-form-item label="密码最小长度" prop="password_min_length">
          <el-input-number
            v-model="formData.password_min_length"
            :min="6"
            :max="32"
            :step="1"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">个字符</span>
          <div class="field-description">
            用户密码的最小字符数要求
          </div>
        </el-form-item>
        
        <el-form-item label="密码复杂度要求">
          <div class="password-requirements">
            <el-checkbox
              v-model="formData.password_require_uppercase"
              label="必须包含大写字母"
            />
            <el-checkbox
              v-model="formData.password_require_lowercase"
              label="必须包含小写字母"
            />
            <el-checkbox
              v-model="formData.password_require_numbers"
              label="必须包含数字"
            />
            <el-checkbox
              v-model="formData.password_require_special"
              label="必须包含特殊字符"
            />
          </div>
          <div class="field-description">
            选择密码必须满足的复杂度要求
          </div>
        </el-form-item>
      </div>
      
      <!-- 高级安全 -->
      <div class="settings-section">
        <h4 class="section-title">高级安全</h4>
        
        <el-form-item label="双因素认证">
          <el-switch
            v-model="formData.two_factor_auth_enabled"
            active-text="启用"
            inactive-text="禁用"
          />
          <div class="field-description">
            为管理员账户启用双因素认证，提高账户安全性
          </div>
        </el-form-item>
      </div>
      
      <!-- 密码强度测试 -->
      <div class="settings-section">
        <h4 class="section-title">密码强度测试</h4>
        <el-form-item label="测试密码">
          <el-input
            v-model="testPassword"
            type="password"
            placeholder="输入密码测试强度"
            show-password
            @input="checkPasswordStrength"
          />
          <div v-if="passwordStrength" class="password-strength">
            <div class="strength-bar">
              <div 
                class="strength-fill"
                :class="passwordStrength.level"
                :style="{ width: passwordStrength.score + '%' }"
              ></div>
            </div>
            <div class="strength-text">
              强度：{{ passwordStrength.text }}
            </div>
            <ul v-if="passwordStrength.suggestions.length" class="strength-suggestions">
              <li v-for="suggestion in passwordStrength.suggestions" :key="suggestion">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </el-form-item>
      </div>
      
      <el-form-item class="form-actions">
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存设置
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button
          type="warning"
          @click="generateSecureDefaults"
        >
          生成安全默认值
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'
import type { SystemSettings } from '@/types/settings'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  settings: SystemSettings
}

interface Emits {
  (e: 'update', settings: Partial<SystemSettings>): void
  (e: 'save', settings: Partial<SystemSettings>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const saving = ref(false)
const testPassword = ref('')
const passwordStrength = ref<any>(null)

const formData = reactive<Partial<SystemSettings>>({
  access_token_expire_minutes: 30,
  refresh_token_expire_days: 7,
  session_timeout_minutes: 60,
  max_login_attempts: 5,
  lockout_duration_minutes: 15,
  password_min_length: 8,
  password_require_uppercase: true,
  password_require_lowercase: true,
  password_require_numbers: true,
  password_require_special: true,
  two_factor_auth_enabled: false
})

const formRules: FormRules = {
  access_token_expire_minutes: [
    { required: true, message: '请设置访问令牌过期时间', trigger: 'blur' }
  ],
  refresh_token_expire_days: [
    { required: true, message: '请设置刷新令牌过期时间', trigger: 'blur' }
  ],
  session_timeout_minutes: [
    { required: true, message: '请设置会话超时时间', trigger: 'blur' }
  ],
  max_login_attempts: [
    { required: true, message: '请设置最大登录尝试次数', trigger: 'blur' }
  ],
  lockout_duration_minutes: [
    { required: true, message: '请设置账户锁定时长', trigger: 'blur' }
  ],
  password_min_length: [
    { required: true, message: '请设置密码最小长度', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  Object.assign(formData, newSettings)
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newData) => {
  emit('update', newData)
}, { deep: true })

const checkPasswordStrength = (password: string) => {
  if (!password) {
    passwordStrength.value = null
    return
  }
  
  let score = 0
  let level = 'weak'
  let text = '弱'
  const suggestions: string[] = []
  
  // 长度检查
  if (password.length >= 8) score += 20
  else suggestions.push('密码长度至少8个字符')
  
  if (password.length >= 12) score += 10
  
  // 字符类型检查
  if (/[a-z]/.test(password)) score += 20
  else suggestions.push('包含小写字母')
  
  if (/[A-Z]/.test(password)) score += 20
  else suggestions.push('包含大写字母')
  
  if (/\d/.test(password)) score += 15
  else suggestions.push('包含数字')
  
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 15
  else suggestions.push('包含特殊字符')
  
  // 确定强度等级
  if (score >= 80) {
    level = 'strong'
    text = '强'
  } else if (score >= 60) {
    level = 'medium'
    text = '中等'
  }
  
  passwordStrength.value = {
    score,
    level,
    text,
    suggestions
  }
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    await ElMessageBox.confirm(
      '安全设置的修改可能影响所有用户的登录状态，确定要保存吗？',
      '确认保存安全设置',
      {
        type: 'warning',
        confirmButtonText: '确定保存',
        cancelButtonText: '取消'
      }
    )
    
    saving.value = true
    emit('save', formData)
    
    setTimeout(() => {
      saving.value = false
    }, 1000)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('表单验证失败，请检查输入')
    }
  }
}

const handleReset = () => {
  ElMessageBox.confirm('确定要重置安全设置吗？', '确认重置', {
    type: 'warning'
  }).then(() => {
    formRef.value?.resetFields()
    Object.assign(formData, props.settings)
    ElMessage.success('安全设置已重置')
  }).catch(() => {
    // 用户取消
  })
}

const generateSecureDefaults = () => {
  ElMessageBox.confirm(
    '将应用推荐的安全配置，这将覆盖当前设置。确定继续吗？',
    '应用安全默认值',
    { type: 'info' }
  ).then(() => {
    Object.assign(formData, {
      access_token_expire_minutes: 15,
      refresh_token_expire_days: 3,
      session_timeout_minutes: 30,
      max_login_attempts: 3,
      lockout_duration_minutes: 30,
      password_min_length: 12,
      password_require_uppercase: true,
      password_require_lowercase: true,
      password_require_numbers: true,
      password_require_special: true,
      two_factor_auth_enabled: true
    })
    ElMessage.success('已应用安全默认配置')
  }).catch(() => {
    // 用户取消
  })
}

onMounted(() => {
  Object.assign(formData, props.settings)
})
</script>

<style lang="scss" scoped>
.settings-card {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .settings-form {
    .settings-section {
      margin-bottom: 32px;
      
      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }
    
    .field-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
      line-height: 1.4;
    }
    
    .unit-text {
      margin-left: 8px;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
    
    .password-requirements {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .password-strength {
      margin-top: 8px;
      
      .strength-bar {
        width: 100%;
        height: 6px;
        background: var(--el-border-color-lighter);
        border-radius: 3px;
        overflow: hidden;
        
        .strength-fill {
          height: 100%;
          transition: all 0.3s ease;
          
          &.weak {
            background: var(--el-color-danger);
          }
          
          &.medium {
            background: var(--el-color-warning);
          }
          
          &.strong {
            background: var(--el-color-success);
          }
        }
      }
      
      .strength-text {
        margin-top: 4px;
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
      
      .strength-suggestions {
        margin: 8px 0 0 0;
        padding-left: 16px;
        font-size: 12px;
        color: var(--el-color-warning);
        
        li {
          margin-bottom: 2px;
        }
      }
    }
    
    .form-actions {
      margin-top: 32px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
</style>
