/**
 * 软件更新推送管理API接口
 */
import request from './request'

// ==================== 版本管理 ====================

/**
 * 获取应用版本列表
 */
export const getAppVersions = (params: {
  page?: number
  size?: number
  keyword?: string
  status?: string
  update_type?: string
}) => {
  return request({
    url: '/v1/app-updates/versions',
    method: 'get',
    params
  })
}

/**
 * 获取应用版本详情
 */
export const getAppVersion = (versionId: string) => {
  return request({
    url: `/api/v1/app-updates/versions/${versionId}`,
    method: 'get'
  })
}

/**
 * 创建应用版本
 */
export const createAppVersion = (data: {
  version_number: string
  version_name: string
  description?: string
  update_type: string
  package_url?: string
  package_size?: number
  package_md5?: string
  min_supported_version?: string
  target_platforms: string[]
  release_notes: string
  is_force_update: boolean
  rollout_percentage?: number
  status?: string
}) => {
  return request({
    url: '/api/v1/app-updates/versions',
    method: 'post',
    data
  })
}

/**
 * 更新应用版本
 */
export const updateAppVersion = (versionId: string, data: any) => {
  return request({
    url: `/api/v1/app-updates/versions/${versionId}`,
    method: 'put',
    data
  })
}

/**
 * 发布应用版本
 */
export const releaseAppVersion = (versionId: string) => {
  return request({
    url: `/api/v1/app-updates/versions/${versionId}/release`,
    method: 'post'
  })
}

/**
 * 上传应用安装包
 */
export const uploadAppPackage = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/v1/app-updates/versions/upload-package',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 推送策略管理 ====================

/**
 * 获取更新推送策略列表
 */
export const getUpdateStrategies = (params: {
  page?: number
  size?: number
  version_id?: string
  strategy_type?: string
  status?: string
}) => {
  return request({
    url: '/v1/app-updates/strategies',
    method: 'get',
    params
  })
}

/**
 * 创建更新推送策略
 */
export const createUpdateStrategy = (data: {
  version_id: string
  strategy_name: string
  strategy_type: string
  target_criteria: any
  rollout_percentage: number
  rollout_schedule?: any
  notification_settings: any
  auto_install_settings?: any
  fallback_strategy?: any
  status?: string
}) => {
  return request({
    url: '/api/v1/app-updates/strategies',
    method: 'post',
    data
  })
}

/**
 * 更新推送策略
 */
export const updateUpdateStrategy = (strategyId: string, data: any) => {
  return request({
    url: `/api/v1/app-updates/strategies/${strategyId}`,
    method: 'put',
    data
  })
}

/**
 * 激活推送策略
 */
export const activateUpdateStrategy = (strategyId: string) => {
  return request({
    url: `/api/v1/app-updates/strategies/${strategyId}/activate`,
    method: 'post'
  })
}

/**
 * 暂停推送策略
 */
export const pauseUpdateStrategy = (strategyId: string) => {
  return request({
    url: `/api/v1/app-updates/strategies/${strategyId}/pause`,
    method: 'post'
  })
}

/**
 * 回滚推送策略
 */
export const rollbackUpdateStrategy = (strategyId: string) => {
  return request({
    url: `/api/v1/app-updates/strategies/${strategyId}/rollback`,
    method: 'post'
  })
}

// ==================== 更新记录管理 ====================

/**
 * 获取更新记录列表
 */
export const getUpdateRecords = (params: {
  page?: number
  size?: number
  version_id?: string
  user_id?: string
  status?: string
  update_type?: string
}) => {
  return request({
    url: '/v1/app-updates/records',
    method: 'get',
    params
  })
}

/**
 * 获取更新记录详情
 */
export const getUpdateRecord = (recordId: string) => {
  return request({
    url: `/api/v1/app-updates/records/${recordId}`,
    method: 'get'
  })
}

/**
 * 创建更新记录
 */
export const createUpdateRecord = (data: {
  user_id: string
  version_id: string
  strategy_id?: string
  update_type: string
  device_info: any
  network_info?: any
  status?: string
}) => {
  return request({
    url: '/api/v1/app-updates/records',
    method: 'post',
    data
  })
}

// ==================== 更新通知管理 ====================

/**
 * 获取更新通知列表
 */
export const getUpdateNotifications = (params: {
  page?: number
  size?: number
  version_id?: string
  status?: string
}) => {
  return request({
    url: '/v1/app-updates/notifications',
    method: 'get',
    params
  })
}

/**
 * 创建更新通知
 */
export const createUpdateNotification = (data: {
  version_id: string
  title: string
  content: string
  notification_type: string
  target_users?: string[]
  target_criteria?: any
  send_time?: string
  priority: string
  channels: string[]
  template_data?: any
}) => {
  return request({
    url: '/api/v1/app-updates/notifications',
    method: 'post',
    data
  })
}

/**
 * 发送更新通知
 */
export const sendUpdateNotification = (notificationId: string) => {
  return request({
    url: `/api/v1/app-updates/notifications/${notificationId}/send`,
    method: 'post'
  })
}

// ==================== 更新统计 ====================

/**
 * 获取软件更新推送概览统计
 */
export const getAppUpdatesOverview = () => {
  return request({
    url: '/v1/app-updates/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取版本统计
 */
export const getVersionStatistics = () => {
  return request({
    url: '/v1/app-updates/statistics/versions',
    method: 'get'
  })
}

/**
 * 获取下载统计
 */
export const getDownloadStatistics = () => {
  return request({
    url: '/v1/app-updates/statistics/downloads',
    method: 'get'
  })
}

/**
 * 获取安装统计
 */
export const getInstallationStatistics = () => {
  return request({
    url: '/v1/app-updates/statistics/installations',
    method: 'get'
  })
}

/**
 * 获取错误统计
 */
export const getErrorStatistics = () => {
  return request({
    url: '/v1/app-updates/statistics/errors',
    method: 'get'
  })
}

// ==================== 强制更新控制 ====================

/**
 * 创建强制更新
 */
export const createForceUpdate = (data: {
  version_id: string
  target_criteria: any
  force_update_message: string
  grace_period_hours?: number
  bypass_conditions?: string[]
  emergency_contact?: any
}) => {
  return request({
    url: '/v1/app-updates/force-update',
    method: 'post',
    data
  })
}

/**
 * 获取强制更新状态
 */
export const getForceUpdateStatus = () => {
  return request({
    url: '/v1/app-updates/force-update/status',
    method: 'get'
  })
}

// ==================== 数据类型定义 ====================

export interface AppVersion {
  id: string
  version_number: string
  version_name: string
  description?: string
  update_type: string
  package_url?: string
  package_size?: number
  package_md5?: string
  min_supported_version?: string
  target_platforms: string[]
  release_notes: string
  is_force_update: boolean
  rollout_percentage: number
  status: string
  download_count: number
  install_count: number
  success_rate: number
  error_count: number
  created_at: string
  updated_at: string
  released_at?: string
}

export interface UpdateStrategy {
  id: string
  version_id: string
  strategy_name: string
  strategy_type: string
  target_criteria: any
  rollout_percentage: number
  rollout_schedule?: any
  notification_settings: any
  auto_install_settings?: any
  fallback_strategy?: any
  status: string
  affected_users: number
  success_count: number
  failure_count: number
  created_at: string
  updated_at: string
  activated_at?: string
}

export interface UpdateRecord {
  id: string
  user_id: string
  version_id: string
  strategy_id?: string
  update_type: string
  device_info: any
  network_info?: any
  status: string
  download_start_time?: string
  download_end_time?: string
  install_start_time?: string
  install_end_time?: string
  error_message?: string
  error_code?: string
  retry_count: number
  created_at: string
  updated_at: string
}

export interface UpdateNotification {
  id: string
  version_id: string
  title: string
  content: string
  notification_type: string
  target_users: string[]
  target_criteria?: any
  send_time?: string
  priority: string
  channels: string[]
  template_data?: any
  status: string
  sent_count: number
  delivered_count: number
  read_count: number
  click_count: number
  created_at: string
  updated_at: string
  sent_at?: string
}

export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}