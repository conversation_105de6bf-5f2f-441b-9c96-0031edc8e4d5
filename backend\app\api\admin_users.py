"""
WisCude 后台管理系统 - 管理员用户管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc, func
from datetime import datetime
import logging

from app.core.database import get_db
from app.core.security import get_current_user, get_current_active_superuser
from app.models.admin import AdminUser, UserRole
from app.schemas.admin import (
    AdminUserResponse, AdminUserCreate, AdminUserUpdate, 
    AdminUserListResponse, PaginationParams
)

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=AdminUserListResponse)
async def get_admin_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词(用户名、邮箱、姓名)"),
    role: Optional[UserRole] = Query(None, description="用户角色"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    sort_by: Optional[str] = Query("created_at", description="排序字段"),
    sort_order: Optional[str] = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_active_superuser)
):
    """获取管理员用户列表"""
    try:
        # 构建查询
        query = db.query(AdminUser)
        
        # 搜索过滤
        if search:
            search_filter = or_(
                AdminUser.username.ilike(f"%{search}%"),
                AdminUser.email.ilike(f"%{search}%"),
                AdminUser.full_name.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # 角色过滤
        if role:
            query = query.filter(AdminUser.role == role)
        
        # 状态过滤
        if is_active is not None:
            query = query.filter(AdminUser.is_active == is_active)
        
        # 排序
        if sort_order == "desc":
            query = query.order_by(desc(getattr(AdminUser, sort_by, AdminUser.created_at)))
        else:
            query = query.order_by(getattr(AdminUser, sort_by, AdminUser.created_at))
        
        # 分页
        total = query.count()
        offset = (page - 1) * page_size
        admin_users = query.offset(offset).limit(page_size).all()
        
        return AdminUserListResponse(
            items=[AdminUserResponse.from_orm(user) for user in admin_users],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=(total + page_size - 1) // page_size
        )
        
    except Exception as e:
        logger.error(f"获取管理员用户列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取管理员用户列表失败"
        )

@router.post("/", response_model=AdminUserResponse, status_code=status.HTTP_201_CREATED)
async def create_admin_user(
    admin_data: AdminUserCreate,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_active_superuser)
):
    """创建新的管理员用户"""
    try:
        # 检查用户名是否已存在
        existing_username = db.query(AdminUser).filter(AdminUser.username == admin_data.username).first()
        if existing_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已被使用"
            )
        
        # 检查邮箱是否已存在
        existing_email = db.query(AdminUser).filter(AdminUser.email == admin_data.email).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被使用"
            )
        
        # 创建新管理员用户
        new_admin = AdminUser(
            username=admin_data.username,
            email=admin_data.email,
            full_name=admin_data.full_name,
            role=admin_data.role,
            is_active=admin_data.is_active,
            is_superuser=admin_data.is_superuser,
            phone=getattr(admin_data, 'phone', None),
            department=getattr(admin_data, 'department', None),
            position=getattr(admin_data, 'position', None),
            bio=admin_data.bio
        )
        new_admin.set_password(admin_data.password)
        
        db.add(new_admin)
        db.commit()
        db.refresh(new_admin)
        
        logger.info(f"管理员 {current_user.username} 创建了新管理员用户 {new_admin.username}")
        
        return AdminUserResponse.from_orm(new_admin)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建管理员用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建管理员用户失败"
        )

@router.get("/{admin_id}", response_model=AdminUserResponse)
async def get_admin_user(
    admin_id: str,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """获取管理员用户详情"""
    admin_user = db.query(AdminUser).filter(AdminUser.id == admin_id).first()
    if not admin_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员用户不存在"
        )
    return AdminUserResponse.from_orm(admin_user)

@router.put("/{admin_id}", response_model=AdminUserResponse)
async def update_admin_user(
    admin_id: str,
    admin_update: AdminUserUpdate,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_active_superuser)
):
    """更新管理员用户信息"""
    try:
        admin_user = db.query(AdminUser).filter(AdminUser.id == admin_id).first()
        if not admin_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="管理员用户不存在"
            )
        
        # 检查用户名唯一性
        if admin_update.username and admin_update.username != admin_user.username:
            existing_username = db.query(AdminUser).filter(
                AdminUser.username == admin_update.username,
                AdminUser.id != admin_id
            ).first()
            if existing_username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已被使用"
                )
        
        # 检查邮箱唯一性
        if admin_update.email and admin_update.email != admin_user.email:
            existing_email = db.query(AdminUser).filter(
                AdminUser.email == admin_update.email,
                AdminUser.id != admin_id
            ).first()
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被使用"
                )
        
        # 更新字段
        update_data = admin_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(admin_user, field, value)
        
        admin_user.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(admin_user)
        
        logger.info(f"管理员 {current_user.username} 更新了管理员用户 {admin_user.username}")
        
        return AdminUserResponse.from_orm(admin_user)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新管理员用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新管理员用户失败"
        )

@router.delete("/{admin_id}")
async def delete_admin_user(
    admin_id: str,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_active_superuser)
):
    """删除管理员用户"""
    try:
        admin_user = db.query(AdminUser).filter(AdminUser.id == admin_id).first()
        if not admin_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="管理员用户不存在"
            )
        
        # 防止删除自己
        if admin_user.id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己的账户"
            )
        
        # 防止删除最后一个超级管理员
        if admin_user.is_superuser:
            superuser_count = db.query(AdminUser).filter(AdminUser.is_superuser == True).count()
            if superuser_count <= 1:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能删除最后一个超级管理员"
                )
        
        db.delete(admin_user)
        db.commit()
        
        logger.info(f"管理员 {current_user.username} 删除了管理员用户 {admin_user.username}")
        
        return {"message": "管理员用户删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除管理员用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除管理员用户失败"
        )

@router.post("/{admin_id}/toggle-status")
async def toggle_admin_user_status(
    admin_id: str,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_active_superuser)
):
    """切换管理员用户状态"""
    try:
        admin_user = db.query(AdminUser).filter(AdminUser.id == admin_id).first()
        if not admin_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="管理员用户不存在"
            )
        
        # 防止禁用自己
        if admin_user.id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能禁用自己的账户"
            )
        
        admin_user.is_active = not admin_user.is_active
        admin_user.updated_at = datetime.utcnow()
        db.commit()
        
        status_text = "激活" if admin_user.is_active else "禁用"
        logger.info(f"管理员 {current_user.username} {status_text}了管理员用户 {admin_user.username}")
        
        return {"message": f"管理员用户已{status_text}"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"切换管理员用户状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="切换管理员用户状态失败"
        )
