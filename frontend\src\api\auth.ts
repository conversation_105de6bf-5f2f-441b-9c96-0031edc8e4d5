import request from './request'
import type { LoginRequest, ChangePasswordRequest, AdminUser, TokenResponse } from '@/types/auth'

export const authApi = {
  /**
   * 用户登录
   * @param data 登录数据
   */
  login(data: LoginRequest): Promise<TokenResponse> {
    // 使用 URLSearchParams 创建 application/x-www-form-urlencoded 格式的数据
    const params = new URLSearchParams()
    params.append('username', data.username)
    params.append('password', data.password)

    return request({
      url: '/auth/login',
      method: 'post',
      data: params,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },

  /**
   * 用户登出
   */
  logout(): Promise<any> {
    return request({
      url: '/auth/logout',
      method: 'post'
    })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): Promise<AdminUser> {
    return request({
      url: '/auth/me',
      method: 'get'
    })
  },

  /**
   * 刷新令牌
   * @param refreshToken 刷新令牌
   */
  refreshToken(refreshToken: string): Promise<TokenResponse> {
    return request({
      url: '/auth/refresh',
      method: 'post',
      data: { refresh_token: refreshToken },
      loading: false
    })
  },

  /**
   * 检查令牌有效性
   */
  checkToken(): Promise<any> {
    return request({
      url: '/auth/check',
      method: 'get',
      loading: false
    })
  },

  /**
   * 修改密码
   * @param data 密码数据
   */
  changePassword(data: ChangePasswordRequest): Promise<any> {
    return request({
      url: '/auth/change-password',
      method: 'post',
      data
    })
  }
}
