"""
系统设置相关的Pydantic模式
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class LogFileInfo(BaseModel):
    """日志文件信息"""
    name: str
    size: int
    modified: str
    path: str

class SystemInfo(BaseModel):
    """系统信息"""
    version: str
    uptime: int
    uptime_formatted: str
    memory_usage: Dict[str, Any]
    cpu_usage: float
    disk_usage: Dict[str, Any]
    process_count: int
    network_connections: int
    database_status: str
    cache_status: str
    timestamp: str

class HealthCheck(BaseModel):
    """健康检查结果"""
    status: str
    checks: Dict[str, Dict[str, Any]]
    timestamp: str

class ProcessInfo(BaseModel):
    """进程信息"""
    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    status: str

class ProcessListResponse(BaseModel):
    """进程列表响应"""
    processes: List[ProcessInfo]
    total_count: int

class LogStats(BaseModel):
    """日志统计"""
    total_files: int
    total_size: int
    total_size_mb: float
    level_counts: Dict[str, int]

class PerformanceMetrics(BaseModel):
    """性能指标"""
    cpu: Dict[str, Any]
    memory: Dict[str, Any]
    disk_io: Dict[str, Any]
    network_io: Dict[str, Any]
    timestamp: str

class EmailTemplate(BaseModel):
    """邮件模板"""
    name: str
    subject: str
    content: str
    variables: List[str]

class SettingsValidationRequest(BaseModel):
    """设置验证请求"""
    key: str
    value: Any

class SettingsValidationResponse(BaseModel):
    """设置验证响应"""
    valid: bool
    message: Optional[str] = None

class SettingsApplyRequest(BaseModel):
    """设置应用请求"""
    settings: Dict[str, Any]

class SettingsApplyResponse(BaseModel):
    """设置应用响应"""
    success: bool
    message: str
    requires_restart: bool

class LogCleanupResponse(BaseModel):
    """日志清理响应"""
    deleted: int
    message: str

class TestEmailRequest(BaseModel):
    """测试邮件请求"""
    email: str

class SystemRestartResponse(BaseModel):
    """系统重启响应"""
    success: bool
    message: str
