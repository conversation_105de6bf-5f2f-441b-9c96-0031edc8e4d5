{"name": "wiscude-admin-system", "version": "1.0.0", "description": "WisCude 后台管理系统 - FastAPI + Vue 3", "scripts": {"dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "preview": "cd frontend && npm run preview", "backend": "python main.py", "start": "concurrently \"python main.py\" \"cd frontend && npm run dev\"", "install": "cd frontend && npm install", "install-backend": "cd backend && pip install -e .", "setup": "npm run install && npm run install-backend"}, "keywords": ["wiscude", "admin", "management", "system", "<PERSON><PERSON><PERSON>", "vue"], "author": "WisCude Team", "license": "MIT", "engines": {"node": ">=18.0.0", "python": ">=3.12.0"}, "devDependencies": {"concurrently": "^8.2.2"}}