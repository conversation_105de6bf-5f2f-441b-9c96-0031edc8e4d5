// 认证相关类型定义

export interface LoginRequest {
  username: string
  password: string
}

export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
}

export interface ChangePasswordRequest {
  current_password: string
  new_password: string
}

export interface AdminUser {
  id: string
  username: string
  email: string
  full_name?: string
  role: 'superadmin' | 'admin' | 'editor' | 'viewer'
  is_active: boolean
  is_superuser: boolean
  last_login?: string
  avatar_url?: string
  bio?: string
  created_at: string
  updated_at: string
}
