<template>
  <div class="instructors-management">
    <div class="page-header">
      <h1>讲师管理</h1>
      <p>管理讲师信息、资质认证和教学统计</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增讲师
        </el-button>
        <el-button 
          type="success" 
          :disabled="selectedInstructors.length === 0"
          @click="batchActivate"
        >
          批量启用
        </el-button>
        <el-button 
          type="warning" 
          :disabled="selectedInstructors.length === 0"
          @click="batchDeactivate"
        >
          批量禁用
        </el-button>
        <el-button @click="exportInstructors">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索讲师姓名"
          style="width: 200px"
          clearable
          @change="loadInstructors"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="departmentFilter" placeholder="部门筛选" style="width: 120px" @change="loadInstructors">
          <el-option label="全部部门" value="" />
          <el-option label="计算机系" value="cs" />
          <el-option label="数学系" value="math" />
          <el-option label="英语系" value="english" />
          <el-option label="物理系" value="physics" />
        </el-select>
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadInstructors">
          <el-option label="全部状态" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="禁用" value="inactive" />
          <el-option label="待审核" value="pending" />
        </el-select>
      </div>
    </div>

    <!-- 讲师列表 -->
    <el-table
      :data="instructors"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="讲师信息" min-width="250">
        <template #default="{ row }">
          <div class="instructor-info">
            <div class="instructor-avatar">
              <img v-if="row.avatar" :src="row.avatar" :alt="row.name" />
              <div v-else class="avatar-placeholder">{{ row.name.charAt(0) }}</div>
            </div>
            <div class="instructor-details">
              <div class="instructor-name">{{ row.name }}</div>
              <div class="instructor-title">{{ row.title }}</div>
              <div class="instructor-department">{{ getDepartmentName(row.department) }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" width="200">
        <template #default="{ row }">
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>{{ row.phone }}</span>
            </div>
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span>{{ row.email }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="专业领域" width="200">
        <template #default="{ row }">
          <div class="specialties">
            <el-tag 
              v-for="specialty in row.specialties" 
              :key="specialty" 
              size="small" 
              class="specialty-tag"
            >
              {{ specialty }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="教学统计" width="150">
        <template #default="{ row }">
          <div class="teaching-stats">
            <div class="stat-item">
              <span class="stat-label">课程:</span>
              <span class="stat-value">{{ row.courses }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">学生:</span>
              <span class="stat-value students">{{ row.students }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">评分:</span>
              <span class="stat-value rating">{{ row.rating }}分</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="资质认证" width="120">
        <template #default="{ row }">
          <div class="certifications">
            <el-tag 
              v-for="cert in row.certifications" 
              :key="cert" 
              size="small" 
              type="success"
              class="cert-tag"
            >
              {{ cert }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusName(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="入职时间" width="120">
        <template #default="{ row }">
          <span class="join-time">{{ formatTime(row.joined_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="viewInstructor(row)">
            <el-icon><View /></el-icon>
            查看
          </el-button>
          <el-button type="text" size="small" @click="editInstructor(row)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button type="text" size="small" @click="viewCourses(row)">
            <el-icon><Reading /></el-icon>
            课程
          </el-button>
          <el-button type="text" size="small" @click="viewAnalytics(row)">
            <el-icon><DataAnalysis /></el-icon>
            分析
          </el-button>
          <el-button 
            type="text" 
            size="small" 
            :class="row.status === 'active' ? 'warning' : 'success'"
            @click="toggleStatus(row)"
          >
            <el-icon><Switch /></el-icon>
            {{ row.status === 'active' ? '禁用' : '启用' }}
          </el-button>
          <el-button type="text" size="small" class="danger" @click="deleteInstructor(row)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadInstructors"
        @current-change="loadInstructors"
      />
    </div>

    <!-- 创建/编辑讲师对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingInstructor ? '编辑讲师' : '新增讲师'"
      width="700px"
      @close="resetForm"
    >
      <el-form :model="instructorForm" :rules="instructorRules" ref="instructorFormRef" label-width="100px">
        <el-form-item label="讲师姓名" prop="name">
          <el-input v-model="instructorForm.name" placeholder="请输入讲师姓名" />
        </el-form-item>
        <el-form-item label="职位头衔" prop="title">
          <el-input v-model="instructorForm.title" placeholder="请输入职位头衔" />
        </el-form-item>
        <el-form-item label="所属部门" prop="department">
          <el-select v-model="instructorForm.department" placeholder="请选择所属部门">
            <el-option label="计算机系" value="cs" />
            <el-option label="数学系" value="math" />
            <el-option label="英语系" value="english" />
            <el-option label="物理系" value="physics" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="instructorForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱地址" prop="email">
          <el-input v-model="instructorForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        <el-form-item label="专业领域">
          <el-select v-model="instructorForm.specialties" multiple placeholder="请选择专业领域">
            <el-option label="前端开发" value="frontend" />
            <el-option label="后端开发" value="backend" />
            <el-option label="数据科学" value="data-science" />
            <el-option label="人工智能" value="ai" />
            <el-option label="移动开发" value="mobile" />
            <el-option label="数据库" value="database" />
          </el-select>
        </el-form-item>
        <el-form-item label="资质认证">
          <el-select v-model="instructorForm.certifications" multiple placeholder="请选择资质认证">
            <el-option label="博士" value="phd" />
            <el-option label="硕士" value="master" />
            <el-option label="教授" value="professor" />
            <el-option label="副教授" value="associate-professor" />
            <el-option label="讲师" value="lecturer" />
          </el-select>
        </el-form-item>
        <el-form-item label="个人简介" prop="bio">
          <el-input v-model="instructorForm.bio" type="textarea" rows="4" placeholder="请输入个人简介" />
        </el-form-item>
        <el-form-item label="头像上传">
          <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="uploadAvatar"
            accept="image/*"
          >
            <img v-if="instructorForm.avatar" :src="instructorForm.avatar" class="avatar-preview" />
            <div v-else class="avatar-upload-placeholder">
              <el-icon><Plus /></el-icon>
              <div class="upload-text">上传头像</div>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveInstructor" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Download, Phone, Message, View, Edit, Reading, DataAnalysis, 
  Switch, Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const selectedInstructors = ref([])
const showCreateDialog = ref(false)
const editingInstructor = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const departmentFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 讲师列表
const instructors = ref([
  {
    id: '1',
    name: '张教授',
    title: '高级讲师',
    department: 'cs',
    phone: '13800138001',
    email: '<EMAIL>',
    specialties: ['前端开发', '数据科学'],
    courses: 8,
    students: 234,
    rating: 4.8,
    certifications: ['博士', '教授'],
    status: 'active',
    avatar: '',
    joined_at: '2020-03-15'
  },
  {
    id: '2',
    name: '李老师',
    title: '讲师',
    department: 'math',
    phone: '13800138002',
    email: '<EMAIL>',
    specialties: ['数据科学', '人工智能'],
    courses: 5,
    students: 189,
    rating: 4.6,
    certifications: ['硕士', '讲师'],
    status: 'active',
    avatar: '',
    joined_at: '2021-09-01'
  }
])

// 表单数据
const instructorForm = reactive({
  name: '',
  title: '',
  department: '',
  phone: '',
  email: '',
  specialties: [],
  certifications: [],
  bio: '',
  avatar: ''
})

// 表单验证规则
const instructorRules = {
  name: [
    { required: true, message: '请输入讲师姓名', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 方法
const loadInstructors = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取讲师列表
    console.log('Loading instructors...')
  } catch (error) {
    ElMessage.error('加载讲师列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedInstructors.value = selection
}

const getDepartmentName = (department) => {
  const departments = {
    cs: '计算机系',
    math: '数学系',
    english: '英语系',
    physics: '物理系'
  }
  return departments[department] || department
}

const getStatusName = (status) => {
  const statuses = {
    active: '活跃',
    inactive: '禁用',
    pending: '待审核'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    inactive: 'danger',
    pending: 'warning'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewInstructor = (instructor) => {
  // TODO: 查看讲师详情
  console.log('Viewing instructor:', instructor)
}

const editInstructor = (instructor) => {
  editingInstructor.value = instructor
  Object.assign(instructorForm, instructor)
  showCreateDialog.value = true
}

const viewCourses = (instructor) => {
  // TODO: 查看讲师课程
  console.log('Viewing courses for instructor:', instructor)
}

const viewAnalytics = (instructor) => {
  // TODO: 查看讲师分析
  console.log('Viewing analytics for instructor:', instructor)
}

const toggleStatus = async (instructor) => {
  // TODO: 切换讲师状态
  console.log('Toggling status for instructor:', instructor)
}

const deleteInstructor = async (instructor) => {
  try {
    await ElMessageBox.confirm('确定要删除这个讲师吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting instructor:', instructor)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const resetForm = () => {
  editingInstructor.value = null
  Object.assign(instructorForm, {
    name: '',
    title: '',
    department: '',
    phone: '',
    email: '',
    specialties: [],
    certifications: [],
    bio: '',
    avatar: ''
  })
}

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const uploadAvatar = async (options) => {
  // TODO: 实现头像上传逻辑
  console.log('Uploading avatar...', options.file)
  instructorForm.avatar = URL.createObjectURL(options.file)
  ElMessage.success('头像上传成功')
}

const saveInstructor = async () => {
  saving.value = true
  try {
    // TODO: 实现保存逻辑
    console.log('Saving instructor...', instructorForm)
    showCreateDialog.value = false
    ElMessage.success(editingInstructor.value ? '讲师更新成功' : '讲师创建成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const batchActivate = async () => {
  // TODO: 实现批量启用逻辑
  console.log('Batch activating instructors...', selectedInstructors.value)
}

const batchDeactivate = async () => {
  // TODO: 实现批量禁用逻辑
  console.log('Batch deactivating instructors...', selectedInstructors.value)
}

const exportInstructors = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting instructors...')
}

onMounted(() => {
  loadInstructors()
})
</script>

<style scoped>
.instructors-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.instructor-info {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
}

.instructor-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.instructor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.instructor-details {
  flex: 1;
}

.instructor-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.instructor-title {
  font-size: 13px;
  color: #606266;
  margin-bottom: 2px;
}

.instructor-department {
  font-size: 12px;
  color: #909399;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
}

.specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.specialty-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
  font-size: 11px;
}

.teaching-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.stat-value.students {
  color: #409eff !important;
}

.stat-value.rating {
  color: #e6a23c !important;
}

.certifications {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.cert-tag {
  font-size: 11px;
}

.join-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-preview {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-upload-placeholder {
  width: 80px;
  height: 80px;
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-upload-placeholder:hover {
  border-color: #409eff;
  color: #409eff;
}

.upload-text {
  font-size: 12px;
  margin-top: 4px;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
