"""
WisCude 后台管理系统 - 系统设置服务
"""
import json
import os
import smtplib
from datetime import datetime
from typing import Dict, Any, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from cryptography.fernet import Fernet
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
import logging

from app.models.settings import SystemSettings, SystemSettingsUpdate, SystemSettingsResponse
from app.core.database import get_db

logger = logging.getLogger(__name__)

class SettingsService:
    """系统设置服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self._encryption_key = self._get_or_create_encryption_key()
        self._cipher = Fernet(self._encryption_key)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """获取或创建加密密钥"""
        key_file = "backend/.encryption_key"
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            os.makedirs(os.path.dirname(key_file), exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def _encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        if not data:
            return data
        return self._cipher.encrypt(data.encode()).decode()
    
    def _decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        if not encrypted_data:
            return encrypted_data
        try:
            return self._cipher.decrypt(encrypted_data.encode()).decode()
        except Exception:
            return encrypted_data  # 如果解密失败，返回原数据
    
    def get_settings(self) -> SystemSettingsResponse:
        """获取系统设置"""
        settings = self.db.query(SystemSettings).filter(
            SystemSettings.id == "system_config"
        ).first()
        
        if not settings:
            # 创建默认设置
            settings = self._create_default_settings()
        
        # 解密敏感数据
        settings_dict = {
            column.name: getattr(settings, column.name)
            for column in settings.__table__.columns
        }
        
        # 解密敏感字段
        if settings_dict.get('secret_key'):
            settings_dict['secret_key'] = self._decrypt_sensitive_data(settings_dict['secret_key'])
        if settings_dict.get('smtp_password'):
            settings_dict['smtp_password'] = self._decrypt_sensitive_data(settings_dict['smtp_password'])
        
        return SystemSettingsResponse(**settings_dict)
    
    def update_settings(self, settings_update: SystemSettingsUpdate, updated_by: str) -> SystemSettingsResponse:
        """更新系统设置"""
        settings = self.db.query(SystemSettings).filter(
            SystemSettings.id == "system_config"
        ).first()
        
        if not settings:
            settings = SystemSettings(id="system_config")
            self.db.add(settings)
        
        # 更新设置
        update_data = settings_update.dict(exclude_unset=True)
        
        # 加密敏感数据
        if 'secret_key' in update_data and update_data['secret_key']:
            update_data['secret_key'] = self._encrypt_sensitive_data(update_data['secret_key'])
        if 'smtp_password' in update_data and update_data['smtp_password']:
            update_data['smtp_password'] = self._encrypt_sensitive_data(update_data['smtp_password'])
        
        # 更新字段
        for field, value in update_data.items():
            if hasattr(settings, field):
                setattr(settings, field, value)
        
        settings.updated_at = datetime.utcnow()
        settings.updated_by = updated_by
        
        self.db.commit()
        self.db.refresh(settings)
        
        # 记录设置变更日志
        self._log_settings_change(updated_by, list(update_data.keys()))
        
        return self.get_settings()
    
    def reset_to_default(self, updated_by: str) -> SystemSettingsResponse:
        """重置为默认设置"""
        settings = self.db.query(SystemSettings).filter(
            SystemSettings.id == "system_config"
        ).first()
        
        if settings:
            self.db.delete(settings)
            self.db.commit()
        
        # 创建新的默认设置
        default_settings = self._create_default_settings()
        default_settings.updated_by = updated_by
        
        self._log_settings_change(updated_by, ["reset_to_default"])
        
        return self.get_settings()
    
    def export_settings(self) -> Dict[str, Any]:
        """导出设置"""
        settings = self.get_settings()
        
        # 移除敏感信息
        export_data = settings.dict()
        sensitive_fields = ['secret_key', 'smtp_password', 'database_url']
        for field in sensitive_fields:
            if field in export_data:
                export_data[field] = "***HIDDEN***"
        
        return {
            "settings": export_data,
            "exported_at": datetime.utcnow().isoformat(),
            "version": "1.0.0"
        }
    
    def import_settings(self, import_data: Dict[str, Any], updated_by: str, overwrite: bool = False) -> SystemSettingsResponse:
        """导入设置"""
        if not overwrite:
            # 只更新非敏感字段
            sensitive_fields = ['secret_key', 'smtp_password', 'database_url']
            for field in sensitive_fields:
                import_data.pop(field, None)
        
        settings_update = SystemSettingsUpdate(**import_data)
        return self.update_settings(settings_update, updated_by)
    
    def test_database_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            database_url = config.get('database_url')
            if not database_url:
                return {"success": False, "message": "数据库URL不能为空"}
            
            # 创建测试连接
            engine = create_engine(database_url, echo=False)
            with engine.connect() as conn:
                result = conn.execute("SELECT 1")
                result.fetchone()
            
            return {"success": True, "message": "数据库连接成功"}
        
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return {"success": False, "message": f"数据库连接失败: {str(e)}"}
    
    def test_email_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试邮件配置"""
        try:
            smtp_host = config.get('smtp_host')
            smtp_port = config.get('smtp_port', 587)
            smtp_user = config.get('smtp_user')
            smtp_password = config.get('smtp_password')
            smtp_tls = config.get('smtp_tls', True)
            email_from = config.get('email_from')
            
            if not all([smtp_host, smtp_user, smtp_password, email_from]):
                return {"success": False, "message": "邮件配置信息不完整"}
            
            # 创建SMTP连接
            server = smtplib.SMTP(smtp_host, smtp_port)
            if smtp_tls:
                server.starttls()
            server.login(smtp_user, smtp_password)
            
            # 发送测试邮件
            msg = MIMEMultipart()
            msg['From'] = email_from
            msg['To'] = email_from  # 发送给自己
            msg['Subject'] = "WisCude 邮件配置测试"

            body = "这是一封测试邮件，用于验证WisCude后台管理系统的邮件配置。"
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server.send_message(msg)
            server.quit()
            
            return {"success": True, "message": "邮件配置测试成功，测试邮件已发送"}
        
        except Exception as e:
            logger.error(f"邮件配置测试失败: {e}")
            return {"success": False, "message": f"邮件配置测试失败: {str(e)}"}
    
    def _create_default_settings(self) -> SystemSettings:
        """创建默认设置"""
        default_settings = SystemSettings(
            id="system_config",
            app_name="WisCude 后台管理系统",
            app_version="1.0.0",
            app_description="智慧学习数据管理平台",
            database_echo=False,
            sync_interval_minutes=30,
            sync_batch_size=1000,
            auto_sync_enabled=False,
            jwt_algorithm="HS256",
            access_token_expire_minutes=30,
            refresh_token_expire_days=7,
            password_min_length=6,
            password_require_special=False,
            smtp_port=587,
            smtp_tls=True,
            log_level="INFO",
            log_file_path="backend/logs/wiscude-admin.log",
            log_retention_days=30,
            log_max_size_mb=100,
            ui_theme="light",
            ui_language="zh-CN",
            default_page_size=20,
            max_page_size=100,
            extra_config={}
        )
        
        self.db.add(default_settings)
        self.db.commit()
        self.db.refresh(default_settings)
        
        return default_settings
    
    def _log_settings_change(self, user_id: str, changed_fields: list):
        """记录设置变更日志"""
        logger.info(f"用户 {user_id} 修改了系统设置: {', '.join(changed_fields)}")

def get_settings_service(db: Session = None) -> SettingsService:
    """获取设置服务实例"""
    if db is None:
        db = next(get_db())
    return SettingsService(db)
