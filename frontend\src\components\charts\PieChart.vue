<template>
  <BaseChart
    :width="width"
    :height="height"
    :options="chartOptions"
    :loading="loading"
    :error="error"
    :theme="theme"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'
import type { EChartsOption } from 'echarts'

// Props定义
interface DataItem {
  name: string
  value: number
  color?: string
  [key: string]: any
}

interface Props {
  width?: string
  height?: string
  data: DataItem[]
  title?: string
  subtitle?: string
  loading?: boolean
  error?: string
  theme?: string
  showLegend?: boolean
  showLabel?: boolean
  showLabelLine?: boolean
  innerRadius?: string | number
  outerRadius?: string | number
  roseType?: boolean | 'radius' | 'area'
  colors?: string[]
  center?: [string | number, string | number]
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  loading: false,
  error: '',
  theme: 'default',
  showLegend: true,
  showLabel: true,
  showLabelLine: true,
  innerRadius: 0,
  outerRadius: '75%',
  roseType: false,
  colors: () => ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399', '#c45656', '#8e44ad', '#3498db'],
  center: () => ['50%', '50%']
})

// Emits定义
const emit = defineEmits<{
  chartReady: [chart: any]
  chartClick: [params: any]
}>()

// 计算图表配置
const chartOptions = computed<EChartsOption>(() => {
  // 确保 data 是数组
  const dataArray = Array.isArray(props.data) ? props.data : []

  // 计算总值用于百分比计算
  const total = dataArray.reduce((sum, item) => sum + item.value, 0)

  // 处理数据，添加百分比
  const processedData = dataArray.map((item, index) => ({
    ...item,
    percentage: total > 0 ? ((item.value / total) * 100).toFixed(1) : '0.0',
    itemStyle: {
      color: item.color || props.colors[index % props.colors.length]
    }
  }))

  const option: EChartsOption = {
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      },
      subtextStyle: {
        fontSize: 12,
        color: '#909399'
      }
    } : undefined,

    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: (params: any) => {
        const { name, value, data } = params
        const percentage = data.percentage
        return `
          <div style="display: flex; align-items: center;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 6px;"></span>
            <span style="margin-right: 8px;">${name}:</span>
            <span style="font-weight: bold;">${value} (${percentage}%)</span>
          </div>
        `
      }
    },

    legend: props.showLegend ? {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#606266',
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = processedData.find(d => d.name === name)
        return item ? `${name} (${item.percentage}%)` : name
      }
    } : undefined,

    color: props.colors,

    series: [
      {
        type: 'pie',
        radius: [props.innerRadius, props.outerRadius],
        center: props.center,
        data: processedData,
        roseType: props.roseType || false,
        label: props.showLabel ? {
          show: true,
          position: 'outside',
          formatter: (params: any) => {
            const { name, data } = params
            return `${name}\n${data.percentage}%`
          },
          fontSize: 12,
          color: '#606266'
        } : {
          show: false
        },
        labelLine: props.showLabelLine ? {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: '#c0c4cc'
          }
        } : {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => Math.random() * 200
      }
    ]
  }

  return option
})

// 事件处理
const handleChartReady = (chart: any) => {
  emit('chartReady', chart)
}

const handleChartClick = (params: any) => {
  emit('chartClick', params)
}
</script>
