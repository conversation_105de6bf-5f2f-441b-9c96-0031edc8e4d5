/**
 * 管理员用户管理API接口
 */
import request from './request'

// 管理员用户相关类型定义
export interface AdminUser {
  id: string
  username: string
  email: string
  full_name?: string
  role: 'superadmin' | 'admin' | 'editor' | 'viewer'
  is_active: boolean
  is_superuser: boolean
  phone?: string
  department?: string
  position?: string
  bio?: string
  avatar_url?: string
  last_login?: string
  login_count: number
  created_at: string
  updated_at: string
}

export interface AdminUserCreate {
  username: string
  email: string
  password: string
  full_name?: string
  role?: 'superadmin' | 'admin' | 'editor' | 'viewer'
  is_active?: boolean
  is_superuser?: boolean
  phone?: string
  department?: string
  position?: string
  bio?: string
}

export interface AdminUserUpdate {
  username?: string
  email?: string
  full_name?: string
  role?: 'superadmin' | 'admin' | 'editor' | 'viewer'
  is_active?: boolean
  is_superuser?: boolean
  phone?: string
  department?: string
  position?: string
  bio?: string
}

export interface AdminUserListResponse {
  items: AdminUser[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 管理员用户管理API
export const adminApi = {
  // 获取管理员用户列表
  getAdminUsers: (params: {
    page?: number
    page_size?: number
    search?: string
    role?: string
    is_active?: boolean
    sort_by?: string
    sort_order?: 'asc' | 'desc'
  }) => {
    return request({
      url: '/admin-users/',
      method: 'get',
      params
    })
  },

  // 获取管理员用户详情
  getAdminUser: (id: string) => {
    return request({
      url: `/admin-users/${id}`,
      method: 'get'
    })
  },

  // 创建管理员用户
  createAdminUser: (data: AdminUserCreate) => {
    return request({
      url: '/admin-users/',
      method: 'post',
      data
    })
  },

  // 更新管理员用户
  updateAdminUser: (id: string, data: AdminUserUpdate) => {
    return request({
      url: `/admin-users/${id}`,
      method: 'put',
      data
    })
  },

  // 删除管理员用户
  deleteAdminUser: (id: string) => {
    return request({
      url: `/admin-users/${id}`,
      method: 'delete'
    })
  },

  // 切换管理员用户状态
  toggleAdminUserStatus: (id: string) => {
    return request({
      url: `/admin-users/${id}/toggle-status`,
      method: 'post'
    })
  }
}

export default adminApi
