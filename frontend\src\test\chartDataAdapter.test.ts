/**
 * 图表数据适配器测试
 */

import { describe, it, expect } from 'vitest'
import ChartDataAdapter from '@/utils/chartDataAdapter'
import type { PerformanceMetrics, HistoricalData } from '@/types/performance'

describe('ChartDataAdapter', () => {
  // 模拟性能指标数据
  const mockMetrics: PerformanceMetrics = {
    cpu: {
      usage: 75,
      cores: 8,
      frequency: 2.4,
      change: 5.2
    },
    memory: {
      total: 16,
      used: 8,
      available: 8,
      usage: 50,
      change: -2.1
    },
    disk: {
      total: 500,
      used: 200,
      free: 300,
      usage: 40,
      ioRead: 50,
      ioWrite: 30,
      change: 1.5
    },
    network: {
      latency: 25,
      uploadSpeed: 10,
      downloadSpeed: 50,
      packetsLost: 0.1,
      change: -3.2
    },
    timestamp: new Date().toISOString()
  }

  // 模拟历史数据
  const mockHistoricalData: HistoricalData = {
    timeRange: '1h',
    timestamps: [
      '2024-01-01T10:00:00Z',
      '2024-01-01T10:05:00Z',
      '2024-01-01T10:10:00Z'
    ],
    cpu: [60, 70, 80],
    memory: [45, 50, 55],
    disk: [30, 35, 40],
    network: [20, 25, 30],
    dataPoints: [
      {
        timestamp: '2024-01-01T10:00:00Z',
        cpu: 60,
        memory: 45,
        disk: 30,
        network: 20
      },
      {
        timestamp: '2024-01-01T10:05:00Z',
        cpu: 70,
        memory: 50,
        disk: 35,
        network: 25
      },
      {
        timestamp: '2024-01-01T10:10:00Z',
        cpu: 80,
        memory: 55,
        disk: 40,
        network: 30
      }
    ]
  }

  describe('adaptCpuDataForLineChart', () => {
    it('应该正确转换CPU历史数据为LineChart格式', () => {
      const result = ChartDataAdapter.adaptCpuDataForLineChart(mockHistoricalData)
      
      expect(result.data).toHaveLength(1)
      expect(result.data[0].name).toBe('CPU使用率')
      expect(result.data[0].data).toEqual([60, 70, 80])
      expect(result.data[0].color).toBe('#3b82f6')
      expect(result.labels).toHaveLength(3)
    })

    it('应该处理空数据', () => {
      const emptyData: HistoricalData = {
        timeRange: '1h',
        timestamps: [],
        cpu: [],
        memory: [],
        disk: [],
        network: [],
        dataPoints: []
      }
      
      const result = ChartDataAdapter.adaptCpuDataForLineChart(emptyData)
      
      expect(result.data[0].data).toEqual([])
      expect(result.labels).toEqual([])
    })
  })

  describe('adaptMemoryDataForDoughnutChart', () => {
    it('应该正确转换内存数据为DoughnutChart格式', () => {
      const result = ChartDataAdapter.adaptMemoryDataForDoughnutChart(mockMetrics)
      
      expect(result.data.labels).toEqual(['已使用', '缓存', '缓冲区', '可用'])
      expect(result.data.datasets[0].data).toHaveLength(4)
      expect(result.details).toHaveLength(4)
      
      // 验证数据总和为100%
      const total = result.data.datasets[0].data.reduce((sum, val) => sum + val, 0)
      expect(total).toBe(100)
    })
  })

  describe('adaptNetworkDataForAreaChart', () => {
    it('应该正确转换网络数据为AreaChart格式', () => {
      const result = ChartDataAdapter.adaptNetworkDataForAreaChart(mockHistoricalData)
      
      expect(result.labels).toHaveLength(3)
      expect(result.datasets).toHaveLength(2)
      expect(result.datasets[0].label).toBe('入站流量')
      expect(result.datasets[1].label).toBe('出站流量')
      expect(result.datasets[0].data).toHaveLength(3)
      expect(result.datasets[1].data).toHaveLength(3)
    })
  })

  describe('adaptSystemLoadData', () => {
    it('应该正确转换系统负载数据', () => {
      const result = ChartDataAdapter.adaptSystemLoadData(mockMetrics)
      
      expect(result).toHaveLength(3)
      expect(result[0].label).toBe('1分钟')
      expect(result[1].label).toBe('5分钟')
      expect(result[2].label).toBe('15分钟')
      
      // 验证负载值基于CPU使用率
      expect(result[0].value).toBe(75) // 等于CPU使用率
      expect(result[1].value).toBe(68) // CPU * 0.9
      expect(result[2].value).toBe(60) // CPU * 0.8
    })
  })

  describe('adaptPerformanceMetrics', () => {
    it('应该正确转换性能指标数据', () => {
      const result = ChartDataAdapter.adaptPerformanceMetrics(mockMetrics)
      
      expect(result).toHaveLength(4)
      expect(result[0].label).toBe('CPU使用率')
      expect(result[0].value).toBe(75)
      expect(result[0].unit).toBe('%')
      expect(result[0].change).toBe(5.2)
      
      expect(result[3].label).toBe('网络延迟')
      expect(result[3].value).toBe(25)
      expect(result[3].unit).toBe('ms')
    })
  })

  describe('safeAdapt', () => {
    it('应该在正常情况下返回转换结果', () => {
      const result = ChartDataAdapter.safeAdapt(
        () => ({ success: true }),
        { success: false },
        '测试错误'
      )
      
      expect(result.success).toBe(true)
    })

    it('应该在出错时返回降级数据', () => {
      const result = ChartDataAdapter.safeAdapt(
        () => { throw new Error('测试错误') },
        { success: false },
        '测试错误'
      )
      
      expect(result.success).toBe(false)
    })
  })

  describe('validateData', () => {
    it('应该验证有效数据', () => {
      expect(ChartDataAdapter.validateData({ valid: true })).toBe(true)
      expect(ChartDataAdapter.validateData([])).toBe(true)
      expect(ChartDataAdapter.validateData('')).toBe(true)
    })

    it('应该拒绝无效数据', () => {
      expect(ChartDataAdapter.validateData(null)).toBe(false)
      expect(ChartDataAdapter.validateData(undefined)).toBe(false)
    })
  })
})
