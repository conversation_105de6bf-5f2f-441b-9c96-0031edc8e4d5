"""
WisCude 后台管理系统 - 健康检查API
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import time
import psutil
import os
from pathlib import Path
from ..core.database import enhanced_db_manager, AndroidDatabaseManager
from ..core.config import settings

router = APIRouter()

@router.get("/health", summary="基础健康检查")
async def health_check() -> Dict[str, Any]:
    """
    基础健康检查端点
    返回服务的基本状态信息
    """
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "WisCude Backend",
        "version": settings.APP_VERSION
    }

@router.get("/health/detailed", summary="详细健康检查")
async def detailed_health_check() -> Dict[str, Any]:
    """
    详细健康检查端点
    返回数据库、系统资源、文件系统等详细状态
    """
    health_data = {
        "timestamp": time.time(),
        "service": {
            "name": "WisCude Backend",
            "version": settings.APP_VERSION,
            "status": "healthy"
        }
    }
    
    # 数据库健康检查
    try:
        db_health = enhanced_db_manager.health_check()
        health_data["database"] = db_health
    except Exception as e:
        health_data["database"] = {
            "status": "error",
            "error": str(e)
        }
        health_data["service"]["status"] = "degraded"
    
    # Android数据库检查
    try:
        android_db_path = Path(settings.ANDROID_DB_PATH)
        if android_db_path.exists():
            android_db_manager = AndroidDatabaseManager(settings.ANDROID_DB_PATH)
            with android_db_manager:
                tables = android_db_manager.get_table_names()
                health_data["android_database"] = {
                    "status": "available",
                    "path": str(android_db_path),
                    "size_mb": round(android_db_path.stat().st_size / 1024 / 1024, 2),
                    "table_count": len(tables)
                }
        else:
            health_data["android_database"] = {
                "status": "not_found",
                "path": str(android_db_path)
            }
    except Exception as e:
        health_data["android_database"] = {
            "status": "error",
            "error": str(e)
        }
    
    # 系统资源检查
    try:
        # 获取当前工作目录的磁盘使用情况（跨平台兼容）
        current_path = os.getcwd()
        disk_usage = psutil.disk_usage(current_path)

        health_data["system"] = {
            "cpu_percent": psutil.cpu_percent(interval=0.1),  # 减少等待时间
            "memory": {
                "total_mb": round(psutil.virtual_memory().total / 1024 / 1024, 2),
                "available_mb": round(psutil.virtual_memory().available / 1024 / 1024, 2),
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total_gb": round(disk_usage.total / 1024 / 1024 / 1024, 2),
                "free_gb": round(disk_usage.free / 1024 / 1024 / 1024, 2),
                "percent": round((disk_usage.used / disk_usage.total) * 100, 2),
                "path": current_path
            }
        }
    except Exception as e:
        health_data["system"] = {
            "status": "error",
            "error": str(e)
        }
    
    # 文件系统检查
    try:
        required_dirs = ["logs", "uploads", "backups"]
        dir_status = {}
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            dir_status[dir_name] = {
                "exists": dir_path.exists(),
                "writable": dir_path.exists() and os.access(dir_path, os.W_OK)
            }
        health_data["filesystem"] = dir_status
    except Exception as e:
        health_data["filesystem"] = {
            "status": "error",
            "error": str(e)
        }
    
    return health_data

@router.get("/health/database", summary="数据库健康检查")
async def database_health_check() -> Dict[str, Any]:
    """
    专门的数据库健康检查端点
    """
    try:
        return enhanced_db_manager.health_check()
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"数据库健康检查失败: {str(e)}"
        )

@router.get("/health/ready", summary="就绪状态检查")
async def readiness_check() -> Dict[str, Any]:
    """
    就绪状态检查 - 用于启动脚本判断服务是否准备就绪
    """
    checks = {
        "database": False,
        "filesystem": False,
        "overall": False
    }
    
    # 检查数据库
    try:
        db_health = enhanced_db_manager.health_check()
        checks["database"] = db_health["status"] == "healthy"
    except:
        checks["database"] = False
    
    # 检查文件系统
    try:
        required_dirs = ["logs", "uploads"]
        checks["filesystem"] = all(
            Path(dir_name).exists() for dir_name in required_dirs
        )
    except:
        checks["filesystem"] = False
    
    # 总体就绪状态
    checks["overall"] = checks["database"] and checks["filesystem"]
    
    if not checks["overall"]:
        raise HTTPException(
            status_code=503,
            detail="服务尚未就绪"
        )
    
    return {
        "status": "ready",
        "timestamp": time.time(),
        "checks": checks
    }

@router.get("/health/startup", summary="启动状态检查")
async def startup_check() -> Dict[str, Any]:
    """
    启动状态检查 - 返回启动进度和状态
    """
    startup_status = {
        "timestamp": time.time(),
        "phase": "running",
        "progress": 100,
        "checks": {
            "database_initialized": enhanced_db_manager._is_initialized,
            "directories_created": True,
            "configuration_loaded": True
        }
    }
    
    # 计算启动进度
    completed_checks = sum(1 for check in startup_status["checks"].values() if check)
    total_checks = len(startup_status["checks"])
    startup_status["progress"] = round((completed_checks / total_checks) * 100, 1)
    
    if startup_status["progress"] < 100:
        startup_status["phase"] = "starting"
    
    return startup_status
