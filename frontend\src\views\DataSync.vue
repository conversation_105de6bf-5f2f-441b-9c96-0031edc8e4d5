<template>
  <div class="data-sync">
    <div class="page-header">
      <h1>数据同步管理</h1>
      <p>管理Android应用与后台数据库的数据同步</p>
    </div>
    
    <!-- 同步状态概览 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :xs="24" :lg="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>同步状态</span>
              <el-button @click="refreshStatus" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="sync-overview">
            <div class="status-grid">
              <div class="status-item">
                <div class="status-label">Android数据库</div>
                <div class="status-value">
                  <el-tag :type="syncStatus.android_db_connected ? 'success' : 'danger'">
                    {{ syncStatus.android_db_connected ? '已连接' : '未连接' }}
                  </el-tag>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-label">数据库大小</div>
                <div class="status-value">{{ syncStatus.android_db_size || 'N/A' }}</div>
              </div>
              
              <div class="status-item">
                <div class="status-label">总记录数</div>
                <div class="status-value">{{ formatNumber(syncStatus.total_records) }}</div>
              </div>
              
              <div class="status-item">
                <div class="status-label">最后同步</div>
                <div class="status-value">{{ formatTime(syncStatus.last_sync) }}</div>
              </div>
            </div>
            
            <div class="sync-progress" v-if="syncStatus.is_syncing">
              <el-progress 
                :percentage="syncProgress" 
                :status="syncProgress === 100 ? 'success' : 'active'"
              />
              <p class="progress-text">正在同步数据，请稍候...</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="8">
        <el-card>
          <template #header>
            <span>同步配置</span>
          </template>
          
          <div class="sync-config">
            <div class="config-item">
              <span class="config-label">自动同步</span>
              <el-switch 
                v-model="syncConfig.auto_sync_enabled"
                @change="updateConfig"
              />
            </div>
            
            <div class="config-item">
              <span class="config-label">同步间隔</span>
              <el-select 
                v-model="syncConfig.sync_interval_minutes"
                @change="updateConfig"
                style="width: 120px"
              >
                <el-option label="15分钟" :value="15" />
                <el-option label="30分钟" :value="30" />
                <el-option label="1小时" :value="60" />
                <el-option label="2小时" :value="120" />
              </el-select>
            </div>
            
            <div class="config-item">
              <span class="config-label">批处理大小</span>
              <el-input-number 
                v-model="syncConfig.batch_size"
                :min="100"
                :max="5000"
                :step="100"
                @change="updateConfig"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 数据表同步状态 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>数据表同步状态</span>
          <el-button type="primary" @click="startSync" :loading="syncStatus.is_syncing">
            <el-icon><Refresh /></el-icon>
            开始同步
          </el-button>
        </div>
      </template>
      
      <el-table :data="tableStats" style="width: 100%">
        <el-table-column prop="display_name" label="数据表" width="150" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="record_count" label="记录数" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.record_count) }}
          </template>
        </el-table-column>
        <el-table-column prop="last_sync" label="最后同步" width="180">
          <template #default="{ row }">
            {{ formatTime(row.last_sync) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getSyncStatusType(row.last_sync)">
              {{ getSyncStatusText(row.last_sync) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button 
              size="small" 
              @click="syncSingleTable(row.name)"
              :loading="syncingTables.has(row.name)"
            >
              同步
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 同步历史 -->
    <el-card>
      <template #header>
        <span>同步历史</span>
      </template>
      
      <el-table :data="syncHistory" style="width: 100%">
        <el-table-column prop="sync_time" label="同步时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.sync_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="sync_type" label="同步类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.sync_type === 'full' ? 'primary' : 'info'">
              {{ row.sync_type === 'full' ? '全量' : '增量' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="records_synced" label="同步记录数" width="120" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.records_synced) }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="备注" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const syncingTables = ref(new Set<string>())

const syncStatus = ref({
  last_sync: null,
  is_syncing: false,
  android_db_connected: true,
  android_db_path: '',
  android_db_size: '',
  total_records: 0,
  sync_config: {
    auto_sync_enabled: false,
    sync_interval_minutes: 30,
    batch_size: 1000
  }
})

const syncConfig = computed(() => syncStatus.value.sync_config || {
  auto_sync_enabled: false,
  sync_interval_minutes: 30,
  batch_size: 1000
})
const syncProgress = ref(0)

const tableStats = ref([])
const syncHistory = ref([
  {
    sync_time: '2024-01-20T10:30:00Z',
    sync_type: 'full',
    records_synced: 15420,
    duration: '5分32秒',
    status: 'success',
    message: '全量同步完成'
  },
  {
    sync_time: '2024-01-20T09:00:00Z',
    sync_type: 'incremental',
    records_synced: 234,
    duration: '1分15秒',
    status: 'success',
    message: '增量同步完成'
  }
])

const refreshStatus = async () => {
  loading.value = true
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 使用模拟数据，保持原有的数据结构
    syncStatus.value = {
      last_sync: new Date().toISOString(),
      is_syncing: false,
      android_db_connected: true,
      android_db_path: '/data/data/com.example.app/databases/app.db',
      android_db_size: '15.2 MB',
      total_records: 15420,
      sync_config: {
        auto_sync_enabled: true,
        sync_interval_minutes: 30,
        batch_size: 1000
      }
    }

    tableStats.value = [
      { name: 'users', records: 2847, lastSync: new Date().toISOString(), status: 'synced' },
      { name: 'courses', records: 156, lastSync: new Date().toISOString(), status: 'synced' },
      { name: 'assignments', records: 892, lastSync: new Date().toISOString(), status: 'synced' },
      { name: 'grades', records: 5634, lastSync: new Date().toISOString(), status: 'synced' }
    ]

    ElMessage.success('同步状态已更新')
  } catch (error) {
    ElMessage.error('获取同步状态失败')
  } finally {
    loading.value = false
  }
}

const updateConfig = async () => {
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟配置更新成功
    ElMessage.success('配置已更新')
  } catch (error) {
    ElMessage.error('更新配置失败')
  }
}

const startSync = async () => {
  try {
    await ElMessageBox.confirm('确定要开始全量数据同步吗？', '确认同步', {
      type: 'warning'
    })
    
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success('全量数据同步已启动')
    
    // 模拟同步进度
    syncStatus.value.is_syncing = true
    syncProgress.value = 0
    
    const progressInterval = setInterval(() => {
      syncProgress.value += Math.random() * 10
      if (syncProgress.value >= 100) {
        syncProgress.value = 100
        clearInterval(progressInterval)
        setTimeout(() => {
          syncStatus.value.is_syncing = false
          refreshStatus()
        }, 1000)
      }
    }, 500)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('启动同步失败')
    }
  }
}

const syncSingleTable = async (tableName: string) => {
  syncingTables.value.add(tableName)
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(`${tableName} 表同步已启动`)
  } catch (error) {
    ElMessage.error(`${tableName} 表同步失败`)
  } finally {
    syncingTables.value.delete(tableName)
  }
}

const formatNumber = (num: number) => {
  return num?.toLocaleString() || '0'
}

const formatTime = (time: string) => {
  if (!time) return 'N/A'
  return new Date(time).toLocaleString('zh-CN')
}

const getSyncStatusType = (lastSync: string) => {
  if (!lastSync) return 'info'
  const now = new Date()
  const syncTime = new Date(lastSync)
  const diffHours = (now.getTime() - syncTime.getTime()) / (1000 * 60 * 60)
  
  if (diffHours < 1) return 'success'
  if (diffHours < 24) return 'warning'
  return 'danger'
}

const getSyncStatusText = (lastSync: string) => {
  if (!lastSync) return '未同步'
  const now = new Date()
  const syncTime = new Date(lastSync)
  const diffHours = (now.getTime() - syncTime.getTime()) / (1000 * 60 * 60)
  
  if (diffHours < 1) return '最新'
  if (diffHours < 24) return '较新'
  return '过期'
}

onMounted(() => {
  refreshStatus()
})
</script>

<style lang="scss" scoped>
.data-sync {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .sync-overview {
    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
      
      .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        
        .status-label {
          font-size: 14px;
          color: #606266;
        }
        
        .status-value {
          font-weight: 500;
          color: #303133;
        }
      }
    }
    
    .sync-progress {
      .progress-text {
        text-align: center;
        margin-top: 10px;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .sync-config {
    .config-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .config-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>
