"""
WisCude 后台管理系统 - 用户管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc, func, and_
from datetime import datetime
import math

from app.core.database import get_db
from app.core.security import get_current_user, get_current_active_superuser

# 为了保持一致性，创建别名
get_current_admin_user = get_current_user
from app.models import WiscudeUserUpdate, StudySession, CheckIn, CommunityPost, AdminUser, UserStatus, MembershipType, UserLevel, Gender
from app.api.schemas import (
    WiscudeUserUpdate, WiscudeUserUpdate, PaginationParams, PaginatedResponse
)
from app.schemas.user import (
    CreateUserRequest, UpdateUserRequest, ChangePasswordRequest,
    UserResponse, UserListResponse, UserStatsResponse, UserOverviewResponse
)
from app.utils.security import hash_password, verify_password
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/users", tags=["用户管理"])

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: CreateUserRequest,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """创建新用户"""
    try:
        # 检查邮箱是否已存在
        existing_user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.email == user_data.email).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被使用"
            )
        
        # 检查手机号是否已存在
        if user_data.phone:
            existing_phone = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.phone == user_data.phone).first()
            if existing_phone:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="手机号已被使用"
                )
        
        # 创建新用户
        now = datetime.utcnow()
        new_user = WiscudeUserUpdate(
            email=user_data.email,
            phone=user_data.phone,
            nickname=user_data.nickname,
            hashed_password=hash_password(user_data.password),
            full_name=user_data.full_name,
            gender=user_data.gender,
            birth_date=user_data.birth_date,
            bio=user_data.bio,
            location=user_data.location,
            avatar_url=user_data.avatar_url,
            status=user_data.status,
            membership=user_data.membership,
            level=user_data.level,
            registration_time=now,
            is_premium=user_data.is_premium,
            premium_expires_at=user_data.premium_expires_at,
            total_study_time=0,
            total_focus_sessions=0,
            total_check_ins=0,
            experience_points=0,
            created_at=now,
            updated_at=now
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        return UserResponse.from_orm(new_user)
        
    except Exception as e:
        db.rollback()
        logger.error(f"创建用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )

@router.get("/", response_model=UserListResponse)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词(昵称、邮箱、手机号)"),
    status: Optional[UserStatus] = Query(None, description="用户状态"),
    membership: Optional[MembershipType] = Query(None, description="会员类型"),
    is_premium: Optional[bool] = Query(None, description="是否会员"),
    gender: Optional[Gender] = Query(None, description="性别"),
    level: Optional[UserLevel] = Query(None, description="用户等级"),
    sort_by: Optional[str] = Query("created_at", description="排序字段"),
    sort_order: Optional[str] = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """获取用户列表"""
    try:
        query = db.query(WiscudeUserUpdate)
        
        # 搜索过滤
        if search:
            search_filter = or_(
                WiscudeUserUpdate.nickname.ilike(f"%{search}%"),
                WiscudeUserUpdate.email.ilike(f"%{search}%"),
                WiscudeUserUpdate.phone.ilike(f"%{search}%"),
                WiscudeUserUpdate.full_name.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # 状态过滤
        if status is not None:
            query = query.filter(WiscudeUserUpdate.status == status)
        
        # 会员类型过滤
        if membership is not None:
            query = query.filter(WiscudeUserUpdate.membership == membership)
        
        # 会员状态过滤
        if is_premium is not None:
            query = query.filter(WiscudeUserUpdate.is_premium == is_premium)
        
        # 性别过滤
        if gender is not None:
            query = query.filter(WiscudeUserUpdate.gender == gender)
        
        # 等级过滤
        if level is not None:
            query = query.filter(WiscudeUserUpdate.level == level)
        
        # 排序
        if hasattr(WiscudeUserUpdate, sort_by):
            order_column = getattr(WiscudeUserUpdate, sort_by)
            if sort_order == "desc":
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(order_column)
        
        # 分页
        total = query.count()
        users = query.offset((page - 1) * page_size).limit(page_size).all()
        
        return UserListResponse(
            items=[UserResponse.from_orm(user) for user in users],
            total=total,
            page=page,
            page_size=page_size,
            pages=math.ceil(total / page_size) if total > 0 else 0
        )
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )

@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """获取用户详情"""
    user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return UserResponse.from_orm(user)

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_update: UpdateUserRequest,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """更新用户信息"""
    try:
        user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查邮箱唯一性
        if user_update.email and user_update.email != user.email:
            existing_email = db.query(WiscudeUserUpdate).filter(
                and_(WiscudeUserUpdate.email == user_update.email, WiscudeUserUpdate.id != user_id)
            ).first()
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被使用"
                )
        
        # 检查手机号唯一性
        if user_update.phone and user_update.phone != user.phone:
            existing_phone = db.query(WiscudeUserUpdate).filter(
                and_(WiscudeUserUpdate.phone == user_update.phone, WiscudeUserUpdate.id != user_id)
            ).first()
            if existing_phone:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="手机号已被使用"
                )
        
        # 更新用户信息
        update_data = user_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(user)
        
        return UserResponse.from_orm(user)
         
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """删除用户（硬删除）"""
    try:
        user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 硬删除：直接从数据库删除
        db.delete(user)
        db.commit()
        
        logger.info(f"管理员 {current_admin.username} 删除了用户 {user.nickname}")
        
        return {"message": "用户删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )


@router.post("/{user_id}/change-password")
async def change_user_password(
    user_id: int,
    password_data: ChangePasswordRequest,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """修改用户密码"""
    try:
        user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 更新密码
        user.hashed_password = hash_password(password_data.new_password)
        user.updated_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"管理员 {current_admin.username} 修改了用户 {user.nickname} 的密码")
        
        return {"message": "密码修改成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"修改密码失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="修改密码失败"
        )

@router.post("/{user_id}/toggle-status", summary="切换用户状态")
async def toggle_user_status(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """切换用户激活状态"""
    user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 在ACTIVE和INACTIVE之间切换
    if user.status == UserStatus.ACTIVE:
        user.status = UserStatus.INACTIVE
        status_text = "禁用"
    else:
        user.status = UserStatus.ACTIVE
        status_text = "激活"
    
    user.updated_at = datetime.utcnow()
    db.commit()
    
    logger.info(f"管理员 {current_admin.username} {status_text}了用户 {user.nickname}")
    
    return {"message": f"用户已{status_text}"}

@router.post("/{user_id}/toggle-premium", summary="切换会员状态")
async def toggle_premium_status(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """切换用户会员状态"""
    user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user.is_premium = not user.is_premium
    db.commit()
    
    status_text = "开通" if user.is_premium else "取消"
    logger.info(f"管理员 {current_admin.username} {status_text}了用户 {user.nickname} 的会员")
    
    return {"message": f"会员已{status_text}"}

@router.get("/{user_id}/stats", summary="获取用户统计")
async def get_user_stats(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """获取用户统计信息"""
    user = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 学习统计
    total_sessions = db.query(StudySession).filter(
        StudySession.user_id == user_id
    ).count()
    
    completed_sessions = db.query(StudySession).filter(
        StudySession.user_id == user_id,
        StudySession.is_completed == True
    ).count()
    
    avg_session_duration = db.query(func.avg(StudySession.duration)).filter(
        StudySession.user_id == user_id,
        StudySession.is_completed == True
    ).scalar() or 0
    
    # 打卡统计
    total_checkins = db.query(CheckIn).filter(
        CheckIn.user_id == user_id
    ).count()
    
    # 社区统计
    total_posts = db.query(CommunityPost).filter(
        CommunityPost.author_id == user_id
    ).count()
    
    total_likes = db.query(func.sum(CommunityPost.likes_count)).filter(
        CommunityPost.author_id == user_id
    ).scalar() or 0
    
    return {
        "study_stats": {
            "total_sessions": total_sessions,
            "completed_sessions": completed_sessions,
            "completion_rate": round(
                (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0, 1
            ),
            "avg_session_duration": round(avg_session_duration, 1),
            "total_study_time": user.total_study_time
        },
        "checkin_stats": {
            "total_checkins": total_checkins,
            "total_check_ins": user.total_check_ins
        },
        "community_stats": {
            "total_posts": total_posts,
            "total_likes": total_likes
        },
        "general_stats": {
            "level": user.level,
            "experience_points": user.experience_points,
            "registration_days": (user.updated_at - user.registration_time).days
        }
    }

@router.get("/stats/overview", summary="获取用户概览统计")
async def get_users_overview(
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin_user)
):
    """获取用户概览统计"""
    from datetime import date, timedelta
    
    today = date.today()
    yesterday = today - timedelta(days=1)
    
    # 今日统计
    total_users = db.query(WiscudeUserUpdate).count()
    active_users = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.status == UserStatus.ACTIVE).count()
    premium_users = db.query(WiscudeUserUpdate).filter(WiscudeUserUpdate.is_premium == True).count()
    new_users_today = db.query(WiscudeUserUpdate).filter(
        func.date(WiscudeUserUpdate.created_at) == today
    ).count()
    
    # 昨日统计（截止到昨日结束时的数据）
    total_users_yesterday = db.query(WiscudeUserUpdate).filter(
        func.date(WiscudeUserUpdate.created_at) <= yesterday
    ).count()
    
    active_users_yesterday = db.query(WiscudeUserUpdate).filter(
        WiscudeUserUpdate.status == UserStatus.ACTIVE,
        func.date(WiscudeUserUpdate.created_at) <= yesterday
    ).count()
    
    premium_users_yesterday = db.query(WiscudeUserUpdate).filter(
        WiscudeUserUpdate.is_premium == True,
        func.date(WiscudeUserUpdate.created_at) <= yesterday
    ).count()
    
    new_users_yesterday = db.query(WiscudeUserUpdate).filter(
        func.date(WiscudeUserUpdate.created_at) == yesterday
    ).count()
    
    # 计算增长率
    def calculate_growth_rate(current, previous):
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return round(((current - previous) / previous) * 100, 2)
    
    total_users_growth_rate = calculate_growth_rate(total_users, total_users_yesterday)
    active_users_growth_rate = calculate_growth_rate(active_users, active_users_yesterday)
    premium_users_growth_rate = calculate_growth_rate(premium_users, premium_users_yesterday)
    new_users_today_growth_rate = calculate_growth_rate(new_users_today, new_users_yesterday)
    
    # 性别分布
    gender_stats = db.query(
        WiscudeUserUpdate.gender,
        func.count(WiscudeUserUpdate.id).label('count')
    ).filter(WiscudeUserUpdate.gender.isnot(None)).group_by(WiscudeUserUpdate.gender).all()
    
    # 等级分布
    level_stats = db.query(
        WiscudeUserUpdate.level,
        func.count(WiscudeUserUpdate.id).label('count')
    ).group_by(WiscudeUserUpdate.level).order_by(WiscudeUserUpdate.level).all()
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "premium_users": premium_users,
        "new_users_today": new_users_today,
        "inactive_users": total_users - active_users,
        "free_users": total_users - premium_users,
        "total_users_growth_rate": total_users_growth_rate,
        "active_users_growth_rate": active_users_growth_rate,
        "premium_users_growth_rate": premium_users_growth_rate,
        "new_users_today_growth_rate": new_users_today_growth_rate,
        "gender_distribution": [
            {"gender": stat.gender.value if stat.gender else "未知", "count": stat.count}
            for stat in gender_stats
        ],
        "level_distribution": [
            {"level": stat.level.value if stat.level else "未知", "count": stat.count}
            for stat in level_stats
        ]
    }
