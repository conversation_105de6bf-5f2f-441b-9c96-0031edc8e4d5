"""
题库管理模块数据模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, JSON, Float, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base

class QuestionCategory(Base):
    """题目分类模型"""
    __tablename__ = "question_categories"
    
    id = Column(String(36), primary_key=True)
    name = Column(String(100), nullable=False, comment="分类名称")
    code = Column(String(50), nullable=False, unique=True, comment="分类代码")
    description = Column(Text, comment="分类描述")
    parent_id = Column(String(36), comment="父分类ID")
    level = Column(Integer, default=1, comment="分类层级")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 分类属性
    subject = Column(String(50), nullable=False, comment="学科")
    grade_levels = Column(JSON, comment="适用年级")
    difficulty_levels = Column(JSON, comment="难度等级")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    question_count = Column(Integer, default=0, comment="题目数量")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class Question(Base):
    """题目模型"""
    __tablename__ = "questions"
    
    id = Column(String(36), primary_key=True)
    title = Column(Text, nullable=False, comment="题目标题")
    content = Column(Text, nullable=False, comment="题目内容")
    question_type = Column(String(20), nullable=False, comment="题型: single_choice, multiple_choice, fill_blank, essay, true_false")
    
    # 分类信息
    category_id = Column(String(36), ForeignKey('question_categories.id'), comment="分类ID")
    subject = Column(String(50), nullable=False, comment="学科")
    grade_level = Column(String(20), nullable=False, comment="年级")
    difficulty = Column(Integer, default=1, comment="难度等级 1-5")
    knowledge_points = Column(JSON, comment="知识点标签")
    
    # 题目选项（用于选择题）
    options = Column(JSON, comment="选项列表")
    correct_answer = Column(Text, comment="正确答案")
    answer_analysis = Column(Text, comment="答案解析")
    
    # 评分设置
    score = Column(Float, default=1.0, comment="题目分值")
    time_limit = Column(Integer, comment="答题时间限制（秒）")
    
    # 多媒体资源
    images = Column(JSON, comment="图片资源")
    audio_url = Column(String(500), comment="音频资源")
    video_url = Column(String(500), comment="视频资源")
    
    # 状态和统计
    status = Column(String(20), default="draft", comment="状态: draft, published, archived")
    usage_count = Column(Integer, default=0, comment="使用次数")
    correct_rate = Column(Float, default=0.0, comment="正确率")
    average_time = Column(Float, default=0.0, comment="平均答题时间")
    
    # 质量评估
    quality_score = Column(Float, default=0.0, comment="题目质量评分")
    review_status = Column(String(20), default="pending", comment="审核状态")
    reviewer_id = Column(String(36), comment="审核者ID")
    review_comments = Column(Text, comment="审核意见")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class Paper(Base):
    """试卷模型"""
    __tablename__ = "papers"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="试卷标题")
    description = Column(Text, comment="试卷描述")
    paper_type = Column(String(20), default="practice", comment="试卷类型: practice, exam, homework")
    
    # 基本信息
    subject = Column(String(50), nullable=False, comment="学科")
    grade_level = Column(String(20), nullable=False, comment="年级")
    difficulty = Column(Integer, default=1, comment="整体难度等级")
    total_score = Column(Float, default=100.0, comment="总分")
    time_limit = Column(Integer, comment="考试时间限制（分钟）")
    
    # 题目配置
    question_config = Column(JSON, comment="题目配置")
    question_ids = Column(JSON, comment="题目ID列表")
    question_count = Column(Integer, default=0, comment="题目总数")
    
    # 组卷设置
    generation_type = Column(String(20), default="manual", comment="组卷方式: manual, auto, template")
    auto_config = Column(JSON, comment="自动组卷配置")
    
    # 发布设置
    status = Column(String(20), default="draft", comment="状态: draft, published, archived")
    is_public = Column(Boolean, default=False, comment="是否公开")
    publish_time = Column(DateTime, comment="发布时间")
    expire_time = Column(DateTime, comment="过期时间")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    average_score = Column(Float, default=0.0, comment="平均分")
    completion_rate = Column(Float, default=0.0, comment="完成率")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class AnswerRecord(Base):
    """答题记录模型"""
    __tablename__ = "answer_records"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    question_id = Column(String(36), ForeignKey('questions.id'), comment="题目ID")
    paper_id = Column(String(36), ForeignKey('papers.id'), comment="试卷ID")
    
    # 答题信息
    user_answer = Column(Text, comment="用户答案")
    is_correct = Column(Boolean, comment="是否正确")
    score = Column(Float, default=0.0, comment="得分")
    time_spent = Column(Integer, comment="答题用时（秒）")
    
    # 答题过程
    answer_process = Column(JSON, comment="答题过程数据")
    submit_count = Column(Integer, default=1, comment="提交次数")
    
    # 时间信息
    start_time = Column(DateTime, comment="开始答题时间")
    submit_time = Column(DateTime, default=datetime.utcnow, comment="提交时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")


class PaperRecord(Base):
    """试卷答题记录模型"""
    __tablename__ = "paper_records"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    paper_id = Column(String(36), ForeignKey('papers.id'), comment="试卷ID")
    
    # 答题结果
    total_score = Column(Float, default=0.0, comment="总得分")
    total_time = Column(Integer, comment="总用时（秒）")
    completion_rate = Column(Float, default=0.0, comment="完成率")
    correct_count = Column(Integer, default=0, comment="正确题目数")
    wrong_count = Column(Integer, default=0, comment="错误题目数")
    
    # 状态
    status = Column(String(20), default="in_progress", comment="状态: in_progress, completed, abandoned")
    
    # 时间信息
    start_time = Column(DateTime, comment="开始时间")
    submit_time = Column(DateTime, comment="提交时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
