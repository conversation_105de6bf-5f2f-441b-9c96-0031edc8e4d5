#!/usr/bin/env python3
"""
WisCude 后台管理系统 - 安全配置检查工具
"""
import os
import sys
import secrets
import string
from pathlib import Path


class SecurityChecker:
    """安全配置检查器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.recommendations = []
    
    def check_secret_key(self):
        """检查密钥安全性"""
        secret_key = os.getenv("SECRET_KEY", "wiscude-secret-key-2024")
        
        if secret_key == "wiscude-secret-key-2024":
            self.issues.append("❌ 使用默认密钥，存在严重安全风险")
            self.recommendations.append("生成新的安全密钥：python -c \"import secrets; print(secrets.token_urlsafe(64))\"")
        elif len(secret_key) < 32:
            self.warnings.append("⚠️ 密钥长度过短，建议至少32个字符")
        else:
            print("✅ 密钥配置安全")
    
    def check_cors_config(self):
        """检查CORS配置"""
        cors_origins = os.getenv("CORS_ORIGINS", "")
        
        if not cors_origins:
            self.warnings.append("⚠️ 未配置CORS源，可能影响前端访问")
        elif "localhost" in cors_origins and os.getenv("DEBUG", "true").lower() != "true":
            self.warnings.append("⚠️ 生产环境仍包含localhost，建议移除")
        else:
            print("✅ CORS配置合理")
    
    def check_database_config(self):
        """检查数据库配置"""
        db_url = os.getenv("DATABASE_URL", "")
        
        if not db_url:
            self.warnings.append("⚠️ 未配置数据库URL")
        elif "password" in db_url.lower() or "123456" in db_url:
            self.issues.append("❌ 数据库使用弱密码")
        else:
            print("✅ 数据库配置安全")
    
    def check_password_policy(self):
        """检查密码策略"""
        min_length = int(os.getenv("PASSWORD_MIN_LENGTH", "8"))
        require_uppercase = os.getenv("PASSWORD_REQUIRE_UPPERCASE", "true").lower() == "true"
        require_lowercase = os.getenv("PASSWORD_REQUIRE_LOWERCASE", "true").lower() == "true"
        require_numbers = os.getenv("PASSWORD_REQUIRE_NUMBERS", "true").lower() == "true"
        require_special = os.getenv("PASSWORD_REQUIRE_SPECIAL", "true").lower() == "true"
        
        if min_length < 8:
            self.warnings.append("⚠️ 密码最小长度过短，建议至少8个字符")
        
        if not all([require_uppercase, require_lowercase, require_numbers, require_special]):
            self.warnings.append("⚠️ 密码策略不够严格，建议启用所有字符类型要求")
        else:
            print("✅ 密码策略配置合理")
    
    def check_file_permissions(self):
        """检查文件权限"""
        sensitive_files = [".env", "backend/app_simple.py", "backend/pyproject.toml"]
        
        for file_path in sensitive_files:
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                # 检查是否对其他用户可读
                if stat.st_mode & 0o044:
                    self.warnings.append(f"⚠️ 文件 {file_path} 对其他用户可读")
    
    def check_debug_mode(self):
        """检查调试模式"""
        debug = os.getenv("DEBUG", "true").lower() == "true"
        
        if debug and os.getenv("ENVIRONMENT", "development") == "production":
            self.issues.append("❌ 生产环境启用了调试模式")
        elif debug:
            self.warnings.append("⚠️ 调试模式已启用，生产环境请关闭")
        else:
            print("✅ 调试模式配置正确")
    
    def generate_secure_key(self):
        """生成安全密钥"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*()-_=+[]{}|;:,.<>?"
        return ''.join(secrets.choice(alphabet) for _ in range(64))
    
    def run_check(self):
        """运行安全检查"""
        print("🔒 WisCude 安全配置检查")
        print("=" * 50)
        
        # 加载环境变量
        env_file = Path(".env")
        if env_file.exists():
            print("📄 加载 .env 文件")
            from dotenv import load_dotenv
            load_dotenv()
        else:
            print("⚠️ 未找到 .env 文件，使用默认配置")
        
        # 执行检查
        self.check_secret_key()
        self.check_cors_config()
        self.check_database_config()
        self.check_password_policy()
        self.check_file_permissions()
        self.check_debug_mode()
        
        # 输出结果
        print("\n" + "=" * 50)
        
        if self.issues:
            print("🚨 严重安全问题:")
            for issue in self.issues:
                print(f"  {issue}")
        
        if self.warnings:
            print("\n⚠️ 安全警告:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.recommendations:
            print("\n💡 安全建议:")
            for rec in self.recommendations:
                print(f"  {rec}")
        
        if not self.issues and not self.warnings:
            print("✅ 所有安全检查通过！")
        
        # 生成安全密钥建议
        if any("密钥" in issue for issue in self.issues):
            print(f"\n🔑 建议使用的安全密钥:")
            print(f"SECRET_KEY={self.generate_secure_key()}")
        
        return len(self.issues) == 0


def main():
    """主函数"""
    checker = SecurityChecker()
    success = checker.run_check()
    
    if not success:
        print("\n❌ 发现安全问题，请修复后再启动系统")
        sys.exit(1)
    else:
        print("\n✅ 安全检查通过，可以安全启动系统")
        sys.exit(0)


if __name__ == "__main__":
    main()
