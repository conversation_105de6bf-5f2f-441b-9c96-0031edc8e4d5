<template>
  <div class="versions-management">
    <div class="page-header">
      <h1>版本管理</h1>
      <p>管理软件版本发布、更新包分发、版本回滚和发布计划</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="版本列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              创建版本
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedVersions.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedVersions.length === 0"
              @click="batchRollback"
            >
              批量回滚
            </el-button>
            <el-button @click="exportVersions">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索版本号"
              style="width: 200px"
              clearable
              @change="loadVersions"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="typeFilter" placeholder="版本类型" style="width: 120px" @change="loadVersions">
              <el-option label="全部类型" value="" />
              <el-option label="主版本" value="major" />
              <el-option label="次版本" value="minor" />
              <el-option label="补丁版本" value="patch" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadVersions">
              <el-option label="全部状态" value="" />
              <el-option label="开发中" value="development" />
              <el-option label="测试中" value="testing" />
              <el-option label="已发布" value="released" />
              <el-option label="已回滚" value="rollback" />
            </el-select>
          </div>
        </div>

        <!-- 版本列表 -->
        <el-table
          :data="versions"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="版本信息" min-width="250">
            <template #default="{ row }">
              <div class="version-info">
                <div class="version-number">{{ row.version }}</div>
                <div class="version-name">{{ row.name }}</div>
                <div class="version-meta">
                  <el-tag size="small" :type="getTypeTagType(row.type)">
                    {{ getTypeName(row.type) }}
                  </el-tag>
                  <span class="build-number">Build {{ row.build_number }}</span>
                  <span class="file-size">{{ formatFileSize(row.file_size) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="发布信息" width="200">
            <template #default="{ row }">
              <div class="release-info">
                <div class="release-date">{{ formatTime(row.release_date) }}</div>
                <div class="release-channel">{{ getChannelName(row.release_channel) }}</div>
                <div class="target-platforms">
                  <el-tag 
                    v-for="platform in row.target_platforms" 
                    :key="platform" 
                    size="small" 
                    class="platform-tag"
                  >
                    {{ getPlatformName(platform) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="更新统计" width="150">
            <template #default="{ row }">
              <div class="update-stats">
                <div class="stat-item">
                  <span class="stat-label">下载:</span>
                  <span class="stat-value">{{ row.download_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">安装:</span>
                  <span class="stat-value install-count">{{ row.install_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">成功率:</span>
                  <span class="stat-value success-rate">{{ (row.success_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="推送进度" width="120">
            <template #default="{ row }">
              <div class="push-progress">
                <div class="progress-text">{{ row.push_progress }}%</div>
                <el-progress 
                  :percentage="row.push_progress" 
                  :status="row.push_progress === 100 ? 'success' : ''"
                  :stroke-width="6"
                  :show-text="false"
                />
                <div class="progress-users">{{ row.pushed_users }}/{{ row.target_users }}用户</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewVersion(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="text" size="small" @click="editVersion(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="text" size="small" @click="manageFiles(row)">
                <el-icon><Folder /></el-icon>
                文件
              </el-button>
              <el-button type="text" size="small" @click="viewLogs(row)">
                <el-icon><Document /></el-icon>
                日志
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                :class="row.status === 'released' ? 'warning' : 'success'"
                @click="toggleVersionStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'released' ? '回滚' : '发布' }}
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deleteVersion(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadVersions"
            @current-change="loadVersions"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="发布计划" name="schedule">
        <!-- 发布计划时间线 -->
        <div class="schedule-section">
          <div class="schedule-header">
            <h3>发布计划时间线</h3>
            <el-button type="primary" @click="showScheduleDialog = true">
              <el-icon><Plus /></el-icon>
              添加计划
            </el-button>
          </div>
          <div class="schedule-timeline">
            <div v-for="schedule in releaseSchedules" :key="schedule.id" class="schedule-item">
              <div class="schedule-date">
                <div class="date-month">{{ getMonth(schedule.planned_date) }}</div>
                <div class="date-day">{{ getDay(schedule.planned_date) }}</div>
              </div>
              <div class="schedule-content">
                <div class="schedule-version">{{ schedule.version }}</div>
                <div class="schedule-description">{{ schedule.description }}</div>
                <div class="schedule-features">
                  <el-tag 
                    v-for="feature in schedule.features" 
                    :key="feature" 
                    size="small" 
                    class="feature-tag"
                  >
                    {{ feature }}
                  </el-tag>
                </div>
                <div class="schedule-status">
                  <el-tag :type="getScheduleStatusType(schedule.status)">
                    {{ getScheduleStatusName(schedule.status) }}
                  </el-tag>
                  <span class="schedule-progress">进度: {{ schedule.progress }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="版本发布趋势">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">版本发布趋势图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="更新成功率">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">更新成功率图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="平台分布分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">平台分布分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑版本对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingVersion ? '编辑版本' : '创建版本'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="versionForm" :rules="versionRules" ref="versionFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="versionForm.version" placeholder="如: v2.1.3" />
            </el-form-item>
            <el-form-item label="版本名称" prop="name">
              <el-input v-model="versionForm.name" placeholder="请输入版本名称" />
            </el-form-item>
            <el-form-item label="版本类型" prop="type">
              <el-select v-model="versionForm.type" placeholder="请选择版本类型">
                <el-option label="主版本" value="major" />
                <el-option label="次版本" value="minor" />
                <el-option label="补丁版本" value="patch" />
              </el-select>
            </el-form-item>
            <el-form-item label="构建号" prop="build_number">
              <el-input-number v-model="versionForm.build_number" :min="1" placeholder="构建号" />
            </el-form-item>
            <el-form-item label="发布渠道" prop="release_channel">
              <el-select v-model="versionForm.release_channel" placeholder="请选择发布渠道">
                <el-option label="稳定版" value="stable" />
                <el-option label="测试版" value="beta" />
                <el-option label="开发版" value="alpha" />
              </el-select>
            </el-form-item>
            <el-form-item label="目标平台" prop="target_platforms">
              <el-checkbox-group v-model="versionForm.target_platforms">
                <el-checkbox label="windows">Windows</el-checkbox>
                <el-checkbox label="macos">macOS</el-checkbox>
                <el-checkbox label="linux">Linux</el-checkbox>
                <el-checkbox label="android">Android</el-checkbox>
                <el-checkbox label="ios">iOS</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="更新内容" name="content">
            <el-form-item label="更新描述" prop="description">
              <el-input v-model="versionForm.description" type="textarea" rows="4" placeholder="请输入版本更新描述" />
            </el-form-item>
            <el-form-item label="新增功能">
              <el-input v-model="versionForm.new_features" type="textarea" rows="3" placeholder="请输入新增功能" />
            </el-form-item>
            <el-form-item label="问题修复">
              <el-input v-model="versionForm.bug_fixes" type="textarea" rows="3" placeholder="请输入问题修复" />
            </el-form-item>
            <el-form-item label="性能优化">
              <el-input v-model="versionForm.improvements" type="textarea" rows="3" placeholder="请输入性能优化" />
            </el-form-item>
            <el-form-item label="重要提醒">
              <el-input v-model="versionForm.important_notes" type="textarea" rows="2" placeholder="请输入重要提醒" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="发布设置" name="release">
            <el-form-item label="计划发布时间" prop="planned_release_date">
              <el-date-picker
                v-model="versionForm.planned_release_date"
                type="datetime"
                placeholder="选择发布时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="强制更新">
              <el-switch v-model="versionForm.force_update" />
            </el-form-item>
            <el-form-item label="最小支持版本">
              <el-input v-model="versionForm.min_supported_version" placeholder="如: v2.0.0" />
            </el-form-item>
            <el-form-item label="推送策略">
              <el-select v-model="versionForm.push_strategy" placeholder="请选择推送策略">
                <el-option label="立即推送" value="immediate" />
                <el-option label="渐进推送" value="gradual" />
                <el-option label="定时推送" value="scheduled" />
              </el-select>
            </el-form-item>
            <el-form-item label="目标用户比例" v-if="versionForm.push_strategy === 'gradual'">
              <el-slider v-model="versionForm.target_user_percentage" :min="1" :max="100" show-input />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="文件管理" name="files">
            <el-form-item label="安装包上传">
              <div class="file-upload">
                <el-upload
                  class="upload-demo"
                  :file-list="versionForm.files"
                  :before-upload="beforeFileUpload"
                  :http-request="uploadFile"
                  multiple
                  accept=".exe,.dmg,.deb,.apk,.ipa"
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    上传安装包
                  </el-button>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="更新日志文件">
              <el-upload
                class="upload-demo"
                :file-list="versionForm.changelog_files"
                :before-upload="beforeChangelogUpload"
                :http-request="uploadChangelog"
                accept=".md,.txt,.pdf"
              >
                <el-button>
                  <el-icon><Document /></el-icon>
                  上传更新日志
                </el-button>
              </el-upload>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveVersion" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 版本详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="版本详情"
      width="900px"
    >
      <div class="version-detail" v-if="viewingVersion">
        <div class="detail-header">
          <div class="version-info-large">
            <h2 class="version-number-large">{{ viewingVersion.version }}</h2>
            <div class="version-name-large">{{ viewingVersion.name }}</div>
            <div class="version-meta-large">
              <el-tag :type="getTypeTagType(viewingVersion.type)">
                {{ getTypeName(viewingVersion.type) }}
              </el-tag>
              <span class="build-number-large">Build {{ viewingVersion.build_number }}</span>
              <span class="file-size-large">{{ formatFileSize(viewingVersion.file_size) }}</span>
            </div>
          </div>
          <div class="version-stats-large">
            <div class="stat-item-large">
              <span class="stat-value-large">{{ viewingVersion.download_count }}</span>
              <span class="stat-label-large">下载次数</span>
            </div>
            <div class="stat-item-large">
              <span class="stat-value-large">{{ viewingVersion.install_count }}</span>
              <span class="stat-label-large">安装次数</span>
            </div>
            <div class="stat-item-large">
              <span class="stat-value-large">{{ (viewingVersion.success_rate * 100).toFixed(1) }}%</span>
              <span class="stat-label-large">成功率</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>更新内容</h3>
          <div class="update-content">
            <div class="content-section">
              <h4>版本描述</h4>
              <div class="content-text">{{ viewingVersion.description }}</div>
            </div>
            <div class="content-section" v-if="viewingVersion.new_features">
              <h4>新增功能</h4>
              <div class="content-text">{{ viewingVersion.new_features }}</div>
            </div>
            <div class="content-section" v-if="viewingVersion.bug_fixes">
              <h4>问题修复</h4>
              <div class="content-text">{{ viewingVersion.bug_fixes }}</div>
            </div>
            <div class="content-section" v-if="viewingVersion.improvements">
              <h4>性能优化</h4>
              <div class="content-text">{{ viewingVersion.improvements }}</div>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>发布信息</h3>
          <div class="release-detail">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">发布时间：</span>
                <span class="value">{{ formatTime(viewingVersion.release_date) }}</span>
              </div>
              <div class="info-item">
                <span class="label">发布渠道：</span>
                <span class="value">{{ getChannelName(viewingVersion.release_channel) }}</span>
              </div>
              <div class="info-item">
                <span class="label">目标平台：</span>
                <span class="value">{{ viewingVersion.target_platforms?.map(p => getPlatformName(p)).join('、') }}</span>
              </div>
              <div class="info-item">
                <span class="label">强制更新：</span>
                <span class="value">{{ viewingVersion.force_update ? '是' : '否' }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>推送进度</h3>
          <div class="push-detail">
            <div class="progress-header">
              <span>{{ viewingVersion.push_progress }}% ({{ viewingVersion.pushed_users }}/{{ viewingVersion.target_users }}用户)</span>
            </div>
            <el-progress 
              :percentage="viewingVersion.push_progress" 
              :status="viewingVersion.push_progress === 100 ? 'success' : ''"
              :stroke-width="12"
            />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 发布计划对话框 -->
    <el-dialog
      v-model="showScheduleDialog"
      title="添加发布计划"
      width="600px"
    >
      <el-form :model="scheduleForm" label-width="100px">
        <el-form-item label="版本号">
          <el-input v-model="scheduleForm.version" placeholder="如: v2.2.0" />
        </el-form-item>
        <el-form-item label="计划描述">
          <el-input v-model="scheduleForm.description" type="textarea" rows="3" placeholder="请输入计划描述" />
        </el-form-item>
        <el-form-item label="计划日期">
          <el-date-picker
            v-model="scheduleForm.planned_date"
            type="date"
            placeholder="选择计划日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="主要功能">
          <el-select 
            v-model="scheduleForm.features" 
            multiple 
            filterable 
            allow-create 
            placeholder="请输入主要功能"
          >
            <el-option label="新功能开发" value="新功能开发" />
            <el-option label="性能优化" value="性能优化" />
            <el-option label="Bug修复" value="Bug修复" />
            <el-option label="安全更新" value="安全更新" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showScheduleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSchedule">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, Download, Folder, Document
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedVersions = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const showScheduleDialog = ref(false)
const editingVersion = ref(null)
const viewingVersion = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const typeFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 版本列表
const versions = ref([
  {
    id: '1',
    version: 'v2.1.3',
    name: '稳定性优化版本',
    type: 'patch',
    build_number: 2103,
    file_size: 52428800, // 50MB
    release_date: '2024-01-15 10:30:00',
    release_channel: 'stable',
    target_platforms: ['windows', 'macos', 'linux'],
    download_count: 15678,
    install_count: 14523,
    success_rate: 0.926,
    push_progress: 100,
    pushed_users: 15678,
    target_users: 15678,
    description: '修复已知问题，优化系统性能',
    new_features: '新增自动更新功能',
    bug_fixes: '修复内存泄漏问题',
    improvements: '优化启动速度',
    important_notes: '建议所有用户更新',
    force_update: false,
    min_supported_version: 'v2.0.0',
    push_strategy: 'gradual',
    target_user_percentage: 100,
    status: 'released',
    created_at: '2024-01-14 15:20:00'
  }
])

// 发布计划
const releaseSchedules = ref([
  {
    id: 1,
    version: 'v2.2.0',
    description: '重大功能更新，新增AI助手',
    planned_date: '2024-02-15',
    features: ['AI助手', '新UI设计', '性能优化'],
    status: 'planning',
    progress: 25
  },
  {
    id: 2,
    version: 'v2.1.4',
    description: '安全更新和Bug修复',
    planned_date: '2024-01-30',
    features: ['安全更新', 'Bug修复'],
    status: 'development',
    progress: 80
  }
])

// 表单数据
const versionForm = reactive({
  version: '',
  name: '',
  type: 'patch',
  build_number: 1,
  release_channel: 'stable',
  target_platforms: [],
  description: '',
  new_features: '',
  bug_fixes: '',
  improvements: '',
  important_notes: '',
  planned_release_date: '',
  force_update: false,
  min_supported_version: '',
  push_strategy: 'gradual',
  target_user_percentage: 20,
  files: [],
  changelog_files: []
})

const scheduleForm = reactive({
  version: '',
  description: '',
  planned_date: '',
  features: []
})

// 表单验证规则
const versionRules = {
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入版本名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择版本类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入版本描述', trigger: 'blur' }
  ]
}

// 方法
const loadVersions = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取版本列表
    console.log('Loading versions...')
  } catch (error) {
    ElMessage.error('加载版本列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedVersions.value = selection
}

const getTypeName = (type) => {
  const types = {
    major: '主版本',
    minor: '次版本',
    patch: '补丁版本'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    major: 'danger',
    minor: 'primary',
    patch: 'success'
  }
  return types[type] || ''
}

const getChannelName = (channel) => {
  const channels = {
    stable: '稳定版',
    beta: '测试版',
    alpha: '开发版'
  }
  return channels[channel] || channel
}

const getPlatformName = (platform) => {
  const platforms = {
    windows: 'Windows',
    macos: 'macOS',
    linux: 'Linux',
    android: 'Android',
    ios: 'iOS'
  }
  return platforms[platform] || platform
}

const getStatusName = (status) => {
  const statuses = {
    development: '开发中',
    testing: '测试中',
    released: '已发布',
    rollback: '已回滚'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    development: 'info',
    testing: 'warning',
    released: 'success',
    rollback: 'danger'
  }
  return types[status] || ''
}

const getScheduleStatusName = (status) => {
  const statuses = {
    planning: '计划中',
    development: '开发中',
    testing: '测试中',
    ready: '准备发布'
  }
  return statuses[status] || status
}

const getScheduleStatusType = (status) => {
  const types = {
    planning: 'info',
    development: 'warning',
    testing: 'primary',
    ready: 'success'
  }
  return types[status] || ''
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const getMonth = (date) => {
  return new Date(date).toLocaleDateString('zh-CN', { month: 'short' })
}

const getDay = (date) => {
  return new Date(date).getDate()
}

const viewVersion = (version) => {
  viewingVersion.value = version
  showViewDialog.value = true
}

const editVersion = (version) => {
  editingVersion.value = version
  Object.assign(versionForm, version)
  showCreateDialog.value = true
}

const manageFiles = (version) => {
  // TODO: 跳转到文件管理页面
  console.log('Managing files for version:', version)
}

const viewLogs = (version) => {
  // TODO: 跳转到日志查看页面
  console.log('Viewing logs for version:', version)
}

const resetForm = () => {
  editingVersion.value = null
  Object.assign(versionForm, {
    version: '',
    name: '',
    type: 'patch',
    build_number: 1,
    release_channel: 'stable',
    target_platforms: [],
    description: '',
    new_features: '',
    bug_fixes: '',
    improvements: '',
    important_notes: '',
    planned_release_date: '',
    force_update: false,
    min_supported_version: '',
    push_strategy: 'gradual',
    target_user_percentage: 20,
    files: [],
    changelog_files: []
  })
  formActiveTab.value = 'basic'
}

const beforeFileUpload = (file) => {
  const isValidType = ['.exe', '.dmg', '.deb', '.apk', '.ipa'].some(ext =>
    file.name.toLowerCase().endsWith(ext)
  )
  const isLt100M = file.size / 1024 / 1024 < 100

  if (!isValidType) {
    ElMessage.error('只能上传安装包文件!')
    return false
  }
  if (!isLt100M) {
    ElMessage.error('文件大小不能超过 100MB!')
    return false
  }
  return true
}

const uploadFile = async (options) => {
  // TODO: 实现文件上传逻辑
  console.log('Uploading file...', options.file)
  ElMessage.success('文件上传成功')
}

const beforeChangelogUpload = (file) => {
  const isValidType = ['.md', '.txt', '.pdf'].some(ext =>
    file.name.toLowerCase().endsWith(ext)
  )
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传文档文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

const uploadChangelog = async (options) => {
  // TODO: 实现更新日志上传逻辑
  console.log('Uploading changelog...', options.file)
  ElMessage.success('更新日志上传成功')
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', versionForm)
    showCreateDialog.value = false
    ElMessage.success('版本已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveVersion = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and releasing version...', versionForm)
    showCreateDialog.value = false
    ElMessage.success(editingVersion.value ? '版本更新成功' : '版本创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveSchedule = async () => {
  try {
    // TODO: 实现保存计划逻辑
    console.log('Saving schedule...', scheduleForm)
    showScheduleDialog.value = false
    ElMessage.success('发布计划添加成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const toggleVersionStatus = async (version) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling version status...', version)
}

const deleteVersion = async (version) => {
  try {
    await ElMessageBox.confirm('确定要删除这个版本吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting version...', version)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing versions...', selectedVersions.value)
}

const batchRollback = async () => {
  // TODO: 实现批量回滚逻辑
  console.log('Batch rolling back versions...', selectedVersions.value)
}

const exportVersions = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting versions...')
}

onMounted(() => {
  loadVersions()
})
</script>

<style scoped>
.versions-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.version-info {
  padding: 8px 0;
}

.version-number {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.version-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
}

.version-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.build-number {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.file-size {
  font-size: 11px;
  color: #409eff;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.release-info {
  padding: 8px 0;
}

.release-date {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.release-channel {
  font-size: 12px;
  color: #606266;
  margin-bottom: 6px;
}

.target-platforms {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.platform-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
  font-size: 10px;
}

.update-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.install-count {
  color: #67c23a !important;
}

.success-rate {
  color: #409eff !important;
}

.push-progress {
  text-align: center;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.progress-users {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.schedule-section {
  padding: 20px 0;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.schedule-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.schedule-timeline {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.schedule-item {
  display: flex;
  gap: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.schedule-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: #409eff;
  color: white;
  border-radius: 8px;
  flex-shrink: 0;
}

.date-month {
  font-size: 12px;
  font-weight: 500;
}

.date-day {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
}

.schedule-content {
  flex: 1;
}

.schedule-version {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.schedule-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}

.schedule-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.feature-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
  font-size: 11px;
}

.schedule-status {
  display: flex;
  gap: 12px;
  align-items: center;
}

.schedule-progress {
  font-size: 12px;
  color: #909399;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.file-upload {
  margin-bottom: 16px;
}

.version-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.version-info-large {
  flex: 1;
}

.version-number-large {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.version-name-large {
  font-size: 16px;
  color: #606266;
  margin-bottom: 12px;
}

.version-meta-large {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.build-number-large {
  font-size: 12px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.file-size-large {
  font-size: 12px;
  color: #409eff;
  background-color: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.version-stats-large {
  display: flex;
  gap: 24px;
}

.stat-item-large {
  text-align: center;
}

.stat-value-large {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label-large {
  font-size: 12px;
  color: #909399;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.update-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.content-section {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.content-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.content-text {
  font-size: 13px;
  color: #606266;
  line-height: 1.6;
}

.release-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  color: #606266;
  font-weight: 500;
}

.value {
  color: #303133;
}

.push-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.progress-header {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
  text-align: center;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
