/**
 * 英语练习管理API接口
 */
import request from './request'

// ==================== 词汇库管理 ====================

/**
 * 获取词汇列表
 */
export const getVocabularyWords = (params: {
  page?: number
  size?: number
  keyword?: string
  difficulty_level?: number
  word_type?: string
}) => {
  return request({
    url: '/v1/english-practice/vocabulary',
    method: 'get',
    params
  })
}

/**
 * 创建词汇
 */
export const createVocabularyWord = (data: {
  word: string
  pronunciation?: string
  definition: string
  chinese_meaning: string
  word_type: string
  difficulty_level: number
  example_sentences?: string[]
  audio_url?: string
  image_url?: string
  tags?: string[]
}) => {
  return request({
    url: '/api/v1/english-practice/vocabulary',
    method: 'post',
    data
  })
}

/**
 * 更新词汇
 */
export const updateVocabularyWord = (wordId: string, data: any) => {
  return request({
    url: `/api/v1/english-practice/vocabulary/${wordId}`,
    method: 'put',
    data
  })
}

/**
 * 删除词汇
 */
export const deleteVocabularyWord = (wordId: string) => {
  return request({
    url: `/api/v1/english-practice/vocabulary/${wordId}`,
    method: 'delete'
  })
}

/**
 * 上传词汇发音音频
 */
export const uploadVocabularyAudio = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/v1/english-practice/vocabulary/upload-audio',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 听力材料管理 ====================

/**
 * 获取听力材料列表
 */
export const getListeningMaterials = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  difficulty_level?: number
}) => {
  return request({
    url: '/v1/english-practice/listening',
    method: 'get',
    params
  })
}

/**
 * 创建听力材料
 */
export const createListeningMaterial = (data: {
  title: string
  description?: string
  category: string
  difficulty_level: number
  audio_url: string
  transcript?: string
  duration: number
  questions?: any[]
  tags?: string[]
}) => {
  return request({
    url: '/api/v1/english-practice/listening',
    method: 'post',
    data
  })
}

/**
 * 上传听力音频
 */
export const uploadListeningAudio = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/v1/english-practice/listening/upload-audio',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 口语练习管理 ====================

/**
 * 获取口语练习场景列表
 */
export const getSpeakingScenarios = (params: {
  page?: number
  size?: number
  keyword?: string
  scenario_type?: string
  difficulty_level?: number
}) => {
  return request({
    url: '/v1/english-practice/speaking',
    method: 'get',
    params
  })
}

/**
 * 创建口语练习场景
 */
export const createSpeakingScenario = (data: {
  title: string
  description?: string
  scenario_type: string
  difficulty_level: number
  dialogue?: any[]
  prompts?: string[]
  evaluation_criteria?: any[]
  tags?: string[]
}) => {
  return request({
    url: '/api/v1/english-practice/speaking',
    method: 'post',
    data
  })
}

// ==================== 学习进度管理 ====================

/**
 * 获取用户学习进度
 */
export const getUserProgress = (params: {
  page?: number
  size?: number
  user_id?: string
}) => {
  return request({
    url: '/v1/english-practice/progress',
    method: 'get',
    params
  })
}

/**
 * 获取用户学习进度详情
 */
export const getUserProgressDetail = (userId: string) => {
  return request({
    url: `/api/v1/english-practice/progress/${userId}`,
    method: 'get'
  })
}

// ==================== 练习记录管理 ====================

/**
 * 获取练习记录
 */
export const getPracticeRecords = (params: {
  page?: number
  size?: number
  user_id?: string
  practice_type?: string
}) => {
  return request({
    url: '/v1/english-practice/records',
    method: 'get',
    params
  })
}

/**
 * 创建练习记录
 */
export const createPracticeRecord = (data: {
  practice_type: string
  content_id: string
  user_answer?: any
  score?: number
  time_spent: number
  is_completed: boolean
  feedback?: string
}) => {
  return request({
    url: '/api/v1/english-practice/records',
    method: 'post',
    data
  })
}

// ==================== 统计分析 ====================

/**
 * 获取英语练习管理概览统计
 */
export const getEnglishPracticeOverview = () => {
  return request({
    url: '/v1/english-practice/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取词汇统计
 */
export const getVocabularyStatistics = () => {
  return request({
    url: '/v1/english-practice/statistics/vocabulary',
    method: 'get'
  })
}

/**
 * 获取听力统计
 */
export const getListeningStatistics = () => {
  return request({
    url: '/v1/english-practice/statistics/listening',
    method: 'get'
  })
}

/**
 * 获取口语统计
 */
export const getSpeakingStatistics = () => {
  return request({
    url: '/v1/english-practice/statistics/speaking',
    method: 'get'
  })
}

// ==================== 批量操作 ====================

/**
 * 批量导入词汇
 */
export const batchImportVocabulary = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/v1/english-practice/vocabulary/batch-import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量导出词汇
 */
export const batchExportVocabulary = (params: {
  word_ids?: string[]
  difficulty_level?: number
  word_type?: string
}) => {
  return request({
    url: '/v1/english-practice/vocabulary/batch-export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// ==================== 数据类型定义 ====================

export interface VocabularyWord {
  id: string
  word: string
  pronunciation?: string
  definition: string
  chinese_meaning: string
  word_type: string
  difficulty_level: number
  example_sentences: string[]
  audio_url?: string
  image_url?: string
  tags: string[]
  practice_count: number
  correct_count: number
  accuracy_rate: number
  created_at: string
  updated_at: string
}

export interface ListeningMaterial {
  id: string
  title: string
  description?: string
  category: string
  difficulty_level: number
  audio_url: string
  transcript?: string
  duration: number
  questions: any[]
  tags: string[]
  play_count: number
  completion_count: number
  average_score: number
  created_at: string
  updated_at: string
}

export interface SpeakingScenario {
  id: string
  title: string
  description?: string
  scenario_type: string
  difficulty_level: number
  dialogue: any[]
  prompts: string[]
  evaluation_criteria: any[]
  tags: string[]
  practice_count: number
  average_score: number
  created_at: string
  updated_at: string
}

export interface EnglishPracticeRecord {
  id: string
  user_id: string
  practice_type: string
  content_id: string
  user_answer?: any
  correct_answer?: any
  score?: number
  time_spent: number
  is_completed: boolean
  feedback?: string
  created_at: string
}

export interface UserEnglishProgress {
  id: string
  user_id: string
  vocabulary_level: number
  listening_level: number
  speaking_level: number
  total_practice_time: number
  vocabulary_mastered: number
  listening_completed: number
  speaking_completed: number
  current_streak: number
  longest_streak: number
  last_practice_date?: string
  created_at: string
  updated_at: string
}

export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}