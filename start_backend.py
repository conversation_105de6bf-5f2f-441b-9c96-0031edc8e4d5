#!/usr/bin/env python3
"""
WisCude 后端启动脚本
简洁的后端服务启动器
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 50)
    print("🚀 WisCude 后端服务启动器")
    print("=" * 50)
    print("📍 项目路径:", Path.cwd())
    print("🌐 后端服务: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/api/health")
    print("=" * 50)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    print(f"✓ Python版本: {sys.version.split()[0]}")
    
    # 检查backend目录
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    print("✓ backend目录存在")
    
    # 检查main.py
    main_file = backend_dir / "main.py"
    if not main_file.exists():
        print("❌ backend/main.py不存在")
        return False
    print("✓ main.py文件存在")
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ .env文件不存在，将使用默认配置")
    else:
        print("✓ .env配置文件存在")
    
    return True

def install_dependencies():
    """安装后端依赖"""
    print("\n📦 检查后端依赖...")
    
    backend_dir = Path("backend")
    pyproject_file = backend_dir / "pyproject.toml"
    requirements_file = Path("requirements.txt")
    current_dir = Path.cwd()
    
    def run_pip_command(cmd, description):
        """运行pip命令并处理编码问题"""
        try:
            # Windows环境下使用utf-8编码，并设置错误处理
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                encoding='utf-8', 
                errors='replace',  # 替换无法解码的字符
                timeout=300  # 5分钟超时
            )
            return result
        except subprocess.TimeoutExpired:
            print(f"⚠️ {description}超时（5分钟），可能网络较慢")
            return None
        except UnicodeDecodeError as e:
            print(f"⚠️ {description}编码错误: {e}")
            return None
        except Exception as e:
            print(f"⚠️ {description}执行错误: {e}")
            return None
    
    try:
        if pyproject_file.exists():
            print("📦 使用pyproject.toml安装依赖...")
            os.chdir(backend_dir)
            
            cmd = [sys.executable, "-m", "pip", "install", "-e", ".", "--no-cache-dir"]
            result = run_pip_command(cmd, "pyproject.toml安装")
            os.chdir(current_dir)
            
            if result and result.returncode == 0:
                print("✓ 依赖安装完成")
                return True
            else:
                print("⚠️ pyproject.toml安装失败，尝试requirements文件")
                if result and result.stderr:
                    # 安全地处理错误信息
                    error_msg = result.stderr[:200].replace('\x00', '').strip()
                    if error_msg:
                        print(f"错误信息: {error_msg}...")
                
                if requirements_file.exists():
                    print("📦 使用requirements.txt安装依赖...")
                    cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file), "--no-cache-dir"]
                    result = run_pip_command(cmd, "requirements.txt安装")
                    
                    if result and result.returncode == 0:
                        print("✓ 核心依赖安装完成")
                        return True
                    else:
                        print("⚠️ requirements.txt安装失败")
                        if result and result.stderr:
                            error_msg = result.stderr[:200].replace('\x00', '').strip()
                            if error_msg:
                                print(f"错误信息: {error_msg}...")
                        
        elif requirements_file.exists():
            print("📦 使用requirements.txt安装依赖...")
            cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file), "--no-cache-dir"]
            result = run_pip_command(cmd, "requirements.txt安装")
            
            if result and result.returncode == 0:
                print("✓ 核心依赖安装完成")
                return True
            else:
                print("⚠️ 依赖安装失败")
                if result and result.stderr:
                    error_msg = result.stderr[:200].replace('\x00', '').strip()
                    if error_msg:
                        print(f"错误信息: {error_msg}...")
        else:
            print("⚠️ 未找到依赖文件，继续启动")
            
        return False
        
    except Exception as e:
        print(f"⚠️ 依赖安装过程中出错: {e}")
        return False
    finally:
        # 确保返回原目录
        os.chdir(current_dir)

def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    
    try:
        # 切换到backend目录
        os.chdir("backend")
        
        # 启动uvicorn服务器
        cmd = [sys.executable, "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
        print(f"执行命令: {' '.join(cmd)}")
        print("\n🌟 后端服务启动中...")
        print("💡 按 Ctrl+C 停止服务")
        print("\n" + "=" * 50)
        
        # 启动服务
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务已停止")
    except FileNotFoundError:
        print("❌ 未找到uvicorn，请先安装: pip install uvicorn")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    finally:
        # 返回项目根目录
        os.chdir("..")

def main():
    """主函数"""
    print_banner()
    
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    install_dependencies()
    start_backend()

if __name__ == "__main__":
    main()