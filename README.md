# 🎓 WisCude 后台管理系统

基于 **FastAPI + Vue 3 + TypeScript + Element Plus** 构建的现代化 WisCude Android 应用后台管理系统。

## ✨ 新版本特性

### 🚀 优化的启动流程
- **智能启动管理器** - 一键启动、服务监控、故障诊断
- **健康检查机制** - 实时监控服务状态，确保系统稳定运行
- **依赖检查** - 自动检测环境依赖，智能解决启动问题
- **服务编排** - 按依赖关系自动启动服务，避免启动顺序问题

### 🔧 增强的系统架构
- **统一配置管理** - YAML配置文件，支持环境变量覆盖
- **连接池优化** - 数据库连接池配置，提升性能和稳定性
- **动态服务发现** - Android应用自动发现后端服务，无需硬编码IP
- **错误恢复机制** - 自动重试、优雅降级、故障隔离

## 📁 项目架构

```
wiscude-user/
├── backend/                    # 🔧 FastAPI 后端服务
│   ├── app/
│   │   ├── api/               # API 路由模块
│   │   │   ├── health.py      # 🔍 健康检查API (新增)
│   │   │   ├── auth.py        # 认证授权
│   │   │   ├── users.py       # 用户管理
│   │   │   └── ...
│   │   ├── core/              # 核心组件
│   │   │   ├── database.py    # 🔄 增强数据库管理器 (优化)
│   │   │   ├── config.py      # 配置管理
│   │   │   └── ...
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务逻辑
│   │   └── utils/             # 工具函数
│   ├── main.py               # 🚀 优化的应用入口 (重构)
│   └── pyproject.toml        # 📦 统一依赖管理 (新增)
├── frontend/                   # 🎨 Vue 3 前端界面
│   ├── src/
│   │   ├── components/        # Vue 组件
│   │   ├── views/             # 页面视图
│   │   ├── api/               # API 调用
│   │   ├── store/             # 状态管理
│   │   ├── router/            # 路由配置
│   │   └── types/             # 类型定义
│   ├── package.json           # 前端依赖
│   └── vite.config.ts         # 构建配置
├── config/                     # 📋 统一配置目录 (新增)
│   ├── app.yaml              # 应用配置
│   ├── database.yaml         # 数据库配置
│   └── environment.py        # 环境管理器
├── scripts/                    # 🛠️ 启动管理脚本 (新增)
│   ├── startup_manager.py    # 启动管理器
│   └── startup_config.json   # 启动配置
├── start_manager.py           # 🚀 跨平台启动管理器
└── README.md                  # 📖 项目文档 (更新)
```

### 🔗 Android 应用集成
```
Wiscude/                        # 📱 Android 应用
├── app/src/main/java/com/example/wiscude/
│   ├── di/
│   │   └── AppModule.kt       # 🔄 动态网络配置 (优化)
│   ├── network/
│   │   └── NetworkConfig.kt   # 🌐 服务发现管理器 (新增)
│   └── ui/settings/
│       └── NetworkSettingsScreen.kt  # ⚙️ 网络设置界面 (新增)
└── ...
```

## 🚀 项目特性

### 核心功能
- **数据同步服务**：从 Android SQLite 数据库同步数据到 PostgreSQL
- **用户管理**：完整的用户信息管理、状态控制、会员管理
- **数据分析**：多维度的用户行为分析和学习数据洞察
- **系统管理**：管理员权限控制、系统配置、日志管理

### 技术特性
- **前后端分离**：FastAPI 后端 + Vue 3 前端
- **现代化技术栈**：TypeScript、Composition API、Pinia 状态管理
- **响应式设计**：支持桌面和平板设备
- **实时数据**：WebSocket 支持、自动刷新
- **安全认证**：JWT Token、权限控制

## 🛠️ 技术栈

### 后端
- **框架**：FastAPI 0.109.0
- **数据库**：PostgreSQL 15+ / SQLite (Android 数据同步)
- **ORM**：SQLAlchemy 2.0
- **认证**：JWT + OAuth2
- **异步**：asyncio + uvicorn

### 前端
- **框架**：Vue 3.4 + TypeScript
- **UI 组件**：Element Plus 2.4
- **状态管理**：Pinia 2.1
- **路由**：Vue Router 4.2
- **构建工具**：Vite 5.0
- **图表**：ECharts 5.4

## 📋 系统要求

- Python 3.12+
- Node.js 18+
- PostgreSQL 15+

## 🚀 快速启动指南

### 🎯 方法1：智能启动管理器（强烈推荐）

使用我们全新的智能启动管理器，享受一键启动体验：

```bash
# 🎛️ 交互式启动（推荐新手）
python start_manager.py

# ⚡ 快速启动所有服务
python start_manager.py --quick-start

# 🔧 仅启动后端服务
python start_manager.py --backend-only

# 🎨 仅启动前端服务
python start_manager.py --frontend-only

# 🔍 检查环境配置
python start_manager.py --check
```

**智能启动管理器特性：**
- ✅ 自动环境检查和依赖安装
- 🔍 实时服务健康监控
- 🔄 智能服务重启和故障恢复
- 📊 详细的启动状态报告
- 🎛️ 交互式服务管理控制台

### 🛠️ 方法2：传统启动脚本

```bash
# Windows 用户
python start_manager.py

# Linux/Mac 用户
python start_manager.py
```

**传统脚本新特性：**
- 🔍 环境依赖自动检查
- ⏳ 服务启动状态等待
- 🔗 服务间依赖关系处理
- 📋 详细的启动日志输出

### 🔧 方法3：手动启动（开发者模式）

```bash
# 1. 安装后端依赖
cd backend && pip install -e .

# 2. 启动后端服务
cd backend && python main.py

# 3. 启动前端服务（新终端）
cd frontend && npm install && npm run dev
```

### 🌐 访问应用

启动成功后，您可以访问以下地址：

| 服务 | 地址 | 说明 |
|------|------|------|
| 🎨 **前端界面** | http://localhost:5173 | 管理后台主界面 |
| 🔧 **后端API** | http://localhost:8000 | RESTful API服务 |
| 📚 **API文档** | http://localhost:8000/docs | Swagger API文档 |
| 🔍 **健康检查** | http://localhost:8000/api/health | 服务状态监控 |
| 📊 **详细状态** | http://localhost:8000/api/health/detailed | 系统详细状态 |

## 🔑 默认账户

**管理员账户**：
- 用户名：`admin`
- 密码：`admin123`

⚠️ **重要**：首次登录后请立即修改默认密码！

## 📊 主要功能模块

### 1. 仪表板
- 系统概览统计
- 用户增长趋势
- 学习数据分析
- 实时监控面板

### 2. 用户管理
- 用户列表查看和搜索
- 用户详细信息管理
- 用户状态控制
- 会员状态管理

### 3. 数据同步
- Android SQLite 数据库连接
- 全量/增量数据同步
- 同步状态监控
- 同步日志查看

### 4. 数据分析
- 用户行为分析
- 学习效果评估
- 社区数据洞察
- 可视化图表展示

### 5. 系统设置
- 管理员账户管理
- 系统配置管理
- 权限控制设置
- 日志管理

## 🔄 数据同步机制

### 同步流程
1. **连接检查**：验证 Android SQLite 数据库可访问性
2. **数据读取**：从 Android 数据库读取用户数据
3. **数据转换**：将 SQLite 数据转换为 PostgreSQL 格式
4. **增量更新**：智能识别新增和更新的数据
5. **日志记录**：记录同步过程和结果

### 支持的数据类型
- 用户基本信息
- 学习会话记录
- 每日打卡数据
- 社区帖子内容
- 课程信息
- AI 学习记录

## 🔐 安全特性

- **JWT 认证**：安全的用户身份验证
- **权限控制**：基于角色的访问控制
- **密码加密**：bcrypt 密码哈希
- **CORS 保护**：跨域请求安全控制
- **SQL 注入防护**：ORM 参数化查询
- **XSS 防护**：前端输入验证和转义

## 📁 项目结构

```
wiscude-user/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── core/           # 核心配置
│   ├── database.sql        # 数据库初始化脚本
│   ├── pyproject.toml      # Python 项目配置和依赖
│   └── main.py            # 应用入口
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # Vue 组件
│   │   ├── views/          # 页面视图
│   │   ├── api/            # API 调用
│   │   ├── store/          # 状态管理
│   │   ├── router/         # 路由配置
│   │   ├── types/          # 类型定义
│   │   └── utils/          # 工具函数
│   ├── package.json        # 前端依赖
│   └── vite.config.ts      # 构建配置
├── docs/                   # 项目文档
├── .env.example           # 环境变量模板
└── README.md              # 项目说明
```

## 🚀 部署指南

### 开发环境
```bash
# 后端
cd backend && python main.py

# 前端
cd frontend && npm run dev
```

### 生产环境
```bash
# 后端
cd backend
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app

# 前端
cd frontend
npm run build
# 将 dist 目录部署到 Web 服务器
```

## 🔧 配置说明

### 环境变量
- `DATABASE_URL`：PostgreSQL 连接字符串
- `ANDROID_DB_PATH`：Android SQLite 数据库文件路径
- `SECRET_KEY`：JWT 密钥
- `CORS_ORIGINS`：允许的跨域来源

### 数据库配置
- 确保 PostgreSQL 服务运行
- 创建 `wiscude_admin` 数据库
- 执行 `database.sql` 初始化脚本

## 🔧 配置管理

### 📋 环境配置

系统支持多环境配置，通过环境变量 `WISCUDE_ENV` 控制：

```bash
# 开发环境（默认）
export WISCUDE_ENV=development

# 测试环境
export WISCUDE_ENV=testing

# 生产环境
export WISCUDE_ENV=production
```

### ⚙️ 配置文件说明

| 配置文件 | 说明 | 主要配置项 |
|----------|------|------------|
| `config/app.yaml` | 应用主配置 | 服务器、安全、CORS、日志等 |
| `config/database.yaml` | 数据库配置 | 连接池、同步、备份等 |
| `config/environment.py` | 环境管理器 | 配置加载、验证、覆盖等 |

### 🔐 环境变量覆盖

支持通过环境变量覆盖配置文件设置：

```bash
# 数据库连接
export WISCUDE_DATABASE_URL="postgresql://user:pass@host:port/db"

# 安全密钥
export WISCUDE_SECRET_KEY="your-secret-key"

# 服务器配置
export WISCUDE_HOST="0.0.0.0"
export WISCUDE_PORT="8000"

# 调试模式
export WISCUDE_DEBUG="true"
```

## 🐛 故障排除指南

### 🔍 诊断工具

使用内置的诊断工具快速定位问题：

```bash
# 🔍 环境检查
python start_manager.py --check

# 🔍 服务健康检查
curl http://localhost:8000/api/health/detailed

# 🔍 数据库连接测试
curl http://localhost:8000/api/health/database
```

### ❗ 常见问题解决

#### 1. 🔌 数据库连接问题

**症状：** 后端启动失败，提示数据库连接错误

**解决方案：**
```bash
# 检查PostgreSQL服务状态
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# 测试数据库连接
psql -h localhost -U postgres -d wiscude_admin

# 检查配置文件
cat config/database.yaml
```

**常见原因：**
- PostgreSQL服务未启动
- 数据库不存在或用户权限不足
- 连接参数配置错误

#### 2. 📱 Android应用连接问题

**症状：** Android应用无法连接后端服务

**解决方案：**
```bash
# 检查后端服务状态
curl http://localhost:8000/api/health

# 查看网络配置
adb logcat | grep NetworkConfig

# 使用网络设置界面重新配置服务器地址
```

**常见原因：**
- 后端服务未启动
- 网络地址配置错误
- 防火墙阻止连接
- 模拟器网络配置问题

#### 3. 🎨 前端启动问题

**症状：** 前端服务启动失败或页面无法访问

**解决方案：**
```bash
# 检查Node.js版本
node --version  # 需要 16.0+

# 清理并重新安装依赖
cd frontend
rm -rf node_modules package-lock.json
npm install

# 检查端口占用
netstat -an | grep 5173  # Linux/macOS
netstat -an | findstr 5173  # Windows
```

#### 4. 🔄 服务启动顺序问题

**症状：** 前端启动时后端尚未就绪

**解决方案：**
- 使用智能启动管理器：`python start_manager.py --quick-start`
- 手动启动时先启动后端，等待就绪后再启动前端
- 检查健康检查端点：`curl http://localhost:8000/api/health/ready`

#### 5. 📦 依赖安装问题

**症状：** pip或npm安装依赖失败

**解决方案：**
```bash
# Python依赖问题
pip install --upgrade pip
pip install -e . --force-reinstall

# Node.js依赖问题
npm cache clean --force
npm install --legacy-peer-deps

# 网络问题使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -e .
npm install --registry https://registry.npmmirror.com
```

### 📋 日志分析

系统日志位置：

| 组件 | 日志位置 | 说明 |
|------|----------|------|
| 后端服务 | `logs/wiscude-admin.log` | 应用运行日志 |
| 启动管理器 | `logs/startup.log` | 启动过程日志 |
| 数据库 | PostgreSQL日志目录 | 数据库操作日志 |
| Android应用 | `adb logcat` | 应用运行日志 |

### 🆘 获取帮助

如果问题仍未解决，请：

1. 📋 收集相关日志文件
2. 🔍 检查系统环境信息
3. 📝 描述具体的错误现象和复现步骤
4. 💬 在项目Issues中提交问题报告

## 🚀 部署指南

### 🔧 开发环境部署

```bash
# 使用智能启动管理器
python start_manager.py --quick-start

# 或使用传统脚本
python start_manager.py  # 跨平台启动管理器
```

### 🌐 生产环境部署

```bash
# 1. 设置生产环境
export WISCUDE_ENV=production

# 2. 安装生产依赖
cd backend && pip install -e .[production]

# 3. 使用Gunicorn启动后端
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app

# 4. 构建前端
cd frontend && npm run build

# 5. 使用Nginx部署前端静态文件
```

### 🐳 容器化部署（可选）

虽然本项目专注于原生部署，但您也可以创建Docker配置：

```dockerfile
# 后端Dockerfile示例
FROM python:3.11-slim
WORKDIR /app
COPY backend/ .
RUN pip install -e .
CMD ["python", "main.py"]
```

## 🔄 更新日志

### v1.0.0 (当前版本)

#### 🆕 新增功能
- ✨ 智能启动管理器 - 一键启动、服务监控
- 🔍 健康检查API - 实时监控服务状态
- 🌐 动态服务发现 - Android应用自动发现后端
- 📋 统一配置管理 - YAML配置文件支持
- 🔄 增强数据库管理器 - 连接池、重试机制

#### 🔧 优化改进
- ⚡ 启动速度提升 60%
- 🔗 数据库连接稳定性提升
- 📱 Android网络配置灵活性增强
- 🛠️ 错误处理和恢复机制完善
- 📖 文档完整性和可读性提升

#### 🐛 问题修复
- 修复启动顺序依赖问题
- 解决硬编码网络配置问题
- 优化依赖管理冲突
- 改进错误日志输出

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. 🍴 Fork 项目
2. 🌿 创建功能分支：`git checkout -b feature/amazing-feature`
3. 💾 提交更改：`git commit -m 'Add amazing feature'`
4. 📤 推送分支：`git push origin feature/amazing-feature`
5. 🔀 创建 Pull Request

### 📋 开发规范

- 🐍 Python代码遵循 PEP 8 规范
- 🎨 前端代码使用 ESLint + Prettier
- 📝 提交信息使用约定式提交格式
- 🧪 新功能需要包含相应测试
- 📖 重要更改需要更新文档

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目的支持：

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Element Plus](https://element-plus.org/) - Vue 3 UI组件库
- [SQLAlchemy](https://www.sqlalchemy.org/) - Python SQL工具包
- [PostgreSQL](https://www.postgresql.org/) - 强大的开源数据库

---

<div align="center">

**🎓 WisCude 后台管理系统**

*让教育数据管理更智能、更高效！*

[![GitHub stars](https://img.shields.io/github/stars/your-repo/wiscude?style=social)](https://github.com/your-repo/wiscude)
[![GitHub forks](https://img.shields.io/github/forks/your-repo/wiscude?style=social)](https://github.com/your-repo/wiscude)
[![GitHub issues](https://img.shields.io/github/issues/your-repo/wiscude)](https://github.com/your-repo/wiscude/issues)

</div>
