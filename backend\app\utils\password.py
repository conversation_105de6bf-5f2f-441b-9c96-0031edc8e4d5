"""
密码安全验证工具
"""
import re
import secrets
import string
from typing import List, Tuple
from ..core.config import settings


class PasswordValidator:
    """密码验证器"""
    
    def __init__(self):
        self.min_length = settings.PASSWORD_MIN_LENGTH
        self.require_uppercase = settings.PASSWORD_REQUIRE_UPPERCASE
        self.require_lowercase = settings.PASSWORD_REQUIRE_LOWERCASE
        self.require_numbers = settings.PASSWORD_REQUIRE_NUMBERS
        self.require_special = settings.PASSWORD_REQUIRE_SPECIAL
    
    def validate(self, password: str) -> Tu<PERSON>[bool, List[str]]:
        """
        验证密码强度
        
        Args:
            password: 待验证的密码
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查长度
        if len(password) < self.min_length:
            errors.append(f"密码长度至少需要{self.min_length}个字符")
        
        # 检查大写字母
        if self.require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("密码必须包含至少一个大写字母")
        
        # 检查小写字母
        if self.require_lowercase and not re.search(r'[a-z]', password):
            errors.append("密码必须包含至少一个小写字母")
        
        # 检查数字
        if self.require_numbers and not re.search(r'\d', password):
            errors.append("密码必须包含至少一个数字")
        
        # 检查特殊字符
        if self.require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("密码必须包含至少一个特殊字符")
        
        # 检查常见弱密码
        weak_passwords = [
            "password", "123456", "admin", "root", "user",
            "qwerty", "abc123", "password123", "admin123"
        ]
        if password.lower() in weak_passwords:
            errors.append("密码过于简单，请使用更复杂的密码")
        
        return len(errors) == 0, errors
    
    def generate_secure_password(self, length: int = 12) -> str:
        """
        生成安全密码
        
        Args:
            length: 密码长度
            
        Returns:
            str: 生成的安全密码
        """
        if length < self.min_length:
            length = self.min_length
        
        # 确保包含所有必需的字符类型
        password_chars = []
        
        if self.require_uppercase:
            password_chars.append(secrets.choice(string.ascii_uppercase))
        if self.require_lowercase:
            password_chars.append(secrets.choice(string.ascii_lowercase))
        if self.require_numbers:
            password_chars.append(secrets.choice(string.digits))
        if self.require_special:
            password_chars.append(secrets.choice("!@#$%^&*(),.?\":{}|<>"))
        
        # 填充剩余长度
        all_chars = string.ascii_letters + string.digits + "!@#$%^&*(),.?\":{}|<>"
        for _ in range(length - len(password_chars)):
            password_chars.append(secrets.choice(all_chars))
        
        # 随机打乱
        secrets.SystemRandom().shuffle(password_chars)
        
        return ''.join(password_chars)


def generate_secret_key(length: int = 64) -> str:
    """
    生成安全的密钥
    
    Args:
        length: 密钥长度
        
    Returns:
        str: 生成的密钥
    """
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*()-_=+[]{}|;:,.<>?"
    return ''.join(secrets.choice(alphabet) for _ in range(length))


# 全局密码验证器实例
password_validator = PasswordValidator()
