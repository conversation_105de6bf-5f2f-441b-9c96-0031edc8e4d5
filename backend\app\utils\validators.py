"""
输入验证工具
"""
import re
import ipaddress
from typing import Optional, Union
from urllib.parse import urlparse
from email_validator import validate_email, EmailNotValidError


class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_username(username: str) -> tuple[bool, Optional[str]]:
        """
        验证用户名
        
        Args:
            username: 用户名
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        if not username:
            return False, "用户名不能为空"
        
        if len(username) < 3:
            return False, "用户名长度至少3个字符"
        
        if len(username) > 50:
            return False, "用户名长度不能超过50个字符"
        
        # 只允许字母、数字、下划线、连字符
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            return False, "用户名只能包含字母、数字、下划线和连字符"
        
        # 不能以数字开头
        if username[0].isdigit():
            return False, "用户名不能以数字开头"
        
        return True, None
    
    @staticmethod
    def validate_email_address(email: str) -> tuple[bool, Optional[str]]:
        """
        验证邮箱地址
        
        Args:
            email: 邮箱地址
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        if not email:
            return False, "邮箱地址不能为空"
        
        try:
            # 使用email-validator库进行验证
            validate_email(email)
            return True, None
        except EmailNotValidError as e:
            return False, f"邮箱地址格式无效: {str(e)}"
    
    @staticmethod
    def validate_phone(phone: str) -> tuple[bool, Optional[str]]:
        """
        验证手机号码
        
        Args:
            phone: 手机号码
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        if not phone:
            return True, None  # 手机号可以为空
        
        # 中国手机号格式验证
        if not re.match(r'^1[3-9]\d{9}$', phone):
            return False, "手机号码格式无效"
        
        return True, None
    
    @staticmethod
    def validate_ip_address(ip: str) -> tuple[bool, Optional[str]]:
        """
        验证IP地址
        
        Args:
            ip: IP地址
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        if not ip:
            return False, "IP地址不能为空"
        
        try:
            ipaddress.ip_address(ip)
            return True, None
        except ValueError:
            return False, "IP地址格式无效"
    
    @staticmethod
    def validate_url(url: str) -> tuple[bool, Optional[str]]:
        """
        验证URL
        
        Args:
            url: URL地址
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        if not url:
            return False, "URL不能为空"
        
        try:
            result = urlparse(url)
            if not all([result.scheme, result.netloc]):
                return False, "URL格式无效"
            
            if result.scheme not in ['http', 'https']:
                return False, "URL协议必须是http或https"
            
            return True, None
        except Exception:
            return False, "URL格式无效"
    
    @staticmethod
    def sanitize_string(text: str, max_length: int = 1000) -> str:
        """
        清理字符串，防止XSS攻击
        
        Args:
            text: 待清理的文本
            max_length: 最大长度
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除JavaScript代码
        text = re.sub(r'javascript:', '', text, flags=re.IGNORECASE)
        
        # 移除危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        # 限制长度
        if len(text) > max_length:
            text = text[:max_length]
        
        return text.strip()
    
    @staticmethod
    def validate_json_size(data: Union[dict, list], max_size_mb: float = 10) -> tuple[bool, Optional[str]]:
        """
        验证JSON数据大小
        
        Args:
            data: JSON数据
            max_size_mb: 最大大小（MB）
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        import json
        import sys
        
        try:
            json_str = json.dumps(data)
            size_mb = sys.getsizeof(json_str) / (1024 * 1024)
            
            if size_mb > max_size_mb:
                return False, f"数据大小超过限制（{max_size_mb}MB）"
            
            return True, None
        except Exception as e:
            return False, f"数据格式错误: {str(e)}"


# 全局验证器实例
input_validator = InputValidator()
