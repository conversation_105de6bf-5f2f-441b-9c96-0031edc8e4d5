<template>
  <el-card class="settings-card">
    <template #header>
      <div class="card-header">
        <el-icon><Monitor /></el-icon>
        <span>界面设置</span>
        <el-button size="small" @click="previewChanges">
          <el-icon><View /></el-icon>
          预览效果
        </el-button>
      </div>
    </template>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      class="settings-form"
    >
      <!-- 主题设置 -->
      <div class="settings-section">
        <h4 class="section-title">主题设置</h4>
        
        <el-form-item label="界面主题" prop="ui_theme">
          <el-radio-group v-model="formData.ui_theme" class="theme-options">
            <el-radio-button
              v-for="theme in themeOptions"
              :key="theme.value"
              :label="theme.value"
            >
              <div class="theme-option">
                <div class="theme-preview" :class="theme.value"></div>
                <span>{{ theme.label }}</span>
              </div>
            </el-radio-button>
          </el-radio-group>
          <div class="field-description">
            选择系统界面的主题风格，自动切换会根据系统时间调整
          </div>
        </el-form-item>
        
        <el-form-item label="主题色彩" prop="ui_primary_color">
          <div class="color-picker-container">
            <el-color-picker
              v-model="formData.ui_primary_color"
              :predefine="predefineColors"
              show-alpha
            />
            <div class="color-presets">
              <div
                v-for="color in predefineColors"
                :key="color"
                class="color-preset"
                :style="{ backgroundColor: color }"
                @click="formData.ui_primary_color = color"
              ></div>
            </div>
          </div>
          <div class="field-description">
            自定义系统的主题颜色，影响按钮、链接等元素的颜色
          </div>
        </el-form-item>
      </div>
      
      <!-- 布局设置 -->
      <div class="settings-section">
        <h4 class="section-title">布局设置</h4>
        
        <el-form-item label="布局模式" prop="ui_layout_mode">
          <el-select
            v-model="formData.ui_layout_mode"
            placeholder="请选择布局模式"
            style="width: 100%"
          >
            <el-option
              v-for="layout in layoutOptions"
              :key="layout.value"
              :label="layout.label"
              :value="layout.value"
            >
              <div class="layout-option">
                <span>{{ layout.label }}</span>
                <span class="layout-description">{{ layout.description }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="field-description">
            选择系统的整体布局模式，影响导航菜单的位置和样式
          </div>
        </el-form-item>
        
        <el-form-item label="侧边栏设置">
          <div class="sidebar-settings">
            <el-checkbox
              v-model="formData.ui_sidebar_collapsed"
              label="默认收起侧边栏"
            />
          </div>
          <div class="field-description">
            系统启动时是否默认收起侧边栏，适合小屏幕设备
          </div>
        </el-form-item>
        
        <el-form-item label="导航设置">
          <div class="navigation-settings">
            <el-checkbox
              v-model="formData.ui_show_breadcrumb"
              label="显示面包屑导航"
            />
            <el-checkbox
              v-model="formData.ui_show_tags_view"
              label="显示标签页"
            />
          </div>
          <div class="field-description">
            选择要显示的导航辅助功能
          </div>
        </el-form-item>
      </div>
      
      <!-- 数据显示设置 -->
      <div class="settings-section">
        <h4 class="section-title">数据显示设置</h4>
        
        <el-form-item label="默认分页大小" prop="default_page_size">
          <el-input-number
            v-model="formData.default_page_size"
            :min="10"
            :max="100"
            :step="10"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">条/页</span>
          <div class="field-description">
            列表页面的默认每页显示条数
          </div>
        </el-form-item>
        
        <el-form-item label="最大分页大小" prop="max_page_size">
          <el-input-number
            v-model="formData.max_page_size"
            :min="50"
            :max="500"
            :step="50"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">条/页</span>
          <div class="field-description">
            列表页面允许的最大每页显示条数
          </div>
        </el-form-item>
      </div>
      
      <!-- 首页设置 -->
      <div class="settings-section">
        <h4 class="section-title">首页设置</h4>
        
        <el-form-item label="显示组件" prop="dashboard_widgets">
          <div class="widget-selection">
            <el-checkbox-group v-model="formData.dashboard_widgets">
              <div class="widget-grid">
                <el-checkbox
                  v-for="widget in widgetOptions"
                  :key="widget.value"
                  :label="widget.value"
                  class="widget-checkbox"
                >
                  <div class="widget-option">
                    <el-icon><component :is="widget.icon" /></el-icon>
                    <div class="widget-info">
                      <div class="widget-name">{{ widget.label }}</div>
                      <div class="widget-description">{{ widget.description }}</div>
                    </div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
          <div class="field-description">
            选择在首页显示的功能组件，可以拖拽调整顺序
          </div>
        </el-form-item>
      </div>
      
      <!-- 预览区域 -->
      <div class="settings-section">
        <h4 class="section-title">效果预览</h4>
        
        <div class="preview-container">
          <div class="preview-mockup" :class="formData.ui_theme">
            <div class="mockup-header" :style="{ backgroundColor: formData.ui_primary_color }">
              <div class="mockup-logo">WisCude</div>
              <div class="mockup-nav">
                <span v-if="formData.ui_show_breadcrumb" class="breadcrumb">首页 / 设置</span>
              </div>
            </div>
            <div class="mockup-body">
              <div
                v-if="!formData.ui_sidebar_collapsed"
                class="mockup-sidebar"
                :class="formData.ui_layout_mode"
              >
                <div class="sidebar-menu">菜单</div>
              </div>
              <div class="mockup-content">
                <div v-if="formData.ui_show_tags_view" class="mockup-tags">
                  <span class="tag active">设置</span>
                  <span class="tag">用户</span>
                </div>
                <div class="mockup-main">内容区域</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <el-form-item class="form-actions">
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存设置
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button
          type="success"
          @click="applyChanges"
        >
          立即应用
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Monitor, View, User, TrendCharts, Setting, Document, Timer, Operation } from '@element-plus/icons-vue'
import type { SystemSettings } from '@/types/settings'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  settings: SystemSettings
}

interface Emits {
  (e: 'update', settings: Partial<SystemSettings>): void
  (e: 'save', settings: Partial<SystemSettings>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const saving = ref(false)

const formData = reactive<Partial<SystemSettings>>({
  ui_theme: 'light',
  ui_primary_color: '#409EFF',
  ui_layout_mode: 'classic',
  ui_sidebar_collapsed: false,
  ui_show_breadcrumb: true,
  ui_show_tags_view: true,
  default_page_size: 20,
  max_page_size: 100,
  dashboard_widgets: ['user-stats', 'study-stats', 'system-status', 'recent-activities']
})

const themeOptions = [
  { value: 'light', label: '浅色主题' },
  { value: 'dark', label: '深色主题' },
  { value: 'auto', label: '自动切换' }
]

const layoutOptions = [
  { value: 'classic', label: '经典布局', description: '左侧菜单 + 顶部导航' },
  { value: 'top-nav', label: '顶部导航', description: '顶部水平菜单' },
  { value: 'mix', label: '混合布局', description: '顶部菜单 + 左侧子菜单' }
]

const widgetOptions = [
  { value: 'user-stats', label: '用户统计', description: '显示用户数量和增长趋势', icon: 'User' },
  { value: 'study-stats', label: '学习数据', description: '显示学习时长和会话统计', icon: 'TrendCharts' },
  { value: 'system-status', label: '系统状态', description: '显示系统运行状态', icon: 'Setting' },
  { value: 'recent-activities', label: '最近活动', description: '显示最近的系统活动', icon: 'Document' },
  { value: 'charts', label: '数据图表', description: '显示各类数据图表', icon: 'TrendCharts' },
  { value: 'quick-actions', label: '快速操作', description: '显示常用操作按钮', icon: 'Operation' }
]

const predefineColors = [
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399',
  '#1890ff',
  '#52c41a',
  '#faad14',
  '#f5222d',
  '#722ed1'
]

const formRules: FormRules = {
  ui_theme: [
    { required: true, message: '请选择界面主题', trigger: 'change' }
  ],
  ui_layout_mode: [
    { required: true, message: '请选择布局模式', trigger: 'change' }
  ],
  default_page_size: [
    { required: true, message: '请设置默认分页大小', trigger: 'blur' }
  ],
  max_page_size: [
    { required: true, message: '请设置最大分页大小', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  Object.assign(formData, newSettings)
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newData) => {
  emit('update', newData)
}, { deep: true })

const previewChanges = () => {
  ElMessage.info('预览功能：界面设置将在保存后生效')
}

const applyChanges = () => {
  ElMessageBox.confirm(
    '立即应用界面设置会刷新页面，确定继续吗？',
    '确认应用',
    { type: 'info' }
  ).then(() => {
    // 应用设置到当前页面
    document.documentElement.setAttribute('data-theme', formData.ui_theme || 'light')
    document.documentElement.style.setProperty('--el-color-primary', formData.ui_primary_color)
    
    ElMessage.success('界面设置已应用')
  }).catch(() => {
    // 用户取消
  })
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 验证分页设置
    if (formData.default_page_size! > formData.max_page_size!) {
      ElMessage.error('默认分页大小不能大于最大分页大小')
      return
    }
    
    saving.value = true
    emit('save', formData)
    
    setTimeout(() => {
      saving.value = false
      ElMessage.success('界面设置保存成功，部分设置需要刷新页面后生效')
    }, 1000)
    
  } catch (error) {
    ElMessage.error('表单验证失败，请检查输入')
  }
}

const handleReset = () => {
  ElMessageBox.confirm('确定要重置界面设置吗？', '确认重置', {
    type: 'warning'
  }).then(() => {
    formRef.value?.resetFields()
    Object.assign(formData, props.settings)
    ElMessage.success('界面设置已重置')
  }).catch(() => {
    // 用户取消
  })
}

onMounted(() => {
  Object.assign(formData, props.settings)
})
</script>

<style lang="scss" scoped>
.settings-card {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .settings-form {
    .settings-section {
      margin-bottom: 32px;
      
      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }
    
    .field-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
      line-height: 1.4;
    }
    
    .unit-text {
      margin-left: 8px;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
    
    .theme-options {
      display: flex;
      gap: 16px;
      
      .theme-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        
        .theme-preview {
          width: 40px;
          height: 30px;
          border-radius: 4px;
          border: 1px solid var(--el-border-color);
          
          &.light {
            background: linear-gradient(to bottom, #fff 50%, #f5f5f5 50%);
          }
          
          &.dark {
            background: linear-gradient(to bottom, #2c2c2c 50%, #1a1a1a 50%);
          }
          
          &.auto {
            background: linear-gradient(45deg, #fff 50%, #2c2c2c 50%);
          }
        }
      }
    }
    
    .color-picker-container {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .color-presets {
        display: flex;
        gap: 8px;
        
        .color-preset {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          cursor: pointer;
          border: 1px solid var(--el-border-color);
          
          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }
    
    .layout-option {
      display: flex;
      justify-content: space-between;
      width: 100%;
      
      .layout-description {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
    
    .sidebar-settings,
    .navigation-settings {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .widget-selection {
      .widget-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
        
        .widget-checkbox {
          margin: 0;
          
          .widget-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 6px;
            transition: all 0.3s ease;
            
            &:hover {
              border-color: var(--el-color-primary);
              background: var(--el-color-primary-light-9);
            }
            
            .widget-info {
              flex: 1;
              
              .widget-name {
                font-weight: 500;
                margin-bottom: 4px;
              }
              
              .widget-description {
                font-size: 12px;
                color: var(--el-text-color-regular);
              }
            }
          }
        }
      }
    }
    
    .preview-container {
      .preview-mockup {
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        overflow: hidden;
        background: #fff;
        
        &.dark {
          background: #2c2c2c;
          color: #fff;
        }
        
        .mockup-header {
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;
          color: white;
          
          .mockup-logo {
            font-weight: bold;
            font-size: 18px;
          }
          
          .breadcrumb {
            font-size: 14px;
            opacity: 0.8;
          }
        }
        
        .mockup-body {
          display: flex;
          height: 200px;
          
          .mockup-sidebar {
            width: 200px;
            background: var(--el-bg-color-page);
            border-right: 1px solid var(--el-border-color);
            padding: 16px;
            
            &.top-nav {
              display: none;
            }
            
            .sidebar-menu {
              font-size: 14px;
              color: var(--el-text-color-regular);
            }
          }
          
          .mockup-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            
            .mockup-tags {
              height: 40px;
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 0 16px;
              border-bottom: 1px solid var(--el-border-color);
              
              .tag {
                padding: 4px 12px;
                border-radius: 4px;
                font-size: 12px;
                background: var(--el-bg-color-page);
                
                &.active {
                  background: var(--el-color-primary-light-9);
                  color: var(--el-color-primary);
                }
              }
            }
            
            .mockup-main {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: center;
              color: var(--el-text-color-regular);
            }
          }
        }
      }
    }
    
    .form-actions {
      margin-top: 32px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
</style>
