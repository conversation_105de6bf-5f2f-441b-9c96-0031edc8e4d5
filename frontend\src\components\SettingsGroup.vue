<template>
  <div class="settings-group">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="150px"
      class="settings-form"
    >
      <div class="group-description" v-if="group.description">
        <el-alert
          :title="group.description"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div class="form-section" v-for="field in group.fields" :key="field.key">
        <el-form-item 
          :label="field.label" 
          :prop="field.key"
          :required="field.required"
        >
          <!-- 输入框 -->
          <el-input
            v-if="field.type === 'input'"
            v-model="formData[field.key]"
            :placeholder="field.placeholder"
            :show-password="field.sensitive"
            clearable
            @blur="handleFieldChange(field.key)"
          >
            <template #append v-if="field.testable">
              <el-button 
                :icon="Connection" 
                @click="handleTest(field.key)"
                :loading="testLoading"
              >
                测试
              </el-button>
            </template>
          </el-input>

          <!-- 密码输入框 -->
          <el-input
            v-else-if="field.type === 'password'"
            v-model="formData[field.key]"
            type="password"
            :placeholder="field.placeholder"
            show-password
            clearable
            @blur="handleFieldChange(field.key)"
          />

          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.key]"
            :min="field.min"
            :max="field.max"
            :step="field.step || 1"
            :placeholder="field.placeholder"
            style="width: 100%"
            @blur="handleFieldChange(field.key)"
          />

          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="formData[field.key]"
            @change="handleFieldChange(field.key)"
          />

          <!-- 选择器 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.key]"
            :placeholder="field.placeholder"
            style="width: 100%"
            @change="handleFieldChange(field.key)"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <!-- 文本域 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.key]"
            type="textarea"
            :rows="field.rows || 3"
            :placeholder="field.placeholder"
            @blur="handleFieldChange(field.key)"
          />

          <!-- 字段描述 -->
          <div v-if="field.description" class="field-description">
            <el-text type="info" size="small">{{ field.description }}</el-text>
          </div>
        </el-form-item>
      </div>

      <!-- 操作按钮 -->
      <el-form-item class="form-actions">
        <el-button 
          type="primary" 
          :loading="loading"
          @click="handleSave"
        >
          保存设置
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button 
          v-if="hasTestableFields" 
          type="success"
          :loading="testLoading"
          @click="handleTestAll"
        >
          测试所有配置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { SettingsGroup, SystemSettings } from '@/types/settings'

interface Props {
  group: SettingsGroup
  settings: SystemSettings
  loading?: boolean
}

interface Emits {
  (e: 'update', settings: Partial<SystemSettings>): void
  (e: 'test', testType: string, config: Record<string, any>): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const testLoading = ref(false)
const formData = reactive<Record<string, any>>({})
const changedFields = new Set<string>()

// 计算属性
const hasTestableFields = computed(() => 
  props.group.fields.some(field => field.testable)
)

const formRules = computed<FormRules>(() => {
  const rules: FormRules = {}
  
  props.group.fields.forEach(field => {
    const fieldRules: any[] = []
    
    if (field.required) {
      fieldRules.push({
        required: true,
        message: `请输入${field.label}`,
        trigger: ['blur', 'change']
      })
    }
    
    if (field.type === 'input' || field.type === 'password') {
      if (field.min !== undefined) {
        fieldRules.push({
          min: field.min,
          message: `${field.label}长度不能少于${field.min}个字符`,
          trigger: 'blur'
        })
      }
      if (field.max !== undefined) {
        fieldRules.push({
          max: field.max,
          message: `${field.label}长度不能超过${field.max}个字符`,
          trigger: 'blur'
        })
      }
    }
    
    if (field.type === 'number') {
      fieldRules.push({
        type: 'number',
        message: `${field.label}必须是数字`,
        trigger: ['blur', 'change']
      })
    }
    
    // 特殊验证规则
    if (field.key === 'database_url') {
      fieldRules.push({
        pattern: /^(postgresql|sqlite):\/\/.+/,
        message: '数据库URL格式不正确',
        trigger: 'blur'
      })
    }
    
    if (field.key === 'email_from' || field.key === 'smtp_user') {
      fieldRules.push({
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: '邮箱格式不正确',
        trigger: 'blur'
      })
    }
    
    if (fieldRules.length > 0) {
      rules[field.key] = fieldRules
    }
  })
  
  return rules
})

// 监听设置变化
watch(() => props.settings, (newSettings) => {
  updateFormData(newSettings)
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  updateFormData(props.settings)
})

// 方法
const updateFormData = (settings: SystemSettings) => {
  props.group.fields.forEach(field => {
    if (settings[field.key as keyof SystemSettings] !== undefined) {
      formData[field.key] = settings[field.key as keyof SystemSettings]
    }
  })
}

const handleFieldChange = (fieldKey: string) => {
  changedFields.add(fieldKey)
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    if (changedFields.size === 0) {
      ElMessage.info('没有需要保存的更改')
      return
    }
    
    // 只提交已更改的字段
    const updatedSettings: Partial<SystemSettings> = {}
    changedFields.forEach(fieldKey => {
      updatedSettings[fieldKey as keyof SystemSettings] = formData[fieldKey]
    })
    
    emit('update', updatedSettings)
    changedFields.clear()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleReset = () => {
  updateFormData(props.settings)
  changedFields.clear()
  formRef.value?.clearValidate()
  ElMessage.success('已重置为当前保存的设置')
}

const handleTest = async (fieldKey: string) => {
  const field = props.group.fields.find(f => f.key === fieldKey)
  if (!field || !field.testable) return
  
  testLoading.value = true
  try {
    let testType = ''
    let config: Record<string, any> = {}
    
    if (props.group.key === 'database') {
      testType = 'database'
      config = {
        database_url: formData.database_url
      }
    } else if (props.group.key === 'email') {
      testType = 'email'
      config = {
        smtp_host: formData.smtp_host,
        smtp_port: formData.smtp_port,
        smtp_user: formData.smtp_user,
        smtp_password: formData.smtp_password,
        smtp_tls: formData.smtp_tls,
        email_from: formData.email_from
      }
    }
    
    if (testType) {
      emit('test', testType, config)
    }
  } finally {
    testLoading.value = false
  }
}

const handleTestAll = async () => {
  if (props.group.key === 'database') {
    await handleTest('database_url')
  } else if (props.group.key === 'email') {
    await handleTest('smtp_host')
  }
}
</script>

<style lang="scss" scoped>
.settings-group {
  .group-description {
    margin-bottom: 20px;
  }
  
  .settings-form {
    max-width: 800px;
    
    .form-section {
      margin-bottom: 20px;
      
      .field-description {
        margin-top: 5px;
        font-size: 12px;
        color: #909399;
      }
    }
    
    .form-actions {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
      
      .el-button {
        margin-right: 10px;
      }
    }
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
}
</style>
