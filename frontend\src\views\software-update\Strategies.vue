<template>
  <div class="strategies-management">
    <div class="page-header">
      <h1>推送策略管理</h1>
      <p>配置软件更新推送策略、灰度发布和用户分组</p>
    </div>

    <!-- 策略列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>推送策略配置</span>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建策略
          </el-button>
        </div>
      </template>

      <el-table :data="strategies" v-loading="loading" stripe style="width: 100%">
        <el-table-column label="策略名称" prop="name" min-width="200" />
        <el-table-column label="描述" prop="description" min-width="300" />
        <el-table-column label="目标用户" width="120">
          <template #default="{ row }">
            <span>{{ row.target_percentage }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="推送间隔" width="120">
          <template #default="{ row }">
            <span>{{ row.interval }}小时</span>
          </template>
        </el-table-column>
        <el-table-column label="强制更新" width="100">
          <template #default="{ row }">
            <el-tag :type="row.force_update ? 'danger' : 'success'">
              {{ row.force_update ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="editStrategy(row)">编辑</el-button>
            <el-button type="text" size="small" @click="toggleStatus(row)">
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button type="text" size="small" class="danger" @click="deleteStrategy(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑策略对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingStrategy ? '编辑策略' : '新建策略'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="strategyForm" :rules="strategyRules" ref="strategyFormRef" label-width="100px">
        <el-form-item label="策略名称" prop="name">
          <el-input v-model="strategyForm.name" placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="策略描述" prop="description">
          <el-input v-model="strategyForm.description" type="textarea" rows="3" placeholder="请输入策略描述" />
        </el-form-item>
        <el-form-item label="目标用户比例" prop="target_percentage">
          <el-slider v-model="strategyForm.target_percentage" :min="1" :max="100" show-input />
        </el-form-item>
        <el-form-item label="推送间隔" prop="interval">
          <el-input-number v-model="strategyForm.interval" :min="0" :max="168" placeholder="小时" />
        </el-form-item>
        <el-form-item label="强制更新">
          <el-switch v-model="strategyForm.force_update" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveStrategy" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const editingStrategy = ref(null)

// 策略列表
const strategies = ref([
  {
    id: '1',
    name: '渐进式推送',
    description: '分批次逐步推送给用户，降低风险',
    target_percentage: 20,
    interval: 24,
    force_update: false,
    status: 'active'
  },
  {
    id: '2',
    name: '紧急修复推送',
    description: '立即推送给所有用户，用于紧急修复',
    target_percentage: 100,
    interval: 0,
    force_update: true,
    status: 'inactive'
  }
])

// 表单数据
const strategyForm = reactive({
  name: '',
  description: '',
  target_percentage: 20,
  interval: 24,
  force_update: false
})

// 表单验证规则
const strategyRules = {
  name: [
    { required: true, message: '请输入策略名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入策略描述', trigger: 'blur' }
  ]
}

// 方法
const editStrategy = (strategy) => {
  editingStrategy.value = strategy
  Object.assign(strategyForm, strategy)
  showCreateDialog.value = true
}

const resetForm = () => {
  editingStrategy.value = null
  Object.assign(strategyForm, {
    name: '',
    description: '',
    target_percentage: 20,
    interval: 24,
    force_update: false
  })
}

const saveStrategy = async () => {
  saving.value = true
  try {
    // TODO: 实现保存逻辑
    console.log('Saving strategy...', strategyForm)
    showCreateDialog.value = false
    ElMessage.success(editingStrategy.value ? '策略更新成功' : '策略创建成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleStatus = async (strategy) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling strategy status...', strategy)
}

const deleteStrategy = async (strategy) => {
  try {
    await ElMessageBox.confirm('确定要删除这个策略吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting strategy...', strategy)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  // TODO: 加载策略列表
})
</script>

<style scoped>
.strategies-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.danger {
  color: #f56c6c;
}
</style>
