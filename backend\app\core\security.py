"""
WisCude 后台管理系统 - 安全认证模块
"""
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union
from jose import jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from .config import settings
from .database import get_db
import logging

logger = logging.getLogger(__name__)

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 认证
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)

def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject), "type": "access"}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt

def create_refresh_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """创建刷新令牌"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            days=settings.REFRESH_TOKEN_EXPIRE_DAYS
        )
    
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt

def decode_token(token: str) -> Dict[str, Any]:
    """解码令牌"""
    try:
        logger.info(f"开始解码token: {token[:20]}...")
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        logger.info(f"Token解码成功: user_id={payload.get('sub')}, type={payload.get('type')}, exp={payload.get('exp')}")
        return payload
    except jwt.JWTError as e:
        logger.error(f"JWT解码失败: {e}, token: {token[:20]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
):
    """获取当前用户"""
    logger.info(f"获取当前用户，token: {token[:20]}...")
    try:
        payload = decode_token(token)
        user_id: str = payload.get("sub")
        token_type: str = payload.get("type")

        logger.info(f"Token解析结果: user_id={user_id}, token_type={token_type}")

        if user_id is None or token_type != "access":
            logger.error(f"Token验证失败: user_id={user_id}, token_type={token_type}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except jwt.JWTError as e:
        logger.error(f"JWT解析异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 从数据库查询用户
    from app.models.admin import AdminUser  # 避免循环导入
    logger.info(f"查询用户: user_id={user_id}")
    user = db.query(AdminUser).filter(AdminUser.id == user_id).first()
    if not user:
        logger.error(f"用户不存在: user_id={user_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
        )
    if not user.is_active:
        logger.error(f"用户已被禁用: user_id={user_id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="用户已被禁用"
        )

    logger.info(f"获取用户成功: username={user.username}")
    return user

async def get_current_active_superuser(
    current_user = Depends(get_current_user),
):
    """获取当前超级管理员"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="权限不足"
        )
    return current_user

class RoleChecker:
    """角色检查器"""
    
    def __init__(self, allowed_roles: list):
        self.allowed_roles = allowed_roles
    
    def __call__(self, user = Depends(get_current_user)):
        if user.role not in self.allowed_roles:
            logger.warning(
                f"用户 {user.username} 尝试访问需要角色 {self.allowed_roles} 的资源"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="权限不足"
            )
        return user

# 预定义角色检查器
allow_admin_only = RoleChecker(["admin", "superadmin"])
allow_superadmin_only = RoleChecker(["superadmin"])

def get_current_user_from_token(token: str) -> Optional[Dict[str, Any]]:
    """从token获取当前用户信息（用于WebSocket等场景）"""
    try:
        payload = decode_token(token)
        user_id = payload.get("sub")
        if user_id is None:
            return None

        # 这里应该从数据库获取用户信息
        # 为了简化，返回基本信息
        return {
            "id": user_id,
            "username": payload.get("username", "unknown"),
            "role": payload.get("role", "user")
        }
    except Exception as e:
        logger.error(f"从token获取用户信息失败: {e}")
        return None
