<template>
  <div class="english-practice-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>英语练习中心</h1>
          <p>全方位提升英语听说读写能力，个性化学习路径</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="startPractice">
            <el-icon><VideoPlay /></el-icon>
            开始练习
          </el-button>
          <el-button @click="viewProgress">
            <el-icon><TrendCharts /></el-icon>
            学习进度
          </el-button>
          <el-button @click="showPlanDialog = true">
            <el-icon><Calendar /></el-icon>
            制定计划
          </el-button>
        </div>
      </div>
    </div>

    <!-- 学习进度概览 -->
    <div class="progress-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card listening">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><Headset /></el-icon>
              </div>
              <div class="progress-info">
                <div class="skill-name">听力练习</div>
                <div class="progress-bar">
                  <el-progress
                    :percentage="skillProgress.listening"
                    :stroke-width="8"
                    :show-text="false"
                    color="#4f46e5"
                  />
                </div>
                <div class="progress-stats">
                  <span class="level">Level {{ Math.floor(skillProgress.listening / 20) + 1 }}</span>
                  <span class="percentage">{{ skillProgress.listening }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card speaking">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><Microphone /></el-icon>
              </div>
              <div class="progress-info">
                <div class="skill-name">口语练习</div>
                <div class="progress-bar">
                  <el-progress
                    :percentage="skillProgress.speaking"
                    :stroke-width="8"
                    :show-text="false"
                    color="#10b981"
                  />
                </div>
                <div class="progress-stats">
                  <span class="level">Level {{ Math.floor(skillProgress.speaking / 20) + 1 }}</span>
                  <span class="percentage">{{ skillProgress.speaking }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card reading">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <div class="progress-info">
                <div class="skill-name">阅读理解</div>
                <div class="progress-bar">
                  <el-progress
                    :percentage="skillProgress.reading"
                    :stroke-width="8"
                    :show-text="false"
                    color="#f59e0b"
                  />
                </div>
                <div class="progress-stats">
                  <span class="level">Level {{ Math.floor(skillProgress.reading / 20) + 1 }}</span>
                  <span class="percentage">{{ skillProgress.reading }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card writing">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><EditPen /></el-icon>
              </div>
              <div class="progress-info">
                <div class="skill-name">写作训练</div>
                <div class="progress-bar">
                  <el-progress
                    :percentage="skillProgress.writing"
                    :stroke-width="8"
                    :show-text="false"
                    color="#ef4444"
                  />
                </div>
                <div class="progress-stats">
                  <span class="level">Level {{ Math.floor(skillProgress.writing / 20) + 1 }}</span>
                  <span class="percentage">{{ skillProgress.writing }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 练习推荐 -->
    <div class="practice-recommendations">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>今日推荐练习</span>
            <el-button text @click="refreshRecommendations">
              <el-icon><Refresh /></el-icon>
              换一批
            </el-button>
          </div>
        </template>
        <div class="recommendations-grid">
          <div
            v-for="recommendation in recommendations"
            :key="recommendation.id"
            class="recommendation-card"
            @click="startRecommendedPractice(recommendation)"
          >
            <div class="recommendation-cover">
              <img :src="recommendation.cover" :alt="recommendation.title" />
              <div class="cover-overlay">
                <div class="skill-badge">
                  <el-tag :type="getSkillTagType(recommendation.skill)" size="small">
                    {{ getSkillName(recommendation.skill) }}
                  </el-tag>
                </div>
                <div class="difficulty-badge">
                  <el-rate
                    v-model="recommendation.difficulty"
                    disabled
                    :max="3"
                    size="small"
                  />
                </div>
              </div>
            </div>
            <div class="recommendation-info">
              <h4 class="recommendation-title">{{ recommendation.title }}</h4>
              <p class="recommendation-description">{{ recommendation.description }}</p>
              <div class="recommendation-meta">
                <span class="duration">
                  <el-icon><Clock /></el-icon>
                  {{ recommendation.duration }}分钟
                </span>
                <span class="points">
                  <el-icon><Star /></el-icon>
                  +{{ recommendation.points }}积分
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 学习统计 -->
    <div class="learning-stats">
      <el-row :gutter="24">
        <!-- 学习时长统计 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>学习时长统计</span>
                <el-select v-model="studyPeriod" size="small" @change="updateStudyStats">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <BarChart
                :data="studyTimeData"
                :x-axis-data="studyTimeXAxisData"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 技能提升雷达图 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>技能提升分析</span>
            </template>
            <div class="chart-container">
              <RadarChart
                :data="skillRadarData"
                :options="skillRadarOptions"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 成绩趋势 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <span>成绩趋势分析</span>
            </template>
            <div class="chart-container">
              <LineChart
                :data="scoreTrendData"
                :x-axis-data="scoreTrendXAxisData"
                height="350px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 学习目标 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="section-header">
                <span>学习目标</span>
                <el-button text @click="editGoals">编辑</el-button>
              </div>
            </template>
            <div class="goals-list">
              <div
                v-for="goal in learningGoals"
                :key="goal.id"
                class="goal-item"
              >
                <div class="goal-info">
                  <div class="goal-title">{{ goal.title }}</div>
                  <div class="goal-progress">
                    <el-progress
                      :percentage="goal.progress"
                      :stroke-width="6"
                      :show-text="false"
                    />
                    <span class="progress-text">{{ goal.progress }}%</span>
                  </div>
                  <div class="goal-deadline">目标时间：{{ formatDate(goal.deadline) }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  VideoPlay, TrendCharts, Calendar, Headset, Microphone, Reading,
  EditPen, Refresh, Clock, Star
} from '@element-plus/icons-vue'
import { BarChart, LineChart, RadarChart } from '@/components/charts'

// 路由
const router = useRouter()

// 响应式数据
const showPlanDialog = ref(false)
const studyPeriod = ref('30d')

// 技能进度数据
const skillProgress = reactive({
  listening: 75,
  speaking: 68,
  reading: 82,
  writing: 59
})

// 练习推荐数据
const recommendations = ref([
  {
    id: '1',
    title: '商务英语对话练习',
    description: '提升职场英语交流能力，掌握商务场景常用表达',
    skill: 'speaking',
    difficulty: 2,
    duration: 25,
    points: 50,
    cover: '/images/business-english.jpg'
  },
  {
    id: '2',
    title: 'BBC新闻听力训练',
    description: '通过真实新闻材料提升听力理解能力',
    skill: 'listening',
    difficulty: 3,
    duration: 30,
    points: 60,
    cover: '/images/bbc-news.jpg'
  },
  {
    id: '3',
    title: '雅思写作Task2练习',
    description: '掌握议论文写作技巧，提升逻辑表达能力',
    skill: 'writing',
    difficulty: 3,
    duration: 45,
    points: 80,
    cover: '/images/ielts-writing.jpg'
  },
  {
    id: '4',
    title: '英美文学阅读理解',
    description: '通过经典文学作品提升阅读理解和文化素养',
    skill: 'reading',
    difficulty: 2,
    duration: 35,
    points: 70,
    cover: '/images/literature.jpg'
  }
])

// 学习时长统计数据
const studyTimeData = ref([
  {
    name: '听力练习',
    data: [] as number[],
    color: '#4f46e5'
  },
  {
    name: '口语练习',
    data: [] as number[],
    color: '#10b981'
  },
  {
    name: '阅读理解',
    data: [] as number[],
    color: '#f59e0b'
  },
  {
    name: '写作训练',
    data: [] as number[],
    color: '#ef4444'
  }
])

const studyTimeXAxisData = ref([] as string[])

const studyTimeOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    }
  },
  scales: {
    x: {
      stacked: true,
    },
    y: {
      stacked: true,
      title: {
        display: true,
        text: '学习时长 (分钟)'
      }
    }
  }
})

// 技能雷达图数据
const skillRadarData = ref({
  labels: ['听力', '口语', '阅读', '写作', '语法', '词汇'],
  datasets: [
    {
      label: '当前水平',
      data: [75, 68, 82, 59, 73, 79],
      borderColor: '#4f46e5',
      backgroundColor: 'rgba(79, 70, 229, 0.2)',
      pointBackgroundColor: '#4f46e5'
    },
    {
      label: '目标水平',
      data: [85, 80, 90, 75, 85, 88],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.2)',
      pointBackgroundColor: '#10b981'
    }
  ]
})

const skillRadarOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    }
  },
  scales: {
    r: {
      beginAtZero: true,
      max: 100
    }
  }
})

// 成绩趋势数据
const scoreTrendData = ref([
  {
    name: '听力成绩',
    data: [] as number[],
    color: '#4f46e5',
    smooth: true,
    area: true
  },
  {
    name: '口语成绩',
    data: [] as number[],
    color: '#10b981',
    smooth: true,
    area: true
  },
  {
    name: '阅读成绩',
    data: [] as number[],
    color: '#f59e0b',
    smooth: true,
    area: true
  },
  {
    name: '写作成绩',
    data: [] as number[],
    color: '#ef4444',
    smooth: true,
    area: true
  }
])

const scoreTrendXAxisData = ref([] as string[])

const scoreTrendOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      title: {
        display: true,
        text: '成绩 (分)'
      }
    }
  }
})

// 学习目标数据
const learningGoals = ref([
  {
    id: '1',
    title: '雅思总分7.0',
    progress: 65,
    deadline: '2024-06-30'
  },
  {
    id: '2',
    title: '商务英语中级',
    progress: 45,
    deadline: '2024-05-15'
  },
  {
    id: '3',
    title: '托福口语25分',
    progress: 78,
    deadline: '2024-04-20'
  }
])

// 方法
const startPractice = () => {
  router.push('/english-practice/practice')
}

const viewProgress = () => {
  router.push('/english-practice/progress')
}

const refreshRecommendations = () => {
  ElMessage.success('推荐内容已更新')
}

const startRecommendedPractice = (recommendation: any) => {
  ElMessage.info(`开始练习：${recommendation.title}`)
  router.push(`/english-practice/practice/${recommendation.id}`)
}

const getSkillTagType = (skill: string): string => {
  const types = {
    listening: 'primary',
    speaking: 'success',
    reading: 'warning',
    writing: 'danger'
  }
  return types[skill as keyof typeof types] || 'default'
}

const getSkillName = (skill: string): string => {
  const names = {
    listening: '听力',
    speaking: '口语',
    reading: '阅读',
    writing: '写作'
  }
  return names[skill as keyof typeof names] || skill
}

const updateStudyStats = () => {
  generateStudyTimeData()
  generateScoreTrendData()
}

const generateStudyTimeData = () => {
  const days = studyPeriod.value === '7d' ? 7 : studyPeriod.value === '30d' ? 30 : 90
  const labels = []
  const listeningData = []
  const speakingData = []
  const readingData = []
  const writingData = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString())
    listeningData.push(Math.floor(Math.random() * 60) + 10)
    speakingData.push(Math.floor(Math.random() * 45) + 5)
    readingData.push(Math.floor(Math.random() * 50) + 15)
    writingData.push(Math.floor(Math.random() * 40) + 10)
  }

  studyTimeXAxisData.value = labels
  studyTimeData.value[0].data = listeningData
  studyTimeData.value[1].data = speakingData
  studyTimeData.value[2].data = readingData
  studyTimeData.value[3].data = writingData
}

const generateScoreTrendData = () => {
  const days = studyPeriod.value === '7d' ? 7 : studyPeriod.value === '30d' ? 30 : 90
  const labels = []
  const listeningScores = []
  const speakingScores = []
  const readingScores = []
  const writingScores = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString())
    listeningScores.push(Math.floor(Math.random() * 30) + 60)
    speakingScores.push(Math.floor(Math.random() * 25) + 55)
    readingScores.push(Math.floor(Math.random() * 35) + 65)
    writingScores.push(Math.floor(Math.random() * 20) + 50)
  }

  scoreTrendXAxisData.value = labels
  scoreTrendData.value[0].data = listeningScores
  scoreTrendData.value[1].data = speakingScores
  scoreTrendData.value[2].data = readingScores
  scoreTrendData.value[3].data = writingScores
}

const editGoals = () => {
  ElMessage.info('编辑学习目标功能开发中...')
}

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString()
}

// 生命周期
onMounted(() => {
  generateStudyTimeData()
  generateScoreTrendData()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.english-practice-overview {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 学习进度概览 */
  .progress-overview {
    margin-bottom: var(--spacing-8);

    .progress-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.listening {
        border-left: 4px solid #4f46e5;
      }

      &.speaking {
        border-left: 4px solid #10b981;
      }

      &.reading {
        border-left: 4px solid #f59e0b;
      }

      &.writing {
        border-left: 4px solid #ef4444;
      }

      .progress-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .progress-icon {
          width: 60px;
          height: 60px;
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-xl);
        }

        .listening .progress-icon {
          background: linear-gradient(135deg, #4f46e5, #6366f1);
        }

        .speaking .progress-icon {
          background: linear-gradient(135deg, #10b981, #34d399);
        }

        .reading .progress-icon {
          background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }

        .writing .progress-icon {
          background: linear-gradient(135deg, #ef4444, #f87171);
        }

        .progress-info {
          flex: 1;

          .skill-name {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-3);
          }

          .progress-bar {
            margin-bottom: var(--spacing-2);
          }

          .progress-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .level {
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
              font-weight: var(--font-weight-medium);
            }

            .percentage {
              font-size: var(--font-size-lg);
              font-weight: var(--font-weight-bold);
              color: var(--text-primary);
            }
          }
        }
      }
    }
  }

  /* 练习推荐 */
  .practice-recommendations {
    margin-bottom: var(--spacing-8);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .recommendations-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: var(--spacing-6);
      margin-top: var(--spacing-4);

      .recommendation-card {
        background: white;
        border-radius: var(--radius-xl);
        border: 1px solid var(--border-light);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-fast);
        cursor: pointer;
        overflow: hidden;

        &:hover {
          transform: translateY(-4px);
          box-shadow: var(--shadow-lg);
          border-color: var(--primary-light);
        }

        .recommendation-cover {
          position: relative;
          height: 180px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
          }

          .cover-overlay {
            position: absolute;
            top: var(--spacing-3);
            left: var(--spacing-3);
            right: var(--spacing-3);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            .skill-badge {
              .el-tag {
                background: rgba(255, 255, 255, 0.9);
                border: none;
                font-weight: var(--font-weight-medium);
              }
            }

            .difficulty-badge {
              background: rgba(0, 0, 0, 0.6);
              border-radius: var(--radius-md);
              padding: var(--spacing-1) var(--spacing-2);
            }
          }
        }

        .recommendation-info {
          padding: var(--spacing-5);

          .recommendation-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-2) 0;
            line-height: 1.4;
          }

          .recommendation-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            line-height: 1.6;
            margin: 0 0 var(--spacing-4) 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .recommendation-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .duration, .points {
              display: flex;
              align-items: center;
              gap: var(--spacing-1);
              font-size: var(--font-size-sm);
              color: var(--text-tertiary);

              .el-icon {
                font-size: var(--font-size-base);
              }
            }

            .points {
              color: var(--warning-color);
              font-weight: var(--font-weight-medium);
            }
          }
        }
      }
    }
  }

  /* 学习统计 */
  .learning-stats {
    margin-bottom: var(--spacing-8);

    .chart-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);
        padding: var(--spacing-5) var(--spacing-6);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-container {
        padding: var(--spacing-4);
        min-height: 300px;
      }
    }

    /* 学习目标 */
    .goals-list {
      padding: var(--spacing-4);

      .goal-item {
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: var(--transition-fast);

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        .goal-info {
          .goal-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-3);
          }

          .goal-progress {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            margin-bottom: var(--spacing-2);

            .el-progress {
              flex: 1;
            }

            .progress-text {
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-semibold);
              color: var(--text-primary);
              min-width: 40px;
            }
          }

          .goal-deadline {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .english-practice-overview {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .progress-overview {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }

    .recommendations-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: var(--spacing-4);
    }

    .learning-stats {
      .el-col {
        margin-bottom: var(--spacing-6);
      }
    }
  }
}

@include respond-to('md') {
  .english-practice-overview {
    padding: var(--spacing-4);

    .progress-overview {
      .progress-card .progress-content {
        padding: var(--spacing-4);

        .progress-icon {
          width: 50px;
          height: 50px;
        }

        .progress-info .skill-name {
          font-size: var(--font-size-base);
        }
      }
    }

    .recommendations-grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);

      .recommendation-card {
        .recommendation-cover {
          height: 150px;
        }

        .recommendation-info {
          padding: var(--spacing-4);

          .recommendation-title {
            font-size: var(--font-size-base);
          }
        }
      }
    }

    .learning-stats {
      .goals-list .goal-item {
        padding: var(--spacing-3);

        .goal-info .goal-title {
          font-size: var(--font-size-sm);
        }
      }
    }
  }
}
</style>