"""
软件更新推送模块数据模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, JSON, Float, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base

class AppVersion(Base):
    """应用版本模型"""
    __tablename__ = "app_versions"
    
    id = Column(String(36), primary_key=True)
    version_name = Column(String(50), nullable=False, unique=True, comment="版本名称，如 1.2.3")
    version_code = Column(Integer, nullable=False, unique=True, comment="版本号，用于比较")
    
    # 版本信息
    title = Column(String(200), comment="版本标题")
    description = Column(Text, comment="版本描述")
    release_notes = Column(Text, comment="更新日志")
    
    # 更新内容
    new_features = Column(JSON, comment="新功能列表")
    improvements = Column(JSON, comment="改进内容")
    bug_fixes = Column(JSON, comment="修复问题")
    known_issues = Column(JSON, comment="已知问题")
    
    # 兼容性
    min_supported_version = Column(String(50), comment="最低支持版本")
    target_sdk_version = Column(Integer, comment="目标SDK版本")
    supported_platforms = Column(JSON, comment="支持平台")
    device_requirements = Column(JSON, comment="设备要求")
    
    # 文件信息
    package_url = Column(String(500), comment="安装包下载链接")
    package_size = Column(Integer, comment="安装包大小（字节）")
    package_md5 = Column(String(32), comment="安装包MD5校验")
    package_sha256 = Column(String(64), comment="安装包SHA256校验")
    
    # 更新类型
    update_type = Column(String(20), default="optional", comment="更新类型: optional, recommended, forced")
    is_security_update = Column(Boolean, default=False, comment="是否为安全更新")
    is_critical = Column(Boolean, default=False, comment="是否为关键更新")
    
    # 发布设置
    status = Column(String(20), default="draft", comment="状态: draft, testing, released, archived")
    release_time = Column(DateTime, comment="发布时间")
    rollout_percentage = Column(Float, default=0.0, comment="推送百分比")
    
    # 统计数据
    download_count = Column(Integer, default=0, comment="下载次数")
    install_count = Column(Integer, default=0, comment="安装次数")
    success_rate = Column(Float, default=0.0, comment="安装成功率")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class UpdateStrategy(Base):
    """更新推送策略模型"""
    __tablename__ = "update_strategies"
    
    id = Column(String(36), primary_key=True)
    name = Column(String(100), nullable=False, comment="策略名称")
    description = Column(Text, comment="策略描述")
    version_id = Column(String(36), ForeignKey('app_versions.id'), comment="关联版本ID")
    
    # 推送策略
    strategy_type = Column(String(20), default="gradual", comment="策略类型: immediate, gradual, scheduled, targeted")
    rollout_phases = Column(JSON, comment="分阶段推送配置")
    
    # 目标用户
    target_criteria = Column(JSON, comment="目标用户条件")
    target_regions = Column(JSON, comment="目标地区")
    target_devices = Column(JSON, comment="目标设备")
    target_user_groups = Column(JSON, comment="目标用户群体")
    
    # 时间控制
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    daily_start_hour = Column(Integer, comment="每日开始小时")
    daily_end_hour = Column(Integer, comment="每日结束小时")
    
    # 限制设置
    max_downloads_per_hour = Column(Integer, comment="每小时最大下载数")
    max_concurrent_downloads = Column(Integer, comment="最大并发下载数")
    bandwidth_limit = Column(Integer, comment="带宽限制（KB/s）")
    
    # 灰度发布
    is_gray_release = Column(Boolean, default=False, comment="是否为灰度发布")
    gray_percentage = Column(Float, default=0.0, comment="灰度用户百分比")
    gray_criteria = Column(JSON, comment="灰度用户条件")
    
    # 回滚机制
    auto_rollback_enabled = Column(Boolean, default=False, comment="是否启用自动回滚")
    rollback_conditions = Column(JSON, comment="回滚条件")
    rollback_threshold = Column(Float, comment="回滚阈值")
    
    # 状态
    status = Column(String(20), default="draft", comment="状态: draft, active, paused, completed, cancelled")
    is_active = Column(Boolean, default=False, comment="是否激活")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class UpdateRecord(Base):
    """更新记录模型"""
    __tablename__ = "update_records"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), comment="用户ID")
    device_id = Column(String(100), comment="设备ID")
    version_id = Column(String(36), ForeignKey('app_versions.id'), comment="版本ID")
    
    # 设备信息
    device_type = Column(String(20), comment="设备类型")
    os_version = Column(String(50), comment="操作系统版本")
    current_app_version = Column(String(50), comment="当前应用版本")
    target_app_version = Column(String(50), comment="目标应用版本")
    
    # 更新过程
    update_type = Column(String(20), comment="更新类型: check, download, install")
    status = Column(String(20), comment="状态: pending, downloading, installing, success, failed, cancelled")
    
    # 时间记录
    check_time = Column(DateTime, comment="检查更新时间")
    download_start_time = Column(DateTime, comment="下载开始时间")
    download_end_time = Column(DateTime, comment="下载结束时间")
    install_start_time = Column(DateTime, comment="安装开始时间")
    install_end_time = Column(DateTime, comment="安装结束时间")
    
    # 进度信息
    download_progress = Column(Float, default=0.0, comment="下载进度")
    install_progress = Column(Float, default=0.0, comment="安装进度")
    
    # 错误信息
    error_code = Column(String(20), comment="错误代码")
    error_message = Column(Text, comment="错误信息")
    error_details = Column(JSON, comment="错误详情")
    
    # 网络信息
    network_type = Column(String(20), comment="网络类型")
    download_speed = Column(Float, comment="下载速度（KB/s）")
    
    # 用户行为
    user_action = Column(String(20), comment="用户操作: auto, manual, forced")
    is_background_update = Column(Boolean, default=False, comment="是否后台更新")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class UpdateNotification(Base):
    """更新通知模型"""
    __tablename__ = "update_notifications"
    
    id = Column(String(36), primary_key=True)
    version_id = Column(String(36), ForeignKey('app_versions.id'), comment="版本ID")
    user_id = Column(String(36), comment="用户ID")
    device_id = Column(String(100), comment="设备ID")
    
    # 通知内容
    title = Column(String(200), comment="通知标题")
    message = Column(Text, comment="通知内容")
    notification_type = Column(String(20), comment="通知类型: update_available, download_complete, install_ready")
    
    # 显示设置
    priority = Column(String(20), default="normal", comment="优先级: low, normal, high, urgent")
    display_type = Column(String(20), default="banner", comment="显示类型: banner, dialog, toast")
    
    # 操作按钮
    actions = Column(JSON, comment="操作按钮配置")
    default_action = Column(String(20), comment="默认操作")
    
    # 发送状态
    status = Column(String(20), default="pending", comment="状态: pending, sent, delivered, clicked, dismissed")
    send_time = Column(DateTime, comment="发送时间")
    delivery_time = Column(DateTime, comment="送达时间")
    click_time = Column(DateTime, comment="点击时间")
    
    # 重试机制
    retry_count = Column(Integer, default=0, comment="重试次数")
    max_retries = Column(Integer, default=3, comment="最大重试次数")
    next_retry_time = Column(DateTime, comment="下次重试时间")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class UpdateStatistics(Base):
    """更新统计模型"""
    __tablename__ = "update_statistics"
    
    id = Column(String(36), primary_key=True)
    version_id = Column(String(36), ForeignKey('app_versions.id'), comment="版本ID")
    date = Column(DateTime, nullable=False, comment="统计日期")
    
    # 基础统计
    check_count = Column(Integer, default=0, comment="检查更新次数")
    download_count = Column(Integer, default=0, comment="下载次数")
    install_count = Column(Integer, default=0, comment="安装次数")
    success_count = Column(Integer, default=0, comment="成功次数")
    failure_count = Column(Integer, default=0, comment="失败次数")
    
    # 成功率统计
    download_success_rate = Column(Float, default=0.0, comment="下载成功率")
    install_success_rate = Column(Float, default=0.0, comment="安装成功率")
    overall_success_rate = Column(Float, default=0.0, comment="整体成功率")
    
    # 时间统计
    average_download_time = Column(Float, default=0.0, comment="平均下载时间（秒）")
    average_install_time = Column(Float, default=0.0, comment="平均安装时间（秒）")
    
    # 错误统计
    error_distribution = Column(JSON, comment="错误分布")
    top_errors = Column(JSON, comment="主要错误")
    
    # 设备统计
    device_distribution = Column(JSON, comment="设备分布")
    os_distribution = Column(JSON, comment="操作系统分布")
    network_distribution = Column(JSON, comment="网络类型分布")
    
    # 地区统计
    region_distribution = Column(JSON, comment="地区分布")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
