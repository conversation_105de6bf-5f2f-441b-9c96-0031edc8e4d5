"""用户相关的数据传输对象(DTO)和验证模式"""
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime, date
from app.models.user import Gender, UserStatus, MembershipType, UserLevel

class UserBase(BaseModel):
    """用户基础模式"""
    email: EmailStr
    phone: Optional[str] = None
    nickname: str = Field(..., min_length=2, max_length=50)
    full_name: Optional[str] = Field(None, max_length=100)
    gender: Gender = Gender.UNKNOWN
    birth_date: Optional[date] = None
    bio: Optional[str] = None
    location: Optional[str] = Field(None, max_length=100)
    avatar_url: Optional[str] = None

class CreateUserRequest(UserBase):
    """创建用户请求模式"""
    password: str = Field(..., min_length=6, max_length=50)
    status: UserStatus = UserStatus.ACTIVE
    membership: MembershipType = MembershipType.REGULAR
    level: UserLevel = UserLevel.LEVEL_ONE
    is_premium: bool = False
    premium_expires_at: Optional[datetime] = None
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not v.replace('+', '').replace('-', '').replace(' ', '').isdigit():
            raise ValueError('手机号格式不正确')
        return v

class UpdateUserRequest(BaseModel):
    """更新用户请求模式"""
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    nickname: Optional[str] = Field(None, min_length=2, max_length=50)
    full_name: Optional[str] = Field(None, max_length=100)
    gender: Optional[Gender] = None
    birth_date: Optional[date] = None
    bio: Optional[str] = None
    location: Optional[str] = Field(None, max_length=100)
    avatar_url: Optional[str] = None
    status: Optional[UserStatus] = None
    membership: Optional[MembershipType] = None
    level: Optional[UserLevel] = None
    is_premium: Optional[bool] = None
    premium_expires_at: Optional[datetime] = None
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not v.replace('+', '').replace('-', '').replace(' ', '').isdigit():
            raise ValueError('手机号格式不正确')
        return v

class ChangePasswordRequest(BaseModel):
    """修改密码请求模式"""
    current_password: str
    new_password: str = Field(..., min_length=6, max_length=50)

class UserResponse(UserBase):
    """用户响应模式"""
    id: int
    status: UserStatus
    membership: MembershipType
    level: UserLevel
    registration_time: datetime
    last_login: Optional[datetime] = None
    is_premium: bool
    premium_expires_at: Optional[datetime] = None
    total_study_time: int
    total_focus_sessions: int
    total_check_ins: int
    experience_points: int
    created_at: datetime
    updated_at: datetime
    
    # 兼容性属性
    @property
    def is_active(self) -> bool:
        return self.status == UserStatus.ACTIVE
    
    @property
    def username(self) -> str:
        return self.nickname
    
    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    """用户列表响应模式"""
    items: List[UserResponse]
    total: int
    page: int
    page_size: int
    pages: int

class UserStatsResponse(BaseModel):
    """用户统计响应模式"""
    study_stats: dict
    checkin_stats: dict
    community_stats: dict
    general_stats: dict

class UserOverviewResponse(BaseModel):
    """用户概览统计响应模式"""
    total_users: int
    active_users: int
    premium_users: int
    inactive_users: int
    free_users: int
    gender_distribution: List[dict]
    level_distribution: List[dict]