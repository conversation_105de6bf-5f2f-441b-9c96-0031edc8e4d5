/**
 * 错误处理工具测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { errorHandler, ErrorLevel, ErrorCategory } from '../error-handler'
import type { AxiosError } from 'axios'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElNotification: vi.fn()
}))

describe('ErrorHandler', () => {
  beforeEach(() => {
    errorHandler.clearErrorLog()
    vi.clearAllMocks()
  })

  describe('错误分类', () => {
    it('应该正确分类认证错误', () => {
      const mockError: Partial<AxiosError> = {
        response: {
          status: 401,
          data: {
            success: false,
            error: {
              code: 'AUTHENTICATION_ERROR',
              message: '认证失败',
              timestamp: new Date().toISOString()
            }
          }
        } as any,
        config: {
          url: '/api/test',
          method: 'get'
        } as any
      }

      errorHandler.handleAPIError(mockError as AxiosError)

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog).toHaveLength(1)
      expect(errorLog[0].category).toBe(ErrorCategory.AUTHENTICATION)
      expect(errorLog[0].level).toBe(ErrorLevel.ERROR)
    })

    it('应该正确分类验证错误', () => {
      const mockError: Partial<AxiosError> = {
        response: {
          status: 422,
          data: {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: '参数验证失败',
              timestamp: new Date().toISOString()
            }
          }
        } as any,
        config: {
          url: '/api/test',
          method: 'post'
        } as any
      }

      errorHandler.handleAPIError(mockError as AxiosError)

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog).toHaveLength(1)
      expect(errorLog[0].category).toBe(ErrorCategory.VALIDATION)
      expect(errorLog[0].level).toBe(ErrorLevel.WARNING)
    })

    it('应该正确分类系统错误', () => {
      const mockError: Partial<AxiosError> = {
        response: {
          status: 500,
          data: {
            success: false,
            error: {
              code: 'INTERNAL_SERVER_ERROR',
              message: '内部服务器错误',
              timestamp: new Date().toISOString()
            }
          }
        } as any,
        config: {
          url: '/api/test',
          method: 'get'
        } as any
      }

      errorHandler.handleAPIError(mockError as AxiosError)

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog).toHaveLength(1)
      expect(errorLog[0].category).toBe(ErrorCategory.SYSTEM)
      expect(errorLog[0].level).toBe(ErrorLevel.CRITICAL)
    })
  })

  describe('网络错误处理', () => {
    it('应该处理网络连接错误', () => {
      const mockError: Partial<AxiosError> = {
        request: {},
        config: {
          url: '/api/test',
          method: 'get'
        } as any
      }

      errorHandler.handleAPIError(mockError as AxiosError)

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog).toHaveLength(1)
      expect(errorLog[0].category).toBe(ErrorCategory.NETWORK)
      expect(errorLog[0].level).toBe(ErrorLevel.ERROR)
    })
  })

  describe('错误统计', () => {
    it('应该正确统计错误', () => {
      // 添加不同类型的错误
      const errors = [
        {
          response: {
            status: 401,
            data: {
              success: false,
              error: {
                code: 'AUTHENTICATION_ERROR',
                message: '认证失败',
                timestamp: new Date().toISOString()
              }
            }
          } as any,
          config: { url: '/api/test1', method: 'get' } as any
        },
        {
          response: {
            status: 422,
            data: {
              success: false,
              error: {
                code: 'VALIDATION_ERROR',
                message: '验证失败',
                timestamp: new Date().toISOString()
              }
            }
          } as any,
          config: { url: '/api/test2', method: 'post' } as any
        },
        {
          response: {
            status: 500,
            data: {
              success: false,
              error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: '服务器错误',
                timestamp: new Date().toISOString()
              }
            }
          } as any,
          config: { url: '/api/test3', method: 'get' } as any
        }
      ]

      errors.forEach(error => {
        errorHandler.handleAPIError(error as AxiosError)
      })

      const stats = errorHandler.getErrorStats()
      
      expect(stats.total).toBe(3)
      expect(stats.byLevel[ErrorLevel.ERROR]).toBe(1) // 认证错误
      expect(stats.byLevel[ErrorLevel.WARNING]).toBe(1) // 验证错误
      expect(stats.byLevel[ErrorLevel.CRITICAL]).toBe(1) // 系统错误
      expect(stats.byCategory[ErrorCategory.AUTHENTICATION]).toBe(1)
      expect(stats.byCategory[ErrorCategory.VALIDATION]).toBe(1)
      expect(stats.byCategory[ErrorCategory.SYSTEM]).toBe(1)
    })
  })

  describe('错误日志管理', () => {
    it('应该限制错误日志大小', () => {
      // 添加超过100个错误
      for (let i = 0; i < 150; i++) {
        const mockError: Partial<AxiosError> = {
          response: {
            status: 400,
            data: {
              success: false,
              error: {
                code: 'TEST_ERROR',
                message: `测试错误 ${i}`,
                timestamp: new Date().toISOString()
              }
            }
          } as any,
          config: {
            url: `/api/test${i}`,
            method: 'get'
          } as any
        }

        errorHandler.handleAPIError(mockError as AxiosError)
      }

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog.length).toBeLessThanOrEqual(100)
    })

    it('应该能够清空错误日志', () => {
      const mockError: Partial<AxiosError> = {
        response: {
          status: 400,
          data: {
            success: false,
            error: {
              code: 'TEST_ERROR',
              message: '测试错误',
              timestamp: new Date().toISOString()
            }
          }
        } as any,
        config: {
          url: '/api/test',
          method: 'get'
        } as any
      }

      errorHandler.handleAPIError(mockError as AxiosError)
      expect(errorHandler.getErrorLog()).toHaveLength(1)

      errorHandler.clearErrorLog()
      expect(errorHandler.getErrorLog()).toHaveLength(0)
    })
  })

  describe('错误消息格式化', () => {
    it('应该正确格式化包含详细信息的错误', () => {
      const mockError: Partial<AxiosError> = {
        response: {
          status: 422,
          data: {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: '参数验证失败',
              details: {
                summary: '用户名长度不足'
              },
              timestamp: new Date().toISOString(),
              request_id: 'test-request-id'
            }
          }
        } as any,
        config: {
          url: '/api/test',
          method: 'post'
        } as any
      }

      errorHandler.handleAPIError(mockError as AxiosError)

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog).toHaveLength(1)
      expect(errorLog[0].error.details).toBeDefined()
      expect(errorLog[0].error.request_id).toBe('test-request-id')
    })
  })
})
