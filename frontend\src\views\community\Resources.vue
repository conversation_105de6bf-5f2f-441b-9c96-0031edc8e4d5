<template>
  <div class="resources-management">
    <div class="page-header">
      <h1>资源库管理</h1>
      <p>管理学习资源、文档、视频等内容的审核、存储、版权等功能</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="资源审核工作台" name="audit">
        <!-- 审核统计卡片 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon pending">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ auditStats.pending }}</div>
                  <div class="stat-label">待审核资源</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ auditStats.approved }}</div>
                  <div class="stat-label">今日已通过</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ auditStats.rejected }}</div>
                  <div class="stat-label">今日已驳回</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ auditStats.avgTime }}</div>
                  <div class="stat-label">平均审核时长(分钟)</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 自动审核白名单设置 -->
        <el-card class="whitelist-card">
          <template #header>
            <div class="card-header">
              <span>自动审核白名单</span>
              <el-button type="primary" size="small" @click="showWhitelistDialog = true">
                <el-icon><Plus /></el-icon>
                添加白名单用户
              </el-button>
            </div>
          </template>
          <div class="whitelist-content">
            <el-tag
              v-for="user in whitelistUsers"
              :key="user.id"
              closable
              @close="removeFromWhitelist(user.id)"
              class="whitelist-tag"
            >
              {{ user.name }} ({{ user.role }})
            </el-tag>
            <el-empty v-if="!whitelistUsers.length" description="暂无白名单用户" />
          </div>
        </el-card>

        <!-- 待审核资源列表 -->
        <el-card class="audit-table-card">
          <template #header>
            <div class="card-header">
              <span>待审核资源列表</span>
              <div class="header-actions">
                <el-button type="success" @click="handleBatchApprove" :disabled="!selectedResources.length">
                  <el-icon><Check /></el-icon>
                  批量通过
                </el-button>
                <el-button type="danger" @click="handleBatchReject" :disabled="!selectedResources.length">
                  <el-icon><Close /></el-icon>
                  批量驳回
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="pendingResources"
            v-loading="loading"
            @selection-change="handleSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" label="资源标题" min-width="200">
              <template #default="{ row }">
                <div class="resource-info">
                  <div class="resource-title">{{ row.title }}</div>
                  <div class="resource-meta">
                    <el-tag :type="getTypeTagType(row.type)" size="small">{{ getTypeName(row.type) }}</el-tag>
                    <span class="file-size">{{ formatFileSize(row.fileSize) }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="authorName" label="上传者" width="120" />
            <el-table-column prop="subject" label="学科" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ getSubjectName(row.subject) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" label="提交时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.submitTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="waitingTime" label="等待时长" width="120">
              <template #default="{ row }">
                <span :class="getWaitingTimeClass(row.waitingTime)">
                  {{ formatWaitingTime(row.waitingTime) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="previewResource(row)">
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
                <el-button type="success" size="small" @click="approveResource(row)">
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button type="danger" size="small" @click="rejectResource(row)">
                  <el-icon><Close /></el-icon>
                  驳回
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="auditPagination.page"
              v-model:page-size="auditPagination.size"
              :total="auditPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleAuditSizeChange"
              @current-change="handleAuditPageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="违规资源处理" name="violation">
        <!-- 违规统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.reported }}</div>
                  <div class="stat-label">被举报资源</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Remove /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.removed }}</div>
                  <div class="stat-label">已下架资源</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.punishedUsers }}</div>
                  <div class="stat-label">处罚用户数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.resolved }}</div>
                  <div class="stat-label">已处理举报</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 违规资源列表 -->
        <el-card class="violation-table-card">
          <template #header>
            <div class="card-header">
              <span>违规资源列表</span>
              <div class="header-actions">
                <el-select v-model="violationFilter" placeholder="筛选举报类型" clearable>
                  <el-option label="版权侵犯" value="copyright" />
                  <el-option label="内容违规" value="content" />
                  <el-option label="虚假信息" value="fake" />
                  <el-option label="恶意广告" value="spam" />
                  <el-option label="其他" value="other" />
                </el-select>
              </div>
            </div>
          </template>

          <el-table
            :data="violationResources"
            v-loading="violationLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="title" label="资源标题" min-width="200">
              <template #default="{ row }">
                <div class="resource-info">
                  <div class="resource-title">{{ row.title }}</div>
                  <div class="resource-meta">
                    <el-tag :type="getTypeTagType(row.type)" size="small">{{ getTypeName(row.type) }}</el-tag>
                    <span class="author">{{ row.authorName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="reportCount" label="举报次数" width="100">
              <template #default="{ row }">
                <el-tag :type="row.reportCount > 5 ? 'danger' : 'warning'">
                  {{ row.reportCount }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reportReasons" label="举报理由" width="200">
              <template #default="{ row }">
                <div class="report-reasons">
                  <el-tag
                    v-for="reason in row.reportReasons"
                    :key="reason"
                    size="small"
                    class="reason-tag"
                  >
                    {{ getReasonText(reason) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="reportTime" label="最新举报时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.reportTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="处理状态" width="120">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewReportDetails(row)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
                <el-button type="warning" size="small" @click="temporaryRemove(row)">
                  <el-icon><Download /></el-icon>
                  临时下架
                </el-button>
                <el-button type="danger" size="small" @click="permanentDelete(row)">
                  <el-icon><Delete /></el-icon>
                  永久删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock, Check, Close, Timer, Plus, Warning, Remove, User, CircleCheck,
  View, Delete, Download
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('audit')
const loading = ref(false)
const violationLoading = ref(false)
const selectedResources = ref([])
const showWhitelistDialog = ref(false)

// 审核统计数据
const auditStats = reactive({
  pending: 45,
  approved: 23,
  rejected: 8,
  avgTime: 12
})

// 违规统计数据
const violationStats = reactive({
  reported: 12,
  removed: 8,
  punishedUsers: 5,
  resolved: 15
})

// 白名单用户
const whitelistUsers = ref([
  { id: 1, name: '张教授', role: '认证教师' },
  { id: 2, name: '李老师', role: '优质作者' }
])

// 待审核资源
const pendingResources = ref([
  {
    id: 1,
    title: '高中数学知识点总结',
    type: 'document',
    fileSize: 2048000,
    authorName: '学生A',
    subject: 'math',
    submitTime: '2024-01-15 10:30:00',
    waitingTime: 120
  }
])

// 违规资源
const violationResources = ref([
  {
    id: 1,
    title: '疑似抄袭的学习资料',
    type: 'document',
    authorName: '用户B',
    reportCount: 3,
    reportReasons: ['copyright', 'content'],
    reportTime: '2024-01-15 14:20:00',
    status: 'pending'
  }
])

// 分页数据
const auditPagination = reactive({
  page: 1,
  size: 20,
  total: 100
})

const violationFilter = ref('')

// 工具函数
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    document: 'primary',
    video: 'success',
    audio: 'warning',
    image: 'info'
  }
  return typeMap[type] || 'default'
}

const getTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    document: '文档',
    video: '视频',
    audio: '音频',
    image: '图片'
  }
  return typeMap[type] || type
}

const getSubjectName = (subject: string) => {
  const subjectMap: Record<string, string> = {
    math: '数学',
    chinese: '语文',
    english: '英语',
    physics: '物理',
    chemistry: '化学',
    biology: '生物'
  }
  return subjectMap[subject] || subject
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatWaitingTime = (minutes: number) => {
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins}分钟`
}

const getWaitingTimeClass = (minutes: number) => {
  if (minutes > 240) return 'text-danger'
  if (minutes > 120) return 'text-warning'
  return 'text-success'
}

const getReasonText = (reason: string) => {
  const reasonMap: Record<string, string> = {
    copyright: '版权侵犯',
    content: '内容违规',
    fake: '虚假信息',
    spam: '恶意广告',
    other: '其他'
  }
  return reasonMap[reason] || reason
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    removed: 'info'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    approved: '已通过',
    rejected: '已驳回',
    removed: '已下架'
  }
  return statusMap[status] || status
}

// 事件处理函数
const handleSelectionChange = (selection: any[]) => {
  selectedResources.value = selection
}

const handleBatchApprove = () => {
  ElMessageBox.confirm('确定要批量通过选中的资源吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量审核通过成功')
    // TODO: 调用API
  })
}

const handleBatchReject = () => {
  ElMessageBox.confirm('确定要批量驳回选中的资源吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量驳回成功')
    // TODO: 调用API
  })
}

const previewResource = (resource: any) => {
  console.log('预览资源:', resource)
  // TODO: 实现资源预览
}

const approveResource = (resource: any) => {
  ElMessage.success(`资源 "${resource.title}" 审核通过`)
  // TODO: 调用API
}

const rejectResource = (resource: any) => {
  ElMessage.warning(`资源 "${resource.title}" 已驳回`)
  // TODO: 调用API
}

const removeFromWhitelist = (userId: number) => {
  const index = whitelistUsers.value.findIndex(user => user.id === userId)
  if (index > -1) {
    whitelistUsers.value.splice(index, 1)
    ElMessage.success('已从白名单中移除')
  }
}

const viewReportDetails = (resource: any) => {
  console.log('查看举报详情:', resource)
  // TODO: 实现举报详情查看
}

const temporaryRemove = (resource: any) => {
  ElMessageBox.confirm('确定要临时下架此资源吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('资源已临时下架')
    // TODO: 调用API
  })
}

const permanentDelete = (resource: any) => {
  ElMessageBox.confirm('确定要永久删除此资源吗？此操作不可恢复！', '危险操作', {
    type: 'error'
  }).then(() => {
    ElMessage.success('资源已永久删除')
    // TODO: 调用API
  })
}

const handleAuditSizeChange = (size: number) => {
  auditPagination.size = size
  // TODO: 重新加载数据
}

const handleAuditPageChange = (page: number) => {
  auditPagination.page = page
  // TODO: 重新加载数据
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.resources-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.pending {
  background-color: #e6a23c;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.danger {
  background-color: #f56c6c;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.info {
  background-color: #409eff;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.whitelist-content {
  min-height: 60px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.whitelist-tag {
  margin-bottom: 8px;
}

.resource-info {
  display: flex;
  flex-direction: column;
}

.resource-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.resource-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.file-size {
  color: #909399;
}

.author {
  color: #909399;
}

.report-reasons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.reason-tag {
  margin-bottom: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.text-danger {
  color: #f56c6c;
}

.text-warning {
  color: #e6a23c;
}

.text-success {
  color: #67c23a;
}

@media (max-width: 768px) {
  .resources-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }
}
</style>
