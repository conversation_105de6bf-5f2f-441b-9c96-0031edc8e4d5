<template>
  <el-card class="error-monitor">
    <template #header>
      <div class="card-header">
        <span>错误监控</span>
        <div class="header-actions">
          <el-button size="small" @click="refreshStats">刷新</el-button>
          <el-button size="small" type="danger" @click="clearErrors">清空</el-button>
        </div>
      </div>
    </template>
    
    <!-- 错误统计 -->
    <div class="error-stats">
      <div class="stat-item">
        <div class="stat-label">总错误数</div>
        <div class="stat-value error">{{ errorStats.total }}</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-label">严重错误</div>
        <div class="stat-value critical">{{ errorStats.byLevel.critical }}</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-label">一般错误</div>
        <div class="stat-value warning">{{ errorStats.byLevel.error }}</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-label">警告</div>
        <div class="stat-value info">{{ errorStats.byLevel.warning }}</div>
      </div>
    </div>
    
    <!-- 错误分类图表 -->
    <div class="error-chart" ref="chartRef" style="height: 200px; margin: 20px 0;"></div>
    
    <!-- 错误日志列表 -->
    <el-collapse v-model="activeCollapse">
      <el-collapse-item title="错误日志" name="logs">
        <div class="error-logs">
          <div v-if="errorLogs.length === 0" class="no-errors">
            <el-empty description="暂无错误记录" />
          </div>
          
          <div v-else class="log-list">
            <div
              v-for="(log, index) in errorLogs.slice(0, 20)"
              :key="index"
              class="log-item"
              :class="log.level"
            >
              <div class="log-header">
                <span class="log-level">{{ log.level.toUpperCase() }}</span>
                <span class="log-category">{{ log.category }}</span>
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              </div>
              
              <div class="log-content">
                <div class="log-message">{{ log.error.message }}</div>
                <div v-if="log.error.code" class="log-code">错误码: {{ log.error.code }}</div>
                <div v-if="log.url" class="log-url">{{ log.method }} {{ log.url }}</div>
                <div v-if="log.error.request_id" class="log-request-id">
                  请求ID: {{ log.error.request_id }}
                </div>
              </div>
              
              <div v-if="log.error.details" class="log-details">
                <el-button size="small" text @click="toggleDetails(index)">
                  {{ expandedLogs.has(index) ? '收起' : '详情' }}
                </el-button>
                <div v-if="expandedLogs.has(index)" class="details-content">
                  <pre>{{ JSON.stringify(log.error.details, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { errorHandler, ErrorLevel, ErrorCategory } from '@/utils/error-handler'
import * as echarts from 'echarts'

const activeCollapse = ref(['logs'])
const chartRef = ref<HTMLElement>()
const expandedLogs = ref(new Set<number>())

const errorStats = ref({
  total: 0,
  byLevel: {} as Record<ErrorLevel, number>,
  byCategory: {} as Record<ErrorCategory, number>
})

const errorLogs = ref<any[]>([])
let chart: echarts.ECharts | null = null
let refreshInterval: number | null = null

const refreshStats = () => {
  errorStats.value = errorHandler.getErrorStats()
  errorLogs.value = errorHandler.getErrorLog().reverse() // 最新的在前面
  updateChart()
}

const clearErrors = () => {
  errorHandler.clearErrorLog()
  refreshStats()
  ElMessage.success('错误日志已清空')
}

const toggleDetails = (index: number) => {
  if (expandedLogs.value.has(index)) {
    expandedLogs.value.delete(index)
  } else {
    expandedLogs.value.add(index)
  }
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const updateChart = () => {
  if (!chart || !chartRef.value) return

  const categoryData = Object.entries(errorStats.value.byCategory)
    .filter(([_, count]) => count > 0)
    .map(([category, count]) => ({
      name: category,
      value: count
    }))

  const option = {
    title: {
      text: '错误分类分布',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '错误分类',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: categoryData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  chart.setOption(option)
}

const initChart = async () => {
  await nextTick()
  if (chartRef.value) {
    chart = echarts.init(chartRef.value)
    updateChart()
  }
}

onMounted(() => {
  refreshStats()
  initChart()
  
  // 每30秒刷新一次
  refreshInterval = window.setInterval(refreshStats, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
  if (chart) {
    chart.dispose()
  }
})
</script>

<style lang="scss" scoped>
.error-monitor {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .error-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
    
    .stat-item {
      text-align: center;
      padding: 12px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      
      .stat-label {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-bottom: 4px;
      }
      
      .stat-value {
        font-size: 20px;
        font-weight: bold;
        
        &.error { color: var(--el-color-danger); }
        &.critical { color: #722ed1; }
        &.warning { color: var(--el-color-warning); }
        &.info { color: var(--el-color-info); }
      }
    }
  }
  
  .error-logs {
    .no-errors {
      text-align: center;
      padding: 40px;
    }
    
    .log-list {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .log-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 6px;
      border-left: 4px solid;
      
      &.critical {
        background: rgba(114, 46, 209, 0.1);
        border-left-color: #722ed1;
      }
      
      &.error {
        background: rgba(245, 108, 108, 0.1);
        border-left-color: var(--el-color-danger);
      }
      
      &.warning {
        background: rgba(230, 162, 60, 0.1);
        border-left-color: var(--el-color-warning);
      }
      
      &.info {
        background: rgba(144, 147, 153, 0.1);
        border-left-color: var(--el-color-info);
      }
      
      .log-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        
        .log-level {
          font-size: 12px;
          font-weight: bold;
          padding: 2px 6px;
          border-radius: 3px;
          background: var(--el-color-primary);
          color: white;
        }
        
        .log-category {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
        
        .log-time {
          font-size: 12px;
          color: var(--el-text-color-placeholder);
          margin-left: auto;
        }
      }
      
      .log-content {
        .log-message {
          font-weight: 500;
          margin-bottom: 4px;
        }
        
        .log-code, .log-url, .log-request-id {
          font-size: 12px;
          color: var(--el-text-color-regular);
          font-family: monospace;
          margin-bottom: 2px;
        }
      }
      
      .log-details {
        margin-top: 8px;
        
        .details-content {
          margin-top: 8px;
          padding: 8px;
          background: var(--el-bg-color);
          border-radius: 4px;
          
          pre {
            font-size: 12px;
            margin: 0;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }
    }
  }
}
</style>
