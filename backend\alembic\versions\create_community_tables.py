"""Create community tables

Revision ID: community_001
Revises: 
Create Date: 2024-01-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'community_001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 创建学习资源表
    op.create_table('learning_resources',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('title', sa.String(200), nullable=False, comment='资源标题'),
        sa.Column('description', sa.Text(), comment='资源描述'),
        sa.Column('type', sa.String(20), nullable=False, comment='资源类型'),
        sa.Column('url', sa.String(500), nullable=False, comment='资源链接'),
        sa.Column('thumbnail_url', sa.String(500), comment='缩略图链接'),
        sa.Column('file_size', sa.Integer(), comment='文件大小(字节)'),
        sa.Column('duration', sa.Integer(), comment='时长(秒)'),
        sa.Column('subject', sa.String(50), nullable=False, comment='学科'),
        sa.Column('grade_level', sa.String(20), nullable=False, comment='年级'),
        sa.Column('tags', sa.JSON(), comment='标签列表'),
        sa.Column('author_id', sa.String(36), nullable=False, comment='作者ID'),
        sa.Column('author_name', sa.String(100), nullable=False, comment='作者姓名'),
        sa.Column('status', sa.String(20), server_default='pending', comment='审核状态'),
        sa.Column('download_count', sa.Integer(), server_default='0', comment='下载次数'),
        sa.Column('view_count', sa.Integer(), server_default='0', comment='浏览次数'),
        sa.Column('like_count', sa.Integer(), server_default='0', comment='点赞次数'),
        sa.Column('rating', sa.Float(), server_default='0.0', comment='评分'),
        sa.Column('rating_count', sa.Integer(), server_default='0', comment='评分人数'),
        sa.Column('is_approved', sa.Boolean(), server_default='false', comment='是否已审核'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.Column('approved_at', sa.DateTime(), comment='审核时间'),
        sa.Column('approved_by', sa.String(36), comment='审核人ID'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建自习室表
    op.create_table('study_rooms',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('name', sa.String(100), nullable=False, comment='自习室名称'),
        sa.Column('description', sa.Text(), comment='自习室描述'),
        sa.Column('owner_id', sa.String(36), nullable=False, comment='房主ID'),
        sa.Column('owner_name', sa.String(100), nullable=False, comment='房主姓名'),
        sa.Column('max_members', sa.Integer(), server_default='50', comment='最大成员数'),
        sa.Column('current_members', sa.Integer(), server_default='0', comment='当前成员数'),
        sa.Column('is_public', sa.Boolean(), server_default='true', comment='是否公开'),
        sa.Column('password', sa.String(100), comment='房间密码'),
        sa.Column('tags', sa.JSON(), comment='标签列表'),
        sa.Column('rules', sa.Text(), comment='房间规则'),
        sa.Column('status', sa.String(20), server_default='active', comment='房间状态'),
        sa.Column('total_study_time', sa.Integer(), server_default='0', comment='总学习时长(分钟)'),
        sa.Column('average_study_time', sa.Integer(), server_default='0', comment='平均学习时长(分钟)'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建自习室成员表
    op.create_table('study_room_members',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('room_id', sa.String(36), nullable=False, comment='自习室ID'),
        sa.Column('user_id', sa.String(36), nullable=False, comment='用户ID'),
        sa.Column('user_name', sa.String(100), nullable=False, comment='用户姓名'),
        sa.Column('user_avatar', sa.String(500), comment='用户头像'),
        sa.Column('joined_at', sa.DateTime(), server_default=sa.text('now()'), comment='加入时间'),
        sa.Column('total_study_time', sa.Integer(), server_default='0', comment='总学习时长(分钟)'),
        sa.Column('weekly_study_time', sa.Integer(), server_default='0', comment='本周学习时长(分钟)'),
        sa.Column('is_online', sa.Boolean(), server_default='false', comment='是否在线'),
        sa.Column('last_active_at', sa.DateTime(), server_default=sa.text('now()'), comment='最后活跃时间'),
        sa.ForeignKeyConstraint(['room_id'], ['study_rooms.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建话题表
    op.create_table('topics',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('title', sa.String(200), nullable=False, comment='话题标题'),
        sa.Column('content', sa.Text(), nullable=False, comment='话题内容'),
        sa.Column('author_id', sa.String(36), nullable=False, comment='作者ID'),
        sa.Column('author_name', sa.String(100), nullable=False, comment='作者姓名'),
        sa.Column('author_avatar', sa.String(500), comment='作者头像'),
        sa.Column('category_id', sa.String(36), nullable=False, comment='分类ID'),
        sa.Column('category_name', sa.String(100), nullable=False, comment='分类名称'),
        sa.Column('tags', sa.JSON(), comment='标签列表'),
        sa.Column('status', sa.String(20), server_default='active', comment='话题状态'),
        sa.Column('view_count', sa.Integer(), server_default='0', comment='浏览次数'),
        sa.Column('like_count', sa.Integer(), server_default='0', comment='点赞次数'),
        sa.Column('reply_count', sa.Integer(), server_default='0', comment='回复次数'),
        sa.Column('last_reply_at', sa.DateTime(), comment='最后回复时间'),
        sa.Column('last_reply_by', sa.String(100), comment='最后回复人'),
        sa.Column('is_pinned', sa.Boolean(), server_default='false', comment='是否置顶'),
        sa.Column('is_hot', sa.Boolean(), server_default='false', comment='是否热门'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建话题回复表
    op.create_table('topic_replies',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('topic_id', sa.String(36), nullable=False, comment='话题ID'),
        sa.Column('content', sa.Text(), nullable=False, comment='回复内容'),
        sa.Column('author_id', sa.String(36), nullable=False, comment='作者ID'),
        sa.Column('author_name', sa.String(100), nullable=False, comment='作者姓名'),
        sa.Column('author_avatar', sa.String(500), comment='作者头像'),
        sa.Column('like_count', sa.Integer(), server_default='0', comment='点赞次数'),
        sa.Column('is_liked', sa.Boolean(), server_default='false', comment='是否已点赞'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.ForeignKeyConstraint(['topic_id'], ['topics.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建部落表
    op.create_table('tribes',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('name', sa.String(100), nullable=False, comment='部落名称'),
        sa.Column('description', sa.Text(), comment='部落描述'),
        sa.Column('avatar', sa.String(500), comment='部落头像'),
        sa.Column('banner', sa.String(500), comment='部落横幅'),
        sa.Column('founder_id', sa.String(36), nullable=False, comment='创始人ID'),
        sa.Column('founder_name', sa.String(100), nullable=False, comment='创始人姓名'),
        sa.Column('category', sa.String(50), nullable=False, comment='部落分类'),
        sa.Column('level', sa.Integer(), server_default='1', comment='部落等级'),
        sa.Column('experience', sa.Integer(), server_default='0', comment='经验值'),
        sa.Column('member_count', sa.Integer(), server_default='0', comment='成员数量'),
        sa.Column('max_members', sa.Integer(), server_default='100', comment='最大成员数'),
        sa.Column('is_public', sa.Boolean(), server_default='true', comment='是否公开'),
        sa.Column('join_condition', sa.String(200), comment='加入条件'),
        sa.Column('tags', sa.JSON(), comment='标签列表'),
        sa.Column('announcement', sa.Text(), comment='部落公告'),
        sa.Column('rules', sa.Text(), comment='部落规则'),
        sa.Column('total_points', sa.Integer(), server_default='0', comment='总积分'),
        sa.Column('weekly_points', sa.Integer(), server_default='0', comment='本周积分'),
        sa.Column('rank', sa.Integer(), server_default='0', comment='部落排名'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建部落成员表
    op.create_table('tribe_members',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('tribe_id', sa.String(36), nullable=False, comment='部落ID'),
        sa.Column('user_id', sa.String(36), nullable=False, comment='用户ID'),
        sa.Column('user_name', sa.String(100), nullable=False, comment='用户姓名'),
        sa.Column('user_avatar', sa.String(500), comment='用户头像'),
        sa.Column('role', sa.String(20), server_default='member', comment='角色'),
        sa.Column('contribution', sa.Integer(), server_default='0', comment='贡献度'),
        sa.Column('weekly_contribution', sa.Integer(), server_default='0', comment='本周贡献'),
        sa.Column('join_time', sa.DateTime(), server_default=sa.text('now()'), comment='加入时间'),
        sa.Column('last_active_time', sa.DateTime(), server_default=sa.text('now()'), comment='最后活跃时间'),
        sa.Column('title', sa.String(50), comment='部落内称号'),
        sa.ForeignKeyConstraint(['tribe_id'], ['tribes.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建导师档案表
    op.create_table('mentor_profiles',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('user_id', sa.String(36), nullable=False, comment='用户ID'),
        sa.Column('user_name', sa.String(100), nullable=False, comment='用户姓名'),
        sa.Column('user_avatar', sa.String(500), comment='用户头像'),
        sa.Column('subjects', sa.JSON(), comment='擅长学科'),
        sa.Column('experience', sa.Text(), comment='教学经验'),
        sa.Column('achievements', sa.JSON(), comment='成就列表'),
        sa.Column('rating', sa.Float(), server_default='0.0', comment='评分'),
        sa.Column('rating_count', sa.Integer(), server_default='0', comment='评分人数'),
        sa.Column('student_count', sa.Integer(), server_default='0', comment='学生数量'),
        sa.Column('max_students', sa.Integer(), server_default='10', comment='最大学生数'),
        sa.Column('is_available', sa.Boolean(), server_default='true', comment='是否可接收学生'),
        sa.Column('introduction', sa.Text(), comment='个人介绍'),
        sa.Column('teaching_style', sa.Text(), comment='教学风格'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id')
    )

    # 创建师徒关系表
    op.create_table('mentorship_relations',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('mentor_id', sa.String(36), nullable=False, comment='导师ID'),
        sa.Column('mentor_name', sa.String(100), nullable=False, comment='导师姓名'),
        sa.Column('student_id', sa.String(36), nullable=False, comment='学生ID'),
        sa.Column('student_name', sa.String(100), nullable=False, comment='学生姓名'),
        sa.Column('subject', sa.String(50), nullable=False, comment='学科'),
        sa.Column('goals', sa.Text(), comment='学习目标'),
        sa.Column('status', sa.String(20), server_default='pending', comment='关系状态'),
        sa.Column('progress', sa.Integer(), server_default='0', comment='进度百分比'),
        sa.Column('start_date', sa.DateTime(), server_default=sa.text('now()'), comment='开始时间'),
        sa.Column('end_date', sa.DateTime(), comment='结束时间'),
        sa.Column('notes', sa.Text(), comment='备注'),
        sa.Column('rating', sa.Float(), comment='评分'),
        sa.Column('feedback', sa.Text(), comment='反馈'),
        sa.ForeignKeyConstraint(['mentor_id'], ['mentor_profiles.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建学霸经验表
    op.create_table('experiences',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('title', sa.String(200), nullable=False, comment='经验标题'),
        sa.Column('content', sa.Text(), nullable=False, comment='经验内容'),
        sa.Column('author_id', sa.String(36), nullable=False, comment='作者ID'),
        sa.Column('author_name', sa.String(100), nullable=False, comment='作者姓名'),
        sa.Column('author_avatar', sa.String(500), comment='作者头像'),
        sa.Column('author_level', sa.String(50), comment='作者等级'),
        sa.Column('author_achievements', sa.JSON(), comment='作者成就'),
        sa.Column('subject', sa.String(50), nullable=False, comment='学科'),
        sa.Column('tags', sa.JSON(), comment='标签列表'),
        sa.Column('images', sa.JSON(), comment='图片列表'),
        sa.Column('video_url', sa.String(500), comment='视频链接'),
        sa.Column('view_count', sa.Integer(), server_default='0', comment='浏览次数'),
        sa.Column('like_count', sa.Integer(), server_default='0', comment='点赞次数'),
        sa.Column('comment_count', sa.Integer(), server_default='0', comment='评论次数'),
        sa.Column('share_count', sa.Integer(), server_default='0', comment='分享次数'),
        sa.Column('is_recommended', sa.Boolean(), server_default='false', comment='是否推荐'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建活动竞赛表
    op.create_table('activities',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('title', sa.String(200), nullable=False, comment='活动标题'),
        sa.Column('description', sa.Text(), nullable=False, comment='活动描述'),
        sa.Column('type', sa.String(20), nullable=False, comment='活动类型'),
        sa.Column('organizer_id', sa.String(36), nullable=False, comment='组织者ID'),
        sa.Column('organizer_name', sa.String(100), nullable=False, comment='组织者姓名'),
        sa.Column('subject', sa.String(50), comment='学科'),
        sa.Column('start_date', sa.DateTime(), nullable=False, comment='开始时间'),
        sa.Column('end_date', sa.DateTime(), nullable=False, comment='结束时间'),
        sa.Column('registration_deadline', sa.DateTime(), nullable=False, comment='报名截止时间'),
        sa.Column('max_participants', sa.Integer(), comment='最大参与人数'),
        sa.Column('current_participants', sa.Integer(), server_default='0', comment='当前参与人数'),
        sa.Column('rules', sa.Text(), comment='活动规则'),
        sa.Column('prizes', sa.JSON(), comment='奖品设置'),
        sa.Column('status', sa.String(20), server_default='draft', comment='活动状态'),
        sa.Column('is_public', sa.Boolean(), server_default='true', comment='是否公开'),
        sa.Column('tags', sa.JSON(), comment='标签列表'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建活动参与者表
    op.create_table('activity_participants',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('activity_id', sa.String(36), nullable=False, comment='活动ID'),
        sa.Column('user_id', sa.String(36), nullable=False, comment='用户ID'),
        sa.Column('user_name', sa.String(100), nullable=False, comment='用户姓名'),
        sa.Column('user_avatar', sa.String(500), comment='用户头像'),
        sa.Column('registered_at', sa.DateTime(), server_default=sa.text('now()'), comment='报名时间'),
        sa.Column('score', sa.Float(), comment='得分'),
        sa.Column('rank', sa.Integer(), comment='排名'),
        sa.Column('status', sa.String(20), server_default='registered', comment='参与状态'),
        sa.ForeignKeyConstraint(['activity_id'], ['activities.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建心愿表
    op.create_table('wishes',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('user_id', sa.String(36), nullable=False, comment='用户ID'),
        sa.Column('user_name', sa.String(100), nullable=False, comment='用户姓名'),
        sa.Column('user_avatar', sa.String(500), comment='用户头像'),
        sa.Column('content', sa.Text(), nullable=False, comment='心愿内容'),
        sa.Column('tags', sa.JSON(), comment='标签列表'),
        sa.Column('status', sa.String(20), server_default='active', comment='心愿状态'),
        sa.Column('is_achieved', sa.Boolean(), server_default='false', comment='是否已实现'),
        sa.Column('support_count', sa.Integer(), server_default='0', comment='支持次数'),
        sa.Column('comment_count', sa.Integer(), server_default='0', comment='评论次数'),
        sa.Column('view_count', sa.Integer(), server_default='0', comment='浏览次数'),
        sa.Column('hot_score', sa.Float(), server_default='0.0', comment='热度分数'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.Column('achieved_at', sa.DateTime(), comment='实现时间'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建心愿评论表
    op.create_table('wish_comments',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('wish_id', sa.String(36), nullable=False, comment='心愿ID'),
        sa.Column('user_id', sa.String(36), nullable=False, comment='用户ID'),
        sa.Column('user_name', sa.String(100), nullable=False, comment='用户姓名'),
        sa.Column('user_avatar', sa.String(500), comment='用户头像'),
        sa.Column('content', sa.Text(), nullable=False, comment='评论内容'),
        sa.Column('like_count', sa.Integer(), server_default='0', comment='点赞次数'),
        sa.Column('is_liked', sa.Boolean(), server_default='false', comment='是否已点赞'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.ForeignKeyConstraint(['wish_id'], ['wishes.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建游戏活动表
    op.create_table('game_activities',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('name', sa.String(100), nullable=False, comment='游戏名称'),
        sa.Column('description', sa.Text(), comment='游戏描述'),
        sa.Column('type', sa.String(20), nullable=False, comment='游戏类型'),
        sa.Column('rules', sa.Text(), comment='游戏规则'),
        sa.Column('rewards', sa.JSON(), comment='奖励设置'),
        sa.Column('participant_count', sa.Integer(), server_default='0', comment='参与人数'),
        sa.Column('is_active', sa.Boolean(), server_default='true', comment='是否活跃'),
        sa.Column('start_date', sa.DateTime(), nullable=False, comment='开始时间'),
        sa.Column('end_date', sa.DateTime(), comment='结束时间'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建游戏参与者表
    op.create_table('game_participants',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('game_id', sa.String(36), nullable=False, comment='游戏ID'),
        sa.Column('user_id', sa.String(36), nullable=False, comment='用户ID'),
        sa.Column('user_name', sa.String(100), nullable=False, comment='用户姓名'),
        sa.Column('user_avatar', sa.String(500), comment='用户头像'),
        sa.Column('score', sa.Float(), server_default='0.0', comment='得分'),
        sa.Column('rank', sa.Integer(), comment='排名'),
        sa.Column('achievements', sa.JSON(), comment='成就列表'),
        sa.Column('participated_at', sa.DateTime(), server_default=sa.text('now()'), comment='参与时间'),
        sa.Column('last_played_at', sa.DateTime(), server_default=sa.text('now()'), comment='最后游戏时间'),
        sa.ForeignKeyConstraint(['game_id'], ['game_activities.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建索引
    op.create_index('idx_learning_resources_status', 'learning_resources', ['status'])
    op.create_index('idx_learning_resources_subject', 'learning_resources', ['subject'])
    op.create_index('idx_study_rooms_status', 'study_rooms', ['status'])
    op.create_index('idx_topics_status', 'topics', ['status'])
    op.create_index('idx_topics_category', 'topics', ['category_id'])
    op.create_index('idx_tribes_category', 'tribes', ['category'])
    op.create_index('idx_mentorship_relations_status', 'mentorship_relations', ['status'])
    op.create_index('idx_experiences_subject', 'experiences', ['subject'])
    op.create_index('idx_activities_status', 'activities', ['status'])
    op.create_index('idx_wishes_status', 'wishes', ['status'])
    op.create_index('idx_game_activities_active', 'game_activities', ['is_active'])


def downgrade():
    # 删除索引
    op.drop_index('idx_game_activities_active')
    op.drop_index('idx_wishes_status')
    op.drop_index('idx_activities_status')
    op.drop_index('idx_experiences_subject')
    op.drop_index('idx_mentorship_relations_status')
    op.drop_index('idx_tribes_category')
    op.drop_index('idx_topics_category')
    op.drop_index('idx_topics_status')
    op.drop_index('idx_study_rooms_status')
    op.drop_index('idx_learning_resources_subject')
    op.drop_index('idx_learning_resources_status')

    # 删除表
    op.drop_table('game_participants')
    op.drop_table('game_activities')
    op.drop_table('wish_comments')
    op.drop_table('wishes')
    op.drop_table('activity_participants')
    op.drop_table('activities')
    op.drop_table('experiences')
    op.drop_table('mentorship_relations')
    op.drop_table('mentor_profiles')
    op.drop_table('tribe_members')
    op.drop_table('tribes')
    op.drop_table('topic_replies')
    op.drop_table('topics')
    op.drop_table('study_room_members')
    op.drop_table('study_rooms')
    op.drop_table('learning_resources')
