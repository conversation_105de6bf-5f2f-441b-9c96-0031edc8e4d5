"""
WisCude 后台管理系统 - 数据同步服务
"""
import sqlite3
import asyncio
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.core.database import get_db, AndroidDatabaseManager
from app.models import (
    WiscudeUser, StudySession, CheckIn, CommunityPost,
    CourseEnrollment, CourseEnrollment, SyncLog, AILearningRecord
)
from app.core.config import settings
import logging
import json

logger = logging.getLogger(__name__)

class DataSyncService:
    """数据同步服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.android_db = AndroidDatabaseManager(settings.ANDROID_DB_PATH)
        self.sync_stats = {
            "users": {"processed": 0, "inserted": 0, "updated": 0, "failed": 0},
            "study_sessions": {"processed": 0, "inserted": 0, "updated": 0, "failed": 0},
            "check_ins": {"processed": 0, "inserted": 0, "updated": 0, "failed": 0},
            "community_posts": {"processed": 0, "inserted": 0, "updated": 0, "failed": 0},
            "courses": {"processed": 0, "inserted": 0, "updated": 0, "failed": 0},
            "ai_learning": {"processed": 0, "inserted": 0, "updated": 0, "failed": 0},
        }
    
    async def sync_all_data(self) -> Dict[str, Any]:
        """同步所有数据"""
        sync_log = SyncLog(
            sync_type="full",
            status="running",
            started_at=datetime.utcnow()
        )
        self.db.add(sync_log)
        self.db.commit()
        
        try:
            logger.info("开始全量数据同步")
            
            # 检查Android数据库连接
            with self.android_db as android_conn:
                # 按依赖顺序同步数据
                await self._sync_users(android_conn)
                await self._sync_courses(android_conn)
                await self._sync_study_sessions(android_conn)
                await self._sync_check_ins(android_conn)
                await self._sync_community_posts(android_conn)
                await self._sync_ai_learning_records(android_conn)
            
            # 更新同步日志
            sync_log.status = "success"
            sync_log.completed_at = datetime.utcnow()
            sync_log.duration_seconds = int(
                (sync_log.completed_at - sync_log.started_at).total_seconds()
            )
            sync_log.details = json.dumps(self.sync_stats)
            
            # 计算总统计
            total_stats = {
                "processed": sum(stats["processed"] for stats in self.sync_stats.values()),
                "inserted": sum(stats["inserted"] for stats in self.sync_stats.values()),
                "updated": sum(stats["updated"] for stats in self.sync_stats.values()),
                "failed": sum(stats["failed"] for stats in self.sync_stats.values()),
            }
            
            sync_log.records_processed = total_stats["processed"]
            sync_log.records_inserted = total_stats["inserted"]
            sync_log.records_updated = total_stats["updated"]
            sync_log.records_failed = total_stats["failed"]
            
            self.db.commit()
            logger.info(f"数据同步完成: {total_stats}")
            
            return {
                "status": "success",
                "stats": self.sync_stats,
                "total": total_stats,
                "duration": sync_log.duration_seconds
            }
            
        except Exception as e:
            logger.error(f"数据同步失败: {e}")
            sync_log.status = "failed"
            sync_log.completed_at = datetime.utcnow()
            sync_log.error_message = str(e)
            self.db.commit()
            
            return {
                "status": "failed",
                "error": str(e),
                "stats": self.sync_stats
            }
    
    async def _sync_users(self, android_conn):
        """同步用户数据"""
        logger.info("同步用户数据...")
        
        try:
            # 查询Android数据库中的用户
            users_data = android_conn.execute_query("""
                SELECT id, username, email, phone, full_name, avatar_url,
                       gender, birth_date, bio, location, is_active, is_premium,
                       total_study_time, total_focus_sessions, level, experience_points,
                       created_at, updated_at, last_login
                FROM users
            """)
            
            for user_row in users_data:
                self.sync_stats["users"]["processed"] += 1
                
                try:
                    # 检查用户是否已存在
                    existing_user = self.db.query(WiscudeUser).filter_by(
                        android_user_id=user_row["id"]
                    ).first()
                    
                    if existing_user:
                        # 更新现有用户
                        self._update_user_from_row(existing_user, user_row)
                        self.sync_stats["users"]["updated"] += 1
                    else:
                        # 创建新用户
                        new_user = self._create_user_from_row(user_row)
                        self.db.add(new_user)
                        self.sync_stats["users"]["inserted"] += 1
                    
                    self.db.commit()
                    
                except Exception as e:
                    logger.error(f"同步用户 {user_row['id']} 失败: {e}")
                    self.sync_stats["users"]["failed"] += 1
                    self.db.rollback()
                    
        except Exception as e:
            logger.error(f"查询用户数据失败: {e}")
            raise
    
    def _create_user_from_row(self, row) -> WiscudeUser:
        """从数据行创建用户"""
        return WiscudeUser(
            android_user_id=row["id"],
            username=row["username"],
            email=row["email"],
            phone=row["phone"],
            full_name=row["full_name"],
            avatar_url=row["avatar_url"],
            gender=row["gender"] or "unknown",
            birth_date=self._parse_date(row["birth_date"]),
            bio=row["bio"],
            location=row["location"],
            is_active=bool(row["is_active"]),
            is_premium=bool(row["is_premium"]),
            total_study_time=row["total_study_time"] or 0,
            total_focus_sessions=row["total_focus_sessions"] or 0,
            level=row["level"] or 1,
            experience_points=row["experience_points"] or 0,
            registration_date=self._parse_datetime(row["created_at"]) or datetime.utcnow(),
            last_login=self._parse_datetime(row["last_login"])
        )
    
    def _update_user_from_row(self, user: WiscudeUser, row):
        """从数据行更新用户"""
        user.username = row["username"]
        user.email = row["email"]
        user.phone = row["phone"]
        user.full_name = row["full_name"]
        user.avatar_url = row["avatar_url"]
        user.gender = row["gender"] or "unknown"
        user.birth_date = self._parse_date(row["birth_date"])
        user.bio = row["bio"]
        user.location = row["location"]
        user.is_active = bool(row["is_active"])
        user.is_premium = bool(row["is_premium"])
        user.total_study_time = row["total_study_time"] or 0
        user.total_focus_sessions = row["total_focus_sessions"] or 0
        user.level = row["level"] or 1
        user.experience_points = row["experience_points"] or 0
        user.last_login = self._parse_datetime(row["last_login"])
        user.updated_at = datetime.utcnow()
    
    async def _sync_study_sessions(self, android_conn):
        """同步学习会话数据"""
        logger.info("同步学习会话数据...")
        
        try:
            sessions_data = android_conn.execute_query("""
                SELECT id, user_id, session_type, title, subject, duration,
                       start_time, end_time, interruptions, productivity_score,
                       notes, is_completed, completion_percentage, created_at
                FROM study_sessions
            """)
            
            for session_row in sessions_data:
                self.sync_stats["study_sessions"]["processed"] += 1
                
                try:
                    # 查找对应的用户
                    user = self.db.query(WiscudeUser).filter_by(
                        android_user_id=session_row["user_id"]
                    ).first()
                    
                    if not user:
                        logger.warning(f"用户 {session_row['user_id']} 不存在，跳过会话 {session_row['id']}")
                        self.sync_stats["study_sessions"]["failed"] += 1
                        continue
                    
                    # 检查会话是否已存在
                    existing_session = self.db.query(StudySession).filter_by(
                        android_session_id=session_row["id"]
                    ).first()
                    
                    if existing_session:
                        # 更新现有会话
                        self._update_session_from_row(existing_session, session_row, user.id)
                        self.sync_stats["study_sessions"]["updated"] += 1
                    else:
                        # 创建新会话
                        new_session = self._create_session_from_row(session_row, user.id)
                        self.db.add(new_session)
                        self.sync_stats["study_sessions"]["inserted"] += 1
                    
                    self.db.commit()
                    
                except Exception as e:
                    logger.error(f"同步学习会话 {session_row['id']} 失败: {e}")
                    self.sync_stats["study_sessions"]["failed"] += 1
                    self.db.rollback()
                    
        except Exception as e:
            logger.error(f"查询学习会话数据失败: {e}")
            raise
    
    def _create_session_from_row(self, row, user_id: str) -> StudySession:
        """从数据行创建学习会话"""
        return StudySession(
            android_session_id=row["id"],
            user_id=user_id,
            session_type=row["session_type"],
            title=row["title"],
            subject=row["subject"],
            duration=row["duration"],
            start_time=self._parse_datetime(row["start_time"]) or datetime.utcnow(),
            end_time=self._parse_datetime(row["end_time"]),
            interruptions=row["interruptions"] or 0,
            productivity_score=row["productivity_score"],
            notes=row["notes"],
            is_completed=bool(row["is_completed"]),
            completion_percentage=row["completion_percentage"] or 0
        )
    
    def _update_session_from_row(self, session: StudySession, row, user_id: str):
        """从数据行更新学习会话"""
        session.session_type = row["session_type"]
        session.title = row["title"]
        session.subject = row["subject"]
        session.duration = row["duration"]
        session.start_time = self._parse_datetime(row["start_time"]) or session.start_time
        session.end_time = self._parse_datetime(row["end_time"])
        session.interruptions = row["interruptions"] or 0
        session.productivity_score = row["productivity_score"]
        session.notes = row["notes"]
        session.is_completed = bool(row["is_completed"])
        session.completion_percentage = row["completion_percentage"] or 0
        session.updated_at = datetime.utcnow()
    
    async def _sync_check_ins(self, android_conn):
        """同步打卡数据"""
        logger.info("同步打卡数据...")
        # 实现类似的同步逻辑...
        pass
    
    async def _sync_community_posts(self, android_conn):
        """同步社区帖子数据"""
        logger.info("同步社区帖子数据...")
        # 实现类似的同步逻辑...
        pass
    
    async def _sync_courses(self, android_conn):
        """同步课程数据"""
        logger.info("同步课程数据...")
        # 实现类似的同步逻辑...
        pass
    
    async def _sync_ai_learning_records(self, android_conn):
        """同步AI学习记录"""
        logger.info("同步AI学习记录...")
        # 实现类似的同步逻辑...
        pass
    
    def _parse_datetime(self, date_str: str) -> Optional[datetime]:
        """解析日期时间字符串"""
        if not date_str:
            return None
        
        try:
            # 尝试不同的日期格式
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%d %H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S.%fZ"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            
            logger.warning(f"无法解析日期时间: {date_str}")
            return None
            
        except Exception as e:
            logger.error(f"解析日期时间失败: {e}")
            return None
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """解析日期字符串"""
        if not date_str:
            return None
        
        try:
            return datetime.strptime(date_str, "%Y-%m-%d").date()
        except Exception as e:
            logger.error(f"解析日期失败: {e}")
            return None

async def create_sync_service(db: Session) -> DataSyncService:
    """创建数据同步服务实例"""
    return DataSyncService(db)
