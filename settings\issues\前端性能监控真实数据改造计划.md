# 前端性能监控真实数据改造执行计划

## 执行概述

**目标：** 将前端性能监控中的所有模拟数据替换为浏览器原生API获取的真实数据

**方案：** 使用浏览器原生API（方案2）

**预期完成时间：** 1-2小时

## 详细执行步骤

### 步骤1：创建真实数据获取工具函数

**文件：** `frontend/src/utils/systemInfo.ts`

**功能：** 创建专门的工具函数来获取浏览器可访问的真实系统信息

**实现内容：**
- `getRealCpuInfo()` - 获取CPU信息
- `getRealMemoryInfo()` - 获取内存信息
- `getRealNetworkInfo()` - 获取网络信息
- `getRealStorageInfo()` - 获取存储信息
- `estimateCpuUsage()` - 估算CPU使用率
- 兼容性检查和降级处理

**预期结果：** 完整的系统信息获取工具库

### 步骤2：修改 getRealTimeMetrics() 方法

**文件：** `frontend/src/services/performanceService.ts`

**修改内容：**
- 移除 `mockApiCall` 调用
- 使用 `systemInfo` 工具获取真实数据
- 保持数据结构不变
- 添加错误处理和降级机制

**具体修改：**
```typescript
// 替换前：使用 Math.random() 生成模拟数据
// 替换后：使用 systemInfo.getRealCpuInfo() 等获取真实数据
```

**预期结果：** CPU、内存、网络数据变为真实数据

### 步骤3：修改 getSystemHealth() 方法

**文件：** `frontend/src/services/performanceService.ts`

**修改内容：**
- 基于真实系统信息动态生成健康状态
- 使用真实的性能指标判断系统状态
- 保持组件结构，但状态基于真实数据

**健康状态判断逻辑：**
- 内存使用率 > 90% → 错误状态
- 内存使用率 > 70% → 警告状态
- 网络延迟 > 200ms → 警告状态
- 其他情况 → 正常状态

**预期结果：** 系统健康状态基于真实性能数据

### 步骤4：检查并修改其他方法

**需要检查的方法：**
- `getQuickStats()` - 快速统计
- `getAlerts()` - 告警信息
- `generateHistoricalData()` - 历史数据生成

**修改原则：**
- 能获取真实数据的使用真实数据
- 无法获取的使用合理的计算值
- 避免使用随机数

**预期结果：** 所有方法都使用真实或计算得出的数据

### 步骤5：添加浏览器兼容性处理

**实现内容：**
- 检测浏览器API支持情况
- 提供降级方案
- 添加用户友好的提示信息

**兼容性策略：**
```typescript
// 如果浏览器不支持某个API，使用默认值或计算值
if (!('memory' in performance)) {
  // 使用默认内存信息或其他方式估算
}
```

**预期结果：** 在不同浏览器中都能正常工作

### 步骤6：更新类型定义（如需要）

**文件：** `frontend/src/types/performance.ts`

**可能需要的修改：**
- 添加新的系统信息类型
- 更新现有接口以支持真实数据
- 添加浏览器兼容性相关类型

**预期结果：** 类型定义与实际实现保持一致

### 步骤7：测试验证

**测试内容：**
1. **功能测试：**
   - 访问性能监控页面
   - 验证数据显示正常
   - 检查数据是否为真实值

2. **兼容性测试：**
   - 在不同浏览器中测试
   - 验证降级机制工作正常

3. **性能测试：**
   - 检查页面加载性能
   - 验证实时更新功能

4. **错误处理测试：**
   - 模拟API不可用情况
   - 验证错误提示和降级处理

**预期结果：** 所有功能正常，数据真实可靠

### 步骤8：文档更新

**更新内容：**
- 修复完成报告
- API文档（如有变化）
- 用户使用说明

**预期结果：** 文档与实际功能保持同步

## 技术实现要点

### 1. CPU使用率估算方法

```typescript
// 通过执行计算密集型任务来估算CPU性能
function estimateCpuUsage(): number {
  const start = performance.now()
  // 执行标准计算任务
  let result = 0
  for (let i = 0; i < 100000; i++) {
    result += Math.sqrt(i)
  }
  const duration = performance.now() - start
  
  // 基于执行时间估算CPU使用率
  // 这是一个相对值，需要根据基准测试校准
  return Math.min(100, Math.max(0, (duration - 5) * 10))
}
```

### 2. 内存信息获取

```typescript
function getRealMemoryInfo() {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
      available: Math.round((memory.jsHeapSizeLimit - memory.usedJSHeapSize) / 1024 / 1024),
      usage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
    }
  }
  return null
}
```

### 3. 网络信息获取

```typescript
function getRealNetworkInfo() {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    return {
      type: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false
    }
  }
  return null
}
```

### 4. 存储信息获取

```typescript
async function getRealStorageInfo() {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate()
      return {
        quota: Math.round((estimate.quota || 0) / 1024 / 1024), // MB
        usage: Math.round((estimate.usage || 0) / 1024 / 1024), // MB
        available: Math.round(((estimate.quota || 0) - (estimate.usage || 0)) / 1024 / 1024)
      }
    } catch (error) {
      console.warn('获取存储信息失败:', error)
    }
  }
  return null
}
```

## 风险评估

### 低风险
- 浏览器API兼容性问题 → 已提供降级方案
- 数据格式变化 → 保持现有数据结构

### 中等风险
- CPU使用率估算不准确 → 使用相对值和基准校准
- 某些浏览器不支持部分API → 提供默认值

### 缓解措施
- 完整的兼容性检查
- 详细的错误处理
- 用户友好的降级提示

## 验收标准

1. ✅ 所有模拟数据已替换为真实数据
2. ✅ 页面功能正常，无控制台错误
3. ✅ 数据显示真实且合理
4. ✅ 在主流浏览器中兼容性良好
5. ✅ 错误处理和降级机制工作正常
6. ✅ 性能监控功能保持稳定

---

**准备开始执行：** 请确认是否开始按此计划执行真实数据改造