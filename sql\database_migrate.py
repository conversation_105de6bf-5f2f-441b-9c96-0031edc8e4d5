#!/usr/bin/env python3
"""
WisCude 后台管理系统 - SQLite到PostgreSQL迁移脚本
"""

import os
import sys
import logging
import sqlite3
import psycopg2
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent / "backend"))

from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """
    数据库迁移器：从SQLite迁移到PostgreSQL
    """
    
    def __init__(self, sqlite_path: str, postgresql_url: str):
        self.sqlite_path = sqlite_path
        self.postgresql_url = postgresql_url
        self.sqlite_conn = None
        self.pg_conn = None
        
        # 表映射配置
        self.table_mappings = {
            'admin': 'admin',
            'user': '"user"',  # PostgreSQL中user是保留字，需要引号
            'login_logs': 'login_logs',
            'system_logs': 'system_logs',
            'study_sessions': 'study_sessions',
            'check_ins': 'check_ins',
            'ai_learning_records': 'ai_learning_records',
            'community_posts': 'community_posts',
            'post_comments': 'post_comments',
            'courses': 'courses',
            'course_enrollments': 'course_enrollments',
            'sync_logs': 'sync_logs'
        }
        
        # 字段类型映射
        self.type_mappings = {
            'INTEGER': 'INTEGER',
            'TEXT': 'TEXT',
            'REAL': 'FLOAT',
            'BLOB': 'BYTEA',
            'BOOLEAN': 'BOOLEAN',
            'DATETIME': 'TIMESTAMP',
            'DATE': 'DATE'
        }
    
    def connect_databases(self) -> bool:
        """
        连接到SQLite和PostgreSQL数据库
        """
        try:
            # 连接SQLite
            if os.path.exists(self.sqlite_path):
                self.sqlite_conn = sqlite3.connect(self.sqlite_path)
                self.sqlite_conn.row_factory = sqlite3.Row
                logger.info(f"成功连接到SQLite数据库: {self.sqlite_path}")
            else:
                logger.warning(f"SQLite数据库文件不存在: {self.sqlite_path}")
                return False
            
            # 连接PostgreSQL
            url_parts = self.postgresql_url.replace('postgresql://', '').split('/')
            db_name = url_parts[-1]
            connection_info = url_parts[0]
            
            user_pass, host_port = connection_info.split('@')
            user, password = user_pass.split(':')
            host, port = host_port.split(':')
            
            self.pg_conn = psycopg2.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=db_name
            )
            logger.info("成功连接到PostgreSQL数据库")
            
            return True
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def get_sqlite_tables(self) -> List[str]:
        """
        获取SQLite数据库中的所有表
        """
        try:
            cursor = self.sqlite_conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"SQLite数据库中的表: {tables}")
            return tables
        except Exception as e:
            logger.error(f"获取SQLite表列表失败: {e}")
            return []
    
    def get_table_data(self, table_name: str) -> List[Dict[str, Any]]:
        """
        从SQLite表中获取所有数据
        """
        try:
            cursor = self.sqlite_conn.cursor()
            cursor.execute(f"SELECT * FROM {table_name}")
            rows = cursor.fetchall()
            
            # 转换为字典列表
            data = []
            for row in rows:
                row_dict = dict(row)
                # 处理特殊数据类型
                for key, value in row_dict.items():
                    if isinstance(value, str):
                        # 尝试解析JSON字符串
                        if value.startswith('{') or value.startswith('['):
                            try:
                                row_dict[key] = json.loads(value)
                            except:
                                pass
                data.append(row_dict)
            
            logger.info(f"从表 {table_name} 获取了 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取表 {table_name} 数据失败: {e}")
            return []
    
    def insert_data_to_postgresql(self, table_name: str, data: List[Dict[str, Any]]) -> bool:
        """
        将数据插入到PostgreSQL表中
        """
        if not data:
            logger.info(f"表 {table_name} 没有数据需要迁移")
            return True
            
        try:
            pg_table_name = self.table_mappings.get(table_name, table_name)
            cursor = self.pg_conn.cursor()
            
            # 获取第一行数据的键作为列名
            columns = list(data[0].keys())
            
            # 构建插入SQL
            placeholders = ', '.join(['%s'] * len(columns))
            columns_str = ', '.join([f'"{col}"' for col in columns])
            
            insert_sql = f"""
                INSERT INTO {pg_table_name} ({columns_str}) 
                VALUES ({placeholders})
                ON CONFLICT DO NOTHING
            """
            
            # 批量插入数据
            success_count = 0
            for row in data:
                try:
                    values = []
                    for col in columns:
                        value = row[col]
                        # 处理JSON数据
                        if isinstance(value, (dict, list)):
                            value = json.dumps(value)
                        values.append(value)
                    
                    cursor.execute(insert_sql, values)
                    success_count += 1
                    
                except Exception as row_error:
                    logger.warning(f"插入行数据失败: {row_error}")
                    continue
            
            self.pg_conn.commit()
            logger.info(f"成功迁移表 {table_name}: {success_count}/{len(data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"迁移表 {table_name} 失败: {e}")
            self.pg_conn.rollback()
            return False
    
    def migrate_table(self, table_name: str) -> bool:
        """
        迁移单个表
        """
        logger.info(f"开始迁移表: {table_name}")
        
        # 获取SQLite表数据
        data = self.get_table_data(table_name)
        
        # 插入到PostgreSQL
        return self.insert_data_to_postgresql(table_name, data)
    
    def migrate_all_tables(self) -> bool:
        """
        迁移所有表
        """
        try:
            sqlite_tables = self.get_sqlite_tables()
            
            # 过滤系统表
            user_tables = [t for t in sqlite_tables if not t.startswith('sqlite_')]
            
            success_count = 0
            total_count = len(user_tables)
            
            for table_name in user_tables:
                if table_name in self.table_mappings:
                    if self.migrate_table(table_name):
                        success_count += 1
                else:
                    logger.warning(f"跳过未映射的表: {table_name}")
            
            logger.info(f"迁移完成: {success_count}/{total_count} 个表成功迁移")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"批量迁移失败: {e}")
            return False
    
    def close_connections(self):
        """
        关闭数据库连接
        """
        if self.sqlite_conn:
            self.sqlite_conn.close()
            logger.info("SQLite连接已关闭")
        
        if self.pg_conn:
            self.pg_conn.close()
            logger.info("PostgreSQL连接已关闭")
    
    def create_backup(self) -> str:
        """
        创建SQLite数据库备份
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backups/sqlite_backup_{timestamp}.db"
            
            # 确保备份目录存在
            os.makedirs("backups", exist_ok=True)
            
            # 复制数据库文件
            import shutil
            shutil.copy2(self.sqlite_path, backup_path)
            
            logger.info(f"SQLite数据库备份已创建: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return ""
    
    def drop_all_postgresql_tables(self) -> bool:
        """
        删除PostgreSQL数据库中的所有表、序列、枚举类型和函数
        """
        try:
            cursor = self.pg_conn.cursor()
            
            # 删除所有表（包括依赖关系）
            logger.info("正在删除所有表...")
            cursor.execute("""
                DO $$ DECLARE
                    r RECORD;
                BEGIN
                    -- 删除所有表
                    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
                        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
                    END LOOP;
                END $$;
            """)
            
            # 删除所有序列
            logger.info("正在删除所有序列...")
            cursor.execute("""
                DO $$ DECLARE
                    r RECORD;
                BEGIN
                    -- 删除所有序列
                    FOR r IN (SELECT sequencename FROM pg_sequences WHERE schemaname = 'public') LOOP
                        EXECUTE 'DROP SEQUENCE IF EXISTS ' || quote_ident(r.sequencename) || ' CASCADE';
                    END LOOP;
                END $$;
            """)
            
            # 删除所有枚举类型
            logger.info("正在删除所有枚举类型...")
            cursor.execute("""
                DO $$ DECLARE
                    r RECORD;
                BEGIN
                    -- 删除所有枚举类型
                    FOR r IN (SELECT typname FROM pg_type WHERE typtype = 'e' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) LOOP
                        EXECUTE 'DROP TYPE IF EXISTS ' || quote_ident(r.typname) || ' CASCADE';
                    END LOOP;
                END $$;
            """)
            
            # 删除所有函数
            logger.info("正在删除所有函数...")
            cursor.execute("""
                DO $$ DECLARE
                    r RECORD;
                BEGIN
                    -- 删除所有函数
                    FOR r IN (SELECT proname, oidvectortypes(proargtypes) as argtypes 
                             FROM pg_proc 
                             WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) LOOP
                        EXECUTE 'DROP FUNCTION IF EXISTS ' || quote_ident(r.proname) || '(' || r.argtypes || ') CASCADE';
                    END LOOP;
                END $$;
            """)
            
            self.pg_conn.commit()
            logger.info("成功删除所有PostgreSQL数据库对象")
            return True
            
        except Exception as e:
            logger.error(f"删除PostgreSQL数据库对象失败: {e}")
            self.pg_conn.rollback()
            return False

def drop_all_tables():
    """
    删除PostgreSQL数据库中的所有表
    """
    logger.info("WisCude PostgreSQL数据库清理工具")
    
    # 创建迁移器（只需要PostgreSQL连接）
    migrator = DatabaseMigrator("", settings.DATABASE_URL)
    
    try:
        # 只连接PostgreSQL
        url_parts = migrator.postgresql_url.replace('postgresql://', '').split('/')
        db_name = url_parts[-1]
        connection_info = url_parts[0]
        
        user_pass, host_port = connection_info.split('@')
        user, password = user_pass.split(':')
        host, port = host_port.split(':')
        
        migrator.pg_conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=db_name
        )
        logger.info("成功连接到PostgreSQL数据库")
        
        # 用户确认
        print(f"\n⚠️  警告：即将删除数据库 '{db_name}' 中的所有表、序列、枚举类型和函数！")
        print("这个操作不可逆转，请确保您已经备份了重要数据。")
        
        confirm = input("\n确认删除所有数据库对象？(输入 'YES' 确认): ")
        
        if confirm != 'YES':
            print("\n❌ 操作已取消")
            return 1
        
        # 执行删除
        print("\n🗑️  开始删除所有数据库对象...")
        if migrator.drop_all_postgresql_tables():
            print("\n✅ 成功删除所有PostgreSQL数据库对象！")
            print("\n📋 后续步骤:")
            print("1. 可以重新运行 database.sql 脚本来重建数据库结构")
            print("2. 或者运行数据迁移工具来从SQLite导入数据")
            return 0
        else:
            print("\n❌ 删除操作失败！")
            return 1
            
    except Exception as e:
        logger.error(f"删除过程中发生错误: {e}")
        print(f"\n💥 删除失败: {e}")
        return 1
        
    finally:
        if migrator.pg_conn:
            migrator.pg_conn.close()
            logger.info("PostgreSQL连接已关闭")

def main():
    """
    主函数
    """
    logger.info("WisCude SQLite到PostgreSQL迁移工具")
    
    # 查找SQLite数据库文件
    sqlite_paths = [
        "wiscude.db",
        "../wiscude.db",
        settings.ANDROID_DB_PATH
    ]
    
    sqlite_path = None
    for path in sqlite_paths:
        if os.path.exists(path):
            sqlite_path = path
            break
    
    if not sqlite_path:
        logger.error("找不到SQLite数据库文件")
        print("\n❌ 找不到SQLite数据库文件！")
        print("\n请确保以下路径之一存在SQLite数据库:")
        for path in sqlite_paths:
            print(f"  - {path}")
        return 1
    
    # 创建迁移器
    migrator = DatabaseMigrator(sqlite_path, settings.DATABASE_URL)
    
    try:
        # 连接数据库
        if not migrator.connect_databases():
            return 1
        
        # 创建备份
        backup_path = migrator.create_backup()
        if backup_path:
            print(f"\n📦 SQLite数据库备份已创建: {backup_path}")
        
        # 执行迁移
        print("\n🚀 开始数据迁移...")
        if migrator.migrate_all_tables():
            print("\n✅ 数据迁移成功完成！")
            print("\n📋 迁移完成后的步骤:")
            print("1. 验证PostgreSQL数据库中的数据")
            print("2. 启动后端服务测试功能")
            print("3. 确认无误后可删除SQLite备份文件")
            return 0
        else:
            print("\n❌ 数据迁移失败！")
            return 1
            
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")
        print(f"\n💥 迁移失败: {e}")
        return 1
        
    finally:
        migrator.close_connections()

if __name__ == "__main__":
    import sys
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--drop-all":
        sys.exit(drop_all_tables())
    else:
        sys.exit(main())