"""
WisCude 后台管理系统 - 管理员用户模型
"""
from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, JSON, Integer
from app.models.base import BaseModel
from app.core.security import get_password_hash, verify_password
import enum

class UserRole(str, enum.Enum):
    """用户角色枚举"""
    SUPERADMIN = "superadmin"  # 超级管理员
    ADMIN = "admin"  # 管理员
    EDITOR = "editor"  # 编辑
    VIEWER = "viewer"  # 只读用户

class AdminUser(BaseModel):
    """管理员用户模型"""
    __tablename__ = "admin_users"
    
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(100), nullable=False)
    full_name = Column(String(100), nullable=True)
    role = Column(Enum(UserRole), default=UserRole.VIEWER, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    # 个人资料字段
    phone = Column(String(20), nullable=True)
    department = Column(String(100), nullable=True)
    position = Column(String(100), nullable=True)
    bio = Column(Text, nullable=True)
    avatar = Column(String(255), nullable=True)  # 头像文件路径
    avatar_url = Column(String(255), nullable=True)  # 头像URL（兼容性）
    
    # 双因素认证
    two_factor_enabled = Column(Boolean, default=False, nullable=False)
    two_factor_secret = Column(String(255), nullable=True)
    two_factor_backup_codes = Column(JSON, nullable=True)
    
    # 偏好设置
    preferences = Column(JSON, nullable=True)
    
    # 安全相关
    password_changed_at = Column(DateTime, nullable=True)
    login_count = Column(Integer, default=0, nullable=False)
    
    def set_password(self, password: str):
        """设置密码"""
        self.hashed_password = get_password_hash(password)
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return verify_password(password, self.hashed_password)
    
    def is_admin(self) -> bool:
        """是否为管理员"""
        return self.role in [UserRole.ADMIN, UserRole.SUPERADMIN] or self.is_superuser
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 移除敏感信息
        data.pop("hashed_password", None)
        return data
    
    def __repr__(self):
        return f"<AdminUser {self.username}>"

class LoginLog(BaseModel):
    """登录日志模型"""
    __tablename__ = "login_logs"
    
    user_id = Column(String(36), index=True, nullable=True)
    username = Column(String(50), index=True, nullable=False)
    ip_address = Column(String(50), nullable=True)
    user_agent = Column(String(255), nullable=True)
    status = Column(Boolean, default=True, nullable=False)  # 成功或失败
    message = Column(String(255), nullable=True)
    
    def __repr__(self):
        return f"<LoginLog {self.username} {self.created_at}>"

class SystemLog(BaseModel):
    """系统日志模型"""
    __tablename__ = "system_logs"

    user_id = Column(String(36), index=True, nullable=True)
    username = Column(String(50), index=True, nullable=True)
    action = Column(String(100), nullable=False, index=True)
    module = Column(String(50), nullable=False, index=True)
    level = Column(String(20), nullable=False, default="INFO", index=True)
    message = Column(Text, nullable=False)
    details = Column(Text, nullable=True)
    ip_address = Column(String(50), nullable=True)

    def __repr__(self):
        return f"<SystemLog {self.action} {self.created_at}>"
