<template>
  <div class="question-bank-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>题库管理概览</h1>
          <p>全面管理题目资源，智能组卷，数据分析</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建题目
          </el-button>
          <el-button @click="showImportDialog = true">
            <el-icon><Upload /></el-icon>
            导入题库
          </el-button>
          <el-button @click="generatePaper">
            <el-icon><Document /></el-icon>
            智能组卷
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card primary">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(statistics.totalQuestions) }}</div>
                <div class="stat-label">题目总数</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  {{ statistics.questionGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.categories }}</div>
                <div class="stat-label">题目分类</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  {{ statistics.categoryGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.papers }}</div>
                <div class="stat-label">试卷数量</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  {{ statistics.paperGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card info">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.accuracy }}%</div>
                <div class="stat-label">平均正确率</div>
                <div class="stat-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  {{ statistics.accuracyGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作区 -->
    <div class="quick-actions">
      <el-row :gutter="24">
        <el-col :xs="24" :md="8">
          <el-card class="action-card" @click="navigateTo('/question-bank/questions')">
            <div class="action-content">
              <div class="action-icon">
                <el-icon><EditPen /></el-icon>
              </div>
              <div class="action-info">
                <h3>题目管理</h3>
                <p>添加、编辑、分类管理题目</p>
              </div>
              <div class="action-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :md="8">
          <el-card class="action-card" @click="navigateTo('/question-bank/papers')">
            <div class="action-content">
              <div class="action-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="action-info">
                <h3>试卷管理</h3>
                <p>创建、编辑、发布试卷</p>
              </div>
              <div class="action-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :md="8">
          <el-card class="action-card" @click="navigateTo('/question-bank/analytics')">
            <div class="action-content">
              <div class="action-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="action-info">
                <h3>答题统计</h3>
                <p>查看答题数据和分析报告</p>
              </div>
              <div class="action-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据图表区 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <!-- 题目分类分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>题目分类分布</span>
            </template>
            <div class="chart-container">
              <PieChart
                :data="categoryDistributionData"
                :options="categoryDistributionOptions"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 难度分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>题目难度分布</span>
            </template>
            <div class="chart-container">
              <BarChart
                :data="difficultyDistributionData"
                :x-axis-data="difficultyDistributionXAxisData"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 答题正确率趋势 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>答题正确率趋势</span>
                <el-select v-model="accuracyPeriod" size="small" @change="updateAccuracyTrend">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <LineChart
                :data="accuracyTrendData"
                :x-axis-data="accuracyTrendXAxisData"
                height="350px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 热门题目排行 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>热门题目排行</span>
            </template>
            <div class="hot-questions">
              <div
                v-for="(question, index) in hotQuestions"
                :key="question.id"
                class="question-item"
                :class="getRankClass(index)"
              >
                <div class="rank">{{ index + 1 }}</div>
                <div class="question-info">
                  <div class="question-title">{{ question.title }}</div>
                  <div class="question-meta">
                    <span class="category">{{ question.category }}</span>
                    <span class="attempts">{{ question.attempts }}次答题</span>
                  </div>
                </div>
                <div class="accuracy">{{ question.accuracy }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>最近活动</span>
            <el-button text @click="viewAllActivities">查看全部</el-button>
          </div>
        </template>
        <div class="activities-list">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon" :class="getActivityIconClass(activity.type)">
              <el-icon>
                <component :is="getActivityIcon(activity.type)" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-description">{{ activity.description }}</div>
            </div>
            <div class="activity-time">{{ formatTime(activity.created_at) }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 待办事项 -->
    <div class="todo-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>待办事项</span>
            <el-badge :value="pendingTodos.length" type="primary" />
          </div>
        </template>
        <div class="todo-list">
          <div
            v-for="todo in pendingTodos"
            :key="todo.id"
            class="todo-item"
            :class="{ urgent: todo.urgent }"
          >
            <el-checkbox v-model="todo.completed" @change="updateTodo(todo)" />
            <div class="todo-content">
              <div class="todo-title">{{ todo.title }}</div>
              <div class="todo-meta">
                <span class="due-date">{{ formatDate(todo.due_date) }}</span>
                <el-tag v-if="todo.urgent" type="danger" size="small">紧急</el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus, Upload, Document, Collection, FolderOpened, TrendCharts,
  ArrowUp, EditPen, ArrowRight, DataAnalysis
} from '@element-plus/icons-vue'
import { PieChart, BarChart, LineChart } from '@/components/charts'

// 路由
const router = useRouter()

// 响应式数据
const showCreateDialog = ref(false)
const showImportDialog = ref(false)
const accuracyPeriod = ref('30d')

// 统计数据
const statistics = reactive({
  totalQuestions: 2847,
  categories: 12,
  papers: 156,
  accuracy: 78.5,
  questionGrowth: 15.2,
  categoryGrowth: 8.3,
  paperGrowth: 22.1,
  accuracyGrowth: 5.7
})

// 题目分类分布数据
const categoryDistributionData = ref([
  { name: '数学', value: 25 },
  { name: '语文', value: 20 },
  { name: '英语', value: 18 },
  { name: '物理', value: 12 },
  { name: '化学', value: 10 },
  { name: '生物', value: 8 },
  { name: '历史', value: 4 },
  { name: '地理', value: 3 }
])

const categoryDistributionOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    }
  }
})

// 难度分布数据
const difficultyDistributionData = ref([
  {
    name: '题目数量',
    data: [
      { name: '简单', value: 45 },
      { name: '中等', value: 35 },
      { name: '困难', value: 15 },
      { name: '极难', value: 5 }
    ],
    color: '#4f46e5'
  }
])

const difficultyDistributionXAxisData = ref(['简单', '中等', '困难', '极难'])

const difficultyDistributionOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      title: {
        display: true,
        text: '题目数量'
      }
    }
  }
})

// 答题正确率趋势数据
const accuracyTrendData = ref([
  {
    name: '整体正确率',
    data: [] as number[],
    color: '#4f46e5',
    smooth: true,
    area: true
  },
  {
    name: '数学正确率',
    data: [] as number[],
    color: '#10b981',
    smooth: true,
    area: true
  },
  {
    name: '英语正确率',
    data: [] as number[],
    color: '#f59e0b',
    smooth: true,
    area: true
  }
])

const accuracyTrendXAxisData = ref([] as string[])

const accuracyTrendOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      title: {
        display: true,
        text: '正确率 (%)'
      }
    }
  }
})

// 热门题目数据
const hotQuestions = ref([
  {
    id: '1',
    title: '二次函数的图像与性质',
    category: '数学',
    attempts: 1234,
    accuracy: 85
  },
  {
    id: '2',
    title: '英语语法：现在完成时',
    category: '英语',
    attempts: 987,
    accuracy: 72
  },
  {
    id: '3',
    title: '牛顿第二定律应用',
    category: '物理',
    attempts: 856,
    accuracy: 68
  },
  {
    id: '4',
    title: '化学方程式配平',
    category: '化学',
    attempts: 743,
    accuracy: 79
  },
  {
    id: '5',
    title: '古诗词鉴赏技巧',
    category: '语文',
    attempts: 692,
    accuracy: 81
  }
])

// 最近活动数据
const recentActivities = ref([
  {
    id: '1',
    type: 'create',
    title: '新增题目',
    description: '张老师添加了10道数学题目',
    created_at: '2024-01-20 14:30:00'
  },
  {
    id: '2',
    type: 'paper',
    title: '创建试卷',
    description: '李老师创建了期末数学试卷',
    created_at: '2024-01-20 10:15:00'
  },
  {
    id: '3',
    type: 'import',
    title: '导入题库',
    description: '批量导入了英语题库200题',
    created_at: '2024-01-19 16:45:00'
  },
  {
    id: '4',
    type: 'analysis',
    title: '数据分析',
    description: '生成了本周答题统计报告',
    created_at: '2024-01-19 09:20:00'
  }
])

// 待办事项数据
const pendingTodos = ref([
  {
    id: '1',
    title: '审核新提交的题目',
    due_date: '2024-01-21',
    urgent: true,
    completed: false
  },
  {
    id: '2',
    title: '更新题目分类标签',
    due_date: '2024-01-22',
    urgent: false,
    completed: false
  },
  {
    id: '3',
    title: '导出本月答题数据',
    due_date: '2024-01-23',
    urgent: false,
    completed: false
  }
])

// 计算属性
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const generatePaper = () => {
  ElMessage.info('智能组卷功能开发中...')
}

const updateAccuracyTrend = () => {
  generateAccuracyTrendData()
}

const generateAccuracyTrendData = () => {
  const days = accuracyPeriod.value === '7d' ? 7 : accuracyPeriod.value === '30d' ? 30 : 90
  const labels = []
  const overallData = []
  const mathData = []
  const englishData = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString())
    overallData.push(Math.floor(Math.random() * 20) + 70)
    mathData.push(Math.floor(Math.random() * 25) + 65)
    englishData.push(Math.floor(Math.random() * 30) + 60)
  }

  accuracyTrendXAxisData.value = labels
  accuracyTrendData.value[0].data = overallData
  accuracyTrendData.value[1].data = mathData
  accuracyTrendData.value[2].data = englishData
}

const getRankClass = (index: number): string => {
  const classes = ['rank-1', 'rank-2', 'rank-3']
  return classes[index] || ''
}

const getActivityIcon = (type: string) => {
  const icons = {
    create: 'Plus',
    paper: 'Document',
    import: 'Upload',
    analysis: 'TrendCharts'
  }
  return icons[type as keyof typeof icons] || 'InfoFilled'
}

const getActivityIconClass = (type: string): string => {
  const classes = {
    create: 'icon-create',
    paper: 'icon-paper',
    import: 'icon-import',
    analysis: 'icon-analysis'
  }
  return classes[type as keyof typeof classes] || 'icon-default'
}

const formatTime = (time: string): string => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString()
}

const viewAllActivities = () => {
  router.push('/question-bank/activities')
}

const updateTodo = (todo: any) => {
  if (todo.completed) {
    ElMessage.success('任务已完成')
  }
}

// 生命周期
onMounted(() => {
  generateAccuracyTrendData()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.question-bank-overview {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 统计概览 */
  .stats-overview {
    margin-bottom: var(--spacing-8);

    .stat-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.primary {
        border-left: 4px solid var(--primary-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }
      }

      &.success {
        border-left: 4px solid var(--success-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--success-color), var(--success-light));
        }
      }

      &.warning {
        border-left: 4px solid var(--warning-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
        }
      }

      &.info {
        border-left: 4px solid var(--info-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--info-color), var(--info-light));
        }
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-xl);
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }
          }
        }
      }
    }
  }

  /* 快速操作区 */
  .quick-actions {
    margin-bottom: var(--spacing-8);

    .action-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-light);
      }

      .action-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .action-icon {
          width: 50px;
          height: 50px;
          border-radius: var(--radius-lg);
          background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);
        }

        .action-info {
          flex: 1;

          h3 {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-1) 0;
          }

          p {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin: 0;
          }
        }

        .action-arrow {
          color: var(--text-tertiary);
          font-size: var(--font-size-lg);
        }
      }
    }
  }

  /* 图表区域 */
  .charts-section {
    margin-bottom: var(--spacing-8);

    .chart-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);
        padding: var(--spacing-5) var(--spacing-6);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-container {
        padding: var(--spacing-4);
        min-height: 300px;
      }
    }

    /* 热门题目排行 */
    .hot-questions {
      padding: var(--spacing-4);

      .question-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-3);
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-2);
        transition: var(--transition-fast);

        &:hover {
          background: var(--bg-secondary);
        }

        &.rank-1 {
          background: linear-gradient(135deg, #ffd700, #ffed4e);
          color: #8b5a00;
        }

        &.rank-2 {
          background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
          color: #666;
        }

        &.rank-3 {
          background: linear-gradient(135deg, #cd7f32, #daa520);
          color: #5d4e37;
        }

        .rank {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: var(--text-tertiary);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-bold);
        }

        .question-info {
          flex: 1;

          .question-title {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .question-meta {
            display: flex;
            gap: var(--spacing-2);
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);

            .category {
              background: var(--bg-secondary);
              padding: 2px 6px;
              border-radius: var(--radius-sm);
            }
          }
        }

        .accuracy {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-semibold);
          color: var(--success-color);
        }
      }
    }
  }

  /* 最近活动 */
  .recent-activities {
    margin-bottom: var(--spacing-8);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .activities-list {
      .activity-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: var(--transition-fast);

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        .activity-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-base);

          &.icon-create {
            background: linear-gradient(135deg, var(--success-color), var(--success-light));
          }

          &.icon-paper {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
          }

          &.icon-import {
            background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
          }

          &.icon-analysis {
            background: linear-gradient(135deg, var(--info-color), var(--info-light));
          }
        }

        .activity-content {
          flex: 1;

          .activity-title {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .activity-description {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
          }
        }

        .activity-time {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
        }
      }
    }
  }

  /* 待办事项 */
  .todo-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .todo-list {
      .todo-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: var(--transition-fast);

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        &.urgent {
          border-left: 3px solid var(--error-color);
        }

        .todo-content {
          flex: 1;

          .todo-title {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .todo-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);

            .due-date {
              font-size: var(--font-size-xs);
              color: var(--text-tertiary);
            }
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .question-bank-overview {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .quick-actions {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }

    .charts-section {
      .el-col {
        margin-bottom: var(--spacing-6);
      }
    }
  }
}

@include respond-to('md') {
  .question-bank-overview {
    padding: var(--spacing-4);

    .stats-overview {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }

    .quick-actions {
      .action-card .action-content {
        padding: var(--spacing-4);

        .action-icon {
          width: 40px;
          height: 40px;
        }

        .action-info h3 {
          font-size: var(--font-size-base);
        }
      }
    }

    .charts-section {
      .hot-questions {
        .question-item {
          padding: var(--spacing-2);

          .question-info .question-title {
            font-size: var(--font-size-xs);
          }
        }
      }
    }
  }
}
</style>