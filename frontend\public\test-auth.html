<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API认证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>API认证测试</h1>
    
    <div class="test-section">
        <h3>1. 检查localStorage中的token</h3>
        <button onclick="checkTokens()">检查Token</button>
        <div id="tokenResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试登录</h3>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button onclick="testLogin()">登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试获取用户资料</h3>
        <button onclick="testProfile()">获取用户资料</button>
        <div id="profileResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 清除所有token</h3>
        <button onclick="clearTokens()">清除Token</button>
        <div id="clearResult" class="result"></div>
    </div>

    <script>
        function checkTokens() {
            const result = document.getElementById('tokenResult');
            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');
            const token = localStorage.getItem('token');
            const refreshTokenOld = localStorage.getItem('refreshToken');
            
            let output = '';
            output += `access_token: ${accessToken ? accessToken.substring(0, 50) + '...' : 'null'}\n`;
            output += `refresh_token: ${refreshToken ? refreshToken.substring(0, 50) + '...' : 'null'}\n`;
            output += `token: ${token ? token.substring(0, 50) + '...' : 'null'}\n`;
            output += `refreshToken: ${refreshTokenOld ? refreshTokenOld.substring(0, 50) + '...' : 'null'}\n`;
            
            result.textContent = output;
            result.className = 'result';
        }
        
        async function testLogin() {
            const result = document.getElementById('loginResult');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.textContent = `登录成功!\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result success';
                    
                    // 保存token到localStorage
                    if (data.access_token) {
                        localStorage.setItem('access_token', data.access_token);
                    }
                    if (data.refresh_token) {
                        localStorage.setItem('refresh_token', data.refresh_token);
                    }
                } else {
                    result.textContent = `登录失败: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `请求失败: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        async function testProfile() {
            const result = document.getElementById('profileResult');
            const accessToken = localStorage.getItem('access_token');
            
            if (!accessToken) {
                result.textContent = '没有找到access_token，请先登录';
                result.className = 'result error';
                return;
            }
            
            try {
                const response = await fetch('/profile/', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.textContent = `获取用户资料成功!\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result success';
                } else {
                    result.textContent = `获取用户资料失败: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `请求失败: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        function clearTokens() {
            const result = document.getElementById('clearResult');
            
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');
            
            result.textContent = '所有token已清除';
            result.className = 'result success';
        }
        
        // 页面加载时自动检查token
        window.onload = function() {
            checkTokens();
        };
    </script>
</body>
</html>