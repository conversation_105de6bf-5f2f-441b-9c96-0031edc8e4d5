"""
WisCude 后台管理系统 - 用户和学习数据模型
"""
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, Float, ForeignKey, Enum, Date, JSON
from sqlalchemy.orm import relationship
from app.models.base import BaseModel
import enum

class Gender(str, enum.Enum):
    """性别枚举"""
    MALE = "MALE"
    FEMALE = "FEMALE"
    OTHER = "OTHER"
    UNKNOWN = "UNKNOWN"

class UserStatus(str, enum.Enum):
    """用户状态枚举"""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    SUSPENDED = "SUSPENDED"
    DELETED = "DELETED"

class MembershipType(str, enum.Enum):
    """会员类型枚举"""
    REGULAR = "REGULAR"
    VIP = "VIP"

class UserLevel(str, enum.Enum):
    """用户等级枚举"""
    LEVEL_ONE = "LEVEL_ONE"
    LEVEL_TWO = "LEVEL_TWO"
    LEVEL_THREE = "LEVEL_THREE"
    LEVEL_FOUR = "LEVEL_FOUR"
    LEVEL_FIVE = "LEVEL_FIVE"
    LEVEL_SIX = "LEVEL_SIX"

class WiscudeUser(BaseModel):
    """WisCude应用用户模型 - 匹配数据库user表结构"""
    __tablename__ = "user"
    
    # 基本信息 - 匹配数据库字段
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), unique=True, index=True, nullable=False)
    phone = Column(String(20), unique=True, index=True, nullable=True)
    nickname = Column(String(50), nullable=False)  # 对应前端的昵称
    hashed_password = Column(String(100), nullable=False)  # 加密密码
    full_name = Column(String(100), nullable=True)
    
    # 个人资料
    gender = Column(Enum(Gender), default=Gender.UNKNOWN, nullable=False)
    birth_date = Column(Date, nullable=True)
    bio = Column(Text, nullable=True)
    location = Column(String(100), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    
    # 账户状态
    status = Column(Enum(UserStatus), default=UserStatus.ACTIVE, nullable=False)
    membership = Column(Enum(MembershipType), default=MembershipType.REGULAR, nullable=False)
    level = Column(Enum(UserLevel), default=UserLevel.LEVEL_ONE, nullable=False)
    registration_time = Column(DateTime, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    # 会员信息
    is_premium = Column(Boolean, default=False, nullable=False)
    premium_expires_at = Column(DateTime, nullable=True)
    
    # 学习统计
    total_study_time = Column(Integer, default=0, nullable=False)  # 分钟
    total_focus_sessions = Column(Integer, default=0, nullable=False)
    total_check_ins = Column(Integer, default=0, nullable=False)
    experience_points = Column(Integer, default=0, nullable=False)
    
    # 设置和偏好
    settings = Column(JSON, nullable=True)
    preferences = Column(JSON, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)
    
    # 便利属性
    @property
    def is_active(self):
        """兼容性属性 - 基于status判断是否活跃"""
        return self.status == UserStatus.ACTIVE
    
    @property
    def username(self):
        """兼容性属性 - 返回nickname作为username"""
        return self.nickname
    
    # 关系
    study_sessions = relationship("StudySession", back_populates="user")
    check_ins = relationship("CheckIn", back_populates="user")
    community_posts = relationship("CommunityPost", back_populates="author")
    course_enrollments = relationship("CourseEnrollment", back_populates="user")
    
    def __repr__(self):
        return f"<WiscudeUser {self.username}>"

class StudySession(BaseModel):
    """学习会话模型"""
    __tablename__ = "study_sessions"
    
    android_session_id = Column(String(36), unique=True, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # 会话详情
    session_type = Column(String(50), nullable=False)  # focus, study_plan, ai_learning
    title = Column(String(100), nullable=True)
    subject = Column(String(100), nullable=True)
    duration = Column(Integer, nullable=False)  # 分钟
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=True)
    
    # 会话数据
    interruptions = Column(Integer, default=0, nullable=False)
    productivity_score = Column(Float, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 状态
    is_completed = Column(Boolean, default=False, nullable=False)
    completion_percentage = Column(Float, default=0, nullable=False)
    
    # 关系
    user = relationship("WiscudeUser", back_populates="study_sessions")
    
    def __repr__(self):
        return f"<StudySession {self.session_type} {self.duration}min>"

class CheckIn(BaseModel):
    """每日打卡模型"""
    __tablename__ = "check_ins"
    
    android_checkin_id = Column(String(36), unique=True, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # 打卡数据
    date = Column(Date, nullable=False)
    mood = Column(String(20), nullable=True)
    energy_level = Column(Integer, nullable=True)  # 1-5
    study_goals = Column(Text, nullable=True)
    reflection = Column(Text, nullable=True)
    
    # 成就
    goals_completed = Column(Integer, default=0, nullable=False)
    study_time_today = Column(Integer, default=0, nullable=False)  # 分钟
    
    # 关系
    user = relationship("WiscudeUser", back_populates="check_ins")
    
    def __repr__(self):
        return f"<CheckIn {self.date} {self.user_id}>"

class AILearningRecord(BaseModel):
    """AI学习记录模型"""
    __tablename__ = "ai_learning_records"
    
    android_record_id = Column(String(36), unique=True, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # 学习数据
    topic = Column(String(100), nullable=False)
    difficulty = Column(String(20), nullable=True)
    duration = Column(Integer, nullable=False)  # 分钟
    questions_count = Column(Integer, default=0, nullable=False)
    correct_answers = Column(Integer, default=0, nullable=False)
    
    # 内容
    content = Column(JSON, nullable=True)  # 学习内容和问答记录
    feedback = Column(Text, nullable=True)
    
    # 关系
    user = relationship("WiscudeUser")
    
    def __repr__(self):
        return f"<AILearningRecord {self.topic}>"
