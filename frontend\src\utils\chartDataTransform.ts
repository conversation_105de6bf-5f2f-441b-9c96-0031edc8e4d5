/**
 * 图表数据转换工具
 * 用于将 Chart.js 格式的数据转换为 ECharts 格式
 */

// ECharts 数据接口定义
export interface SeriesData {
  name: string
  data: (number | DataItem)[]
  color?: string
  type?: 'line' | 'bar'
  smooth?: boolean
  area?: boolean
}

export interface DataItem {
  name: string
  value: number
  [key: string]: any
}

// Chart.js 数据接口定义
export interface ChartJsData {
  labels: string[]
  datasets: Array<{
    label?: string
    data: number[]
    borderColor?: string
    backgroundColor?: string | string[]
    tension?: number
    fill?: boolean
    borderWidth?: number
    borderRadius?: number
    borderDash?: number[]
  }>
}

/**
 * 将 Chart.js 格式的数据转换为 ECharts SeriesData 格式
 * @param chartJsData Chart.js 格式的数据
 * @returns ECharts SeriesData 数组
 */
export function transformToSeriesData(chartJsData: ChartJsData): SeriesData[] {
  if (!chartJsData || !chartJsData.datasets) {
    return []
  }

  return chartJsData.datasets.map((dataset, index) => {
    const seriesData: SeriesData = {
      name: dataset.label || `Series ${index + 1}`,
      data: dataset.data || [],
      color: dataset.borderColor || dataset.backgroundColor as string,
      type: 'line',
      smooth: dataset.tension ? dataset.tension > 0 : false,
      area: dataset.fill || false
    }

    return seriesData
  })
}

/**
 * 将 Chart.js 格式的数据转换为 ECharts DataItem 格式（用于饼图等）
 * @param chartJsData Chart.js 格式的数据
 * @returns ECharts DataItem 数组
 */
export function transformToDataItems(chartJsData: ChartJsData): DataItem[] {
  if (!chartJsData || !chartJsData.labels || !chartJsData.datasets || chartJsData.datasets.length === 0) {
    return []
  }

  const dataset = chartJsData.datasets[0]
  const colors = Array.isArray(dataset.backgroundColor) 
    ? dataset.backgroundColor 
    : [dataset.backgroundColor || '#409eff']

  return chartJsData.labels.map((label, index) => ({
    name: label,
    value: dataset.data[index] || 0,
    color: colors[index % colors.length]
  }))
}

/**
 * 为 Chart.js 数据添加缺失的属性以满足类型要求
 * @param chartJsData Chart.js 格式的数据
 * @returns 补全属性后的数据
 */
export function normalizeChartJsData(chartJsData: ChartJsData): ChartJsData {
  if (!chartJsData || !chartJsData.datasets) {
    return { labels: [], datasets: [] }
  }

  const normalizedDatasets = chartJsData.datasets.map(dataset => ({
    label: dataset.label || '',
    data: dataset.data || [],
    borderColor: dataset.borderColor || '#409eff',
    backgroundColor: dataset.backgroundColor || 'rgba(64, 158, 255, 0.1)',
    tension: dataset.tension || 0,
    fill: dataset.fill || false,
    borderWidth: dataset.borderWidth || 1,
    borderRadius: dataset.borderRadius || 0,
    ...dataset
  }))

  return {
    labels: chartJsData.labels || [],
    datasets: normalizedDatasets
  }
}

/**
 * 创建空的 SeriesData 数组
 * @param count 数组长度
 * @returns 空的 SeriesData 数组
 */
export function createEmptySeriesData(count: number = 1): SeriesData[] {
  return Array.from({ length: count }, (_, index) => ({
    name: `Series ${index + 1}`,
    data: [],
    color: '#409eff',
    type: 'line'
  }))
}

/**
 * 创建空的 DataItem 数组
 * @param count 数组长度
 * @returns 空的 DataItem 数组
 */
export function createEmptyDataItems(count: number = 1): DataItem[] {
  return Array.from({ length: count }, (_, index) => ({
    name: `Item ${index + 1}`,
    value: 0
  }))
}

/**
 * 验证 SeriesData 数据格式
 * @param data 待验证的数据
 * @returns 是否为有效的 SeriesData 格式
 */
export function isValidSeriesData(data: any): data is SeriesData[] {
  return Array.isArray(data) && data.every(item => 
    typeof item === 'object' && 
    typeof item.name === 'string' && 
    Array.isArray(item.data)
  )
}

/**
 * 验证 DataItem 数据格式
 * @param data 待验证的数据
 * @returns 是否为有效的 DataItem 格式
 */
export function isValidDataItems(data: any): data is DataItem[] {
  return Array.isArray(data) && data.every(item =>
    typeof item === 'object' &&
    typeof item.name === 'string' &&
    typeof item.value === 'number'
  )
}

/**
 * 为 DoughnutChart 转换数据格式
 * @param chartJsData Chart.js 格式的数据
 * @returns DoughnutChart 兼容的数据格式
 */
export function transformToDoughnutData(chartJsData: ChartJsData): {
  labels: string[]
  datasets: Array<{
    data: number[]
    backgroundColor?: string[]
    borderColor?: string[]
    borderWidth?: number
  }>
} {
  if (!chartJsData || !chartJsData.datasets) {
    return { labels: [], datasets: [] }
  }

  const transformedDatasets = chartJsData.datasets.map(dataset => {
    const backgroundColor = Array.isArray(dataset.backgroundColor)
      ? dataset.backgroundColor
      : dataset.backgroundColor ? [dataset.backgroundColor] : undefined

    const borderColor = Array.isArray(dataset.borderColor)
      ? dataset.borderColor
      : dataset.borderColor ? [dataset.borderColor] : undefined

    return {
      data: dataset.data || [],
      backgroundColor,
      borderColor,
      borderWidth: dataset.borderWidth || 1
    }
  })

  return {
    labels: chartJsData.labels || [],
    datasets: transformedDatasets
  }
}
