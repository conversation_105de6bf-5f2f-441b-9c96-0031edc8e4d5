<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>WisCude API 连接测试</h1>
    
    <div class="test-section">
        <h3>1. 测试后端健康检查</h3>
        <button onclick="testBackendHealth()">测试后端 API</button>
        <div id="backend-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试前端代理</h3>
        <button onclick="testProxyHealth()">测试前端代理</button>
        <div id="proxy-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试登录 API</h3>
        <button onclick="testLogin()">测试登录</button>
        <div id="login-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 测试设置 API</h3>
        <button onclick="testSettings()">测试设置 API</button>
        <div id="settings-result" class="result"></div>
    </div>

    <script>
        async function testBackendHealth() {
            const resultDiv = document.getElementById('backend-result');
            try {
                const response = await fetch('http://localhost:8000/api/health');
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.textContent = `成功！状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testProxyHealth() {
            const resultDiv = document.getElementById('proxy-result');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.textContent = `成功！状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            try {
                const formData = new FormData();
                formData.append('username', 'admin');
                formData.append('password', 'admin123');
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.textContent = `成功！状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                
                // 保存令牌到localStorage
                if (data.access_token) {
                    localStorage.setItem('access_token', data.access_token);
                    localStorage.setItem('refresh_token', data.refresh_token);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testSettings() {
            const resultDiv = document.getElementById('settings-result');
            try {
                const token = localStorage.getItem('access_token');
                const headers = {};
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch('/api/settings/', {
                    headers: headers
                });
                
                const data = await response.json();
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `成功！状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `错误！状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
