# ============================================================================
# WisCude 后台管理系统 - 完整依赖文件
# ============================================================================
# 包含系统运行所需的所有依赖包
# 基于 backend/pyproject.toml 的完整依赖列表
#
# 安装方式：
# pip install -r requirements.txt
#
# 或使用现代化的依赖管理:
# pip install -e backend/[dev]  # 开发环境
# pip install -e backend/      # 生产环境

# ============================================================================
# FastAPI 核心框架
# ============================================================================
fastapi==0.109.0
uvicorn[standard]==0.25.0
pydantic==2.5.3
pydantic-settings==2.1.0
python-multipart==0.0.6
email-validator==2.1.0

# ============================================================================
# 数据库相关
# ============================================================================
sqlalchemy==2.0.25
psycopg2-binary==2.9.9
alembic==1.13.1
asyncpg==0.29.0
aiosqlite==0.19.0

# ============================================================================
# 身份认证
# ============================================================================
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# ============================================================================
# 数据处理
# ============================================================================
pandas==2.1.4
numpy==1.26.2
openpyxl==3.1.2
python-dateutil==2.8.2

# ============================================================================
# 异步任务和缓存
# ============================================================================
celery==5.3.6
redis==5.0.1
flower==2.0.1

# ============================================================================
# 日志和监控
# ============================================================================
loguru==0.7.2
prometheus-fastapi-instrumentator==6.1.0

# ============================================================================
# 基础工具
# ============================================================================
python-dotenv==1.0.0
tenacity==8.2.3
orjson==3.9.10
ujson==5.9.0

# ============================================================================
# 生产环境依赖
# ============================================================================
gunicorn>=21.2.0
psutil>=5.9.0

# ============================================================================
# 开发和测试工具
# ============================================================================
pytest==7.4.3
pytest-asyncio==0.23.2
pytest-cov>=4.1.0
httpx==0.26.0
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0
pre-commit>=3.4.0

# ============================================================================
# 额外工具（可选）
# ============================================================================
PyYAML==6.0.1
requests>=2.31.0
