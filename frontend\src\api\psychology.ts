/**
 * 心理资源库管理API接口
 */
import request from './request'

// ==================== 测评量表管理 ====================

/**
 * 获取心理测评量表列表
 */
export const getPsychologyAssessments = (params: {
  page?: number
  size?: number
  keyword?: string
  assessment_type?: string
  status?: string
}) => {
  return request({
    url: '/v1/psychology/assessments',
    method: 'get',
    params
  })
}

/**
 * 创建心理测评量表
 */
export const createPsychologyAssessment = (data: {
  title: string
  description?: string
  assessment_type: string
  questions: any[]
  scoring_rules: any
  interpretation_rules: any
  time_limit?: number
  target_groups?: string[]
  tags?: string[]
  status?: string
}) => {
  return request({
    url: '/api/v1/psychology/assessments',
    method: 'post',
    data
  })
}

/**
 * 更新心理测评量表
 */
export const updatePsychologyAssessment = (assessmentId: string, data: any) => {
  return request({
    url: `/api/v1/psychology/assessments/${assessmentId}`,
    method: 'put',
    data
  })
}

/**
 * 删除心理测评量表
 */
export const deletePsychologyAssessment = (assessmentId: string) => {
  return request({
    url: `/api/v1/psychology/assessments/${assessmentId}`,
    method: 'delete'
  })
}

// ==================== 心理文章管理 ====================

/**
 * 获取心理健康文章列表
 */
export const getPsychologyArticles = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  status?: string
}) => {
  return request({
    url: '/v1/psychology/articles',
    method: 'get',
    params
  })
}

/**
 * 创建心理健康文章
 */
export const createPsychologyArticle = (data: {
  title: string
  content: string
  summary?: string
  category: string
  tags?: string[]
  cover_image?: string
  author_name?: string
  reading_time?: number
  is_featured?: boolean
  status?: string
}) => {
  return request({
    url: '/api/v1/psychology/articles',
    method: 'post',
    data
  })
}

/**
 * 更新心理健康文章
 */
export const updatePsychologyArticle = (articleId: string, data: any) => {
  return request({
    url: `/api/v1/psychology/articles/${articleId}`,
    method: 'put',
    data
  })
}

/**
 * 审核心理健康文章
 */
export const reviewPsychologyArticle = (articleId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/psychology/articles/${articleId}/review`,
    method: 'post',
    data
  })
}

// ==================== 咨询师管理 ====================

/**
 * 获取心理咨询师列表
 */
export const getPsychologyCounselors = (params: {
  page?: number
  size?: number
  keyword?: string
  status?: string
  is_verified?: boolean
}) => {
  return request({
    url: '/v1/psychology/counselors',
    method: 'get',
    params
  })
}

/**
 * 创建心理咨询师
 */
export const createPsychologyCounselor = (data: {
  user_id: string
  name: string
  avatar?: string
  title: string
  bio?: string
  specialties: string[]
  experience_years: number
  education_background: any[]
  certifications: any[]
  consultation_fee: number
  available_times: any[]
  contact_info: any
  status?: string
}) => {
  return request({
    url: '/api/v1/psychology/counselors',
    method: 'post',
    data
  })
}

/**
 * 更新心理咨询师
 */
export const updatePsychologyCounselor = (counselorId: string, data: any) => {
  return request({
    url: `/api/v1/psychology/counselors/${counselorId}`,
    method: 'put',
    data
  })
}

/**
 * 认证心理咨询师
 */
export const verifyPsychologyCounselor = (counselorId: string, data: {
  is_verified: boolean
  verification_comment?: string
}) => {
  return request({
    url: `/api/v1/psychology/counselors/${counselorId}/verify`,
    method: 'post',
    data
  })
}

// ==================== 咨询预约管理 ====================

/**
 * 获取心理咨询预约列表
 */
export const getPsychologyAppointments = (params: {
  page?: number
  size?: number
  counselor_id?: string
  status?: string
}) => {
  return request({
    url: '/v1/psychology/appointments',
    method: 'get',
    params
  })
}

/**
 * 创建心理咨询预约
 */
export const createPsychologyAppointment = (data: {
  counselor_id: string
  appointment_time: string
  duration: number
  consultation_type: string
  issue_description?: string
  contact_preference: string
  emergency_contact?: any
}) => {
  return request({
    url: '/api/v1/psychology/appointments',
    method: 'post',
    data
  })
}

/**
 * 更新心理咨询预约
 */
export const updatePsychologyAppointment = (appointmentId: string, data: any) => {
  return request({
    url: `/api/v1/psychology/appointments/${appointmentId}`,
    method: 'put',
    data
  })
}

// ==================== 危机预警系统 ====================

/**
 * 获取心理危机预警列表
 */
export const getPsychologyCrisisAlerts = (params: {
  page?: number
  size?: number
  risk_level?: string
  status?: string
}) => {
  return request({
    url: '/v1/psychology/crisis-alerts',
    method: 'get',
    params
  })
}

/**
 * 创建心理危机预警
 */
export const createPsychologyCrisisAlert = (data: {
  user_id: string
  risk_level: string
  risk_factors: string[]
  assessment_score: number
  trigger_event?: string
  recommended_actions: string[]
  emergency_contacts?: any[]
}) => {
  return request({
    url: '/api/v1/psychology/crisis-alerts',
    method: 'post',
    data
  })
}

/**
 * 处理心理危机预警
 */
export const handlePsychologyCrisisAlert = (alertId: string, data: {
  handler_id: string
  action_taken: string
  follow_up_plan?: string
  status: string
  notes?: string
}) => {
  return request({
    url: `/api/v1/psychology/crisis-alerts/${alertId}/handle`,
    method: 'put',
    data
  })
}

// ==================== 统计分析 ====================

/**
 * 获取心理资源库管理概览统计
 */
export const getPsychologyOverview = () => {
  return request({
    url: '/v1/psychology/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取测评统计
 */
export const getAssessmentStatistics = () => {
  return request({
    url: '/v1/psychology/statistics/assessments',
    method: 'get'
  })
}

/**
 * 获取文章统计
 */
export const getArticleStatistics = () => {
  return request({
    url: '/v1/psychology/statistics/articles',
    method: 'get'
  })
}

/**
 * 获取预约统计
 */
export const getAppointmentStatistics = () => {
  return request({
    url: '/v1/psychology/statistics/appointments',
    method: 'get'
  })
}

/**
 * 获取危机预警统计
 */
export const getCrisisStatistics = () => {
  return request({
    url: '/v1/psychology/statistics/crisis',
    method: 'get'
  })
}

// ==================== 数据类型定义 ====================

export interface PsychologyAssessment {
  id: string
  title: string
  description?: string
  assessment_type: string
  questions: any[]
  scoring_rules: any
  interpretation_rules: any
  time_limit?: number
  target_groups: string[]
  tags: string[]
  status: string
  completion_count: number
  average_score: number
  created_at: string
  updated_at: string
}

export interface PsychologyArticle {
  id: string
  title: string
  content: string
  summary?: string
  category: string
  tags: string[]
  cover_image?: string
  author_name?: string
  reading_time?: number
  is_featured: boolean
  status: string
  view_count: number
  like_count: number
  share_count: number
  created_at: string
  updated_at: string
}

export interface PsychologyCounselor {
  id: string
  user_id: string
  name: string
  avatar?: string
  title: string
  bio?: string
  specialties: string[]
  experience_years: number
  education_background: any[]
  certifications: any[]
  consultation_fee: number
  available_times: any[]
  contact_info: any
  status: string
  is_verified: boolean
  rating: number
  rating_count: number
  consultation_count: number
  created_at: string
  updated_at: string
}

export interface PsychologyAppointment {
  id: string
  user_id: string
  counselor_id: string
  appointment_time: string
  duration: number
  consultation_type: string
  issue_description?: string
  contact_preference: string
  emergency_contact?: any
  status: string
  payment_status: string
  payment_amount: number
  session_notes?: string
  follow_up_plan?: string
  created_at: string
  updated_at: string
}

export interface PsychologyCrisisAlert {
  id: string
  user_id: string
  risk_level: string
  risk_factors: string[]
  assessment_score: number
  trigger_event?: string
  recommended_actions: string[]
  emergency_contacts: any[]
  status: string
  handler_id?: string
  action_taken?: string
  follow_up_plan?: string
  handled_at?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}