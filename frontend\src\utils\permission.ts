/**
 * 权限控制工具类
 * 用于管理用户权限验证、角色检查和功能访问控制
 */

import { useAuthStore } from '@/store/auth'

// 权限类型定义
export interface Permission {
  id: string
  name: string
  code: string
  module: string
  description?: string
}

export interface Role {
  id: string
  name: string
  code: string
  permissions: Permission[]
  description?: string
}

export interface User {
  id: string
  username: string
  email: string
  roles: Role[]
  permissions: Permission[]
}

// 模块权限映射
export const MODULE_PERMISSIONS = {
  // 广告管理模块
  advertisement: {
    view: 'advertisement:view',
    create: 'advertisement:create',
    edit: 'advertisement:edit',
    delete: 'advertisement:delete',
    publish: 'advertisement:publish'
  },
  // 题库管理模块
  question_bank: {
    view: 'question_bank:view',
    create: 'question_bank:create',
    edit: 'question_bank:edit',
    delete: 'question_bank:delete',
    export: 'question_bank:export'
  },
  // 英语练习模块
  english_practice: {
    view: 'english_practice:view',
    create: 'english_practice:create',
    edit: 'english_practice:edit',
    delete: 'english_practice:delete',
    manage_materials: 'english_practice:manage_materials'
  },
  // 心理资源库模块
  psychology: {
    view: 'psychology:view',
    create: 'psychology:create',
    edit: 'psychology:edit',
    delete: 'psychology:delete',
    manage_counselors: 'psychology:manage_counselors'
  },
  // 课程管理模块
  course_management: {
    view: 'course_management:view',
    create: 'course_management:create',
    edit: 'course_management:edit',
    delete: 'course_management:delete',
    manage_instructors: 'course_management:manage_instructors'
  },
  // 软件更新模块
  software_update: {
    view: 'software_update:view',
    create: 'software_update:create',
    edit: 'software_update:edit',
    delete: 'software_update:delete',
    publish: 'software_update:publish'
  },
  // 学习分析模块
  learning_analytics: {
    view: 'learning_analytics:view',
    export: 'learning_analytics:export',
    advanced_analysis: 'learning_analytics:advanced_analysis'
  },
  // 社区管理模块
  community: {
    view: 'community:view',
    create: 'community:create',
    edit: 'community:edit',
    delete: 'community:delete',
    moderate: 'community:moderate'
  },
  // 系统管理
  system: {
    user_management: 'system:user_management',
    role_management: 'system:role_management',
    permission_management: 'system:permission_management',
    system_settings: 'system:system_settings'
  }
}

// 角色定义
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  TEACHER: 'teacher',
  STUDENT: 'student',
  COUNSELOR: 'counselor',
  CONTENT_MANAGER: 'content_manager'
}

/**
 * 权限检查类
 */
export class PermissionChecker {
  private authStore = useAuthStore()

  /**
   * 检查用户是否有指定权限
   */
  hasPermission(permission: string): boolean {
    const user = this.authStore.user
    if (!user) return false

    // 超级管理员拥有所有权限
    if (user.is_superuser || user.role === 'superadmin') return true

    // 管理员拥有大部分权限
    if (user.role === 'admin') return true

    // 其他角色的权限检查可以根据具体需求扩展
    return false
  }

  /**
   * 检查用户是否有指定角色
   */
  hasRole(roleCode: string): boolean {
    const user = this.authStore.user
    if (!user) return false

    // 映射角色代码到用户角色
    const roleMapping: Record<string, string[]> = {
      [ROLES.SUPER_ADMIN]: ['superadmin'],
      [ROLES.ADMIN]: ['admin', 'superadmin'],
      [ROLES.TEACHER]: ['editor'],
      [ROLES.STUDENT]: ['viewer']
    }

    const allowedRoles = roleMapping[roleCode] || []
    return allowedRoles.includes(user.role)
  }

  /**
   * 检查用户是否有任一指定权限
   */
  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission))
  }

  /**
   * 检查用户是否有所有指定权限
   */
  hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(permission))
  }

  /**
   * 检查用户是否有任一指定角色
   */
  hasAnyRole(roleCodes: string[]): boolean {
    return roleCodes.some(roleCode => this.hasRole(roleCode))
  }

  /**
   * 检查模块访问权限
   */
  canAccessModule(module: string): boolean {
    const modulePermissions = MODULE_PERMISSIONS[module as keyof typeof MODULE_PERMISSIONS]
    if (!modulePermissions) return false

    return this.hasPermission(modulePermissions.view)
  }

  /**
   * 检查操作权限
   */
  canPerformAction(module: string, action: string): boolean {
    const modulePermissions = MODULE_PERMISSIONS[module as keyof typeof MODULE_PERMISSIONS]
    if (!modulePermissions) return false

    const permission = modulePermissions[action as keyof typeof modulePermissions]
    if (!permission) return false

    return this.hasPermission(permission)
  }

  /**
   * 获取用户可访问的模块列表
   */
  getAccessibleModules(): string[] {
    return Object.keys(MODULE_PERMISSIONS).filter(module => 
      this.canAccessModule(module)
    )
  }

  /**
   * 获取用户在指定模块的可执行操作
   */
  getModuleActions(module: string): string[] {
    const modulePermissions = MODULE_PERMISSIONS[module as keyof typeof MODULE_PERMISSIONS]
    if (!modulePermissions) return []

    return Object.keys(modulePermissions).filter(action => 
      this.canPerformAction(module, action)
    )
  }
}

// 创建全局权限检查器实例
export const permissionChecker = new PermissionChecker()

/**
 * 权限指令 - 用于Vue模板中的权限控制
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding
    if (!value) return

    let hasPermission = false

    if (typeof value === 'string') {
      hasPermission = permissionChecker.hasPermission(value)
    } else if (Array.isArray(value)) {
      hasPermission = permissionChecker.hasAnyPermission(value)
    } else if (typeof value === 'object') {
      const { permissions, roles, requireAll = false } = value
      
      if (permissions) {
        hasPermission = requireAll 
          ? permissionChecker.hasAllPermissions(permissions)
          : permissionChecker.hasAnyPermission(permissions)
      }
      
      if (roles && !hasPermission) {
        hasPermission = permissionChecker.hasAnyRole(roles)
      }
    }

    if (!hasPermission) {
      el.style.display = 'none'
    }
  },
  
  updated(el: HTMLElement, binding: any) {
    // 重新检查权限
    this.mounted(el, binding)
  }
}

/**
 * 路由权限守卫
 */
export function createRouteGuard() {
  return (to: any, from: any, next: any) => {
    const { meta } = to
    
    // 如果路由不需要权限验证，直接通过
    if (!meta?.requiresAuth) {
      next()
      return
    }

    // 检查用户是否已登录
    const authStore = useAuthStore()
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }

    // 检查路由权限
    if (meta.permissions) {
      const hasPermission = Array.isArray(meta.permissions)
        ? permissionChecker.hasAnyPermission(meta.permissions)
        : permissionChecker.hasPermission(meta.permissions)
      
      if (!hasPermission) {
        next('/403') // 无权限页面
        return
      }
    }

    // 检查角色权限
    if (meta.roles) {
      const hasRole = Array.isArray(meta.roles)
        ? permissionChecker.hasAnyRole(meta.roles)
        : permissionChecker.hasRole(meta.roles)
      
      if (!hasRole) {
        next('/403')
        return
      }
    }

    next()
  }
}

/**
 * 权限工具函数
 */
export const permission = {
  /**
   * 检查权限的组合式API
   */
  usePermission: () => {
    return {
      hasPermission: permissionChecker.hasPermission.bind(permissionChecker),
      hasRole: permissionChecker.hasRole.bind(permissionChecker),
      hasAnyPermission: permissionChecker.hasAnyPermission.bind(permissionChecker),
      hasAllPermissions: permissionChecker.hasAllPermissions.bind(permissionChecker),
      hasAnyRole: permissionChecker.hasAnyRole.bind(permissionChecker),
      canAccessModule: permissionChecker.canAccessModule.bind(permissionChecker),
      canPerformAction: permissionChecker.canPerformAction.bind(permissionChecker),
      getAccessibleModules: permissionChecker.getAccessibleModules.bind(permissionChecker),
      getModuleActions: permissionChecker.getModuleActions.bind(permissionChecker)
    }
  },

  /**
   * 权限装饰器 - 用于方法权限控制
   */
  requirePermission: (permission: string) => {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value
      
      descriptor.value = function (...args: any[]) {
        if (!permissionChecker.hasPermission(permission)) {
          throw new Error(`权限不足: 需要权限 ${permission}`)
        }
        return originalMethod.apply(this, args)
      }
      
      return descriptor
    }
  },

  /**
   * 角色装饰器 - 用于方法角色控制
   */
  requireRole: (role: string) => {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value
      
      descriptor.value = function (...args: any[]) {
        if (!permissionChecker.hasRole(role)) {
          throw new Error(`权限不足: 需要角色 ${role}`)
        }
        return originalMethod.apply(this, args)
      }
      
      return descriptor
    }
  }
}

export default permission
