# ============================================================================
# WisCude 最小化运行时依赖
# ============================================================================
# 仅包含运行时必需的预编译包，避免编译器依赖
# 安装方式：pip install -r requirements_minimal.txt

# ============================================================================
# FastAPI 核心框架
# ============================================================================
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-multipart>=0.0.5
email-validator>=2.0.0

# ============================================================================
# 数据库相关（仅SQLite，避免PostgreSQL编译问题）
# ============================================================================
sqlalchemy>=2.0.0
aiosqlite>=0.19.0
alembic>=1.10.0

# ============================================================================
# 身份认证
# ============================================================================
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# ============================================================================
# 基础工具
# ============================================================================
python-dotenv>=1.0.0
requests>=2.28.0
PyYAML>=6.0.0

# ============================================================================
# 日志
# ============================================================================
loguru>=0.7.0

# ============================================================================
# 开发工具（最小化）
# ============================================================================
httpx>=0.24.0
pytest>=7.0.0
pytest-asyncio>=0.21.0