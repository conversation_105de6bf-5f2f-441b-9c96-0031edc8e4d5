"""
个人资料相关的Pydantic模式
"""
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime

class UserPreferencesUpdate(BaseModel):
    """用户偏好设置更新"""
    language: str = Field(default="zh-CN", pattern="^(zh-CN|en-US)$")
    timezone: str = Field(default="Asia/Shanghai")
    theme: str = Field(default="light", pattern="^(light|dark|auto)$")
    email_notifications: List[str] = Field(default_factory=list)
    default_page_size: int = Field(default=20, ge=10, le=100)

class UserProfileUpdate(BaseModel):
    """用户资料更新"""
    username: Optional[str] = Field(None, min_length=3, max_length=20)
    full_name: Optional[str] = Field(None, max_length=50)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, pattern=r"^1[3-9]\d{9}$")
    department: Optional[str] = Field(None, max_length=50)
    position: Optional[str] = Field(None, max_length=50)
    bio: Optional[str] = Field(None, max_length=500)

class UserProfileResponse(BaseModel):
    """用户资料响应"""
    id: str
    username: str
    email: str
    full_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    bio: Optional[str] = None
    avatar: Optional[str] = None
    avatar_url: Optional[str] = None
    role: str
    is_active: bool
    two_factor_enabled: bool = False
    preferences: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    login_count: Optional[int] = 0

    class Config:
        from_attributes = True

class PasswordChangeRequest(BaseModel):
    """密码修改请求"""
    current_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=128)

class LoginHistoryItem(BaseModel):
    """登录历史项"""
    id: str
    login_time: str
    ip_address: str
    user_agent: str
    location: Optional[str]
    status: str

class LoginHistoryResponse(BaseModel):
    """登录历史响应"""
    items: List[LoginHistoryItem]
    total: int
    page: int
    page_size: int

class TwoFactorSetupResponse(BaseModel):
    """双因素认证设置响应"""
    qr_code: str
    secret: str
    backup_codes: List[str]

class TwoFactorConfirmRequest(BaseModel):
    """双因素认证确认请求"""
    code: str = Field(..., min_length=6, max_length=6)

class TwoFactorDisableRequest(BaseModel):
    """双因素认证禁用请求"""
    password: str
    token: Optional[str] = None

class SecurityStatusResponse(BaseModel):
    """安全状态响应"""
    password_strength: str
    last_password_change: Optional[datetime]
    failed_login_attempts: int
    active_sessions: int
    two_factor_enabled: bool
    recent_logins: Optional[List[Dict[str, Any]]] = None

class ActiveSessionItem(BaseModel):
    """活跃会话项"""
    id: str
    ip_address: str
    user_agent: str
    location: Optional[str]
    created_at: str
    last_activity: str
    is_current: bool

class AvatarUploadResponse(BaseModel):
    """头像上传响应"""
    message: str
    avatar_url: str

class PasswordVerifyRequest(BaseModel):
    """密码验证请求"""
    password: str

class PasswordVerifyResponse(BaseModel):
    """密码验证响应"""
    valid: bool

class BackupCodesResponse(BaseModel):
    """备用代码响应"""
    backup_codes: List[str]

class SessionLogoutResponse(BaseModel):
    """会话注销响应"""
    logged_out_sessions: int

class AccountDeleteRequest(BaseModel):
    """账户删除请求"""
    password: str
    confirmation: str = Field(..., pattern="^DELETE$")

class PersonalDataExportResponse(BaseModel):
    """个人数据导出响应"""
    export_id: str
    download_url: str
    expires_at: str

class ProfileStatistics(BaseModel):
    """个人资料统计"""
    total_logins: int
    successful_logins: int
    failed_logins: int
    last_login: Optional[str]
    account_age_days: int
    password_age_days: Optional[int]
    two_factor_enabled: bool

class NotificationSettings(BaseModel):
    """通知设置"""
    email_notifications: Dict[str, bool]
    push_notifications: Dict[str, bool]
    sms_notifications: Dict[str, bool]

class PrivacySettings(BaseModel):
    """隐私设置"""
    profile_visibility: str = Field(default="private", pattern="^(public|private|friends)$")
    show_email: bool = False
    show_phone: bool = False
    show_last_login: bool = False
    allow_search: bool = True

class ApiKeyItem(BaseModel):
    """API密钥项"""
    id: str
    name: str
    key_preview: str
    permissions: List[str]
    created_at: str
    last_used: Optional[str]
    expires_at: Optional[str]
    is_active: bool

class ApiKeyCreateRequest(BaseModel):
    """API密钥创建请求"""
    name: str = Field(..., min_length=1, max_length=50)
    permissions: List[str]
    expires_days: Optional[int] = Field(None, ge=1, le=365)

class ApiKeyCreateResponse(BaseModel):
    """API密钥创建响应"""
    id: str
    name: str
    key: str
    permissions: List[str]
    expires_at: Optional[str]

class UserActivityItem(BaseModel):
    """用户活动项"""
    id: str
    activity_type: str
    description: str
    ip_address: str
    user_agent: str
    created_at: str
    metadata: Optional[Dict[str, Any]]

class UserActivityResponse(BaseModel):
    """用户活动响应"""
    items: List[UserActivityItem]
    total: int
    page: int
    page_size: int
