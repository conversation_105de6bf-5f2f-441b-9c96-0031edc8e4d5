#!/usr/bin/env python3
"""
添加个人资料相关字段到AdminUser表
"""

from app.core.database import get_db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_profile_fields():
    """添加个人资料相关字段"""
    db = next(get_db())
    
    try:
        # 检查字段是否已存在 (PostgreSQL语法)
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'admin_users'
        """))
        existing_columns = [row[0] for row in result.fetchall()]
        
        # 要添加的字段列表 (PostgreSQL语法)
        fields_to_add = [
            ('phone', 'VARCHAR(20)'),
            ('department', 'VARCHAR(100)'),
            ('position', 'VARCHAR(100)'),
            ('avatar', 'VARCHAR(255)'),
            ('two_factor_enabled', 'BOOLEAN DEFAULT FALSE'),
            ('two_factor_secret', 'VARCHAR(255)'),
            ('two_factor_backup_codes', 'JSONB'),  # PostgreSQL使用JSONB存储JSON
            ('preferences', 'JSONB'),  # PostgreSQL使用JSONB存储JSON
            ('password_changed_at', 'TIMESTAMP'),
            ('login_count', 'INTEGER DEFAULT 0')
        ]
        
        # 添加不存在的字段
        for field_name, field_type in fields_to_add:
            if field_name not in existing_columns:
                sql = f"ALTER TABLE admin_users ADD COLUMN {field_name} {field_type}"
                logger.info(f"Adding column: {field_name}")
                db.execute(text(sql))
            else:
                logger.info(f"Column {field_name} already exists, skipping")
        
        # 提交更改
        db.commit()
        logger.info("Profile fields migration completed successfully")
        
    except Exception as e:
        logger.error(f"Error during migration: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    add_profile_fields()