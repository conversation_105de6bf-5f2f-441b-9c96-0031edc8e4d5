<template>
  <div class="login-preview-container">
    <h3>登录页面预览</h3>
    <div class="preview-frame">
      <iframe 
        ref="previewFrame"
        :src="loginUrl" 
        frameborder="0"
        class="preview-iframe"
      ></iframe>
    </div>
    <div class="preview-controls">
      <el-button @click="refreshPreview">
        <el-icon><Refresh /></el-icon>
        刷新预览
      </el-button>
      <el-button type="primary" @click="openInNewTab">
        <el-icon><View /></el-icon>
        在新窗口打开
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Refresh, View } from '@element-plus/icons-vue'

const previewFrame = ref<HTMLIFrameElement>()

const loginUrl = computed(() => {
  return window.location.origin + '/login'
})

const refreshPreview = () => {
  if (previewFrame.value) {
    previewFrame.value.src = previewFrame.value.src
  }
}

const openInNewTab = () => {
  window.open(loginUrl.value, '_blank')
}
</script>

<style scoped>
.login-preview-container {
  padding: 20px;
}

.preview-frame {
  width: 100%;
  height: 500px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  margin: 20px 0;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  transform: scale(0.8);
  transform-origin: top left;
}

.preview-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
