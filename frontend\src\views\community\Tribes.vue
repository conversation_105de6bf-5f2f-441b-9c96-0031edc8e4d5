<template>
  <div class="tribes-management">
    <div class="page-header">
      <h1>部落管理</h1>
      <p>管理学习部落的创建审核、成员管理、活动组织和数据分析</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="部落创建审核" name="approval">
        <!-- 审核统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ approvalStats.pendingApproval }}</div>
                  <div class="stat-label">待审核部落</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ approvalStats.approvedToday }}</div>
                  <div class="stat-label">今日已通过</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ approvalStats.rejectedToday }}</div>
                  <div class="stat-label">今日已拒绝</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ approvalStats.avgProcessTime }}</div>
                  <div class="stat-label">平均处理时长(小时)</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 部落创建规则设置 -->
        <el-card class="rules-card">
          <template #header>
            <div class="card-header">
              <span>部落创建规则</span>
              <el-button type="primary" @click="showRulesDialog = true">
                <el-icon><Setting /></el-icon>
                修改规则
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="rules-content">
            <el-col :span="8">
              <div class="rule-item">
                <h4>基础要求</h4>
                <ul>
                  <li>用户等级 ≥ {{ creationRules.minLevel }}</li>
                  <li>注册时间 ≥ {{ creationRules.minRegisterDays }}天</li>
                  <li>信誉分数 ≥ {{ creationRules.minCreditScore }}</li>
                </ul>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="rule-item">
                <h4>部落限制</h4>
                <ul>
                  <li>最大成员数：{{ creationRules.maxMembers }}人</li>
                  <li>部落名称长度：{{ creationRules.nameMinLength }}-{{ creationRules.nameMaxLength }}字符</li>
                  <li>描述长度：≤ {{ creationRules.descMaxLength }}字符</li>
                </ul>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="rule-item">
                <h4>审核标准</h4>
                <ul>
                  <li>{{ creationRules.autoApprove ? '自动审核' : '人工审核' }}</li>
                  <li>敏感词检测：{{ creationRules.sensitiveWordCheck ? '开启' : '关闭' }}</li>
                  <li>重复名称检测：{{ creationRules.duplicateNameCheck ? '开启' : '关闭' }}</li>
                </ul>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 待审核部落列表 -->
        <el-card class="approval-table-card">
          <template #header>
            <div class="card-header">
              <span>待审核部落申请</span>
              <div class="header-actions">
                <el-button type="success" @click="handleBatchApprove" :disabled="!selectedApplications.length">
                  <el-icon><Check /></el-icon>
                  批量通过
                </el-button>
                <el-button type="danger" @click="handleBatchReject" :disabled="!selectedApplications.length">
                  <el-icon><Close /></el-icon>
                  批量拒绝
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="pendingApplications"
            v-loading="approvalLoading"
            @selection-change="handleApplicationSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="tribeName" label="部落名称" min-width="200">
              <template #default="{ row }">
                <div class="tribe-info">
                  <div class="tribe-name">{{ row.tribeName }}</div>
                  <div class="tribe-meta">
                    <el-tag :type="getCategoryTagType(row.category)" size="small">{{ getCategoryName(row.category) }}</el-tag>
                    <span class="creator">创建者：{{ row.creatorName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="部落描述" min-width="250">
              <template #default="{ row }">
                <el-tooltip :content="row.description" placement="top">
                  <span class="description-text">{{ row.description }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="targetMembers" label="目标人数" width="100">
              <template #default="{ row }">
                <span>{{ row.targetMembers }}人</span>
              </template>
            </el-table-column>
            <el-table-column prop="riskLevel" label="风险等级" width="120">
              <template #default="{ row }">
                <el-tag :type="getRiskLevelTagType(row.riskLevel)">
                  {{ getRiskLevelText(row.riskLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" label="申请时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.submitTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="waitingTime" label="等待时长" width="120">
              <template #default="{ row }">
                <span :class="getWaitingTimeClass(row.waitingTime)">
                  {{ formatWaitingTime(row.waitingTime) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewApplicationDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="approveApplication(row)">
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button type="danger" size="small" @click="rejectApplication(row)">
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="approvalPagination.page"
              v-model:page-size="approvalPagination.size"
              :total="approvalPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleApprovalSizeChange"
              @current-change="handleApprovalPageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="成员管理" name="members">
        <!-- 成员统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><UserFilled /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ memberStats.totalMembers }}</div>
                  <div class="stat-label">总成员数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ memberStats.activeMembers }}</div>
                  <div class="stat-label">活跃成员</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ memberStats.reportedMembers }}</div>
                  <div class="stat-label">被举报成员</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Lock /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ memberStats.bannedMembers }}</div>
                  <div class="stat-label">被封禁成员</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 成员权限管理 -->
        <el-card class="permissions-card">
          <template #header>
            <div class="card-header">
              <span>成员权限设置</span>
              <el-button type="primary" @click="showPermissionDialog = true">
                <el-icon><Key /></el-icon>
                权限配置
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="permissions-content">
            <el-col :span="8">
              <div class="permission-group">
                <h4>部落长权限</h4>
                <div class="permission-list">
                  <el-tag v-for="perm in permissions.leader" :key="perm" type="danger" class="perm-tag">
                    {{ getPermissionName(perm) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="permission-group">
                <h4>管理员权限</h4>
                <div class="permission-list">
                  <el-tag v-for="perm in permissions.admin" :key="perm" type="warning" class="perm-tag">
                    {{ getPermissionName(perm) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="permission-group">
                <h4>普通成员权限</h4>
                <div class="permission-list">
                  <el-tag v-for="perm in permissions.member" :key="perm" type="info" class="perm-tag">
                    {{ getPermissionName(perm) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 成员违规处理 -->
        <el-card class="violations-card">
          <template #header>
            <div class="card-header">
              <span>成员违规处理</span>
              <div class="header-actions">
                <el-select v-model="violationFilter" placeholder="筛选违规类型" clearable>
                  <el-option label="恶意刷屏" value="spam" />
                  <el-option label="人身攻击" value="attack" />
                  <el-option label="发布广告" value="advertisement" />
                  <el-option label="传播谣言" value="rumor" />
                  <el-option label="其他" value="other" />
                </el-select>
              </div>
            </div>
          </template>

          <el-table
            :data="memberViolations"
            v-loading="violationLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="memberName" label="成员" width="120">
              <template #default="{ row }">
                <div class="member-info">
                  <el-avatar :src="row.memberAvatar" :size="32">{{ row.memberName.charAt(0) }}</el-avatar>
                  <span class="member-name">{{ row.memberName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="tribeName" label="所属部落" width="150" />
            <el-table-column prop="violationType" label="违规类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getViolationTypeTagType(row.violationType)">
                  {{ getViolationTypeText(row.violationType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="违规描述" min-width="200">
              <template #default="{ row }">
                <el-tooltip :content="row.description" placement="top">
                  <span class="description-text">{{ row.description }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="reportCount" label="举报次数" width="100">
              <template #default="{ row }">
                <el-tag :type="row.reportCount > 3 ? 'danger' : 'warning'">
                  {{ row.reportCount }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reportTime" label="举报时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.reportTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewViolationDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="warning" size="small" @click="warnMember(row)">
                  <el-icon><Warning /></el-icon>
                  警告
                </el-button>
                <el-button type="danger" size="small" @click="banMember(row)">
                  <el-icon><Lock /></el-icon>
                  封禁
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 成员活跃度分析 -->
        <el-card class="activity-analysis-card">
          <template #header>
            <span>成员活跃度分析</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <h4>活跃度分布</h4>
                <div class="chart-placeholder" ref="activityDistributionChartRef">
                  <div class="chart-mock">活跃度分布图表</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <h4>成员增长趋势</h4>
                <div class="chart-placeholder" ref="memberGrowthChartRef">
                  <div class="chart-mock">成员增长趋势图表</div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div class="activity-insights">
            <h4>活跃度洞察</h4>
            <div class="insights-grid">
              <div class="insight-item">
                <el-icon><TrendCharts /></el-icon>
                <span>本周新增成员较上周增长25%，部落吸引力持续提升</span>
              </div>
              <div class="insight-item">
                <el-icon><Star /></el-icon>
                <span>高活跃成员主要集中在学习讨论类部落，建议加强互动</span>
              </div>
              <div class="insight-item">
                <el-icon><Warning /></el-icon>
                <span>部分部落存在成员流失现象，建议关注部落运营质量</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="活动组织" name="activities">
        <!-- 活动统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Calendar /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ activityStats.totalActivities }}</div>
                  <div class="stat-label">总活动数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ activityStats.ongoingActivities }}</div>
                  <div class="stat-label">进行中活动</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ activityStats.totalParticipants }}</div>
                  <div class="stat-label">总参与人次</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Trophy /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ activityStats.completedActivities }}</div>
                  <div class="stat-label">已完成活动</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 活动模板管理 -->
        <el-card class="activity-templates-card">
          <template #header>
            <div class="card-header">
              <span>活动模板库</span>
              <el-button type="primary" @click="showCreateTemplateDialog = true">
                <el-icon><Plus /></el-icon>
                创建模板
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="templates-grid">
            <el-col :span="8" v-for="template in activityTemplates" :key="template.id">
              <el-card class="template-item" :class="{ 'template-popular': template.isPopular }">
                <div class="template-header">
                  <h3>{{ template.name }}</h3>
                  <el-tag v-if="template.isPopular" type="success" size="small">热门</el-tag>
                </div>
                <div class="template-description">{{ template.description }}</div>
                <div class="template-config">
                  <div class="config-item">
                    <span class="config-label">类型：</span>
                    <span class="config-value">{{ getActivityTypeName(template.type) }}</span>
                  </div>
                  <div class="config-item">
                    <span class="config-label">建议时长：</span>
                    <span class="config-value">{{ template.suggestedDuration }}</span>
                  </div>
                  <div class="config-item">
                    <span class="config-label">使用次数：</span>
                    <span class="config-value">{{ template.usageCount }}次</span>
                  </div>
                </div>
                <div class="template-actions">
                  <el-button type="primary" size="small" @click="useTemplate(template)">
                    <el-icon><Check /></el-icon>
                    使用模板
                  </el-button>
                  <el-button type="warning" size="small" @click="editTemplate(template)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button type="danger" size="small" @click="deleteTemplate(template)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>

        <!-- 活动审核管理 -->
        <el-card class="activity-approval-card">
          <template #header>
            <div class="card-header">
              <span>活动审核</span>
              <div class="header-actions">
                <el-button type="success" @click="handleBatchApproveActivity" :disabled="!selectedActivities.length">
                  <el-icon><Check /></el-icon>
                  批量通过
                </el-button>
                <el-button type="danger" @click="handleBatchRejectActivity" :disabled="!selectedActivities.length">
                  <el-icon><Close /></el-icon>
                  批量拒绝
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="pendingActivities"
            v-loading="activityLoading"
            @selection-change="handleActivitySelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" label="活动标题" min-width="200">
              <template #default="{ row }">
                <div class="activity-info">
                  <div class="activity-title">{{ row.title }}</div>
                  <div class="activity-meta">
                    <el-tag :type="getActivityTypeTagType(row.type)" size="small">{{ getActivityTypeName(row.type) }}</el-tag>
                    <span class="organizer">组织者：{{ row.organizerName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="tribeName" label="所属部落" width="150" />
            <el-table-column prop="startTime" label="开始时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.startTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="expectedParticipants" label="预期参与" width="100">
              <template #default="{ row }">
                <span>{{ row.expectedParticipants }}人</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getActivityStatusTagType(row.status)">
                  {{ getActivityStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewActivityDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="approveActivity(row)">
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button type="danger" size="small" @click="rejectActivity(row)">
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 活动效果分析 -->
        <el-card class="activity-analysis-card">
          <template #header>
            <span>活动效果分析</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <h4>活动参与度趋势</h4>
                <div class="chart-placeholder" ref="activityParticipationChartRef">
                  <div class="chart-mock">活动参与度趋势图表</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <h4>活动类型分布</h4>
                <div class="chart-placeholder" ref="activityTypeDistributionChartRef">
                  <div class="chart-mock">活动类型分布饼图</div>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 热门活动排行 -->
          <div class="popular-activities">
            <h4>热门活动排行</h4>
            <el-table :data="popularActivities" stripe style="width: 100%">
              <el-table-column type="index" label="排名" width="80">
                <template #default="{ $index }">
                  <el-tag :type="getRankTagType($index + 1)" size="small">
                    第{{ $index + 1 }}名
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="活动标题" min-width="200" />
              <el-table-column prop="tribeName" label="部落" width="150" />
              <el-table-column prop="participantCount" label="参与人数" width="120" />
              <el-table-column prop="satisfactionScore" label="满意度" width="120">
                <template #default="{ row }">
                  <el-rate v-model="row.satisfactionScore" disabled show-score />
                </template>
              </el-table-column>
              <el-table-column prop="completionRate" label="完成率" width="120">
                <template #default="{ row }">
                  <el-progress :percentage="row.completionRate" :color="getProgressColor(row.completionRate)" />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="数据分析" name="analytics">
        <!-- 综合数据统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ analyticsStats.totalTribes }}</div>
                  <div class="stat-label">总部落数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ analyticsStats.avgMembersPerTribe }}</div>
                  <div class="stat-label">平均成员数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><ChatDotRound /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ analyticsStats.avgActivityLevel }}</div>
                  <div class="stat-label">平均活跃度</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ analyticsStats.retentionRate }}%</div>
                  <div class="stat-label">成员留存率</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 数据图表展示 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>部落增长趋势</span>
              </template>
              <div class="chart-container" ref="tribeGrowthChartRef">
                <div class="chart-mock">部落增长趋势图表</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>部落分类分布</span>
              </template>
              <div class="chart-container" ref="tribeCategoryChartRef">
                <div class="chart-mock">部落分类分布饼图</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 部落排行榜 -->
        <el-card class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>部落排行榜</span>
              <el-radio-group v-model="rankingPeriod" size="small">
                <el-radio-button label="today">今日</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
              </el-radio-group>
            </div>
          </template>

          <el-table :data="tribeRanking" stripe style="width: 100%">
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <el-tag :type="getRankTagType($index + 1)" size="small">
                  第{{ $index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="部落名称" min-width="200">
              <template #default="{ row }">
                <div class="tribe-info">
                  <el-avatar :src="row.avatar" :size="32">{{ row.name.charAt(0) }}</el-avatar>
                  <span class="tribe-name">{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="category" label="分类" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="memberCount" label="成员数" width="100" />
            <el-table-column prop="activityCount" label="活动数" width="100" />
            <el-table-column prop="activityLevel" label="活跃度" width="120">
              <template #default="{ row }">
                <el-progress :percentage="row.activityLevel" :color="getProgressColor(row.activityLevel)" />
              </template>
            </el-table-column>
            <el-table-column prop="score" label="综合评分" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.score" disabled show-score />
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 数据洞察 -->
        <el-card class="insights-card">
          <template #header>
            <span>数据洞察与建议</span>
          </template>

          <div class="insights-content">
            <div class="insight-section">
              <h4>成长趋势分析</h4>
              <div class="insights-list">
                <div class="insight-item">
                  <el-icon><TrendCharts /></el-icon>
                  <span>本月新增部落数量较上月增长35%，用户创建部落积极性提高</span>
                </div>
                <div class="insight-item">
                  <el-icon><User /></el-icon>
                  <span>平均每个部落成员数为{{ analyticsStats.avgMembersPerTribe }}人，建议优化推荐算法</span>
                </div>
              </div>
            </div>

            <div class="insight-section">
              <h4>活跃度分析</h4>
              <div class="insights-list">
                <div class="insight-item">
                  <el-icon><ChatDotRound /></el-icon>
                  <span>学习讨论类部落活跃度最高，建议增加此类部落的推广力度</span>
                </div>
                <div class="insight-item">
                  <el-icon><Warning /></el-icon>
                  <span>部分部落存在成员流失现象，建议加强部落长培训和支持</span>
                </div>
              </div>
            </div>

            <div class="insight-section">
              <h4>运营建议</h4>
              <div class="insights-list">
                <div class="insight-item">
                  <el-icon><Star /></el-icon>
                  <span>可考虑推出部落等级制度，激励部落长提升管理质量</span>
                </div>
                <div class="insight-item">
                  <el-icon><Trophy /></el-icon>
                  <span>建议定期举办跨部落活动，增强平台凝聚力</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock, Check, Close, Timer, Setting, View, UserFilled, Connection, Warning, Lock,
  Key, Calendar, VideoPlay, User, Trophy, Plus, Edit, Delete, DataAnalysis, TrendCharts,
  ChatDotRound, Star
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('approval')
const approvalLoading = ref(false)
const violationLoading = ref(false)
const activityLoading = ref(false)
const selectedApplications = ref([])
const selectedActivities = ref([])
const showRulesDialog = ref(false)
const showPermissionDialog = ref(false)
const showCreateTemplateDialog = ref(false)

// 审核统计数据
const approvalStats = reactive({
  pendingApproval: 23,
  approvedToday: 15,
  rejectedToday: 3,
  avgProcessTime: 2.5
})

// 成员统计数据
const memberStats = reactive({
  totalMembers: 12456,
  activeMembers: 8934,
  reportedMembers: 45,
  bannedMembers: 12
})

// 活动统计数据
const activityStats = reactive({
  totalActivities: 456,
  ongoingActivities: 23,
  totalParticipants: 5678,
  completedActivities: 389
})

// 数据分析统计
const analyticsStats = reactive({
  totalTribes: 234,
  avgMembersPerTribe: 53,
  avgActivityLevel: 75,
  retentionRate: 85
})

// 部落创建规则
const creationRules = reactive({
  minLevel: 5,
  minRegisterDays: 30,
  minCreditScore: 80,
  maxMembers: 500,
  nameMinLength: 2,
  nameMaxLength: 20,
  descMaxLength: 200,
  autoApprove: false,
  sensitiveWordCheck: true,
  duplicateNameCheck: true
})

// 成员权限设置
const permissions = reactive({
  leader: ['manage_members', 'create_activity', 'edit_tribe', 'delete_tribe'],
  admin: ['manage_members', 'create_activity', 'moderate_content'],
  member: ['participate_activity', 'post_content', 'invite_friends']
})

// 待审核申请
const pendingApplications = ref([
  {
    id: 1,
    tribeName: '高考冲刺学习小组',
    description: '专注高考复习，互相监督，共同进步',
    creatorName: '学霸小王',
    category: 'study',
    targetMembers: 50,
    riskLevel: 'low',
    submitTime: '2024-01-15 10:30:00',
    waitingTime: 120
  },
  {
    id: 2,
    tribeName: '编程技术交流群',
    description: '分享编程经验，讨论技术问题',
    creatorName: '程序员小李',
    category: 'tech',
    targetMembers: 100,
    riskLevel: 'medium',
    submitTime: '2024-01-15 14:20:00',
    waitingTime: 60
  }
])

// 成员违规记录
const memberViolations = ref([
  {
    id: 1,
    memberName: '违规用户A',
    memberAvatar: '',
    tribeName: '学习交流群',
    violationType: 'spam',
    description: '频繁发送无关信息，干扰正常讨论',
    reportCount: 3,
    reportTime: '2024-01-15 16:30:00'
  }
])

// 活动模板
const activityTemplates = ref([
  {
    id: 1,
    name: '学习打卡挑战',
    description: '21天学习打卡活动，培养学习习惯',
    type: 'challenge',
    suggestedDuration: '21天',
    usageCount: 156,
    isPopular: true
  },
  {
    id: 2,
    name: '知识竞赛',
    description: '在线知识竞赛，检验学习成果',
    type: 'competition',
    suggestedDuration: '2小时',
    usageCount: 89,
    isPopular: false
  },
  {
    id: 3,
    name: '学习分享会',
    description: '成员分享学习经验和心得',
    type: 'sharing',
    suggestedDuration: '1小时',
    usageCount: 234,
    isPopular: true
  }
])

// 待审核活动
const pendingActivities = ref([
  {
    id: 1,
    title: '高考数学专题讲座',
    type: 'lecture',
    organizerName: '张老师',
    tribeName: '高考冲刺群',
    startTime: '2024-01-20 19:00:00',
    expectedParticipants: 50,
    status: 'pending'
  }
])

// 热门活动
const popularActivities = ref([
  {
    title: '21天学习打卡挑战',
    tribeName: '学习交流群',
    participantCount: 234,
    satisfactionScore: 4.8,
    completionRate: 85
  },
  {
    title: '编程技能竞赛',
    tribeName: '程序员部落',
    participantCount: 156,
    satisfactionScore: 4.6,
    completionRate: 92
  }
])

// 部落排行榜
const tribeRanking = ref([
  {
    name: '高考冲刺学习小组',
    avatar: '',
    category: 'study',
    memberCount: 234,
    activityCount: 45,
    activityLevel: 95,
    score: 4.9
  },
  {
    name: '编程技术交流群',
    avatar: '',
    category: 'tech',
    memberCount: 189,
    activityCount: 38,
    activityLevel: 88,
    score: 4.7
  }
])

// 分页数据
const approvalPagination = reactive({
  page: 1,
  size: 20,
  total: 100
})

// 筛选条件
const violationFilter = ref('')
const rankingPeriod = ref('today')

// 工具函数
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    study: '学习讨论',
    tech: '技术交流',
    hobby: '兴趣爱好',
    sports: '运动健身',
    life: '生活分享'
  }
  return categoryMap[category] || category
}

const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    study: 'primary',
    tech: 'success',
    hobby: 'warning',
    sports: 'danger',
    life: 'info'
  }
  return typeMap[category] || 'default'
}

const getRiskLevelTagType = (level: string) => {
  const levelMap: Record<string, string> = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return levelMap[level] || 'default'
}

const getRiskLevelText = (level: string) => {
  const levelMap: Record<string, string> = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return levelMap[level] || level
}

const getWaitingTimeClass = (minutes: number) => {
  if (minutes > 240) return 'text-danger'
  if (minutes > 120) return 'text-warning'
  return 'text-success'
}

const formatWaitingTime = (minutes: number) => {
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins}分钟`
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getPermissionName = (permission: string) => {
  const permissionMap: Record<string, string> = {
    manage_members: '成员管理',
    create_activity: '创建活动',
    edit_tribe: '编辑部落',
    delete_tribe: '删除部落',
    moderate_content: '内容审核',
    participate_activity: '参与活动',
    post_content: '发布内容',
    invite_friends: '邀请好友'
  }
  return permissionMap[permission] || permission
}

const getViolationTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    spam: 'danger',
    attack: 'danger',
    advertisement: 'warning',
    rumor: 'warning',
    other: 'info'
  }
  return typeMap[type] || 'default'
}

const getViolationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    spam: '恶意刷屏',
    attack: '人身攻击',
    advertisement: '发布广告',
    rumor: '传播谣言',
    other: '其他'
  }
  return typeMap[type] || type
}

const getActivityTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    challenge: '挑战活动',
    competition: '竞赛活动',
    sharing: '分享活动',
    lecture: '讲座活动',
    discussion: '讨论活动'
  }
  return typeMap[type] || type
}

const getActivityTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    challenge: 'warning',
    competition: 'danger',
    sharing: 'success',
    lecture: 'primary',
    discussion: 'info'
  }
  return typeMap[type] || 'default'
}

const getActivityStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    ongoing: 'primary',
    completed: 'info'
  }
  return statusMap[status] || 'default'
}

const getActivityStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    ongoing: '进行中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 事件处理函数
const handleApplicationSelectionChange = (selection: any[]) => {
  selectedApplications.value = selection
}

const handleActivitySelectionChange = (selection: any[]) => {
  selectedActivities.value = selection
}

const handleBatchApprove = () => {
  ElMessageBox.confirm('确定要批量通过选中的部落申请吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量审核通过成功')
    // TODO: 调用API
  })
}

const handleBatchReject = () => {
  ElMessageBox.confirm('确定要批量拒绝选中的部落申请吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量拒绝成功')
    // TODO: 调用API
  })
}

const viewApplicationDetails = (application: any) => {
  console.log('查看申请详情:', application)
  // TODO: 实现申请详情查看
}

const approveApplication = (application: any) => {
  ElMessage.success(`部落申请"${application.tribeName}"已通过`)
  // TODO: 调用API
}

const rejectApplication = (application: any) => {
  ElMessage.warning(`部落申请"${application.tribeName}"已拒绝`)
  // TODO: 调用API
}

const viewViolationDetails = (violation: any) => {
  console.log('查看违规详情:', violation)
  // TODO: 实现违规详情查看
}

const warnMember = (violation: any) => {
  ElMessage.success(`已对成员"${violation.memberName}"发出警告`)
  // TODO: 调用API
}

const banMember = (violation: any) => {
  ElMessageBox.confirm(`确定要封禁成员"${violation.memberName}"吗？`, '确认操作', {
    type: 'error'
  }).then(() => {
    ElMessage.success('成员已被封禁')
    // TODO: 调用API
  })
}

const useTemplate = (template: any) => {
  ElMessage.success(`已使用模板"${template.name}"`)
  // TODO: 调用API
}

const editTemplate = (template: any) => {
  console.log('编辑模板:', template)
  // TODO: 实现模板编辑
}

const deleteTemplate = (template: any) => {
  ElMessageBox.confirm(`确定要删除模板"${template.name}"吗？`, '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('模板已删除')
    // TODO: 调用API
  })
}

const handleBatchApproveActivity = () => {
  ElMessageBox.confirm('确定要批量通过选中的活动吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量审核通过成功')
    // TODO: 调用API
  })
}

const handleBatchRejectActivity = () => {
  ElMessageBox.confirm('确定要批量拒绝选中的活动吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量拒绝成功')
    // TODO: 调用API
  })
}

const viewActivityDetails = (activity: any) => {
  console.log('查看活动详情:', activity)
  // TODO: 实现活动详情查看
}

const approveActivity = (activity: any) => {
  ElMessage.success(`活动"${activity.title}"已通过`)
  // TODO: 调用API
}

const rejectActivity = (activity: any) => {
  ElMessage.warning(`活动"${activity.title}"已拒绝`)
  // TODO: 调用API
}

const handleApprovalSizeChange = (size: number) => {
  approvalPagination.size = size
  // TODO: 重新加载数据
}

const handleApprovalPageChange = (page: number) => {
  approvalPagination.page = page
  // TODO: 重新加载数据
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.tribes-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.info {
  background-color: #409eff;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.danger {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rules-content {
  margin-top: 16px;
}

.rule-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.rule-item h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.rule-item ul {
  margin: 0;
  padding-left: 16px;
  color: #606266;
}

.rule-item li {
  margin-bottom: 8px;
  font-size: 14px;
}

.tribe-info {
  display: flex;
  flex-direction: column;
}

.tribe-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.tribe-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.creator {
  color: #909399;
}

.description-text {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}

.text-warning {
  color: #e6a23c;
  font-weight: 600;
}

.text-success {
  color: #67c23a;
  font-weight: 600;
}

.permissions-content {
  margin-top: 16px;
}

.permission-group {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.permission-group h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.perm-tag {
  margin-bottom: 8px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-name {
  font-weight: 500;
  color: #303133;
}

.templates-grid {
  margin-top: 16px;
}

.template-item {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.template-item:hover {
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.template-popular {
  border: 2px solid #67c23a;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.template-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.template-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 16px;
  line-height: 1.4;
}

.template-config {
  margin-bottom: 16px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.config-label {
  color: #909399;
}

.config-value {
  color: #303133;
  font-weight: 500;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.activity-info {
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.organizer {
  color: #909399;
}

.chart-container {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 300px;
}

.chart-container h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-placeholder {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

.chart-mock {
  color: #909399;
  font-size: 14px;
}

.popular-activities {
  margin-top: 20px;
}

.popular-activities h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.activity-insights {
  margin-top: 20px;
}

.activity-insights h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.insights-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.insight-item .el-icon {
  color: #409eff;
  font-size: 16px;
}

.chart-card {
  margin-bottom: 20px;
}

.insights-content {
  padding: 16px;
}

.insight-section {
  margin-bottom: 24px;
}

.insight-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .tribes-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .rules-content .el-col,
  .permissions-content .el-col,
  .templates-grid .el-col {
    margin-bottom: 10px;
  }
}
</style>

