<template>
  <div class="listening-management">
    <div class="page-header">
      <h1>听力材料管理</h1>
      <p>管理听力练习音频、文本和题目，提供多样化的听力训练内容</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="听力材料" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建听力材料
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedMaterials.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedMaterials.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索标题或内容"
              style="width: 200px"
              clearable
              @change="loadMaterials"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="categoryFilter" placeholder="分类筛选" style="width: 120px" @change="loadMaterials">
              <el-option label="全部分类" value="" />
              <el-option label="日常对话" value="daily" />
              <el-option label="新闻报道" value="news" />
              <el-option label="学术讲座" value="academic" />
              <el-option label="商务英语" value="business" />
              <el-option label="旅游英语" value="travel" />
            </el-select>
            <el-select v-model="difficultyFilter" placeholder="难度筛选" style="width: 100px" @change="loadMaterials">
              <el-option label="全部难度" value="" />
              <el-option label="简单" :value="1" />
              <el-option label="较易" :value="2" />
              <el-option label="中等" :value="3" />
              <el-option label="较难" :value="4" />
              <el-option label="困难" :value="5" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadMaterials">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>

        <!-- 听力材料列表 -->
        <el-table
          :data="materials"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="听力材料" min-width="350">
            <template #default="{ row }">
              <div class="material-info">
                <div class="material-header">
                  <span class="material-title">{{ row.title }}</span>
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="playAudio(row)"
                    v-if="row.audio_url"
                  >
                    <el-icon><VideoPlay /></el-icon>
                  </el-button>
                </div>
                <div class="material-description">{{ row.description }}</div>
                <div class="material-meta">
                  <el-tag size="small" :type="getCategoryTagType(row.category)">
                    {{ getCategoryName(row.category) }}
                  </el-tag>
                  <span class="difficulty-badge" :class="`difficulty-${row.difficulty_level}`">
                    {{ getDifficultyName(row.difficulty_level) }}
                  </span>
                  <span class="duration-info">{{ formatDuration(row.duration) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="音频信息" width="150">
            <template #default="{ row }">
              <div class="audio-info">
                <div class="audio-item">
                  <span class="audio-label">时长:</span>
                  <span class="audio-value">{{ formatDuration(row.duration) }}</span>
                </div>
                <div class="audio-item">
                  <span class="audio-label">语速:</span>
                  <span class="audio-value">{{ getSpeechRateName(row.speech_rate) }}</span>
                </div>
                <div class="audio-item">
                  <span class="audio-label">口音:</span>
                  <span class="audio-value">{{ getAccentName(row.accent) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="练习统计" width="120">
            <template #default="{ row }">
              <div class="practice-stats">
                <div class="stat-item">
                  <span class="stat-label">播放:</span>
                  <span class="stat-value">{{ row.play_count }}次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">完成:</span>
                  <span class="stat-value">{{ row.completion_count }}次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">正确率:</span>
                  <span class="stat-value accuracy-rate">{{ (row.accuracy_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="题目数量" width="100">
            <template #default="{ row }">
              <span class="question-count">{{ row.question_count }}题</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewMaterial(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="text" size="small" @click="editMaterial(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="toggleMaterialStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '归档' : '发布' }}
              </el-button>
              <el-button type="text" size="small" class="danger" @click="deleteMaterial(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadMaterials"
            @current-change="loadMaterials"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="分类分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">分类分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="难度分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">难度分布图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="学习效果趋势">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">学习效果趋势图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑听力材料对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingMaterial ? '编辑听力材料' : '新建听力材料'"
      width="900px"
      @close="resetForm"
    >
      <el-form :model="materialForm" :rules="materialRules" ref="materialFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="标题" prop="title">
              <el-input v-model="materialForm.title" placeholder="请输入听力材料标题" />
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input v-model="materialForm.description" type="textarea" rows="3" placeholder="请输入听力材料描述" />
            </el-form-item>
            <el-form-item label="分类" prop="category">
              <el-select v-model="materialForm.category" placeholder="请选择分类">
                <el-option label="日常对话" value="daily" />
                <el-option label="新闻报道" value="news" />
                <el-option label="学术讲座" value="academic" />
                <el-option label="商务英语" value="business" />
                <el-option label="旅游英语" value="travel" />
                <el-option label="文化娱乐" value="culture" />
              </el-select>
            </el-form-item>
            <el-form-item label="难度等级" prop="difficulty_level">
              <el-select v-model="materialForm.difficulty_level" placeholder="请选择难度等级">
                <el-option label="简单" :value="1" />
                <el-option label="较易" :value="2" />
                <el-option label="中等" :value="3" />
                <el-option label="较难" :value="4" />
                <el-option label="困难" :value="5" />
              </el-select>
            </el-form-item>
            <el-form-item label="语速" prop="speech_rate">
              <el-select v-model="materialForm.speech_rate" placeholder="请选择语速">
                <el-option label="慢速" value="slow" />
                <el-option label="正常" value="normal" />
                <el-option label="快速" value="fast" />
              </el-select>
            </el-form-item>
            <el-form-item label="口音" prop="accent">
              <el-select v-model="materialForm.accent" placeholder="请选择口音">
                <el-option label="美式英语" value="american" />
                <el-option label="英式英语" value="british" />
                <el-option label="澳式英语" value="australian" />
                <el-option label="加拿大英语" value="canadian" />
              </el-select>
            </el-form-item>
            <el-form-item label="标签" prop="tags">
              <el-select 
                v-model="materialForm.tags" 
                multiple 
                filterable 
                allow-create 
                placeholder="请选择或输入标签"
              >
                <el-option label="初学者" value="初学者" />
                <el-option label="中级" value="中级" />
                <el-option label="高级" value="高级" />
                <el-option label="考试" value="考试" />
                <el-option label="实用" value="实用" />
              </el-select>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="音频文件" name="audio">
            <el-form-item label="音频文件" prop="audio_url">
              <div class="audio-upload">
                <el-upload
                  class="upload-demo"
                  :show-file-list="false"
                  :before-upload="beforeAudioUpload"
                  :http-request="uploadAudio"
                  accept="audio/*"
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    上传音频文件
                  </el-button>
                </el-upload>
                <div v-if="materialForm.audio_url" class="audio-preview">
                  <audio :src="materialForm.audio_url" controls style="width: 100%;"></audio>
                  <el-button type="link" size="small" class="remove-btn" @click="removeAudio">
                    <el-icon><Delete /></el-icon>
                    删除音频
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="音频时长">
              <el-input-number v-model="materialForm.duration" :min="1" :max="3600" placeholder="秒" />
              <span class="form-tip">音频时长（秒），上传音频后自动获取</span>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="听力文本" name="transcript">
            <el-form-item label="听力原文" prop="transcript">
              <el-input 
                v-model="materialForm.transcript" 
                type="textarea" 
                rows="10" 
                placeholder="请输入听力材料的完整文本内容"
              />
            </el-form-item>
            <el-form-item label="关键词汇">
              <el-input 
                v-model="materialForm.key_vocabulary" 
                type="textarea" 
                rows="3" 
                placeholder="请输入关键词汇，每行一个"
              />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="练习题目" name="questions">
            <div class="questions-section">
              <div class="section-header">
                <span>听力题目</span>
                <el-button type="primary" size="small" @click="addQuestion">
                  <el-icon><Plus /></el-icon>
                  添加题目
                </el-button>
              </div>
              <div v-for="(question, index) in materialForm.questions" :key="index" class="question-form">
                <div class="question-header">
                  <span class="question-label">题目 {{ index + 1 }}</span>
                  <el-button type="link" size="small" class="danger" @click="removeQuestion(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-form-item label="题目类型">
                  <el-select v-model="question.type" placeholder="请选择题目类型">
                    <el-option label="单选题" value="single_choice" />
                    <el-option label="多选题" value="multiple_choice" />
                    <el-option label="填空题" value="fill_blank" />
                    <el-option label="判断题" value="true_false" />
                  </el-select>
                </el-form-item>
                <el-form-item label="题目内容">
                  <el-input v-model="question.content" placeholder="请输入题目内容" />
                </el-form-item>
                <el-form-item label="选项" v-if="['single_choice', 'multiple_choice'].includes(question.type)">
                  <div class="options-list">
                    <div v-for="(option, optIndex) in question.options" :key="optIndex" class="option-input">
                      <el-input v-model="option.text" :placeholder="`选项 ${String.fromCharCode(65 + optIndex)}`" />
                      <el-checkbox v-model="option.is_correct">正确答案</el-checkbox>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="正确答案" v-if="!['single_choice', 'multiple_choice'].includes(question.type)">
                  <el-input v-model="question.correct_answer" placeholder="请输入正确答案" />
                </el-form-item>
                <el-form-item label="解析">
                  <el-input v-model="question.explanation" type="textarea" rows="2" placeholder="请输入答案解析" />
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveMaterial" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 听力材料详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="听力材料详情"
      width="800px"
    >
      <div class="material-detail" v-if="viewingMaterial">
        <div class="detail-header">
          <h2 class="material-title">{{ viewingMaterial.title }}</h2>
          <el-button 
            type="primary" 
            @click="playAudio(viewingMaterial)"
            v-if="viewingMaterial.audio_url"
          >
            <el-icon><VideoPlay /></el-icon>
            播放音频
          </el-button>
        </div>
        
        <div class="detail-section">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">分类：</span>
              <span class="value">{{ getCategoryName(viewingMaterial.category) }}</span>
            </div>
            <div class="info-item">
              <span class="label">难度：</span>
              <span class="value">{{ getDifficultyName(viewingMaterial.difficulty_level) }}</span>
            </div>
            <div class="info-item">
              <span class="label">时长：</span>
              <span class="value">{{ formatDuration(viewingMaterial.duration) }}</span>
            </div>
            <div class="info-item">
              <span class="label">语速：</span>
              <span class="value">{{ getSpeechRateName(viewingMaterial.speech_rate) }}</span>
            </div>
            <div class="info-item">
              <span class="label">口音：</span>
              <span class="value">{{ getAccentName(viewingMaterial.accent) }}</span>
            </div>
            <div class="info-item">
              <span class="label">题目数：</span>
              <span class="value">{{ viewingMaterial.question_count }}题</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>描述</h3>
          <div class="description-detail">{{ viewingMaterial.description }}</div>
        </div>
        
        <div class="detail-section" v-if="viewingMaterial.transcript">
          <h3>听力原文</h3>
          <div class="transcript-detail">{{ viewingMaterial.transcript }}</div>
        </div>
        
        <div class="detail-section" v-if="viewingMaterial.key_vocabulary">
          <h3>关键词汇</h3>
          <div class="vocabulary-detail">
            <div v-for="word in viewingMaterial.key_vocabulary.split('\n')" :key="word" class="vocabulary-item">
              {{ word }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, VideoPlay
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedMaterials = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const editingMaterial = ref(null)
const viewingMaterial = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const categoryFilter = ref('')
const difficultyFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 听力材料列表
const materials = ref([
  {
    id: '1',
    title: '日常购物对话',
    description: '在商店购买衣服时的对话练习，包含价格询问、尺寸选择等常用表达',
    category: 'daily',
    difficulty_level: 2,
    duration: 180,
    speech_rate: 'normal',
    accent: 'american',
    audio_url: '',
    transcript: 'A: Can I help you find something?\nB: Yes, I\'m looking for a jacket...',
    key_vocabulary: 'jacket\nsize\nprice\ntry on',
    questions: [],
    question_count: 5,
    play_count: 234,
    completion_count: 189,
    accuracy_rate: 0.82,
    tags: ['初学者', '实用'],
    status: 'published',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const materialForm = reactive({
  title: '',
  description: '',
  category: '',
  difficulty_level: 1,
  speech_rate: 'normal',
  accent: 'american',
  audio_url: '',
  duration: 0,
  transcript: '',
  key_vocabulary: '',
  questions: [],
  tags: []
})

// 表单验证规则
const materialRules = {
  title: [
    { required: true, message: '请输入听力材料标题', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入听力材料描述', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  difficulty_level: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ]
}

// 方法
const loadMaterials = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取听力材料列表
    console.log('Loading listening materials...')
  } catch (error) {
    ElMessage.error('加载听力材料列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedMaterials.value = selection
}

const getCategoryName = (category) => {
  const categories = {
    daily: '日常对话',
    news: '新闻报道',
    academic: '学术讲座',
    business: '商务英语',
    travel: '旅游英语',
    culture: '文化娱乐'
  }
  return categories[category] || category
}

const getCategoryTagType = (category) => {
  const types = {
    daily: 'primary',
    news: 'success',
    academic: 'warning',
    business: 'danger',
    travel: 'info'
  }
  return types[category] || ''
}

const getDifficultyName = (level) => {
  const names = {
    1: '简单',
    2: '较易',
    3: '中等',
    4: '较难',
    5: '困难'
  }
  return names[level] || `难度${level}`
}

const getSpeechRateName = (rate) => {
  const rates = {
    slow: '慢速',
    normal: '正常',
    fast: '快速'
  }
  return rates[rate] || rate
}

const getAccentName = (accent) => {
  const accents = {
    american: '美式',
    british: '英式',
    australian: '澳式',
    canadian: '加拿大'
  }
  return accents[accent] || accent
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const playAudio = (material) => {
  if (material.audio_url) {
    const audio = new Audio(material.audio_url)
    audio.play().catch(error => {
      ElMessage.error('播放音频失败')
      console.error('Audio play error:', error)
    })
  } else {
    ElMessage.warning('该听力材料暂无音频文件')
  }
}

const viewMaterial = (material) => {
  viewingMaterial.value = material
  showViewDialog.value = true
}

const editMaterial = (material) => {
  editingMaterial.value = material
  Object.assign(materialForm, material)
  showCreateDialog.value = true
}

const resetForm = () => {
  editingMaterial.value = null
  Object.assign(materialForm, {
    title: '',
    description: '',
    category: '',
    difficulty_level: 1,
    speech_rate: 'normal',
    accent: 'american',
    audio_url: '',
    duration: 0,
    transcript: '',
    key_vocabulary: '',
    questions: [],
    tags: []
  })
  formActiveTab.value = 'basic'
}

const addQuestion = () => {
  materialForm.questions.push({
    type: 'single_choice',
    content: '',
    options: [
      { text: '', is_correct: false },
      { text: '', is_correct: false },
      { text: '', is_correct: false },
      { text: '', is_correct: false }
    ],
    correct_answer: '',
    explanation: ''
  })
}

const removeQuestion = (index) => {
  materialForm.questions.splice(index, 1)
}

const beforeAudioUpload = (file) => {
  const isAudio = file.type.startsWith('audio/')
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isAudio) {
    ElMessage.error('只能上传音频文件!')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('音频文件大小不能超过 50MB!')
    return false
  }
  return true
}

const uploadAudio = async (options) => {
  // TODO: 实现音频上传逻辑
  console.log('Uploading audio...', options.file)
  // 模拟上传成功
  materialForm.audio_url = URL.createObjectURL(options.file)

  // 获取音频时长
  const audio = new Audio(materialForm.audio_url)
  audio.addEventListener('loadedmetadata', () => {
    materialForm.duration = Math.floor(audio.duration)
  })

  ElMessage.success('音频文件上传成功')
}

const removeAudio = () => {
  materialForm.audio_url = ''
  materialForm.duration = 0
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', materialForm)
    showCreateDialog.value = false
    ElMessage.success('听力材料已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveMaterial = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing material...', materialForm)
    showCreateDialog.value = false
    ElMessage.success(editingMaterial.value ? '听力材料更新成功' : '听力材料创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleMaterialStatus = async (material) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling material status...', material)
}

const deleteMaterial = async (material) => {
  try {
    await ElMessageBox.confirm('确定要删除这个听力材料吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting material...', material)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing materials...', selectedMaterials.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving materials...', selectedMaterials.value)
}

onMounted(() => {
  loadMaterials()
})
</script>

<style scoped>
.listening-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.material-info {
  padding: 8px 0;
}

.material-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.material-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.material-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.material-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.difficulty-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.difficulty-badge.difficulty-1 {
  background-color: #67c23a;
}

.difficulty-badge.difficulty-2 {
  background-color: #95d475;
}

.difficulty-badge.difficulty-3 {
  background-color: #e6a23c;
}

.difficulty-badge.difficulty-4 {
  background-color: #f78989;
}

.difficulty-badge.difficulty-5 {
  background-color: #f56c6c;
}

.duration-info {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.audio-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.audio-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.audio-label {
  color: #909399;
}

.audio-value {
  color: #606266;
  font-weight: 500;
}

.practice-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.accuracy-rate {
  color: #67c23a !important;
}

.question-count {
  font-weight: 600;
  color: #409eff;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.audio-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audio-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.remove-btn {
  color: #f56c6c;
  align-self: flex-start;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.questions-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #303133;
}

.question-form {
  margin-bottom: 24px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-label {
  font-weight: 500;
  color: #303133;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.material-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.material-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 60px;
}

.info-item .value {
  color: #303133;
}

.description-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
}

.transcript-detail {
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 6px;
  color: #303133;
  line-height: 1.8;
  white-space: pre-wrap;
  border-left: 4px solid #409eff;
}

.vocabulary-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.vocabulary-item {
  padding: 4px 8px;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
