<template>
  <div class="users-page page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统用户信息、权限和状态</p>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon primary">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(userStats.totalUsers) }}</div>
                <div class="stat-label">总用户数</div>
                <div :class="['stat-trend', userStats.totalUsersGrowthRate >= 0 ? 'positive' : 'negative']">
                  <el-icon><ArrowUp /></el-icon>
                  <span>{{ formatGrowthRate(userStats.totalUsersGrowthRate) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(userStats.activeUsers) }}</div>
                <div class="stat-label">活跃用户</div>
                <div :class="['stat-trend', userStats.activeUsersGrowthRate >= 0 ? 'positive' : 'negative']">
                  <el-icon><ArrowUp /></el-icon>
                  <span>{{ formatGrowthRate(userStats.activeUsersGrowthRate) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon warning">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(userStats.premiumUsers) }}</div>
                <div class="stat-label">会员用户</div>
                <div :class="['stat-trend', userStats.premiumUsersGrowthRate >= 0 ? 'positive' : 'negative']">
                  <el-icon><ArrowUp /></el-icon>
                  <span>{{ formatGrowthRate(userStats.premiumUsersGrowthRate) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon info">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ userStats.newUsersToday }}</div>
                <div class="stat-label">今日新增</div>
                <div :class="['stat-trend', userStats.newUsersTodayGrowthRate >= 0 ? 'positive' : 'negative']">
                  <el-icon><ArrowUp /></el-icon>
                  <span>{{ formatGrowthRate(userStats.newUsersTodayGrowthRate) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card" shadow="hover">
      <!-- 标题栏 -->
      <div class="table-header">
        <div class="table-title">
          <h3>用户列表</h3>
        </div>
      </div>
      
      <div class="search-form">
        <!-- 第一行：搜索框和所有操作按钮 -->
        <el-row :gutter="16" class="search-row">
          <el-col :xs="24" :sm="10" :md="11" :lg="12">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索昵称、姓名、邮箱或手机号"
              clearable
              @keyup.enter="handleSearch"
              class="search-input"
              size="default"
            >
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :xs="24" :sm="14" :md="13" :lg="12">
            <div class="search-actions">
              <el-button 
                type="primary" 
                @click="handleSearch" 
                :loading="searchLoading"
                size="small"
                class="search-btn"
              >
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleRefresh" :loading="loading" size="small" class="action-btn">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button 
                @click="handleReset"
                size="small"
                class="reset-btn"
              >
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
              <el-button type="primary" @click="handleAddUser" size="small" class="primary-btn">
                <el-icon><Plus /></el-icon>
                添加用户
              </el-button>
              <el-button type="success" @click="handleExportUsers" size="small" class="success-btn">
                <el-icon><Download /></el-icon>
                导出用户
              </el-button>
            </div>
          </el-col>
        </el-row>
        
        <!-- 第二行：筛选项 -->
        <el-row :gutter="16" class="filter-row">
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-select 
              v-model="searchForm.status" 
              placeholder="用户状态" 
              clearable 
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option label="活跃" value="ACTIVE" />
              <el-option label="禁用" value="INACTIVE" />
              <el-option label="已删除" value="DELETED" />
            </el-select>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-select 
              v-model="searchForm.membership" 
              placeholder="会员状态" 
              clearable 
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option label="普通会员" value="REGULAR" />
              <el-option label="VIP会员" value="VIP" />
            </el-select>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-select 
              v-model="searchForm.gender" 
              placeholder="性别" 
              clearable 
              class="filter-select"
            >
               <el-option label="全部" value="" />
               <el-option label="男" value="MALE" />
               <el-option label="女" value="FEMALE" />
               <el-option label="其他" value="OTHER" />
               <el-option label="未知" value="UNKNOWN" />
             </el-select>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-select 
              v-model="searchForm.level" 
              placeholder="等级" 
              clearable 
              class="filter-select"
            >
               <el-option label="全部" value="" />
               <el-option label="等级一" value="LEVEL_ONE" />
               <el-option label="等级二" value="LEVEL_TWO" />
               <el-option label="等级三" value="LEVEL_THREE" />
               <el-option label="等级四" value="LEVEL_FOUR" />
               <el-option label="等级五" value="LEVEL_FIVE" />
               <el-option label="等级六" value="LEVEL_SIX" />
             </el-select>
          </el-col>
          <el-col :xs="24" :sm="16" :md="12" :lg="8">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="date-picker"
            />
          </el-col>
        </el-row>
      </div>
      
      <!-- 用户表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="userList"
          style="width: 100%; cursor: pointer; min-width: 1200px"
          @sort-change="handleSortChange"
          @row-click="handleRowClick"
        >
        <el-table-column prop="nickname" label="昵称" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="full_name" label="姓名" width="120" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.gender === 'MALE'" type="primary" size="small">男</el-tag>
            <el-tag v-else-if="row.gender === 'FEMALE'" type="danger" size="small">女</el-tag>
            <el-tag v-else-if="row.gender === 'OTHER'" type="warning" size="small">其他</el-tag>
            <el-tag v-else type="info" size="small">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ACTIVE' ? 'success' : row.status === 'INACTIVE' ? 'danger' : 'info'" size="small">
              {{ row.status === 'ACTIVE' ? '活跃' : row.status === 'INACTIVE' ? '禁用' : '已删除' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="membership" label="会员" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.membership === 'REGULAR' ? 'info' : row.membership === 'VIP' ? 'danger' : 'warning'" 
              size="small"
            >
              {{ 
                row.membership === 'REGULAR' ? '普通' : 
                row.membership === 'VIP' ? 'VIP' : '未知'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="等级" width="80">
          <template #default="{ row }">
            <el-tag 
              :type="getLevelTagType(row.level)" 
              size="small"
            >
              {{ getLevelDisplayText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_study_time" label="学习时长" width="100">
          <template #default="{ row }">
            {{ Math.floor(row.total_study_time / 60) }}h{{ row.total_study_time % 60 }}m
          </template>
        </el-table-column>
        <el-table-column prop="registration_time" label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.registration_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              :type="row.status === 'ACTIVE' ? 'danger' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
              :disabled="row.status === 'DELETED'"
            >
              {{ row.status === 'ACTIVE' ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :disabled="row.status === 'DELETED'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加用户对话框 -->
    <el-dialog
      v-model="addUserDialogVisible"
      title="添加用户"
      width="600px"
      :close-on-click-modal="false"
      @close="resetAddUserForm"
    >
      <el-form
        ref="addUserFormRef"
        :model="addUserForm"
        :rules="addUserRules"
        label-width="100px"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="addUserForm.nickname" placeholder="请输入用户昵称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="full_name">
              <el-input v-model="addUserForm.full_name" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="addUserForm.email" placeholder="请输入邮箱地址" type="email" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="addUserForm.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="addUserForm.gender" placeholder="请选择性别" class="w-full">
                <el-option label="男" value="MALE" />
                <el-option label="女" value="FEMALE" />
                <el-option label="其他" value="OTHER" />
                <el-option label="未知" value="UNKNOWN" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="addUserForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="addUserForm.status" placeholder="请选择状态" class="w-full">
                <el-option label="活跃" value="ACTIVE" />
                <el-option label="非活跃" value="INACTIVE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会员类型" prop="membership">
              <el-select v-model="addUserForm.membership" placeholder="请选择会员类型" class="w-full">
                <el-option label="普通会员" value="REGULAR" />
                <el-option label="VIP会员" value="VIP" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="个人简介" prop="bio">
          <el-input
            v-model="addUserForm.bio"
            type="textarea"
            :rows="3"
            placeholder="请输入个人简介（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addUserDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitAddUser" :loading="addUserLoading">
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  User, UserFilled, Calendar, ArrowUp, Plus, Download, Refresh, Search, RefreshLeft, Star
} from '@element-plus/icons-vue'
import { userApi } from '@/api/user'
import { dataExport } from '@/utils/dataExport'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

// 用户统计数据
const userStats = reactive({
  totalUsers: 0,
  activeUsers: 0,
  premiumUsers: 0,
  newUsersToday: 0,
  totalUsersGrowthRate: 0,
  activeUsersGrowthRate: 0,
  premiumUsersGrowthRate: 0,
  newUsersTodayGrowthRate: 0
})
const userList = ref([])
const searchForm = reactive({
  keyword: '',
  status: '',
  membership: '',
  gender: '',
  level: '',
  dateRange: undefined as [string, string] | undefined
})
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 添加用户对话框相关
const addUserDialogVisible = ref(false)
const addUserLoading = ref(false)
const addUserFormRef = ref<FormInstance>()
// 生成随机测试数据的函数
const generateTestData = () => {
  const randomNum = Math.floor(Math.random() * 999) + 1
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const randomName = names[Math.floor(Math.random() * names.length)]
  const genders = ['MALE', 'FEMALE'] as const
  const randomGender = genders[Math.floor(Math.random() * genders.length)]
  
  return {
    nickname: `测试用户${randomNum.toString().padStart(3, '0')}`,
    email: `test${randomNum.toString().padStart(3, '0')}@example.com`,
    phone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
    full_name: randomName,
    gender: randomGender,
    password: '123456',
    status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE',
    membership: 'REGULAR' as 'REGULAR' | 'VIP',
    bio: `这是一个测试用户账号（${randomName}），用于验证系统功能是否正常工作。`
  }
}

const addUserForm = reactive(generateTestData())

// 表单验证规则
const addUserRules = reactive({
  nickname: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email' as const, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择用户状态', trigger: 'change' }
  ],
  membership: [
    { required: true, message: '请选择会员类型', trigger: 'change' }
  ]
})

// 生命周期
onMounted(() => {
  loadUsers()
  loadUserStats()
})

// 方法
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      search: searchForm.keyword || undefined,
      status: searchForm.status || undefined,
      membership: searchForm.membership || undefined,
      gender: searchForm.gender || undefined,
      level: searchForm.level || undefined,
      start_date: searchForm.dateRange?.[0] || undefined,
      end_date: searchForm.dateRange?.[1] || undefined
    }
    
    const response = await userApi.getUsers(params)
    userList.value = response.items || []
    pagination.total = response.total || 0
    
    // 更新统计数据
    userStats.totalUsers = response.total || 0
    userStats.activeUsers = (response.items || []).filter((u: any) => u.status === 'ACTIVE').length
    userStats.premiumUsers = (response.items || []).filter((u: any) => u.is_premium).length
    
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 加载用户概览统计
const loadUserStats = async () => {
  try {
    const response = await userApi.getUsersOverview()
    Object.assign(userStats, {
      totalUsers: response.total_users || 0,
      activeUsers: response.active_users || 0,
      premiumUsers: response.premium_users || 0,
      newUsersToday: response.new_users_today || 0,
      totalUsersGrowthRate: response.total_users_growth_rate || 0,
      activeUsersGrowthRate: response.active_users_growth_rate || 0,
      premiumUsersGrowthRate: response.premium_users_growth_rate || 0,
      newUsersTodayGrowthRate: response.new_users_today_growth_rate || 0
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    membership: '',
    gender: '',
    level: '',
    dateRange: undefined
  })
  handleSearch()
}

const handleRefresh = () => {
  loadUsers()
}

const handleView = (row: any) => {
  router.push(`/users/${row.id}`)
}

const handleEdit = (row: any) => {
  ElMessage.info('编辑功能开发中')
}

const handleToggleStatus = async (row: any) => {
  try {
    const action = row.status === 'ACTIVE' ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}用户 ${row.nickname} 吗？`, '确认操作', {
      type: 'warning'
    })
    
    await userApi.toggleUserStatus(row.id)
    row.status = row.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.nickname}" 吗？\n\n删除后该用户将无法登录系统，此操作不可恢复！`,
      '确认删除用户',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    await userApi.deleteUser(row.id)
    ElMessage.success('用户删除成功')
    
    // 刷新用户列表和统计数据
    loadUsers()
    loadUserStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败，请稍后重试')
    }
  }
}

const handleSortChange = ({ column, prop, order }: any) => {
  // TODO: 实现排序
  console.log('Sort change:', { column, prop, order })
}

const handleRowClick = (row: any) => {
  router.push(`/users/${row.id}`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadUsers()
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取等级显示文本
const getLevelDisplayText = (level: string) => {
  const levelMap: Record<string, string> = {
    'LEVEL_ONE': '等级一',
    'LEVEL_TWO': '等级二',
    'LEVEL_THREE': '等级三',
    'LEVEL_FOUR': '等级四',
    'LEVEL_FIVE': '等级五',
    'LEVEL_SIX': '等级六'
  }
  return levelMap[level] || '未知等级'
}

// 获取等级标签类型
const getLevelTagType = (level: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'LEVEL_ONE': 'info',
    'LEVEL_TWO': 'success',
    'LEVEL_THREE': 'warning',
    'LEVEL_FOUR': 'danger',
    'LEVEL_FIVE': 'primary',
    'LEVEL_SIX': 'danger'
  }
  return typeMap[level] || 'info'
}

// 格式化数字
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化增长率
const formatGrowthRate = (rate: number): string => {
  const sign = rate >= 0 ? '+' : ''
  return `${sign}${rate.toFixed(1)}%`
}

// 工具栏方法
const handleAddUser = () => {
  // 生成新的测试数据
  const testData = generateTestData()
  Object.assign(addUserForm, testData)
  addUserDialogVisible.value = true
}

const handleExportUsers = async () => {
  try {
    // 显示加载状态
    const loadingMessage = ElMessage({
      message: '正在导出用户数据...',
      type: 'info',
      duration: 0
    })
    
    // 分批获取所有用户数据
    const baseParams = {
      search: searchForm.keyword || undefined,
      status: searchForm.status || undefined,
      membership: searchForm.membership || undefined,
      gender: searchForm.gender || undefined,
      level: searchForm.level || undefined,
      start_date: searchForm.dateRange?.[0] || undefined,
      end_date: searchForm.dateRange?.[1] || undefined
    }
    
    // 先获取第一页数据以确定总数
    const firstPageResponse = await userApi.getUsers({
      ...baseParams,
      page: 1,
      page_size: 100
    })
    
    if (!firstPageResponse.items || firstPageResponse.total === 0) {
      loadingMessage.close()
      ElMessage.warning('没有用户数据可导出')
      return
    }
    
    let allUsers = [...firstPageResponse.items]
    const totalPages = Math.ceil(firstPageResponse.total / 100)
    
    // 获取剩余页面的数据
    for (let page = 2; page <= totalPages; page++) {
      const pageResponse = await userApi.getUsers({
        ...baseParams,
        page,
        page_size: 100
      })
      if (pageResponse.items) {
        allUsers.push(...pageResponse.items)
      }
    }
    
    // 处理导出数据，转换为中文字段名
    const exportData = allUsers.map(user => ({
      '用户ID': user.id,
      '昵称': user.nickname,
      '邮箱': user.email || '',
      '手机号': user.phone || '',
      '真实姓名': user.full_name || '',
      '性别': user.gender === 'MALE' ? '男' : user.gender === 'FEMALE' ? '女' : user.gender === 'OTHER' ? '其他' : '未知',
      '用户状态': user.status === 'ACTIVE' ? '正常' : user.status === 'INACTIVE' ? '禁用' : '已删除',
      '会员状态': user.membership === 'FREE' ? '免费用户' : user.membership === 'BASIC' ? '基础会员' : user.membership === 'PREMIUM' ? '高级会员' : 'VIP会员',
      '是否会员': user.is_premium ? '是' : '否',
      '等级': getLevelDisplayText(user.level),
      '个人简介': user.bio || '',
      '所在地': user.location || '',
      '注册时间': formatDate(user.registration_time),
      '最后登录': user.last_login ? formatDate(user.last_login) : '从未登录',
      '会员到期时间': user.premium_expires_at ? formatDate(user.premium_expires_at) : ''
    }))
    
    // 使用dataExport工具导出Excel
    await dataExport.excel({
      filename: `用户列表_${new Date().toISOString().split('T')[0]}`,
      sheetName: '用户数据',
      data: exportData,
      module: 'user' as any,
      includeTimestamp: true
    })
    
    loadingMessage.close()
    ElMessage.success(`成功导出 ${allUsers.length} 条用户数据`)
    
  } catch (error) {
    console.error('导出用户数据失败:', error)
    ElMessage.error('导出用户数据失败，请稍后重试')
  }
}



// 添加用户相关方法
const resetAddUserForm = () => {
  addUserFormRef.value?.resetFields()
  const testData = generateTestData()
  Object.assign(addUserForm, testData)
}

const handleSubmitAddUser = async () => {
  if (!addUserFormRef.value) return
  
  try {
    // 验证表单
    await addUserFormRef.value.validate()
    
    addUserLoading.value = true
    
    // 准备提交数据
    const submitData = {
      nickname: addUserForm.nickname,
      email: addUserForm.email,
      phone: addUserForm.phone || undefined,
      full_name: addUserForm.full_name || undefined,
      gender: (addUserForm.gender || undefined) as 'MALE' | 'FEMALE' | 'OTHER' | undefined,
      password: addUserForm.password,
      status: addUserForm.status,
      membership: addUserForm.membership,
      is_premium: addUserForm.membership === 'VIP',
      bio: addUserForm.bio || undefined
    }
    
    // 调用API创建用户
    await userApi.createUser(submitData)
    
    ElMessage.success('用户添加成功')
    addUserDialogVisible.value = false
    resetAddUserForm()
    
    // 刷新用户列表
    loadUsers()
    loadUserStats()
    
  } catch (error) {
    console.error('添加用户失败:', error)
    ElMessage.error('添加用户失败，请检查输入信息')
  } finally {
    addUserLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.users-page {
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}

.search-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.search-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.table-header {
  padding: 20px 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;
}

.table-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.table-actions .el-button:hover {
  transform: translateY(-1px);
}

.search-form {
  padding: 0 0 24px 0;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(64, 158, 255, 0.08);
}

.search-row {
  margin-bottom: 20px;
  align-items: stretch;
  min-height: 40px;
}

.filter-row {
  margin-top: 20px;
  align-items: stretch;
  min-height: 40px;
}

.search-input {
  width: 100%;
  height: 40px;
}

.search-input .el-input__wrapper {
  height: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e4e7ed;
  background: #ffffff;
}

.search-input .el-input__wrapper:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #c0c4cc;
  transform: translateY(-1px);
}

.search-input .el-input__wrapper.is-focus {
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
  border-color: #409eff;
  transform: translateY(-2px);
}

.search-icon {
  color: #909399;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input:hover .search-icon {
  color: #409eff;
  transform: scale(1.1);
}

.search-input .el-input__wrapper.is-focus .search-icon {
  color: #409eff;
  transform: scale(1.15) rotate(15deg);
}

/* 添加加载状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 添加按钮点击反馈 */
.search-btn:active,
.action-btn:active,
.reset-btn:active,
.primary-btn:active,
.success-btn:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

/* 添加焦点可见性 */
.search-btn:focus-visible,
.action-btn:focus-visible,
.reset-btn:focus-visible,
.primary-btn:focus-visible,
.success-btn:focus-visible {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 优化表单容器的视觉层次 */
.search-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #409eff 0%, #67c23a 50%, #409eff 100%);
  border-radius: 12px 12px 0 0;
  opacity: 0.6;
}

.search-form {
  position: relative;
  overflow: hidden;
}

/* 添加微妙的网格背景 */
.search-form::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(64, 158, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(64, 158, 255, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

.search-form > * {
  position: relative;
  z-index: 1;
}

.search-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  height: 40px;
  align-items: stretch;
  flex-wrap: wrap;
}

.search-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  min-width: 56px;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border: none;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.35);
  background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
}

.action-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  min-width: 56px;
  padding: 0 16px;
  border: 1px solid #e4e7ed;
  color: #606266;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.action-btn:hover {
  border-color: #c0c4cc;
  color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(144, 147, 153, 0.15);
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.reset-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  min-width: 56px;
  padding: 0 16px;
  border: 1px solid #e4e7ed;
  color: #606266;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.reset-btn:hover {
  border-color: #409eff;
  color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(64, 158, 255, 0.15);
  background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
}

.primary-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  min-width: 72px;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border: none;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.35);
  background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
}

.success-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  min-width: 72px;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.success-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.35);
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
}

.filter-select {
  width: 100%;
  height: 40px;
}

.filter-select .el-select__wrapper {
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e4e7ed;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.filter-select .el-select__wrapper:hover {
  border-color: #c0c4cc;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.filter-select .el-select__wrapper.is-focused {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.date-picker {
  width: 100%;
  height: 40px;
}

.date-picker .el-date-editor {
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e4e7ed;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.date-picker .el-date-editor:hover {
  border-color: #c0c4cc;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.date-picker .el-date-editor.is-active {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .search-form {
    padding: 20px;
  }
  
  .search-actions {
    gap: 6px;
  }
  
  .search-btn, .action-btn, .reset-btn {
    min-width: 52px;
    padding: 0 14px;
    font-size: 13px;
  }
  
  .primary-btn, .success-btn {
    min-width: 68px;
    padding: 0 18px;
    font-size: 13px;
  }
}

@media (max-width: 992px) {
  .search-form {
    padding: 16px;
  }
  
  .search-actions {
    gap: 4px;
    height: 36px;
  }
  
  .search-input {
    height: 36px;
  }
  
  .search-input .el-input__wrapper {
    height: 36px;
  }
  
  .filter-select {
    height: 36px;
  }
  
  .filter-select .el-select__wrapper {
    height: 36px;
  }
  
  .date-picker {
    height: 36px;
  }
  
  .date-picker .el-date-editor {
    height: 36px;
  }
  
  .search-btn, .action-btn, .reset-btn {
    height: 36px;
    min-width: 48px;
    padding: 0 12px;
    font-size: 12px;
  }
  
  .primary-btn, .success-btn {
    height: 36px;
    min-width: 64px;
    padding: 0 16px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .table-header {
    padding: 16px 0;
  }
  
  .search-form {
    padding: 12px;
  }
  
  .search-row {
    flex-direction: column;
    margin-bottom: 16px;
    min-height: 32px;
  }
  
  .search-row .el-col {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .filter-row {
    margin-top: 16px;
    min-height: 32px;
  }
  
  .search-actions {
    justify-content: center;
    margin-top: 12px;
    gap: 3px;
    height: 32px;
    flex-wrap: wrap;
  }
  
  .search-input {
    height: 32px;
  }
  
  .search-input .el-input__wrapper {
    height: 32px;
  }
  
  .filter-select {
    height: 32px;
  }
  
  .filter-select .el-select__wrapper {
    height: 32px;
  }
  
  .date-picker {
    height: 32px;
  }
  
  .date-picker .el-date-editor {
    height: 32px;
  }
  
  .search-btn, .action-btn, .reset-btn {
    height: 32px;
    min-width: 44px;
    padding: 0 10px;
    font-size: 11px;
  }
  
  .primary-btn, .success-btn {
    height: 32px;
    min-width: 60px;
    padding: 0 14px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .table-actions {
    justify-content: center;
  }
  
  .table-title h3 {
    font-size: 16px;
    text-align: center;
    width: 100%;
  }
  
  .search-form {
    padding: 8px;
  }
  
  .search-actions {
    justify-content: center;
    flex-wrap: wrap;
    gap: 2px;
    height: auto;
  }
  
  .search-btn, .action-btn, .reset-btn, .primary-btn, .success-btn {
    flex: 1;
    min-width: auto;
    margin: 1px;
    height: 32px;
    font-size: 10px;
  }
}

// 表格容器样式
.table-container {
  width: 100%;
  overflow-x: auto;
  
  // 滚动条样式优化
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

// 表格固定列样式
:deep(.el-table) {
  .el-table__fixed-right {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }
  
  .el-table__fixed-right-patch {
    background-color: #fff;
  }
  
  // 操作列按钮样式优化
  .el-table__fixed-right .el-button {
    margin-right: 8px;
    
    &:last-child {
      margin-right: 0;
    }
  }
  
  // 固定列头部样式
  .el-table__fixed-right .el-table__header {
    background-color: #fafafa;
  }
}

// 添加用户对话框样式
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 表单样式优化
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 18px;
  }
  
  .el-input,
  .el-select {
    width: 100%;
  }
  
  .el-textarea {
    .el-textarea__inner {
      resize: vertical;
    }
  }
}

// 工具类
.w-full {
  width: 100%;
}
</style>
