{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./node_modules/axios/index.d.ts", "./node_modules/element-plus/es/utils/vue3.3.polyfill.d.ts", "./node_modules/dayjs/locale/types.d.ts", "./node_modules/dayjs/locale/index.d.ts", "./node_modules/dayjs/index.d.ts", "./node_modules/@types/lodash/common/common.d.ts", "./node_modules/@types/lodash/common/array.d.ts", "./node_modules/@types/lodash/common/collection.d.ts", "./node_modules/@types/lodash/common/date.d.ts", "./node_modules/@types/lodash/common/function.d.ts", "./node_modules/@types/lodash/common/lang.d.ts", "./node_modules/@types/lodash/common/math.d.ts", "./node_modules/@types/lodash/common/number.d.ts", "./node_modules/@types/lodash/common/object.d.ts", "./node_modules/@types/lodash/common/seq.d.ts", "./node_modules/@types/lodash/common/string.d.ts", "./node_modules/@types/lodash/common/util.d.ts", "./node_modules/@types/lodash/index.d.ts", "./node_modules/@vueuse/shared/index.d.ts", "./node_modules/@vueuse/core/index.d.ts", "./node_modules/memoize-one/dist/memoize-one.d.ts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "./node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "./node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "./node_modules/vue-router/dist/vue-router.d.ts", "./node_modules/@ctrl/tinycolor/dist/interfaces.d.ts", "./node_modules/@ctrl/tinycolor/dist/index.d.ts", "./node_modules/@ctrl/tinycolor/dist/css-color-names.d.ts", "./node_modules/@ctrl/tinycolor/dist/readability.d.ts", "./node_modules/@ctrl/tinycolor/dist/to-ms-filter.d.ts", "./node_modules/@ctrl/tinycolor/dist/from-ratio.d.ts", "./node_modules/@ctrl/tinycolor/dist/format-input.d.ts", "./node_modules/@ctrl/tinycolor/dist/random.d.ts", "./node_modules/@ctrl/tinycolor/dist/conversion.d.ts", "./node_modules/@ctrl/tinycolor/dist/public_api.d.ts", "./node_modules/async-validator/dist-types/interface.d.ts", "./node_modules/async-validator/dist-types/index.d.ts", "./node_modules/element-plus/es/index.d.ts", "./node_modules/@types/nprogress/index.d.ts", "./src/types/auth.ts", "./src/views/login.vue.ts", "./src/components/layout.vue.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/add-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/aim.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/alarm-clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/apple.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/avatar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/back.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/baseball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/basketball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bicycle.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bowl.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/briefcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/burger.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/calendar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cellphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cherry.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chicken.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chrome-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee-cup.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coin.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cold-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/comment.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/compass.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/connection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coordinate.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/copy-document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cpu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/credit-card.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/crop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-caret.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-analysis.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-board.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dessert.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/discount.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish-dot.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-copy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/download.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/drizzling.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit-pen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/element-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/expand.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/failed.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/female.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/files.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/film.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/filter.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/finished.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/first-aid-kit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/flag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-opened.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/food.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/football.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fork-spoon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fries.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/full-screen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/gold-medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grape.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grid.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/guide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/handbag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/headset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/histogram.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/home-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hot-water.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/house.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/info-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/iphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/key.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/knife-fork.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lightning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/link.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/list.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/loading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-information.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lollipop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magic-stick.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magnet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/male.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/management.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/map-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/memo.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/menu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mic.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/microphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/milk-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/minus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/money.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/monitor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon-night.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mostly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mouse.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mug.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute-notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/no-smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notebook.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/odometer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/office-building.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/open.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/operation.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/opportunity.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/orange.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/paperclip.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/partly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pear.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-rounded.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pie-chart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/place.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/platform.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pointer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/position.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/postcard.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pouring.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/present.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/price-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/printer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/promotion.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/quartz-watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/question-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/rank.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading-lamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refrigerator.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scale-to-original.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/school.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scissor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/search.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/semi-select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/service.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/set-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/setting.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/share.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ship.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-bag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-trolley.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/soccer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sold-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stopwatch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/success-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sugar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunny.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunrise.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-button.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/takeaway-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ticket.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tickets.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/timer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/toilet-paper.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tools.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trend-charts.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy-base.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/turn-off.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/umbrella.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/unlock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/van.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-pause.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-play.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/view.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warn-triangle-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watermelon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wind-power.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-in.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/index.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/index.d.ts", "./src/views/dashboard.vue.ts", "./src/api/user.ts", "./node_modules/xlsx/types/index.d.ts", "./node_modules/@types/file-saver/index.d.ts", "./src/utils/logger.ts", "./src/utils/dataexport.ts", "./src/views/users.vue.ts", "./src/views/userdetail.vue.ts", "./src/api/profile.ts", "./src/api/admin.ts", "./src/views/profile.vue.ts", "./src/types/performance.ts", "./src/utils/cache.ts", "./src/utils/systeminfo.ts", "./src/services/performanceservice.ts", "./src/stores/performance.ts", "./src/components/performanceoverview.vue.ts", "./src/views/system-monitoring/systemmonitoringlayout.vue.ts", "./node_modules/echarts/types/dist/echarts.d.ts", "./node_modules/echarts/index.d.ts", "./src/components/charts/basechart.vue.ts", "./src/components/charts/linechart.vue.ts", "./src/components/charts/piechart.vue.ts", "./src/components/charts/barchart.vue.ts", "./src/components/charts/radarchart.vue.ts", "./src/components/charts/doughnutchart.vue.ts", "./src/components/charts/areachart.vue.ts", "./src/components/charts/index.ts", "./src/views/system-monitoring/index.vue.ts", "./src/api/system-logs.ts", "./src/stores/user.ts", "./src/views/system-monitoring/logmanagement.vue.ts", "./src/utils/chartdataadapter.ts", "./src/views/system-monitoring/performancemonitoring.vue.ts", "./src/views/data-management/datamanagementlayout.vue.ts", "./src/views/data-management/databackup.vue.ts", "./src/utils/chartdatatransform.ts", "./src/views/data-management/dataanalysis.vue.ts", "./src/views/datasync.vue.ts", "./src/views/community/communitylayout.vue.ts", "./src/views/community/index.vue.ts", "./src/views/community/resources.vue.ts", "./src/views/community/studyrooms.vue.ts", "./src/views/community/topics.vue.ts", "./src/views/community/tribes.vue.ts", "./src/views/community/mentorship.vue.ts", "./src/views/community/experiences.vue.ts", "./src/views/community/activities.vue.ts", "./src/types/community.ts", "./src/views/community/wishes.vue.ts", "./src/views/community/games.vue.ts", "./src/store/user.ts", "./src/types/settings.ts", "./src/views/settings.vue.ts", "./src/views/advertising/advertisinglayout.vue.ts", "./src/views/advertising/index.vue.ts", "./src/views/advertising/banners.vue.ts", "./src/views/advertising/popups.vue.ts", "./src/views/question-bank/questionbanklayout.vue.ts", "./src/views/question-bank/index.vue.ts", "./src/views/question-bank/categories.vue.ts", "./src/views/question-bank/questions.vue.ts", "./src/views/question-bank/papers.vue.ts", "./src/views/question-bank/statistics.vue.ts", "./src/views/english-practice/englishpracticelayout.vue.ts", "./src/views/english-practice/index.vue.ts", "./src/views/english-practice/vocabulary.vue.ts", "./src/views/english-practice/listening.vue.ts", "./src/views/english-practice/speaking.vue.ts", "./src/views/english-practice/progress.vue.ts", "./src/views/psychology/psychologylayout.vue.ts", "./src/views/psychology/index.vue.ts", "./src/views/psychology/assessments.vue.ts", "./src/views/psychology/articles.vue.ts", "./src/views/psychology/appointments.vue.ts", "./src/views/psychology/crisis.vue.ts", "./src/views/courses/courseslayout.vue.ts", "./src/views/courses/index.vue.ts", "./src/views/courses/content.vue.ts", "./src/views/courses/instructors.vue.ts", "./src/views/courses/progress.vue.ts", "./src/views/courses/evaluations.vue.ts", "./src/views/software-update/softwareupdatelayout.vue.ts", "./src/views/software-update/index.vue.ts", "./src/views/software-update/versions.vue.ts", "./src/views/software-update/strategies.vue.ts", "./src/views/software-update/statistics.vue.ts", "./src/views/404.vue.ts", "./src/router/index.ts", "./src/utils/error-handler.ts", "./src/api/request.ts", "./src/api/auth.ts", "./src/store/auth.ts", "./src/app.vue.ts", "./src/components/errormonitor.vue.ts", "./src/components/loginpagepreview.vue.ts", "./src/components/settingsgroup.vue.ts", "./src/components/settings/basicsettings.vue.ts", "./src/components/settings/emailsettings.vue.ts", "./src/components/settings/logsettings.vue.ts", "./src/components/settings/securitysettings.vue.ts", "./src/components/settings/uisettings.vue.ts", "./src/views/analytics.vue.ts", "./src/views/app-updates/appupdateslayout.vue.ts", "./src/views/community/gamecircle.vue.ts", "./src/views/community/wishwall.vue.ts", "./src/views/course-management/courses.vue.ts", "./src/views/course-management/index.vue.ts", "./src/views/data-management/index.vue.ts", "./src/views/learning-analytics/behavior.vue.ts", "./src/views/learning-analytics/index.vue.ts", "./src/views/psychology/counselors.vue.ts", "./__vls_types.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./env.d.ts", "./src/utils/route-diagnostics.ts", "./src/main.ts", "./src/api/advertising.ts", "./src/api/app-updates.ts", "./src/api/community.ts", "./src/api/courses.ts", "./src/api/english-practice.ts", "./src/api/psychology.ts", "./src/api/question-bank.ts", "./src/api/settings.ts", "./src/stores/app.ts", "./src/stores/index.ts", "./src/test/chartdataadapter.test.ts", ".test.ts", "./src/test/settings.test.ts", "./src/test/setup.ts", "./src/types/element-plus.d.ts", "./src/types/global.d.ts", "./src/types/user.ts", "./src/utils/permission.ts", "./auto-imports.d.ts", "./components.d.ts", "./node_modules/element-plus/global.d.ts", "./src/views/login.vue", "./src/components/layout.vue", "./src/views/dashboard.vue", "./src/views/users.vue", "./src/views/userdetail.vue", "./src/views/sync.vue", "./src/views/analytics.vue", "./src/views/settings.vue", "./src/views/404.vue", "./src/app.vue", "./src/views/profile.vue", "./src/components/settings/basicsettings.vue", "./src/components/settings/securitysettings.vue", "./src/components/settings/emailsettings.vue", "./src/components/settings/logsettings.vue", "./src/components/settings/uisettings.vue"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0"], "root": [[99, 101], 397, 398, [401, 414], [417, 509], [516, 539]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[48, 50, 51, 56, 58, 60, 84, 535, 539, 540], [56, 58, 60, 84, 97, 539, 540], [56, 58, 60, 84, 97, 101, 413, 417, 418, 419, 420, 421, 422, 423, 491, 492, 493, 494, 495, 496, 497, 498, 540], [56, 58, 60, 84, 515, 539, 540], [52], [85], [86], [85, 86, 87, 88, 89, 90, 91, 92, 93], [56, 58, 60, 84, 539, 540], [102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394], [395], [80], [81, 82], [64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76], [65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], [46, 52, 53], [54], [46], [46, 47, 48, 50, 535], [47, 48, 49, 50, 535], [57, 77], [57], [95], [62], [61], [415], [47, 48, 49, 50, 56, 58, 60, 63, 76, 78, 79, 81, 83, 84, 94, 96, 539, 540], [56, 58, 60, 84, 97, 539], [56, 57, 60, 84, 539, 540], [514], [510], [511], [512, 513], [50, 55], [50], [51, 487], [51, 99, 487], [51, 59, 97, 409, 485, 486, 487, 489], [51, 449, 487], [51, 56, 58, 60, 84, 489, 539, 540], [51, 56, 58, 60, 84, 416, 417, 539, 540], [51, 56, 58, 60, 84, 396, 416, 539, 540], [51, 56, 58, 60, 84, 416, 539, 540], [51, 417, 418, 419, 420, 421, 422, 423], [51, 56, 58, 60, 84, 416, 486, 539, 540], [51, 56, 58, 60, 84, 97, 489, 539, 540], [51, 56, 58, 60, 84, 396, 539, 540], [51, 56, 58, 60, 84, 97, 396, 408, 412, 539, 540], [51, 56, 58, 60, 84, 97, 396, 449, 539, 540], [51, 56, 58, 60, 84, 97, 396, 485, 490, 515, 516, 517, 534, 535, 539, 540], [51, 84, 98, 100, 101, 397, 403, 404, 407, 414, 425, 428, 430, 431, 432, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 446, 447, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 489, 515], [51, 56, 58, 60, 84, 408, 409, 410, 539, 540], [51, 56, 58, 60, 84, 97, 99, 488, 539, 540], [51, 56, 58, 60, 84, 97, 405, 539, 540], [51, 56, 58, 60, 84, 539, 540], [51, 58, 427, 527], [51, 56, 58, 60, 84, 408, 411, 539, 540], [51, 408, 429], [51, 58, 84, 101, 489], [51, 58, 405, 407, 448], [51, 97, 449, 494, 495, 496, 497, 498], [51], [516, 535], [48, 50, 97, 516, 534], [51, 408], [51, 97, 399, 400, 401], [51, 59, 97, 487], [51, 489], [51, 84], [51, 56, 58, 60, 84, 97, 396, 539, 540], [51, 56, 58, 60, 84, 97, 396, 445, 539, 540], [51, 56, 58, 60, 84, 97, 396, 424, 539, 540], [51, 56, 58, 60, 84, 97, 396, 399, 400, 424, 433, 539, 540], [51, 56, 58, 60, 84, 97, 396, 399, 400, 424, 539, 540], [51, 56, 58, 60, 84, 97, 539, 540], [51, 56, 58, 60, 84, 97, 99, 489, 539, 540], [51, 56, 58, 60, 84, 97, 396, 405, 406, 489, 539, 540], [51, 56, 58, 60, 84, 97, 396, 448, 449, 539, 540], [51, 56, 58, 60, 84, 97, 396, 426, 427, 539, 540], [51, 56, 58, 60, 84, 97, 396, 408, 411, 412, 418, 424, 429, 539, 540], [51, 56, 58, 60, 84, 97, 396, 413, 539, 540], [51, 56, 58, 60, 84, 97, 396, 398, 539, 540], [51, 56, 58, 60, 84, 97, 396, 398, 402, 539, 540], [48, 50, 97, 526, 536], [51, 56, 58, 60, 84, 97, 485, 526, 531, 541], [56, 58, 60, 84, 526, 541], [56, 58, 60, 84, 541], [103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395], [46, 47, 48, 50], [47, 48, 49, 50], [47, 48, 49, 50, 56, 58, 60, 63, 76, 78, 79, 81, 83, 84, 94, 96, 541], [56, 57, 60, 84, 541], [48, 50, 51, 56, 58, 60, 84, 538, 541, 542], [48, 50, 51, 56, 58, 60, 84, 536, 540, 542], [48, 50, 51, 56, 58, 60, 84, 541], [513], [51, 59, 97, 485, 487, 489], [518], [515, 516], [51, 84, 98, 489, 526, 543, 544, 545, 546, 547, 548, 549, 550, 551], [51, 56, 58, 60, 84, 97, 99, 488, 541], [51, 56, 58, 60, 84, 97, 405, 541, 542], [396], [51, 58, 84, 489, 541], [51, 58, 405, 448, 552], [51, 97, 449, 553, 554, 555, 556], [526, 538]], "referencedMap": [[509, 1], [538, 2], [539, 3], [516, 4], [53, 5], [93, 6], [91, 6], [90, 7], [86, 6], [94, 8], [92, 7], [88, 7], [89, 7], [102, 9], [103, 9], [104, 9], [105, 9], [106, 9], [107, 9], [108, 9], [109, 9], [110, 9], [111, 9], [112, 9], [113, 9], [114, 9], [115, 9], [116, 9], [117, 9], [118, 9], [119, 9], [120, 9], [121, 9], [122, 9], [123, 9], [124, 9], [125, 9], [126, 9], [127, 9], [128, 9], [129, 9], [130, 9], [131, 9], [132, 9], [133, 9], [134, 9], [135, 9], [136, 9], [137, 9], [138, 9], [139, 9], [140, 9], [141, 9], [142, 9], [143, 9], [144, 9], [145, 9], [146, 9], [147, 9], [148, 9], [149, 9], [150, 9], [151, 9], [152, 9], [153, 9], [154, 9], [155, 9], [156, 9], [157, 9], [158, 9], [159, 9], [160, 9], [161, 9], [162, 9], [163, 9], [164, 9], [165, 9], [166, 9], [167, 9], [168, 9], [169, 9], [170, 9], [171, 9], [172, 9], [173, 9], [174, 9], [175, 9], [176, 9], [177, 9], [178, 9], [179, 9], [180, 9], [181, 9], [182, 9], [183, 9], [184, 9], [185, 9], [186, 9], [187, 9], [188, 9], [189, 9], [190, 9], [191, 9], [192, 9], [193, 9], [194, 9], [195, 9], [196, 9], [197, 9], [198, 9], [199, 9], [200, 9], [201, 9], [202, 9], [203, 9], [204, 9], [205, 9], [206, 9], [207, 9], [208, 9], [209, 9], [210, 9], [211, 9], [212, 9], [213, 9], [214, 9], [215, 9], [216, 9], [217, 9], [218, 9], [219, 9], [220, 9], [221, 9], [222, 9], [223, 9], [224, 9], [225, 9], [226, 9], [227, 9], [228, 9], [229, 9], [230, 9], [231, 9], [232, 9], [233, 9], [234, 9], [235, 9], [236, 9], [237, 9], [238, 9], [239, 9], [240, 9], [241, 9], [242, 9], [243, 9], [395, 10], [244, 9], [245, 9], [246, 9], [247, 9], [248, 9], [249, 9], [250, 9], [251, 9], [252, 9], [253, 9], [254, 9], [255, 9], [256, 9], [257, 9], [258, 9], [259, 9], [260, 9], [261, 9], [262, 9], [263, 9], [264, 9], [265, 9], [266, 9], [267, 9], [268, 9], [269, 9], [270, 9], [271, 9], [272, 9], [273, 9], [274, 9], [275, 9], [276, 9], [277, 9], [278, 9], [279, 9], [280, 9], [281, 9], [282, 9], [283, 9], [284, 9], [285, 9], [286, 9], [287, 9], [288, 9], [289, 9], [290, 9], [291, 9], [292, 9], [293, 9], [294, 9], [295, 9], [296, 9], [297, 9], [298, 9], [299, 9], [300, 9], [301, 9], [302, 9], [303, 9], [304, 9], [305, 9], [306, 9], [307, 9], [308, 9], [309, 9], [310, 9], [311, 9], [312, 9], [313, 9], [314, 9], [315, 9], [316, 9], [317, 9], [318, 9], [319, 9], [320, 9], [321, 9], [322, 9], [323, 9], [324, 9], [325, 9], [326, 9], [327, 9], [328, 9], [329, 9], [330, 9], [331, 9], [332, 9], [333, 9], [334, 9], [335, 9], [336, 9], [337, 9], [338, 9], [339, 9], [340, 9], [341, 9], [342, 9], [343, 9], [344, 9], [345, 9], [346, 9], [347, 9], [348, 9], [349, 9], [350, 9], [351, 9], [352, 9], [353, 9], [354, 9], [355, 9], [356, 9], [357, 9], [358, 9], [359, 9], [360, 9], [361, 9], [362, 9], [363, 9], [364, 9], [365, 9], [366, 9], [367, 9], [368, 9], [369, 9], [370, 9], [371, 9], [372, 9], [373, 9], [374, 9], [375, 9], [376, 9], [377, 9], [378, 9], [379, 9], [380, 9], [381, 9], [382, 9], [383, 9], [384, 9], [385, 9], [386, 9], [387, 9], [388, 9], [389, 9], [390, 9], [391, 9], [392, 9], [393, 9], [394, 9], [396, 11], [81, 12], [83, 13], [65, 14], [66, 15], [64, 16], [67, 17], [68, 18], [69, 19], [70, 20], [71, 21], [72, 22], [73, 23], [74, 24], [75, 25], [76, 26], [54, 27], [55, 28], [47, 29], [48, 30], [50, 31], [78, 32], [77, 33], [96, 34], [63, 35], [62, 36], [416, 37], [97, 38], [60, 9], [540, 39], [58, 40], [515, 41], [511, 42], [512, 43], [514, 44], [57, 9], [84, 9], [56, 45], [51, 46], [406, 47], [519, 47], [520, 47], [488, 48], [521, 47], [522, 47], [523, 47], [405, 47], [524, 47], [525, 47], [487, 49], [526, 50], [426, 47], [398, 47], [490, 51], [423, 52], [420, 52], [417, 53], [422, 54], [424, 55], [418, 52], [419, 52], [421, 54], [491, 56], [101, 57], [492, 58], [413, 59], [494, 60], [495, 60], [496, 60], [497, 60], [498, 60], [493, 60], [518, 61], [485, 62], [411, 63], [489, 64], [448, 65], [527, 66], [528, 67], [412, 68], [427, 66], [529, 69], [530, 70], [531, 71], [532, 72], [533, 73], [99, 73], [445, 73], [534, 74], [535, 75], [408, 73], [449, 73], [536, 73], [409, 73], [429, 76], [433, 73], [402, 77], [486, 78], [401, 79], [537, 79], [517, 80], [410, 73], [484, 66], [451, 66], [453, 81], [452, 58], [454, 81], [499, 66], [500, 66], [444, 81], [436, 66], [443, 81], [501, 81], [447, 82], [437, 81], [442, 81], [438, 81], [439, 81], [440, 81], [441, 81], [446, 82], [502, 81], [503, 81], [504, 58], [475, 81], [473, 66], [478, 81], [474, 83], [476, 81], [477, 81], [397, 81], [434, 84], [432, 81], [431, 66], [505, 85], [435, 86], [461, 66], [462, 83], [464, 81], [466, 81], [465, 81], [463, 81], [506, 81], [507, 58], [100, 87], [407, 88], [471, 81], [470, 81], [469, 81], [508, 81], [472, 81], [468, 81], [467, 66], [457, 81], [456, 83], [459, 81], [455, 66], [458, 81], [460, 81], [450, 89], [480, 81], [479, 66], [483, 81], [482, 81], [481, 81], [425, 83], [428, 90], [430, 91], [414, 92], [404, 93], [403, 94]], "exportedModulesMap": [[509, 1], [538, 95], [539, 96], [53, 5], [93, 6], [91, 6], [90, 7], [86, 6], [94, 8], [92, 7], [88, 7], [89, 7], [102, 97], [103, 98], [104, 98], [105, 98], [106, 98], [107, 98], [108, 98], [109, 98], [110, 98], [111, 98], [112, 98], [113, 98], [114, 98], [115, 98], [116, 98], [117, 98], [118, 98], [119, 98], [120, 98], [121, 98], [122, 98], [123, 98], [124, 98], [125, 98], [126, 98], [127, 98], [128, 98], [129, 98], [130, 98], [131, 98], [132, 98], [133, 98], [134, 98], [135, 98], [136, 98], [137, 98], [138, 98], [139, 98], [140, 98], [141, 98], [142, 98], [143, 98], [144, 98], [145, 98], [146, 98], [147, 98], [148, 98], [149, 98], [150, 98], [151, 98], [152, 98], [153, 98], [154, 98], [155, 98], [156, 98], [157, 98], [158, 98], [159, 98], [160, 98], [161, 98], [162, 98], [163, 98], [164, 98], [165, 98], [166, 98], [167, 98], [168, 98], [169, 98], [170, 98], [171, 98], [172, 98], [173, 98], [174, 98], [175, 98], [176, 98], [177, 98], [178, 98], [179, 98], [180, 98], [181, 98], [182, 98], [183, 98], [184, 98], [185, 98], [186, 98], [187, 98], [188, 98], [189, 98], [190, 98], [191, 98], [192, 98], [193, 98], [194, 98], [195, 98], [196, 98], [197, 98], [198, 98], [199, 98], [200, 98], [201, 98], [202, 98], [203, 98], [204, 98], [205, 98], [206, 98], [207, 98], [208, 98], [209, 98], [210, 98], [211, 98], [212, 98], [213, 98], [214, 98], [215, 98], [216, 98], [217, 98], [218, 98], [219, 98], [220, 98], [221, 98], [222, 98], [223, 98], [224, 98], [225, 98], [226, 98], [227, 98], [228, 98], [229, 98], [230, 98], [231, 98], [232, 98], [233, 98], [234, 98], [235, 98], [236, 98], [237, 98], [238, 98], [239, 98], [240, 98], [241, 98], [242, 98], [243, 98], [395, 98], [244, 98], [245, 98], [246, 98], [247, 98], [248, 98], [249, 98], [250, 98], [251, 98], [252, 98], [253, 98], [254, 98], [255, 98], [256, 98], [257, 98], [258, 98], [259, 98], [260, 98], [261, 98], [262, 98], [263, 98], [264, 98], [265, 98], [266, 98], [267, 98], [268, 98], [269, 98], [270, 98], [271, 98], [272, 98], [273, 98], [274, 98], [275, 98], [276, 98], [277, 98], [278, 98], [279, 98], [280, 98], [281, 98], [282, 98], [283, 98], [284, 98], [285, 98], [286, 98], [287, 98], [288, 98], [289, 98], [290, 98], [291, 98], [292, 98], [293, 98], [294, 98], [295, 98], [296, 98], [297, 98], [298, 98], [299, 98], [300, 98], [301, 98], [302, 98], [303, 98], [304, 98], [305, 98], [306, 98], [307, 98], [308, 98], [309, 98], [310, 98], [311, 98], [312, 98], [313, 98], [314, 98], [315, 98], [316, 98], [317, 98], [318, 98], [319, 98], [320, 98], [321, 98], [322, 98], [323, 98], [324, 98], [325, 98], [326, 98], [327, 98], [328, 98], [329, 98], [330, 98], [331, 98], [332, 98], [333, 98], [334, 98], [335, 98], [336, 98], [337, 98], [338, 98], [339, 98], [340, 98], [341, 98], [342, 98], [343, 98], [344, 98], [345, 98], [346, 98], [347, 98], [348, 98], [349, 98], [350, 98], [351, 98], [352, 98], [353, 98], [354, 98], [355, 98], [356, 98], [357, 98], [358, 98], [359, 98], [360, 98], [361, 98], [362, 98], [363, 98], [364, 98], [365, 98], [366, 98], [367, 98], [368, 98], [369, 98], [370, 98], [371, 98], [372, 98], [373, 98], [374, 98], [375, 98], [376, 98], [377, 98], [378, 98], [379, 98], [380, 98], [381, 98], [382, 98], [383, 98], [384, 98], [385, 98], [386, 98], [387, 98], [388, 98], [389, 98], [390, 98], [391, 98], [392, 98], [393, 98], [394, 98], [396, 99], [81, 12], [83, 13], [65, 14], [66, 15], [64, 16], [67, 17], [68, 18], [69, 19], [70, 20], [71, 21], [72, 22], [73, 23], [74, 24], [75, 25], [76, 26], [54, 27], [55, 28], [47, 29], [48, 100], [50, 101], [78, 32], [77, 33], [96, 34], [63, 35], [62, 36], [416, 37], [97, 102], [60, 98], [540, 73], [58, 103], [515, 41], [511, 104], [510, 105], [512, 106], [514, 107], [57, 98], [84, 98], [56, 45], [51, 46], [406, 47], [519, 47], [520, 47], [488, 48], [521, 47], [522, 47], [523, 47], [405, 47], [524, 47], [525, 47], [487, 108], [526, 109], [426, 47], [398, 47], [490, 51], [423, 52], [420, 52], [417, 53], [422, 54], [424, 55], [418, 52], [419, 52], [421, 54], [491, 56], [101, 57], [492, 58], [413, 59], [494, 60], [495, 60], [496, 60], [497, 60], [498, 60], [493, 60], [518, 110], [485, 111], [411, 63], [489, 112], [448, 113], [527, 66], [528, 67], [412, 68], [427, 66], [529, 69], [530, 50], [531, 114], [532, 115], [533, 116], [99, 73], [445, 73], [534, 117], [535, 73], [408, 73], [449, 73], [536, 118], [409, 73], [429, 76], [433, 73], [402, 77], [486, 78], [401, 79], [537, 79], [517, 80], [410, 73], [484, 66], [451, 66], [453, 81], [452, 58], [454, 81], [499, 66], [500, 66], [444, 81], [436, 66], [443, 81], [501, 81], [447, 82], [437, 81], [442, 81], [438, 81], [439, 81], [440, 81], [441, 81], [446, 82], [502, 81], [503, 81], [504, 58], [475, 81], [473, 66], [478, 81], [474, 83], [476, 81], [477, 81], [397, 81], [434, 84], [432, 81], [431, 66], [505, 85], [435, 86], [461, 66], [462, 83], [464, 81], [466, 81], [465, 81], [463, 81], [506, 81], [507, 58], [100, 87], [407, 88], [471, 81], [470, 81], [469, 81], [508, 81], [472, 81], [468, 81], [467, 66], [457, 81], [456, 83], [459, 81], [455, 66], [458, 81], [460, 81], [450, 89], [480, 81], [479, 66], [483, 81], [482, 81], [481, 81], [425, 83], [428, 90], [430, 91], [414, 92], [404, 93], [403, 94]], "semanticDiagnosticsPerFile": [509, 538, 539, 516, 53, 52, 93, 87, 91, 90, 86, 85, 94, 92, 88, 89, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 395, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 396, 81, 83, 80, 82, 400, 65, 66, 64, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 98, 54, 55, 47, 48, 50, 46, 78, 77, 96, 95, 59, 49, 63, 62, 61, 416, 415, 97, 60, 540, 79, 58, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 515, 511, 510, 512, 513, 514, 57, 84, 56, 51, 399, 406, 519, 520, 488, 521, 522, 523, 405, 524, 525, 487, 526, 426, 398, 490, 423, [420, [{"file": "./src/components/charts/barchart.vue", "start": 3667, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'interval' does not exist in type 'AxisLabelBaseOption'."}, {"file": "./src/components/charts/barchart.vue", "start": 4055, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'data' does not exist in type 'AxisBaseOptionCommon & { gridIndex?: number | undefined; gridId?: string | undefined; position?: CartesianAxisPosition | undefined; offset?: number | undefined; categorySortInfo?: OrdinalSortInfo | undefined; } & { ...; }'.", "relatedInformation": [{"file": "./node_modules/echarts/types/dist/echarts.d.ts", "start": 398286, "length": 5, "messageText": "The expected type comes from property 'yAxis' which is declared here on type 'EChartsOption'", "category": 3, "code": 6500}]}]], [417, [{"file": "./src/components/charts/basechart.vue", "start": 3422, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"image/jpeg\" | \"image/png\"' is not assignable to type '\"svg\" | \"png\" | \"jpeg\" | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"image/jpeg\"' is not assignable to type '\"svg\" | \"png\" | \"jpeg\" | undefined'.", "category": 1, "code": 2322}]}}]], 422, 424, 418, [419, [{"file": "./src/components/charts/piechart.vue", "start": 3390, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ type: \"pie\"; radius: (string | number)[]; center: [string | number, string | number]; data: { percentage: string; itemStyle: { color: string; }; name: string; value: number; color?: string | undefined; }[]; ... 6 more ...; animationDelay: (idx: number) => number; }[]' is not assignable to type 'SeriesOption$1 | SeriesOption$1[] | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ type: \"pie\"; radius: (string | number)[]; center: [string | number, string | number]; data: { percentage: string; itemStyle: { color: string; }; name: string; value: number; color?: string | undefined; }[]; ... 6 more ...; animationDelay: (idx: number) => number; }[]' is not assignable to type 'MapSeriesOption$1 | LineSeriesOption$1 | TreeSeriesOption$1 | BarSeriesOption$1 | ScatterSeriesOption$1 | ... 17 more ... | SeriesOption$1[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ type: \"pie\"; radius: (string | number)[]; center: [string | number, string | number]; data: { percentage: string; itemStyle: { color: string; }; name: string; value: number; color?: string | undefined; }[]; ... 6 more ...; animationDelay: (idx: number) => number; }' is not assignable to type 'SeriesOption$1'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'roseType' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'boolean | \"area\" | \"radius\"' is not assignable to type '\"area\" | \"radius\" | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'false' is not assignable to type '\"area\" | \"radius\" | undefined'.", "category": 1, "code": 2322}]}]}]}]}]}, "relatedInformation": [{"file": "./node_modules/echarts/types/dist/echarts.d.ts", "start": 399139, "length": 6, "messageText": "The expected type comes from property 'series' which is declared here on type 'EChartsOption'", "category": 3, "code": 6500}]}]], 421, 491, [101, [{"file": "./src/components/layout.vue", "start": 5801, "length": 6, "messageText": "The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.", "category": 1, "code": 2362}, {"file": "./src/components/layout.vue", "start": 5810, "length": 6, "messageText": "The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.", "category": 1, "code": 2363}, {"file": "./src/components/layout.vue", "start": 5948, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}]], 492, [413, [{"file": "./src/components/performanceoverview.vue", "start": 479, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(showMessage?: boolean) => Promise<void>' is not assignable to type '(evt: MouseEvent) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'showMessage' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'MouseEvent' is not assignable to type 'boolean | undefined'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/components/performanceoverview.vue", "start": 2093, "length": 5, "code": 2322, "category": 1, "messageText": "Type '(showMessage?: boolean) => Promise<void>' is not assignable to type '(evt: MouseEvent) => any'.", "relatedInformation": []}]], 494, 495, 496, 497, [498, [{"file": "./src/components/settings/uisettings.vue", "start": 10581, "length": 25, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | undefined' is not assignable to parameter of type 'string | null'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string | null'.", "category": 1, "code": 2322}]}}]], [493, [{"file": "./src/components/settingsgroup.vue", "start": 233, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'SettingsGroup'."}, {"file": "./src/components/settingsgroup.vue", "start": 289, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'SettingsGroup'."}]], 518, 485, 411, 489, [448, [{"file": "./src/store/user.ts", "start": 3014, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type '{ token: string; secret: string; }' is not assignable to parameter of type 'string'."}, {"file": "./src/store/user.ts", "start": 3412, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type '{ password: string; token?: string | undefined; }' is not assignable to parameter of type 'string'."}]], 527, 528, 412, [427, [{"file": "./src/stores/user.ts", "start": 4704, "length": 13, "messageText": "'fetchUserInfo' implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.", "category": 1, "code": 7023}]], [529, [{"file": "./src/test/chartdataadapter.test.ts", "start": 59, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}]], [530, [{"file": "./src/test/profile-navigation.test.ts", "start": 75, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/profile-navigation.test.ts", "start": 106, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}]], [531, [{"file": "./src/test/profile.test.ts", "start": 73, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/profile.test.ts", "start": 104, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/profile.test.ts", "start": 1687, "length": 12, "messageText": "Cannot find name 'useAuthStore'.", "category": 1, "code": 2304}]], [532, [{"file": "./src/test/settings.test.ts", "start": 73, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/settings.test.ts", "start": 104, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/settings.test.ts", "start": 843, "length": 12, "code": 2739, "category": 1, "messageText": "Type '{ app_name: string; app_version: string; app_description: string; timezone: string; language: string; sync_interval_minutes: number; sync_batch_size: number; auto_sync_enabled: false; access_token_expire_minutes: number; ... 35 more ...; updated_by: string; }' is missing the following properties from type 'SystemSettings': jwt_algorithm, ai_configs, ai_enabled"}]], [533, [{"file": "./src/test/setup.ts", "start": 37, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/setup.ts", "start": 69, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/setup.ts", "start": 1929, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 1974, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 2130, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 2288, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 2538, "length": 5, "messageText": "Parameter 'query' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/test/setup.ts", "start": 3200, "length": 2, "messageText": "Parameter 'cb' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/test/setup.ts", "start": 3322, "length": 2, "messageText": "Parameter 'id' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/test/setup.ts", "start": 4648, "length": 9, "messageText": "Cannot find name 'after<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 4692, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node` and then add 'node' to the types field in your tsconfig.", "category": 1, "code": 2591}, {"file": "./src/test/setup.ts", "start": 4726, "length": 6, "messageText": "Parameter 'reason' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 99, 445, 534, 535, 408, 449, 536, 409, 429, [433, [{"file": "./src/utils/chartdatatransform.ts", "start": 2334, "length": 24, "messageText": "'data' is specified more than once, so this usage will be overwritten.", "category": 1, "code": 2783, "relatedInformation": [{"file": "./src/utils/chartdatatransform.ts", "start": 2646, "length": 10, "messageText": "This spread always overwrites this property.", "category": 1, "code": 2785}]}]], 402, 486, 401, [537, [{"file": "./src/utils/permission.ts", "start": 4263, "length": 4, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'view' does not exist on type '{ view: string; create: string; edit: string; delete: string; publish: string; } | { view: string; create: string; edit: string; delete: string; export: string; } | { view: string; create: string; edit: string; delete: string; manage_materials: string; } | ... 5 more ... | { ...; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'view' does not exist on type '{ user_management: string; role_management: string; permission_management: string; system_settings: string; }'.", "category": 1, "code": 2339}]}}]], 517, 410, 484, 451, [453, [{"file": "./src/views/advertising/banners.vue", "start": 11158, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 11238, "length": 8, "messageText": "Parameter 'position' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 11344, "length": 19, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ home: string; course: string; community: string; }'."}, {"file": "./src/views/advertising/banners.vue", "start": 11402, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 11525, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; active: string; inactive: string; expired: string; }'."}, {"file": "./src/views/advertising/banners.vue", "start": 11581, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 11710, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; active: string; inactive: string; expired: string; }'."}, {"file": "./src/views/advertising/banners.vue", "start": 11753, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 11828, "length": 6, "messageText": "Parameter 'banner' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 12000, "length": 25, "code": 2322, "category": 1, "messageText": "Type 'any' is not assignable to type 'never'."}, {"file": "./src/views/advertising/banners.vue", "start": 12027, "length": 23, "code": 2322, "category": 1, "messageText": "Type 'any' is not assignable to type 'never'."}, {"file": "./src/views/advertising/banners.vue", "start": 12354, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 12651, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 13041, "length": 6, "messageText": "Parameter 'banner' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 13156, "length": 6, "messageText": "Parameter 'banner' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 13726, "length": 6, "messageText": "Parameter 'banner' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/banners.vue", "start": 4744, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/advertising/banners.vue", "start": 4926, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/advertising/banners.vue", "start": 5263, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/advertising/banners.vue", "start": 5435, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/advertising/banners.vue", "start": 7438, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], [452, [{"file": "./src/views/advertising/index.vue", "start": 3339, "length": 27, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ Picture: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Monitor: DefineComponent<...>; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ Picture: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Monitor: DefineComponent<...>; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/advertising/index.vue", "start": 4246, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/advertising/index.vue", "start": 5298, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [454, [{"file": "./src/views/advertising/popups.vue", "start": 16832, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 16912, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 17007, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ modal: string; toast: string; banner: string; }'."}, {"file": "./src/views/advertising/popups.vue", "start": 17058, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 17171, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ immediate: string; delay: string; scroll: string; exit: string; }'."}, {"file": "./src/views/advertising/popups.vue", "start": 17217, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 17338, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; active: string; paused: string; expired: string; }'."}, {"file": "./src/views/advertising/popups.vue", "start": 17394, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 17521, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; active: string; paused: string; expired: string; }'."}, {"file": "./src/views/advertising/popups.vue", "start": 17570, "length": 8, "messageText": "Parameter 'priority' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 17697, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 17771, "length": 5, "messageText": "Parameter 'popup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 17920, "length": 16, "code": 2322, "category": 1, "messageText": "Type 'any' is not assignable to type 'never'."}, {"file": "./src/views/advertising/popups.vue", "start": 17938, "length": 14, "code": 2322, "category": 1, "messageText": "Type 'any' is not assignable to type 'never'."}, {"file": "./src/views/advertising/popups.vue", "start": 18060, "length": 30, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'never'."}, {"file": "./src/views/advertising/popups.vue", "start": 18092, "length": 28, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'never'."}, {"file": "./src/views/advertising/popups.vue", "start": 18919, "length": 5, "messageText": "Parameter 'popup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 19030, "length": 5, "messageText": "Parameter 'popup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 19341, "length": 5, "messageText": "Parameter 'popup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 19685, "length": 5, "messageText": "Parameter 'popup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/advertising/popups.vue", "start": 8563, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/advertising/popups.vue", "start": 13798, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'popup_type' does not exist on type '(popup: any) => void'."}, {"file": "./src/views/advertising/popups.vue", "start": 13880, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type '(popup: any) => void'."}, {"file": "./src/views/advertising/popups.vue", "start": 14092, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type '(popup: any) => void'."}, {"file": "./src/views/advertising/popups.vue", "start": 14179, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'action_type' does not exist on type '(popup: any) => void'."}, {"file": "./src/views/advertising/popups.vue", "start": 14258, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'action_text' does not exist on type '(popup: any) => void'."}]], 499, 500, [444, [{"file": "./src/views/community/activities.vue", "start": 723, "length": 0, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(tab: string) => void' is not assignable to type '(name: TabPaneName) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'tab' and 'name' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'TabPaneName' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"file": "./src/views/community/activities.vue", "start": 3939, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], 436, [443, [{"file": "./src/views/community/experiences.vue", "start": 3013, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [501, [{"file": "./src/views/community/gamecircle.vue", "start": 35324, "length": 7, "messageText": "Module '\"@element-plus/icons-vue\"' has no exported member 'GamePad'.", "category": 1, "code": 2305}, {"file": "./src/views/community/gamecircle.vue", "start": 35478, "length": 4, "messageText": "Module '\"@element-plus/icons-vue\"' has no exported member 'Gift'.", "category": 1, "code": 2305}, {"file": "./src/views/community/gamecircle.vue", "start": 42527, "length": 19, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/gamecircle.vue", "start": 6636, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/gamecircle.vue", "start": 7802, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/gamecircle.vue", "start": 12699, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/gamecircle.vue", "start": 23776, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/gamecircle.vue", "start": 29400, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/gamecircle.vue", "start": 33130, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [447, [{"file": "./src/views/community/games.vue", "start": 12981, "length": 11, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ quiz: string; challenge: string; ranking: string; achievement: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ quiz: string; challenge: string; ranking: string; achievement: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/community/games.vue", "start": 13175, "length": 11, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ quiz: string; challenge: string; ranking: string; achievement: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ quiz: string; challenge: string; ranking: string; achievement: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/community/games.vue", "start": 15558, "length": 156, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(message: string | VNode<RendererNode, RendererElement, { [key: string]: any; }> | (() => VNode<RendererNode, RendererElement, { ...; }>) | undefined, options?: ElMessageBoxOptions | undefined, appContext?: AppContext | ... 1 more ... | undefined): Promise<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"确认删除\"' has no properties in common with type 'ElMessageBoxOptions'.", "category": 1, "code": 2559}]}, {"messageText": "Overload 2 of 2, '(message: string | VNode<RendererNode, RendererElement, { [key: string]: any; }> | (() => VNode<RendererNode, RendererElement, { ...; }>) | undefined, title: string | ... 1 more ... | undefined, options?: ElMessageBoxOptions | undefined, appContext?: AppContext | ... 1 more ... | undefined): Promise<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"danger\"' is not assignable to type 'MessageType | undefined'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/views/community/games.vue", "start": 16788, "length": 10, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'totalPlays' does not exist in type 'GameActivity | { id: string; name: string; description: string; type: \"challenge\" | \"achievement\" | \"quiz\" | \"ranking\"; rules: string; rewards: string[]; participantCount: number; ... 4 more ...; updatedAt: string; }'."}, {"file": "./src/views/community/games.vue", "start": 7975, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalPlays' does not exist on type '{ id: string; name: string; description: string; type: \"challenge\" | \"achievement\" | \"quiz\" | \"ranking\"; rules: string; rewards: string[]; participantCount: number; isActive: boolean; startDate: string; endDate?: string | undefined; createdAt: string; updatedAt: string; }'."}, {"file": "./src/views/community/games.vue", "start": 9120, "length": 17, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; description: string; type: \"challenge\" | \"achievement\" | \"quiz\" | \"ranking\"; rules: string; rewards: string[]; participantCount: number; isActive: boolean; startDate: string; endDate?: string | undefined; createdAt: string; updatedAt: string; } | null' is not assignable to parameter of type 'GameActivity'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'GameActivity'.", "category": 1, "code": 2322}]}}]], 437, [442, [{"file": "./src/views/community/mentorship.vue", "start": 49167, "length": 26, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/mentorship.vue", "start": 49269, "length": 19, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/mentorship.vue", "start": 7573, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/mentorship.vue", "start": 18889, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/mentorship.vue", "start": 26997, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/mentorship.vue", "start": 28715, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/mentorship.vue", "start": 34139, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/mentorship.vue", "start": 36791, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/mentorship.vue", "start": 37899, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [438, [{"file": "./src/views/community/resources.vue", "start": 16056, "length": 23, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'any[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'any' is not assignable to type 'never'.", "category": 1, "code": 2322}]}}, {"file": "./src/views/community/resources.vue", "start": 4405, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/resources.vue", "start": 10061, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/resources.vue", "start": 11530, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [439, [{"file": "./src/views/community/studyrooms.vue", "start": 28213, "length": 19, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/studyrooms.vue", "start": 3563, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/studyrooms.vue", "start": 4877, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/studyrooms.vue", "start": 9929, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/studyrooms.vue", "start": 10851, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [440, [{"file": "./src/views/community/topics.vue", "start": 31823, "length": 20, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/topics.vue", "start": 31921, "length": 26, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/topics.vue", "start": 4540, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/topics.vue", "start": 15059, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [441, [{"file": "./src/views/community/tribes.vue", "start": 41668, "length": 26, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/tribes.vue", "start": 41774, "length": 24, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/tribes.vue", "start": 5337, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/tribes.vue", "start": 6346, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/tribes.vue", "start": 13631, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/tribes.vue", "start": 22786, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/tribes.vue", "start": 23748, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [446, [{"file": "./src/views/community/wishes.vue", "start": 14588, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'timeAgo' does not exist in type '{ id: string; wishId: string; userId: string; userName: string; userAvatar: string; content: string; likeCount: number; isLiked: boolean; createdAt: string; }'."}, {"file": "./src/views/community/wishes.vue", "start": 15605, "length": 142, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(message: string | VNode<RendererNode, RendererElement, { [key: string]: any; }> | (() => VNode<RendererNode, RendererElement, { ...; }>) | undefined, options?: ElMessageBoxOptions | undefined, appContext?: AppContext | ... 1 more ... | undefined): Promise<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"确认删除\"' has no properties in common with type 'ElMessageBoxOptions'.", "category": 1, "code": 2559}]}, {"messageText": "Overload 2 of 2, '(message: string | VNode<RendererNode, RendererElement, { [key: string]: any; }> | (() => VNode<RendererNode, RendererElement, { ...; }>) | undefined, title: string | ... 1 more ... | undefined, options?: ElMessageBoxOptions | undefined, appContext?: AppContext | ... 1 more ... | undefined): Promise<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"danger\"' is not assignable to type 'MessageType | undefined'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/views/community/wishes.vue", "start": 16256, "length": 142, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(message: string | VNode<RendererNode, RendererElement, { [key: string]: any; }> | (() => VNode<RendererNode, RendererElement, { ...; }>) | undefined, options?: ElMessageBoxOptions | undefined, appContext?: AppContext | ... 1 more ... | undefined): Promise<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"确认删除\"' has no properties in common with type 'ElMessageBoxOptions'.", "category": 1, "code": 2559}]}, {"messageText": "Overload 2 of 2, '(message: string | VNode<RendererNode, RendererElement, { [key: string]: any; }> | (() => VNode<RendererNode, RendererElement, { ...; }>) | undefined, title: string | ... 1 more ... | undefined, options?: ElMessageBoxOptions | undefined, appContext?: AppContext | ... 1 more ... | undefined): Promise<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"danger\"' is not assignable to type 'MessageType | undefined'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/views/community/wishes.vue", "start": 17136, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'timeAgo' does not exist in type 'Wish | { id: string; userId: string; userName: string; userAvatar: string; content: string; tags: string[]; status: WishStatus; isAchieved: boolean; supportCount: number; commentCount: number; ... 4 more ...; achievedAt?: string | undefined; }'."}, {"file": "./src/views/community/wishes.vue", "start": 5463, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/wishes.vue", "start": 8797, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/wishes.vue", "start": 10124, "length": 17, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; userId: string; userName: string; userAvatar: string; content: string; tags: string[]; status: WishStatus; isAchieved: boolean; supportCount: number; commentCount: number; ... 4 more ...; achievedAt?: string | undefined; } | null' is not assignable to parameter of type 'Wish'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'Wish'.", "category": 1, "code": 2322}]}}, {"file": "./src/views/community/wishes.vue", "start": 10286, "length": 17, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; userId: string; userName: string; userAvatar: string; content: string; tags: string[]; status: WishStatus; isAchieved: boolean; supportCount: number; commentCount: number; ... 4 more ...; achievedAt?: string | undefined; } | null' is not assignable to parameter of type 'Wish'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'Wish'.", "category": 1, "code": 2322}]}}]], [502, [{"file": "./src/views/community/wishwall.vue", "start": 32857, "length": 5, "messageText": "Module '\"@element-plus/icons-vue\"' has no exported member 'Heart'.", "category": 1, "code": 2305}, {"file": "./src/views/community/wishwall.vue", "start": 33035, "length": 4, "messageText": "Module '\"@element-plus/icons-vue\"' has no exported member 'Gift'.", "category": 1, "code": 2305}, {"file": "./src/views/community/wishwall.vue", "start": 39840, "length": 20, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/wishwall.vue", "start": 41985, "length": 22, "code": 2322, "category": 1, "messageText": "Type 'any[]' is not assignable to type 'never[]'."}, {"file": "./src/views/community/wishwall.vue", "start": 6641, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/wishwall.vue", "start": 6816, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/wishwall.vue", "start": 17324, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/wishwall.vue", "start": 19027, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/wishwall.vue", "start": 25386, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/community/wishwall.vue", "start": 30044, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [503, [{"file": "./src/views/course-management/courses.vue", "start": 22464, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 22544, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 22703, "length": 20, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ programming: string; design: string; business: string; language: string; skills: string; }'."}, {"file": "./src/views/course-management/courses.vue", "start": 22767, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 22932, "length": 15, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ programming: string; design: string; business: string; language: string; skills: string; }'."}, {"file": "./src/views/course-management/courses.vue", "start": 22984, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 23113, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ beginner: string; elementary: string; intermediate: string; advanced: string; }'."}, {"file": "./src/views/course-management/courses.vue", "start": 23162, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 23268, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/course-management/courses.vue", "start": 23324, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 23437, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/course-management/courses.vue", "start": 23484, "length": 7, "messageText": "Parameter 'minutes' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 23644, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 23723, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 23820, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 23958, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 24071, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 24845, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 25011, "length": 12, "messageText": "Parameter 'chapterIndex' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 25133, "length": 12, "messageText": "Parameter 'chapterIndex' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 25147, "length": 11, "messageText": "Parameter 'lessonIndex' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 25381, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 25678, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 26609, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 26724, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/course-management/courses.vue", "start": 10630, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/course-management/courses.vue", "start": 13458, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/course-management/courses.vue", "start": 17341, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'cover_image' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 17399, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 17528, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 17648, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'category' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 17709, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'category' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 17827, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'difficulty_level' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 17899, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'difficulty_level' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18016, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'price' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18055, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'price' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18268, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18505, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'instructor' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18569, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'instructor' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18722, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'instructor' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18814, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'instructor' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 18901, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'instructor_intro' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 19043, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'chapters' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 19069, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'chapters' does not exist on type 'never'."}, {"file": "./src/views/course-management/courses.vue", "start": 19209, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'chapters' does not exist on type 'never'."}]], [504, [{"file": "./src/views/course-management/index.vue", "start": 17077, "length": 13, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ junior: string; intermediate: string; senior: string; expert: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ junior: string; intermediate: string; senior: string; expert: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/course-management/index.vue", "start": 2473, "length": 27, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ Reading: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; User: DefineComponent<...>; UserFilled: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ Reading: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; User: DefineComponent<...>; UserFilled: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/course-management/index.vue", "start": 11872, "length": 27, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ Reading: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; User: DefineComponent<...>; UserFilled: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ Reading: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; User: DefineComponent<...>; UserFilled: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7054}]}}]], [475, [{"file": "./src/views/courses/content.vue", "start": 12564, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 12644, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 12791, "length": 20, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ frontend: string; backend: string; mobile: string; data: string; ai: string; }'."}, {"file": "./src/views/courses/content.vue", "start": 12855, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13008, "length": 15, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ frontend: string; backend: string; mobile: string; data: string; ai: string; }'."}, {"file": "./src/views/courses/content.vue", "start": 13055, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13162, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ beginner: string; intermediate: string; advanced: string; }'."}, {"file": "./src/views/courses/content.vue", "start": 13211, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13317, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/courses/content.vue", "start": 13373, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13486, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/courses/content.vue", "start": 13529, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13608, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13703, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13842, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 13954, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 14071, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 14186, "length": 6, "messageText": "Parameter 'course' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 14770, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 15067, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/content.vue", "start": 7362, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/content.vue", "start": 10012, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], 473, [478, [{"file": "./src/views/courses/evaluations.vue", "start": 14756, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 14834, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 14942, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ approved: string; pending: string; rejected: string; }'."}, {"file": "./src/views/courses/evaluations.vue", "start": 14998, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 15114, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ approved: string; pending: string; rejected: string; }'."}, {"file": "./src/views/courses/evaluations.vue", "start": 15157, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 15232, "length": 6, "messageText": "Parameter 'review' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 15341, "length": 6, "messageText": "Parameter 'review' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 15473, "length": 6, "messageText": "Parameter 'review' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 15598, "length": 6, "messageText": "Parameter 'review' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 15705, "length": 6, "messageText": "Parameter 'review' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/evaluations.vue", "start": 6727, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/evaluations.vue", "start": 6909, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/evaluations.vue", "start": 7190, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/evaluations.vue", "start": 7454, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/evaluations.vue", "start": 7629, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/evaluations.vue", "start": 10611, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'user_avatar' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 10645, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'user_avatar' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 10679, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'user_name' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 10770, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'user_name' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 10922, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'user_name' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11000, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'user_id' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11230, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'course_title' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11323, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'instructor' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11520, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'rating' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11602, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'rating' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11693, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11766, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11789, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 11867, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 12179, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'created_at' does not exist on type 'never'."}, {"file": "./src/views/courses/evaluations.vue", "start": 12712, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], [474, [{"file": "./src/views/courses/index.vue", "start": 11143, "length": 25, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ date: string; value: number; }[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ date: string; value: number; }' is not assignable to type 'never'.", "category": 1, "code": 2322}]}}]], [476, [{"file": "./src/views/courses/instructors.vue", "start": 12320, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 12406, "length": 10, "messageText": "Parameter 'department' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 12532, "length": 23, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ cs: string; math: string; english: string; physics: string; }'."}, {"file": "./src/views/courses/instructors.vue", "start": 12596, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 12700, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; inactive: string; pending: string; }'."}, {"file": "./src/views/courses/instructors.vue", "start": 12756, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 12870, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; inactive: string; pending: string; }'."}, {"file": "./src/views/courses/instructors.vue", "start": 12913, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 12996, "length": 10, "messageText": "Parameter 'instructor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 13107, "length": 10, "messageText": "Parameter 'instructor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 13263, "length": 10, "messageText": "Parameter 'instructor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 13385, "length": 10, "messageText": "Parameter 'instructor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 13514, "length": 10, "messageText": "Parameter 'instructor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 13645, "length": 10, "messageText": "Parameter 'instructor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 14228, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 14526, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/instructors.vue", "start": 7217, "length": 6, "code": 2322, "category": 1, "messageText": "Type '{ name: { required: boolean; message: string; trigger: string; }[]; department: { required: boolean; message: string; trigger: string; }[]; email: ({ required: boolean; message: string; trigger: string; type?: undefined; } | { ...; })[]; }' is not assignable to type 'Partial<Record<string, Arrayable<FormItemRule>>>'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 144103, "length": 5, "messageText": "The expected type comes from property 'rules' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly inline: boolean; readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>; ... 9 more ...; readonly scrollToError: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/instructors.vue", "start": 9338, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], [477, [{"file": "./src/views/courses/progress.vue", "start": 12533, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 12710, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 12875, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13047, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13171, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; completed: string; paused: string; dropped: string; }'."}, {"file": "./src/views/courses/progress.vue", "start": 13227, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13365, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; completed: string; paused: string; dropped: string; }'."}, {"file": "./src/views/courses/progress.vue", "start": 13412, "length": 7, "messageText": "Parameter 'seconds' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13578, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13654, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13766, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13878, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 13987, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/courses/progress.vue", "start": 7589, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/progress.vue", "start": 7756, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/progress.vue", "start": 7933, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/courses/progress.vue", "start": 8108, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [397, [{"file": "./src/views/dashboard.vue", "start": 5194, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/dashboard.vue", "start": 7778, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], 434, [432, [{"file": "./src/views/data-management/databackup.vue", "start": 8412, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 8536, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ full: string; database: string; files: string; incremental: string; }'."}, {"file": "./src/views/data-management/databackup.vue", "start": 8583, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 8717, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ full: string; database: string; files: string; incremental: string; }'."}, {"file": "./src/views/data-management/databackup.vue", "start": 8761, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 8889, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ completed: string; running: string; failed: string; cancelled: string; }'."}, {"file": "./src/views/data-management/databackup.vue", "start": 8945, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 9086, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ completed: string; running: string; failed: string; cancelled: string; }'."}, {"file": "./src/views/data-management/databackup.vue", "start": 9129, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 9983, "length": 6, "messageText": "Parameter 'backup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 10085, "length": 6, "messageText": "Parameter 'backup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 10196, "length": 6, "messageText": "Parameter 'backup' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/data-management/databackup.vue", "start": 10307, "length": 6, "messageText": "Parameter 'backup' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 431, [505, [{"file": "./src/views/data-management/index.vue", "start": 12410, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}, {"file": "./src/views/data-management/index.vue", "start": 6227, "length": 5, "code": 2322, "category": 1, "messageText": "Type '{ labels: string[]; datasets: { label: string; data: number[]; borderColor: string; backgroundColor: string; tension: number; }[]; }' is not assignable to type 'SeriesData[]'.", "relatedInformation": [{"file": "./src/components/charts/linechart.vue", "start": 675, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type 'Partial<{ error: string; loading: boolean; area: boolean; width: string; height: string; smooth: boolean; theme: string; showLegend: boolean; showGrid: boolean; showTooltip: boolean; showDataZoom: boolean; colors: string[]; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/data-management/index.vue", "start": 6669, "length": 5, "code": 2322, "category": 1, "messageText": "Type '{ labels: string[]; datasets: { data: number[]; backgroundColor: string[]; }[]; }' is not assignable to type 'DataItem[]'.", "relatedInformation": [{"file": "./src/components/charts/piechart.vue", "start": 544, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type 'Partial<{ error: string; loading: boolean; center: [string | number, string | number]; width: string; height: string; theme: string; showLegend: boolean; colors: string[]; showLabel: boolean; showLabelLine: boolean; innerRadius: string | number; outerRadius: string | number; roseType: boolean | ... 1 more ... | \"rad...'", "category": 3, "code": 6500}]}, {"file": "./src/views/data-management/index.vue", "start": 7187, "length": 5, "code": 2322, "category": 1, "messageText": "Type '{ labels: string[]; datasets: { label: string; data: number[]; backgroundColor: string; borderColor: string; borderWidth: number; }[]; }' is not assignable to type 'SeriesData[]'.", "relatedInformation": [{"file": "./src/components/charts/barchart.vue", "start": 656, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type 'Partial<{ error: string; loading: boolean; horizontal: boolean; width: string; height: string; theme: string; showLegend: boolean; showGrid: boolean; showTooltip: boolean; showDataZoom: boolean; colors: string[]; stack: boolean; barWidth: string | number; barMaxWidth: string | number; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [435, [{"file": "./src/views/datasync.vue", "start": 7708, "length": 9, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'null'.", "relatedInformation": [{"file": "./src/views/datasync.vue", "start": 6698, "length": 15, "messageText": "The expected type comes from property 'last_sync' which is declared here on type '{ last_sync: null; is_syncing: boolean; android_db_connected: boolean; android_db_path: string; android_db_size: string; total_records: number; sync_config: { auto_sync_enabled: boolean; sync_interval_minutes: number; batch_size: number; }; } | { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/views/datasync.vue", "start": 8096, "length": 86, "code": 2322, "category": 1, "messageText": "Type '{ name: string; records: number; lastSync: string; status: string; }' is not assignable to type 'never'."}, {"file": "./src/views/datasync.vue", "start": 8190, "length": 87, "code": 2322, "category": 1, "messageText": "Type '{ name: string; records: number; lastSync: string; status: string; }' is not assignable to type 'never'."}, {"file": "./src/views/datasync.vue", "start": 8285, "length": 91, "code": 2322, "category": 1, "messageText": "Type '{ name: string; records: number; lastSync: string; status: string; }' is not assignable to type 'never'."}, {"file": "./src/views/datasync.vue", "start": 8384, "length": 87, "code": 2322, "category": 1, "messageText": "Type '{ name: string; records: number; lastSync: string; status: string; }' is not assignable to type 'never'."}, {"file": "./src/views/datasync.vue", "start": 1624, "length": 20, "code": 2345, "category": 1, "messageText": "Argument of type 'null' is not assignable to parameter of type 'string'."}, {"file": "./src/views/datasync.vue", "start": 1865, "length": 7, "code": 2322, "category": 1, "messageText": "Type '\"success\" | \"active\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"success\" | \"warning\" | \"exception\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 229149, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'Partial<{ readonly width: number; readonly color: EpPropMergeType<(new (...args: any[]) => string | ProgressFn | ProgressColor[]) | (() => string | ProgressFn | ProgressColor[]) | ((new (...args: any[]) => string | ... 1 more ... | ProgressColor[]) | (() => string | ... 1 more ... | ProgressColor[]))[], unknown, unk...'", "category": 3, "code": 6500}]}]], 461, [462, [{"file": "./src/views/english-practice/index.vue", "start": 5685, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [464, [{"file": "./src/views/english-practice/listening.vue", "start": 21810, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 21892, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 22064, "length": 20, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ daily: string; news: string; academic: string; business: string; travel: string; culture: string; }'."}, {"file": "./src/views/english-practice/listening.vue", "start": 22128, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 22285, "length": 15, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ daily: string; news: string; academic: string; business: string; travel: string; }'."}, {"file": "./src/views/english-practice/listening.vue", "start": 22337, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 22444, "length": 12, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ 1: string; 2: string; 3: string; 4: string; 5: string; }'."}, {"file": "./src/views/english-practice/listening.vue", "start": 22503, "length": 4, "messageText": "Parameter 'rate' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 22594, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ slow: string; normal: string; fast: string; }'."}, {"file": "./src/views/english-practice/listening.vue", "start": 22640, "length": 6, "messageText": "Parameter 'accent' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 22767, "length": 15, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ american: string; british: string; australian: string; canadian: string; }'."}, {"file": "./src/views/english-practice/listening.vue", "start": 22819, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 22925, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/english-practice/listening.vue", "start": 22981, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 23094, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/english-practice/listening.vue", "start": 23141, "length": 7, "messageText": "Parameter 'seconds' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 23331, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 23409, "length": 8, "messageText": "Parameter 'material' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 23698, "length": 8, "messageText": "Parameter 'material' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 23803, "length": 8, "messageText": "Parameter 'material' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 24357, "length": 270, "code": 2345, "category": 1, "messageText": "Argument of type '{ type: string; content: string; options: { text: string; is_correct: boolean; }[]; correct_answer: string; explanation: string; }' is not assignable to parameter of type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 24656, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 24740, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 25043, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 26197, "length": 8, "messageText": "Parameter 'material' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 26320, "length": 8, "messageText": "Parameter 'material' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/listening.vue", "start": 9830, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/listening.vue", "start": 13079, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/listening.vue", "start": 13830, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/listening.vue", "start": 14100, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/listening.vue", "start": 14860, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/listening.vue", "start": 15127, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'type' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 15577, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 15745, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'type' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 15861, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'options' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 16294, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'type' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 16348, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'correct_answer' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 16509, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'explanation' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 16538, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/listening.vue", "start": 17271, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 17417, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'audio_url' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 17770, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'category' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 17961, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'difficulty_level' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 18157, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'duration' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 18348, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'speech_rate' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 18538, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'accent' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 18710, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'question_count' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 18916, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 19019, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'transcript' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 19116, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'transcript' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 19218, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'key_vocabulary' does not exist on type 'never'."}, {"file": "./src/views/english-practice/listening.vue", "start": 19349, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'key_vocabulary' does not exist on type 'never'."}]], [466, [{"file": "./src/views/english-practice/progress.vue", "start": 14609, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 14785, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 14947, "length": 8, "messageText": "Parameter 'progress' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 15119, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 15245, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; paused: string; completed: string; inactive: string; }'."}, {"file": "./src/views/english-practice/progress.vue", "start": 15301, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 15438, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; paused: string; completed: string; inactive: string; }'."}, {"file": "./src/views/english-practice/progress.vue", "start": 15488, "length": 10, "messageText": "Parameter 'difficulty' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 15606, "length": 24, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ beginner: string; intermediate: string; advanced: string; }'."}, {"file": "./src/views/english-practice/progress.vue", "start": 15672, "length": 7, "messageText": "Parameter 'seconds' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 15838, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 15914, "length": 7, "messageText": "Parameter 'student' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 16023, "length": 7, "messageText": "Parameter 'student' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 16132, "length": 7, "messageText": "Parameter 'student' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 16242, "length": 7, "messageText": "Parameter 'student' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 16750, "length": 4, "messageText": "Parameter 'path' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 16853, "length": 4, "messageText": "Parameter 'path' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/progress.vue", "start": 16956, "length": 4, "messageText": "Parameter 'path' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [465, [{"file": "./src/views/english-practice/speaking.vue", "start": 12955, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 13030, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 13137, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ beginner: string; intermediate: string; advanced: string; }'."}, {"file": "./src/views/english-practice/speaking.vue", "start": 13188, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 13308, "length": 12, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ beginner: string; intermediate: string; advanced: string; }'."}, {"file": "./src/views/english-practice/speaking.vue", "start": 13351, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 13487, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ conversation: string; speech: string; pronunciation: string; scenario: string; }'."}, {"file": "./src/views/english-practice/speaking.vue", "start": 13534, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 13679, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ conversation: string; speech: string; pronunciation: string; scenario: string; }'."}, {"file": "./src/views/english-practice/speaking.vue", "start": 13723, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 13829, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/english-practice/speaking.vue", "start": 13885, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 13998, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/english-practice/speaking.vue", "start": 14041, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 14124, "length": 7, "messageText": "Parameter 'seconds' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 14313, "length": 8, "messageText": "Parameter 'audioUrl' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 14412, "length": 7, "messageText": "Parameter 'content' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 14511, "length": 7, "messageText": "Parameter 'content' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 14655, "length": 7, "messageText": "Parameter 'content' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 14759, "length": 7, "messageText": "Parameter 'content' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 15099, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 15402, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 16318, "length": 7, "messageText": "Parameter 'content' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 16437, "length": 7, "messageText": "Parameter 'content' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/speaking.vue", "start": 8841, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/speaking.vue", "start": 9920, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/speaking.vue", "start": 10537, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [463, [{"file": "./src/views/english-practice/vocabulary.vue", "start": 20741, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 20819, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21041, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ noun: string; verb: string; adjective: string; adverb: string; preposition: string; conjunction: string; pronoun: string; numeral: string; interjection: string; }'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21088, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21244, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ noun: string; verb: string; adjective: string; adverb: string; preposition: string; }'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21292, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21399, "length": 12, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ 1: string; 2: string; 3: string; 4: string; 5: string; }'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21454, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21560, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21616, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21729, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21772, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 21858, "length": 4, "messageText": "Parameter 'word' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 22145, "length": 4, "messageText": "Parameter 'word' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 22234, "length": 4, "messageText": "Parameter 'word' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 22815, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 23003, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 23306, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 24243, "length": 4, "messageText": "Parameter 'word' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 24350, "length": 4, "messageText": "Parameter 'word' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 25008, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 25484, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 7303, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 7467, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 7647, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 7988, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 10625, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 13106, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 14429, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 14798, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 15465, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'word' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 15605, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'pronunciation_url' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 15511, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 15911, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'pronunciation' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 16079, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'translation' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 16259, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'word_type' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 16454, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'difficulty_level' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 16522, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'difficulty_level' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 16658, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'example_sentences' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 16691, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'example_sentences' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 16836, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'example_sentences' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 17138, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 17158, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 17276, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/english-practice/vocabulary.vue", "start": 18196, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [506, [{"file": "./src/views/learning-analytics/behavior.vue", "start": 20290, "length": 11, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ start: string; browse: string; learn: string; practice: string; submit: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ start: string; browse: string; learn: string; practice: string; submit: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/learning-analytics/behavior.vue", "start": 20484, "length": 11, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ click: string; scroll: string; search: string; share: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ click: string; scroll: string; search: string; share: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/learning-analytics/behavior.vue", "start": 20674, "length": 11, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ diligent: string; casual: string; goal: string; risk: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ diligent: string; casual: string; goal: string; risk: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/learning-analytics/behavior.vue", "start": 20851, "length": 13, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ low: string; medium: string; high: string; critical: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ low: string; medium: string; high: string; critical: string; }'.", "category": 1, "code": 7054}]}}]], [507, [{"file": "./src/views/learning-analytics/index.vue", "start": 15508, "length": 13, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ low: string; medium: string; high: string; critical: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ low: string; medium: string; high: string; critical: string; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/learning-analytics/index.vue", "start": 2739, "length": 27, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ User: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Document: DefineComponent<...>; Reading: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ User: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Document: DefineComponent<...>; Reading: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/learning-analytics/index.vue", "start": 8002, "length": 29, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ User: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Document: DefineComponent<...>; Reading: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ User: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Document: DefineComponent<...>; Reading: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7054}]}}, {"file": "./src/views/learning-analytics/index.vue", "start": 10664, "length": 27, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ User: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Document: DefineComponent<...>; Reading: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ User: DefineComponent<{}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, VNodeProps & AllowedComponentProps & ComponentCustomProps, ... 9 more ..., any>; Document: DefineComponent<...>; Reading: DefineComponent<...>; TrendCharts: DefineComponent<...>; DataLine: DefineComponent<...>; }'.", "category": 1, "code": 7054}]}}]], [100, [{"file": "./src/views/login.vue", "start": 3727, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}]], [407, [{"file": "./src/views/profile.vue", "start": 55925, "length": 18, "messageText": "Expected 1 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/api/profile.ts", "start": 2446, "length": 16, "messageText": "An argument for 'password' was not provided.", "category": 3, "code": 6210}]}]], [471, [{"file": "./src/views/psychology/appointments.vue", "start": 19458, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 19539, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 19665, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ individual: string; group: string; crisis: string; academic: string; }'."}, {"file": "./src/views/psychology/appointments.vue", "start": 19712, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 19849, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ individual: string; group: string; crisis: string; academic: string; }'."}, {"file": "./src/views/psychology/appointments.vue", "start": 19893, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20025, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ pending: string; confirmed: string; completed: string; cancelled: string; }'."}, {"file": "./src/views/psychology/appointments.vue", "start": 20081, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20225, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ pending: string; confirmed: string; completed: string; cancelled: string; }'."}, {"file": "./src/views/psychology/appointments.vue", "start": 20272, "length": 8, "messageText": "Parameter 'datetime' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20355, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20439, "length": 11, "messageText": "Parameter 'appointment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20554, "length": 11, "messageText": "Parameter 'appointment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20728, "length": 11, "messageText": "Parameter 'appointment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20879, "length": 11, "messageText": "Parameter 'appointment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 20996, "length": 11, "messageText": "Parameter 'appointment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 21116, "length": 11, "messageText": "Parameter 'appointment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/appointments.vue", "start": 14913, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], [470, [{"file": "./src/views/psychology/articles.vue", "start": 19480, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 19561, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 19747, "length": 20, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ psychology: string; emotion: string; stress: string; relationship: string; learning: string; workplace: string; }'."}, {"file": "./src/views/psychology/articles.vue", "start": 19811, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 19980, "length": 15, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ psychology: string; emotion: string; stress: string; relationship: string; learning: string; }'."}, {"file": "./src/views/psychology/articles.vue", "start": 20028, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 20134, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/psychology/articles.vue", "start": 20190, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 20303, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/psychology/articles.vue", "start": 20349, "length": 5, "messageText": "Parameter 'score' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 20504, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 20584, "length": 7, "messageText": "Parameter 'article' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 20685, "length": 7, "messageText": "Parameter 'article' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 20827, "length": 7, "messageText": "Parameter 'article' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 21310, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 21607, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 22546, "length": 7, "messageText": "Parameter 'article' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 22665, "length": 7, "messageText": "Parameter 'article' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/articles.vue", "start": 6230, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 6397, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 6564, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 6756, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 7100, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 9588, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 11822, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 13414, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 13822, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/articles.vue", "start": 14528, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'cover_image' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 14588, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 14720, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 14842, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'category' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 14904, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'category' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15006, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'reading_time' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15096, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'word_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15239, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'view_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15319, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'like_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15398, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'favorite_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15482, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'comment_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15686, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'summary' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15912, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'author' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 15973, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'author' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 16115, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'author' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 16200, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'author' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 16278, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'author_bio' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 16480, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 16577, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 16600, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}, {"file": "./src/views/psychology/articles.vue", "start": 16723, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'never'."}]], [469, [{"file": "./src/views/psychology/assessments.vue", "start": 25519, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 25599, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 25796, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ depression: string; anxiety: string; stress: string; personality: string; comprehensive: string; sleep: string; social: string; }'."}, {"file": "./src/views/psychology/assessments.vue", "start": 25843, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 26012, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ depression: string; anxiety: string; stress: string; personality: string; comprehensive: string; }'."}, {"file": "./src/views/psychology/assessments.vue", "start": 26056, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 26162, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/psychology/assessments.vue", "start": 26218, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 26331, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/psychology/assessments.vue", "start": 26380, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 26514, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ none: string; low: string; medium: string; high: string; critical: string; }'."}, {"file": "./src/views/psychology/assessments.vue", "start": 26569, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 26719, "length": 12, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ none: string; low: string; medium: string; high: string; critical: string; }'."}, {"file": "./src/views/psychology/assessments.vue", "start": 26761, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 26844, "length": 10, "messageText": "Parameter 'assessment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 26957, "length": 10, "messageText": "Parameter 'assessment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 27123, "length": 10, "messageText": "Parameter 'assessment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 28117, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 28500, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 28958, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 29819, "length": 10, "messageText": "Parameter 'assessment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 29950, "length": 10, "messageText": "Parameter 'assessment' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/assessments.vue", "start": 6166, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 6336, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 6506, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 6702, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 7049, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 9576, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 12334, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 13704, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 15304, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 15492, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 16266, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/assessments.vue", "start": 18362, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 18434, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'assessment_type' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 18498, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'assessment_type' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 18771, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'question_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 18954, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'estimated_time' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 19138, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'reliability' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 19317, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'validity' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 19520, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 19625, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'dimensions' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 19657, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'dimensions' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 19807, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'dimensions' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 20207, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'score_levels' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 20241, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'score_levels' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 20385, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'score_levels' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 21325, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 21373, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 21469, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'question_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 21536, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'estimated_time' does not exist on type 'never'."}, {"file": "./src/views/psychology/assessments.vue", "start": 21705, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'questions' does not exist on type 'never'."}]], [508, [{"file": "./src/views/psychology/counselors.vue", "start": 24383, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 24463, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 24584, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ junior: string; intermediate: string; senior: string; expert: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 24635, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 24774, "length": 12, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ junior: string; intermediate: string; senior: string; expert: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 24827, "length": 4, "messageText": "Parameter 'spec' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 25005, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ adolescent: string; relationship: string; workplace: string; academic: string; family: string; anxiety: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 25051, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 25156, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; suspended: string; inactive: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 25212, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 25326, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ active: string; suspended: string; inactive: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 25379, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 25484, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ valid: string; expired: string; suspended: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 25534, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 25613, "length": 4, "messageText": "Parameter 'date' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 25695, "length": 9, "messageText": "Parameter 'counselor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 25804, "length": 9, "messageText": "Parameter 'counselor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 26041, "length": 9, "messageText": "Parameter 'counselor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 26167, "length": 9, "messageText": "Parameter 'counselor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 26835, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 27133, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 27479, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'work_days' does not exist on type '{ name: string; title: string; level: string; experience: number; specializations: never[]; phone: string; email: string; license_number: string; license_authority: string; license_expiry: string; ... 9 more ...; expertise: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 27652, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'work_hours' does not exist on type '{ name: string; title: string; level: string; experience: number; specializations: never[]; phone: string; email: string; license_number: string; license_authority: string; license_expiry: string; ... 9 more ...; expertise: string; }'."}, {"file": "./src/views/psychology/counselors.vue", "start": 28058, "length": 9, "messageText": "Parameter 'counselor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 28185, "length": 9, "messageText": "Parameter 'counselor' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/counselors.vue", "start": 10623, "length": 6, "code": 2322, "category": 1, "messageText": "Type '{ name: { required: boolean; message: string; trigger: string; }[]; title: { required: boolean; message: string; trigger: string; }[]; level: { required: boolean; message: string; trigger: string; }[]; specializations: { ...; }[]; phone: { ...; }[]; email: ({ ...; } | { ...; })[]; }' is not assignable to type 'Partial<Record<string, Arrayable<FormItemRule>>>'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 144103, "length": 5, "messageText": "The expected type comes from property 'rules' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly inline: boolean; readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>; ... 9 more ...; readonly scrollToError: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/counselors.vue", "start": 13589, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/counselors.vue", "start": 13780, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/counselors.vue", "start": 16676, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/counselors.vue", "start": 16871, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/counselors.vue", "start": 17064, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/counselors.vue", "start": 17710, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'avatar' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 17766, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 17845, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'online_status' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 17994, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 18074, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 18198, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'level' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 18256, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'level' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 18355, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'experience' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 18440, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'rating' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 18472, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'review_count' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 18733, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'specializations' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 19084, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'bio' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 19243, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'philosophy' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 19565, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'consultation_fee' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 19759, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'work_days' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 19792, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'work_hours' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 19977, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'service_methods' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 20178, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'satisfaction_rate' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 20599, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'license_number' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 20798, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'license_authority' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 21011, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'license_expiry' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 21216, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'license_status' does not exist on type 'never'."}, {"file": "./src/views/psychology/counselors.vue", "start": 21292, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'license_status' does not exist on type 'never'."}]], [472, [{"file": "./src/views/psychology/crisis.vue", "start": 20858, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 20933, "length": 4, "messageText": "Parameter 'risk' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 21023, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ high: string; medium: string; low: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 21070, "length": 4, "messageText": "Parameter 'risk' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 21174, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ high: string; medium: string; low: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 21216, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 21367, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ suicide: string; depression: string; anxiety: string; behavior: string; academic: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 21414, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 21576, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ suicide: string; depression: string; anxiety: string; behavior: string; academic: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 21620, "length": 6, "messageText": "Parameter 'source' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 21762, "length": 15, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ system: string; teacher: string; peer: string; parent: string; self: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 21814, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 21943, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ pending: string; processing: string; resolved: string; closed: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 21999, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 22138, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ pending: string; processing: string; resolved: string; closed: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 22194, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 22348, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ counseling: string; crisis: string; medication: string; family: string; environment: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 22403, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 22568, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ counseling: string; crisis: string; medication: string; family: string; environment: string; }'."}, {"file": "./src/views/psychology/crisis.vue", "start": 22609, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 22695, "length": 5, "messageText": "Parameter 'alert' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 22786, "length": 5, "messageText": "Parameter 'alert' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 22917, "length": 5, "messageText": "Parameter 'alert' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 23014, "length": 5, "messageText": "Parameter 'alert' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 23113, "length": 5, "messageText": "Parameter 'alert' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 24055, "length": 12, "messageText": "Parameter 'intervention' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 24176, "length": 12, "messageText": "Parameter 'intervention' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 24303, "length": 12, "messageText": "Parameter 'intervention' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/psychology/crisis.vue", "start": 15642, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/psychology/crisis.vue", "start": 17503, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], 468, 467, [457, [{"file": "./src/views/question-bank/categories.vue", "start": 11329, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 11411, "length": 7, "messageText": "Parameter 'subject' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 11611, "length": 17, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ math: string; chinese: string; english: string; physics: string; chemistry: string; biology: string; history: string; geography: string; }'."}, {"file": "./src/views/question-bank/categories.vue", "start": 11670, "length": 7, "messageText": "Parameter 'subject' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 11829, "length": 14, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ math: string; chinese: string; english: string; physics: string; chemistry: string; }'."}, {"file": "./src/views/question-bank/categories.vue", "start": 11880, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 11987, "length": 12, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ 1: string; 2: string; 3: string; 4: string; 5: string; }'."}, {"file": "./src/views/question-bank/categories.vue", "start": 12041, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 12190, "length": 14, "messageText": "Parameter 'parentCategory' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 12896, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 13019, "length": 8, "messageText": "Parameter 'category' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/categories.vue", "start": 4423, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/categories.vue", "start": 4575, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/categories.vue", "start": 4744, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/categories.vue", "start": 5031, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/categories.vue", "start": 6312, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], 456, [459, [{"file": "./src/views/question-bank/papers.vue", "start": 12396, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 12474, "length": 7, "messageText": "Parameter 'subject' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 12615, "length": 17, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ chinese: string; math: string; english: string; physics: string; chemistry: string; }'."}, {"file": "./src/views/question-bank/papers.vue", "start": 12674, "length": 7, "messageText": "Parameter 'subject' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 12833, "length": 14, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ chinese: string; math: string; english: string; physics: string; chemistry: string; }'."}, {"file": "./src/views/question-bank/papers.vue", "start": 12884, "length": 10, "messageText": "Parameter 'difficulty' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 12988, "length": 24, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ easy: string; medium: string; hard: string; }'."}, {"file": "./src/views/question-bank/papers.vue", "start": 13053, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 13159, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/question-bank/papers.vue", "start": 13215, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 13328, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/question-bank/papers.vue", "start": 13371, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 13449, "length": 5, "messageText": "Parameter 'paper' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 13540, "length": 5, "messageText": "Parameter 'paper' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 13672, "length": 5, "messageText": "Parameter 'paper' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 13766, "length": 5, "messageText": "Parameter 'paper' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 14753, "length": 5, "messageText": "Parameter 'paper' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 14864, "length": 5, "messageText": "Parameter 'paper' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/papers.vue", "start": 5630, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/papers.vue", "start": 5795, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/papers.vue", "start": 5960, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/papers.vue", "start": 6132, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/papers.vue", "start": 6323, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/papers.vue", "start": 6665, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/papers.vue", "start": 9030, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], 455, [458, [{"file": "./src/views/question-bank/questions.vue", "start": 24247, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 24325, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 24484, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ single_choice: string; multiple_choice: string; fill_blank: string; essay: string; true_false: string; }'."}, {"file": "./src/views/question-bank/questions.vue", "start": 24531, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 24706, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ single_choice: string; multiple_choice: string; fill_blank: string; essay: string; true_false: string; }'."}, {"file": "./src/views/question-bank/questions.vue", "start": 24754, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 24861, "length": 12, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ 1: string; 2: string; 3: string; 4: string; 5: string; }'."}, {"file": "./src/views/question-bank/questions.vue", "start": 24916, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 25022, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/question-bank/questions.vue", "start": 25078, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 25191, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ draft: string; published: string; archived: string; }'."}, {"file": "./src/views/question-bank/questions.vue", "start": 25243, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 25351, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ pending: string; approved: string; rejected: string; }'."}, {"file": "./src/views/question-bank/questions.vue", "start": 25413, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 25529, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ pending: string; approved: string; rejected: string; }'."}, {"file": "./src/views/question-bank/questions.vue", "start": 25572, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 25653, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 25758, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 26528, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 27063, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 27243, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 27250, "length": 9, "messageText": "Parameter 'isCorrect' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 27513, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 27810, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 27986, "length": 8, "code": 2345, "category": 1, "messageText": "Argument of type 'string' is not assignable to parameter of type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 28020, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 28760, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 28881, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 29004, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 29688, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 30163, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/questions.vue", "start": 7201, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 7369, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 7537, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 7763, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 8108, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 10599, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 14798, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 15631, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 15835, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 16741, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/questions.vue", "start": 18058, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'title' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 18241, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'question_type' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 18419, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'subject' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 18590, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'grade_level' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 18783, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'difficulty' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 18958, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'score' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 19162, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 19261, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'options' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 19288, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'options' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 19425, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'options' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 19898, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'answer_analysis' does not exist on type 'never'."}, {"file": "./src/views/question-bank/questions.vue", "start": 20729, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [460, [{"file": "./src/views/question-bank/statistics.vue", "start": 10359, "length": 7, "messageText": "Parameter 'subject' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 10500, "length": 17, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ chinese: string; math: string; english: string; physics: string; chemistry: string; }'."}, {"file": "./src/views/question-bank/statistics.vue", "start": 10559, "length": 7, "messageText": "Parameter 'subject' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 10718, "length": 14, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ chinese: string; math: string; english: string; physics: string; chemistry: string; }'."}, {"file": "./src/views/question-bank/statistics.vue", "start": 10769, "length": 10, "messageText": "Parameter 'difficulty' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 10873, "length": 24, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ easy: string; medium: string; hard: string; }'."}, {"file": "./src/views/question-bank/statistics.vue", "start": 10936, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 11095, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ single_choice: string; multiple_choice: string; true_false: string; fill_blank: string; essay: string; }'."}, {"file": "./src/views/question-bank/statistics.vue", "start": 11144, "length": 8, "messageText": "Parameter 'accuracy' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 11324, "length": 8, "messageText": "Parameter 'accuracy' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 11504, "length": 11, "messageText": "Parameter 'coefficient' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 11643, "length": 7, "messageText": "Parameter 'seconds' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 11847, "length": 4, "messageText": "Parameter 'date' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 11927, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 12038, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 12154, "length": 8, "messageText": "Parameter 'question' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/question-bank/statistics.vue", "start": 8192, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/statistics.vue", "start": 8351, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/question-bank/statistics.vue", "start": 8519, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], [450, [{"file": "./src/views/settings.vue", "start": 58849, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'token' does not exist on type 'Store<\"user\", Pick<{ profile: Ref<{ id: number; username: string; full_name: string; email: string; phone?: string | undefined; department?: string | undefined; position?: string | undefined; bio?: string | undefined; ... 7 more ...; preferences?: { ...; } | undefined; } | null, UserProfile | ... 1 more ... | null>;...'."}, {"file": "./src/views/settings.vue", "start": 71200, "length": 33, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'never'.", "category": 1, "code": 2322}]}}, {"file": "./src/views/settings.vue", "start": 84611, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'testing' does not exist on type 'AIModelConfig'."}, {"file": "./src/views/settings.vue", "start": 84877, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'testing' does not exist on type 'AIModelConfig'."}, {"file": "./src/views/settings.vue", "start": 87732, "length": 22, "messageText": "Cannot find name 'refreshPerformanceData'.", "category": 1, "code": 2304}, {"file": "./src/views/settings.vue", "start": 21760, "length": 7, "code": 2322, "category": 1, "messageText": "Type 'null' is not assignable to type 'EpPropMergeType<(new (...args: any[]) => string | number | boolean | Record<string, any> | EpPropMergeType<(BooleanConstructor | ObjectConstructor | StringConstructor | NumberConstructor)[], unknown, unknown>[]) | (() => EpPropMergeType<...> | EpPropMergeType<...>[]) | ((new (...args: any[]) => string | ... 3 more ....'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 692474, "length": 10, "messageText": "The expected type comes from property 'modelValue' which is declared here on type 'Partial<{ disabled: boolean; offset: number; multiple: boolean; loading: boolean; modelValue: EpPropMergeType<(new (...args: any[]) => string | number | boolean | Record<string, any> | EpPropMergeType<(BooleanConstructor | ObjectConstructor | StringConstructor | NumberConstructor)[], unknown, unknown>[]) | (() => Ep...'", "category": 3, "code": 6500}]}, {"file": "./src/views/settings.vue", "start": 23664, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/settings.vue", "start": 32152, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(mode: string) => void' is not assignable to type '(val: string | number | boolean | undefined) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'mode' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"file": "./src/views/settings.vue", "start": 32675, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(color: string) => void' is not assignable to type '(val: string | null) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'color' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | null' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"file": "./src/views/settings.vue", "start": 35058, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(enabled: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'enabled' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"file": "./src/views/settings.vue", "start": 35353, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(enabled: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'enabled' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/views/settings.vue", "start": 43142, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(enabled: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'enabled' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/views/settings.vue", "start": 43785, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(size: string) => void' is not assignable to type '(val: string | number | boolean | undefined) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'size' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/views/settings.vue", "start": 48654, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(enabled: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'enabled' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/views/settings.vue", "start": 49745, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/settings.vue", "start": 52346, "length": 6, "code": 2322, "category": 1, "messageText": "Type '{ provider: { required: boolean; message: string; trigger: string; }[]; display_name: ({ required: boolean; message: string; trigger: string; min?: undefined; max?: undefined; } | { min: number; max: number; message: string; trigger: string; required?: undefined; })[]; model_name: { ...; }[]; api_key: { ...; }[]; ap...' is not assignable to type 'Partial<Record<string, Arrayable<FormItemRule>>>'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 144103, "length": 5, "messageText": "The expected type comes from property 'rules' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly inline: boolean; readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>; ... 9 more ...; readonly scrollToError: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/settings.vue", "start": 55670, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/views/settings.vue", "start": 55784, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/views/settings.vue", "start": 56010, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/views/settings.vue", "start": 56345, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/views/settings.vue", "start": 56454, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/views/settings.vue", "start": 56671, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/views/settings.vue", "start": 57020, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/views/settings.vue", "start": 57308, "length": 29, "messageText": "'__VLS_ctx.aiModelDialog.form.parameters' is possibly 'undefined'.", "category": 1, "code": 18048}]], [480, [{"file": "./src/views/software-update/index.vue", "start": 1232, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 269544, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}]], 479, [483, [{"file": "./src/views/software-update/statistics.vue", "start": 9013, "length": 4, "messageText": "Parameter 'rate' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/statistics.vue", "start": 9177, "length": 4, "messageText": "Parameter 'rate' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/statistics.vue", "start": 9333, "length": 4, "messageText": "Parameter 'date' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/statistics.vue", "start": 9413, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/statistics.vue", "start": 9521, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [482, [{"file": "./src/views/software-update/strategies.vue", "start": 4552, "length": 8, "messageText": "Parameter 'strategy' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/strategies.vue", "start": 5239, "length": 8, "messageText": "Parameter 'strategy' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/strategies.vue", "start": 5362, "length": 8, "messageText": "Parameter 'strategy' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/strategies.vue", "start": 2718, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], [481, [{"file": "./src/views/software-update/versions.vue", "start": 25292, "length": 9, "messageText": "Parameter 'selection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 25369, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 25465, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ major: string; minor: string; patch: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 25512, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 25618, "length": 11, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ major: string; minor: string; patch: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 25663, "length": 7, "messageText": "Parameter 'channel' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 25764, "length": 17, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ stable: string; beta: string; alpha: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 25821, "length": 8, "messageText": "Parameter 'platform' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 25974, "length": 19, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ windows: string; macos: string; linux: string; android: string; ios: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 26032, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 26164, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ development: string; testing: string; released: string; rollback: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 26220, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 26361, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ development: string; testing: string; released: string; rollback: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 26415, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 26545, "length": 16, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ planning: string; development: string; testing: string; ready: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 26606, "length": 6, "messageText": "Parameter 'status' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 26745, "length": 13, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ planning: string; development: string; testing: string; ready: string; }'."}, {"file": "./src/views/software-update/versions.vue", "start": 26792, "length": 5, "messageText": "Parameter 'bytes' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 27044, "length": 4, "messageText": "Parameter 'time' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 27117, "length": 4, "messageText": "Parameter 'date' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 27219, "length": 4, "messageText": "Parameter 'date' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 27288, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 27389, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 27530, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 27640, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 28307, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 28688, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 28835, "length": 4, "messageText": "Parameter 'file' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 29199, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 30252, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 30371, "length": 7, "messageText": "Parameter 'version' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 7122, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"link\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"default\" | \"text\" | \"success\" | \"primary\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 18756, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly link: boolean; readonly circle: boolean; readonly text: boolean; readonly disabled: boolean; readonly round: EpPropMergeType<BooleanConstructor, unknown, unknown>; ... 9 more ...; readonly autoInsertSpace: EpPropMergeType<...>; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/views/software-update/versions.vue", "start": 12984, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/software-update/versions.vue", "start": 13163, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/software-update/versions.vue", "start": 13337, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/software-update/versions.vue", "start": 13514, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/software-update/versions.vue", "start": 13694, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}, {"file": "./src/views/software-update/versions.vue", "start": 16970, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'version' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 17048, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 17166, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'type' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 17220, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'type' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 17324, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'build_number' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 17424, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'file_size' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 17630, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'download_count' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 17836, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'install_count' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 18042, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'success_rate' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 18428, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 18530, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'new_features' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 18631, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'new_features' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 18734, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'bug_fixes' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 18832, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'bug_fixes' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 18932, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'improvements' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 19033, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'improvements' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 19403, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'release_date' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 19604, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'release_channel' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 19793, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'target_platforms' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 19815, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/software-update/versions.vue", "start": 20022, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'force_update' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 20315, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'push_progress' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 20352, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'pushed_users' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 20386, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'target_users' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 20499, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'push_progress' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 20553, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'push_progress' does not exist on type 'never'."}, {"file": "./src/views/software-update/versions.vue", "start": 21124, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 127935, "length": 4, "messageText": "The expected type comes from property 'rows' which is declared here on type 'Partial<{ readonly disabled: boolean; readonly id: string; readonly type: string; readonly modelValue: EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number | null | undefined) | ((new (...args: any[]) => string | number) | (() => string | ... 2 more ... | undefined))[], unknown, unknown...'", "category": 3, "code": 6500}]}]], 425, 428, 430, 414, [404, [{"file": "./src/views/userdetail.vue", "start": 4778, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'AxiosResponse<any, any>' is not assignable to type '{ id: number; nickname: string; email: string; full_name: string; avatar_url: string; gender: string; level: number; experience_points: number; total_study_time: number; total_focus_sessions: number; ... 4 more ...; is_premium: boolean; } | { ...; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'AxiosResponse<any, any>' is missing the following properties from type '{ id: number; nickname: string; email: string; full_name: string; avatar_url: string; gender: string; level: number; experience_points: number; total_study_time: number; total_focus_sessions: number; ... 4 more ...; is_premium: boolean; }': id, nickname, email, full_name, and 10 more.", "category": 1, "code": 2740}]}}, {"file": "./src/views/userdetail.vue", "start": 5066, "length": 108, "code": 2322, "category": 1, "messageText": "Type '{ type: string; description: string; created_at: string; }' is not assignable to type 'never'."}, {"file": "./src/views/userdetail.vue", "start": 5182, "length": 105, "code": 2322, "category": 1, "messageText": "Type '{ type: string; description: string; created_at: string; }' is not assignable to type 'never'."}]], [403, [{"file": "./src/views/users.vue", "start": 19099, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19143, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'total' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19209, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'total' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19258, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19359, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19722, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'total_users' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19768, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'active_users' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19816, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'premium_users' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19866, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'new_users_today' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19925, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'total_users_growth_rate' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 19993, "length": 24, "code": 2339, "category": 1, "messageText": "Property 'active_users_growth_rate' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 20063, "length": 25, "code": 2339, "category": 1, "messageText": "Property 'premium_users_growth_rate' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 20135, "length": 27, "code": 2339, "category": 1, "messageText": "Property 'new_users_today_growth_rate' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 24253, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 24280, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'total' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 24426, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 24484, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'total' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 24716, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/users.vue", "start": 24763, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type 'AxiosResponse<any, any>'."}]]], "affectedFilesPendingEmit": [406, 519, 520, 488, 521, 522, 523, 405, 524, 525, 487, 526, 426, 398, 490, 423, 420, 417, 422, 424, 418, 419, 421, 491, 101, 492, 413, 494, 495, 496, 497, 498, 493, 518, 485, 411, 489, 448, 527, 528, 412, 427, 529, 530, 531, 532, 533, 99, 445, 408, 449, 536, 409, 429, 433, 402, 486, 401, 537, 517, 410, 484, 451, 453, 452, 454, 499, 500, 444, 436, 443, 501, 447, 437, 442, 438, 439, 440, 441, 446, 502, 503, 504, 475, 473, 478, 474, 476, 477, 397, 434, 432, 431, 505, 435, 461, 462, 464, 466, 465, 463, 506, 507, 100, 407, 471, 470, 469, 508, 472, 468, 467, 457, 456, 459, 455, 458, 460, 450, 480, 479, 483, 482, 481, 425, 428, 430, 414, 404, 403], "emitSignatures": [99, 100, 101, 397, 398, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 517, 519, 520, 521, 522, 523, 524, 525, 527, 528, 529, 530, 532, 533, 537]}, "version": "5.3.3"}