{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./node_modules/axios/index.d.ts", "./node_modules/element-plus/es/utils/vue3.3.polyfill.d.ts", "./node_modules/dayjs/locale/types.d.ts", "./node_modules/dayjs/locale/index.d.ts", "./node_modules/dayjs/index.d.ts", "./node_modules/@types/lodash/common/common.d.ts", "./node_modules/@types/lodash/common/array.d.ts", "./node_modules/@types/lodash/common/collection.d.ts", "./node_modules/@types/lodash/common/date.d.ts", "./node_modules/@types/lodash/common/function.d.ts", "./node_modules/@types/lodash/common/lang.d.ts", "./node_modules/@types/lodash/common/math.d.ts", "./node_modules/@types/lodash/common/number.d.ts", "./node_modules/@types/lodash/common/object.d.ts", "./node_modules/@types/lodash/common/seq.d.ts", "./node_modules/@types/lodash/common/string.d.ts", "./node_modules/@types/lodash/common/util.d.ts", "./node_modules/@types/lodash/index.d.ts", "./node_modules/@vueuse/shared/index.d.ts", "./node_modules/@vueuse/core/index.d.ts", "./node_modules/memoize-one/dist/memoize-one.d.ts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "./node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "./node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "./node_modules/vue-router/dist/vue-router.d.ts", "./node_modules/@ctrl/tinycolor/dist/interfaces.d.ts", "./node_modules/@ctrl/tinycolor/dist/index.d.ts", "./node_modules/@ctrl/tinycolor/dist/css-color-names.d.ts", "./node_modules/@ctrl/tinycolor/dist/readability.d.ts", "./node_modules/@ctrl/tinycolor/dist/to-ms-filter.d.ts", "./node_modules/@ctrl/tinycolor/dist/from-ratio.d.ts", "./node_modules/@ctrl/tinycolor/dist/format-input.d.ts", "./node_modules/@ctrl/tinycolor/dist/random.d.ts", "./node_modules/@ctrl/tinycolor/dist/conversion.d.ts", "./node_modules/@ctrl/tinycolor/dist/public_api.d.ts", "./node_modules/async-validator/dist-types/interface.d.ts", "./node_modules/async-validator/dist-types/index.d.ts", "./node_modules/element-plus/es/index.d.ts", "./node_modules/@types/nprogress/index.d.ts", "./src/types/auth.ts", "./src/views/login.vue.ts", "./src/components/layout.vue.ts", "./src/views/dashboard.vue.ts", "./src/views/users.vue.ts", "./src/views/userdetail.vue.ts", "./src/views/analytics.vue.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/add-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/aim.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/alarm-clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/apple.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/avatar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/back.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/baseball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/basketball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bicycle.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bowl.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/briefcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/burger.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/calendar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cellphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cherry.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chicken.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chrome-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee-cup.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coin.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cold-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/comment.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/compass.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/connection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coordinate.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/copy-document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cpu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/credit-card.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/crop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-caret.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-analysis.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-board.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dessert.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/discount.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish-dot.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-copy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/download.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/drizzling.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit-pen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/element-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/expand.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/failed.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/female.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/files.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/film.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/filter.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/finished.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/first-aid-kit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/flag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-opened.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/food.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/football.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fork-spoon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fries.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/full-screen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/gold-medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grape.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grid.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/guide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/handbag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/headset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/histogram.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/home-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hot-water.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/house.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/info-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/iphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/key.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/knife-fork.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lightning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/link.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/list.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/loading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-information.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lollipop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magic-stick.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magnet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/male.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/management.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/map-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/memo.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/menu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mic.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/microphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/milk-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/minus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/money.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/monitor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon-night.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mostly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mouse.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mug.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute-notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/no-smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notebook.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/odometer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/office-building.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/open.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/operation.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/opportunity.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/orange.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/paperclip.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/partly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pear.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-rounded.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pie-chart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/place.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/platform.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pointer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/position.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/postcard.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pouring.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/present.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/price-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/printer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/promotion.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/quartz-watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/question-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/rank.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading-lamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refrigerator.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scale-to-original.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/school.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scissor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/search.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/semi-select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/service.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/set-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/setting.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/share.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ship.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-bag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-trolley.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/soccer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sold-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stopwatch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/success-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sugar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunny.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunrise.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-button.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/takeaway-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ticket.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tickets.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/timer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/toilet-paper.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tools.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trend-charts.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy-base.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/turn-off.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/umbrella.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/unlock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/van.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-pause.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-play.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/view.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warn-triangle-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watermelon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wind-power.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-in.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/index.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/index.d.ts", "./src/api/profile.ts", "./src/store/user.ts", "./src/views/profile.vue.ts", "./src/views/datasync.vue.ts", "./src/views/404.vue.ts", "./src/router/index.ts", "./src/utils/cache.ts", "./src/utils/error-handler.ts", "./src/api/request.ts", "./src/api/auth.ts", "./src/store/auth.ts", "./src/app.vue.ts", "./node_modules/echarts/types/dist/echarts.d.ts", "./node_modules/echarts/index.d.ts", "./src/components/errormonitor.vue.ts", "./src/components/performancemonitor.vue.ts", "./src/types/settings.ts", "./src/components/settingsgroup.vue.ts", "./src/components/settings/basicsettings.vue.ts", "./src/components/settings/emailsettings.vue.ts", "./src/components/settings/logsettings.vue.ts", "./src/components/settings/securitysettings.vue.ts", "./src/components/settings/uisettings.vue.ts", "./__vls_types.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./env.d.ts", "./src/main.ts", "./src/api/settings.ts", "./src/test/profile-navigation.test.ts", "./src/test/profile.test.ts", "./src/test/settings.test.ts", "./src/test/setup.ts", "./src/types/element-plus.d.ts", "./src/types/global.d.ts", "./src/types/user.ts", "./auto-imports.d.ts", "./components.d.ts", "./node_modules/element-plus/global.d.ts", "./src/components/layout.vue", "./src/views/login.vue", "./src/views/dashboard.vue", "./src/views/users.vue", "./src/views/userdetail.vue", "./src/views/sync.vue", "./src/views/analytics.vue", "./src/views/settings.vue", "./src/views/404.vue", "./src/app.vue", "./src/views/profile.vue", "./src/components/settings/basicsettings.vue", "./src/components/settings/securitysettings.vue", "./src/components/settings/emailsettings.vue", "./src/components/settings/logsettings.vue", "./src/components/settings/uisettings.vue"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0"], "root": [[99, 105], [401, 412], [415, 424], [431, 442]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[48, 50, 51, 56, 58, 60, 84, 439, 442, 443], [56, 58, 60, 84, 97, 442, 443], [56, 58, 60, 84, 97, 101, 415, 416, 418, 419, 420, 421, 422, 423, 431, 443], [56, 58, 60, 84, 430, 442, 443], [52], [85], [86], [85, 86, 87, 88, 89, 90, 91, 92, 93], [56, 58, 60, 84, 442, 443], [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398], [399], [80], [81, 82], [64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76], [65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 69, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 70, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 71, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 72, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 74, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76], [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], [46, 52, 53], [54], [46], [46, 47, 48, 50, 439], [47, 48, 49, 50, 439], [57, 77], [57], [95], [62], [61], [413], [47, 48, 49, 50, 56, 58, 60, 63, 76, 78, 79, 81, 83, 84, 94, 96, 442, 443], [56, 58, 60, 84, 97, 442], [56, 57, 60, 84, 442, 443], [429], [425], [426], [427, 428], [50, 55], [50], [51, 99, 409], [51, 409], [51, 59, 97, 406, 407, 408, 409, 411], [51, 409, 417], [51, 56, 58, 60, 84, 411, 442, 443], [51, 56, 58, 60, 84, 408, 414, 442, 443], [51, 56, 58, 60, 84, 97, 411, 442, 443], [51, 56, 58, 60, 84, 407, 442, 443], [51, 56, 58, 60, 84, 97, 400, 417, 442, 443], [51, 56, 58, 60, 84, 97, 400, 406, 412, 430, 431, 438, 439, 442, 443], [51, 84, 98, 100, 101, 102, 103, 104, 105, 403, 404, 405, 411, 430, 431], [51, 56, 58, 60, 84, 97, 99, 410, 442, 443], [51, 56, 58, 60, 84, 97, 401, 442, 443], [51, 58, 84, 101, 411], [51, 58, 401, 402, 403], [51, 97, 417, 419, 420, 421, 422, 423], [51], [431, 439], [48, 50, 97, 431, 438], [51, 59, 97, 409], [51, 56, 58, 60, 84, 442, 443], [51, 56, 58, 60, 84, 97, 409, 442, 443], [51, 56, 58, 60, 84, 97, 99, 411, 442, 443], [51, 56, 58, 60, 84, 97, 400, 401, 402, 442, 443], [51, 56, 58, 60, 84, 97, 442, 443], [51, 56, 58, 60, 84, 97, 406, 432, 434, 444], [429, 430], [56, 58, 60, 84, 432, 444], [56, 58, 60, 84, 444], [107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399], [46, 47, 48, 50], [47, 48, 49, 50], [47, 48, 49, 50, 56, 58, 60, 63, 76, 78, 79, 81, 83, 84, 94, 96, 444], [56, 58, 60, 84, 97, 444], [56, 57, 60, 84, 444], [48, 50, 51, 56, 58, 60, 84, 444], [48, 50, 51, 56, 58, 60, 84, 440, 443, 444], [427], [428], [51, 59, 97, 406, 409, 411], [431], [51, 84, 98, 411, 432, 445, 446, 447, 448, 449, 450, 451, 452, 453], [51, 56, 58, 60, 84, 97, 99, 410, 444], [51, 56, 58, 60, 84, 97, 401, 443, 444], [400], [51, 58, 84, 411, 444], [51, 58, 401, 402, 454], [51, 97, 417, 455, 456, 457, 458, 459], [432, 440], [48, 50, 97, 432, 439]], "referencedMap": [[424, 1], [441, 2], [442, 3], [431, 4], [53, 5], [93, 6], [91, 6], [90, 7], [86, 6], [94, 8], [92, 7], [88, 7], [89, 7], [106, 9], [107, 9], [108, 9], [109, 9], [110, 9], [111, 9], [112, 9], [113, 9], [114, 9], [115, 9], [116, 9], [117, 9], [118, 9], [119, 9], [120, 9], [121, 9], [122, 9], [123, 9], [124, 9], [125, 9], [126, 9], [127, 9], [128, 9], [129, 9], [130, 9], [131, 9], [132, 9], [133, 9], [134, 9], [135, 9], [136, 9], [137, 9], [138, 9], [139, 9], [140, 9], [141, 9], [142, 9], [143, 9], [144, 9], [145, 9], [146, 9], [147, 9], [148, 9], [149, 9], [150, 9], [151, 9], [152, 9], [153, 9], [154, 9], [155, 9], [156, 9], [157, 9], [158, 9], [159, 9], [160, 9], [161, 9], [162, 9], [163, 9], [164, 9], [165, 9], [166, 9], [167, 9], [168, 9], [169, 9], [170, 9], [171, 9], [172, 9], [173, 9], [174, 9], [175, 9], [176, 9], [177, 9], [178, 9], [179, 9], [180, 9], [181, 9], [182, 9], [183, 9], [184, 9], [185, 9], [186, 9], [187, 9], [188, 9], [189, 9], [190, 9], [191, 9], [192, 9], [193, 9], [194, 9], [195, 9], [196, 9], [197, 9], [198, 9], [199, 9], [200, 9], [201, 9], [202, 9], [203, 9], [204, 9], [205, 9], [206, 9], [207, 9], [208, 9], [209, 9], [210, 9], [211, 9], [212, 9], [213, 9], [214, 9], [215, 9], [216, 9], [217, 9], [218, 9], [219, 9], [220, 9], [221, 9], [222, 9], [223, 9], [224, 9], [225, 9], [226, 9], [227, 9], [228, 9], [229, 9], [230, 9], [231, 9], [232, 9], [233, 9], [234, 9], [235, 9], [236, 9], [237, 9], [238, 9], [239, 9], [240, 9], [241, 9], [242, 9], [243, 9], [244, 9], [245, 9], [246, 9], [247, 9], [399, 10], [248, 9], [249, 9], [250, 9], [251, 9], [252, 9], [253, 9], [254, 9], [255, 9], [256, 9], [257, 9], [258, 9], [259, 9], [260, 9], [261, 9], [262, 9], [263, 9], [264, 9], [265, 9], [266, 9], [267, 9], [268, 9], [269, 9], [270, 9], [271, 9], [272, 9], [273, 9], [274, 9], [275, 9], [276, 9], [277, 9], [278, 9], [279, 9], [280, 9], [281, 9], [282, 9], [283, 9], [284, 9], [285, 9], [286, 9], [287, 9], [288, 9], [289, 9], [290, 9], [291, 9], [292, 9], [293, 9], [294, 9], [295, 9], [296, 9], [297, 9], [298, 9], [299, 9], [300, 9], [301, 9], [302, 9], [303, 9], [304, 9], [305, 9], [306, 9], [307, 9], [308, 9], [309, 9], [310, 9], [311, 9], [312, 9], [313, 9], [314, 9], [315, 9], [316, 9], [317, 9], [318, 9], [319, 9], [320, 9], [321, 9], [322, 9], [323, 9], [324, 9], [325, 9], [326, 9], [327, 9], [328, 9], [329, 9], [330, 9], [331, 9], [332, 9], [333, 9], [334, 9], [335, 9], [336, 9], [337, 9], [338, 9], [339, 9], [340, 9], [341, 9], [342, 9], [343, 9], [344, 9], [345, 9], [346, 9], [347, 9], [348, 9], [349, 9], [350, 9], [351, 9], [352, 9], [353, 9], [354, 9], [355, 9], [356, 9], [357, 9], [358, 9], [359, 9], [360, 9], [361, 9], [362, 9], [363, 9], [364, 9], [365, 9], [366, 9], [367, 9], [368, 9], [369, 9], [370, 9], [371, 9], [372, 9], [373, 9], [374, 9], [375, 9], [376, 9], [377, 9], [378, 9], [379, 9], [380, 9], [381, 9], [382, 9], [383, 9], [384, 9], [385, 9], [386, 9], [387, 9], [388, 9], [389, 9], [390, 9], [391, 9], [392, 9], [393, 9], [394, 9], [395, 9], [396, 9], [397, 9], [398, 9], [400, 11], [81, 12], [83, 13], [65, 14], [66, 15], [64, 16], [67, 17], [68, 18], [69, 19], [70, 20], [71, 21], [72, 22], [73, 23], [74, 24], [75, 25], [76, 26], [54, 27], [55, 28], [47, 29], [48, 30], [50, 31], [78, 32], [77, 33], [96, 34], [63, 35], [62, 36], [414, 37], [97, 38], [60, 9], [443, 39], [58, 40], [430, 41], [426, 42], [427, 43], [429, 44], [57, 9], [84, 9], [56, 45], [51, 46], [410, 47], [401, 48], [409, 49], [433, 50], [412, 51], [415, 52], [101, 53], [416, 54], [419, 55], [420, 55], [421, 55], [422, 55], [423, 55], [418, 55], [432, 56], [406, 57], [411, 58], [402, 59], [434, 60], [435, 61], [436, 62], [437, 63], [99, 63], [438, 64], [439, 65], [417, 63], [440, 63], [407, 63], [408, 66], [405, 67], [105, 67], [102, 67], [404, 68], [100, 69], [403, 70], [104, 67], [103, 71]], "exportedModulesMap": [[424, 1], [441, 72], [442, 63], [431, 73], [53, 5], [93, 6], [91, 6], [90, 7], [86, 6], [94, 8], [92, 7], [88, 7], [89, 7], [106, 74], [107, 75], [108, 75], [109, 75], [110, 75], [111, 75], [112, 75], [113, 75], [114, 75], [115, 75], [116, 75], [117, 75], [118, 75], [119, 75], [120, 75], [121, 75], [122, 75], [123, 75], [124, 75], [125, 75], [126, 75], [127, 75], [128, 75], [129, 75], [130, 75], [131, 75], [132, 75], [133, 75], [134, 75], [135, 75], [136, 75], [137, 75], [138, 75], [139, 75], [140, 75], [141, 75], [142, 75], [143, 75], [144, 75], [145, 75], [146, 75], [147, 75], [148, 75], [149, 75], [150, 75], [151, 75], [152, 75], [153, 75], [154, 75], [155, 75], [156, 75], [157, 75], [158, 75], [159, 75], [160, 75], [161, 75], [162, 75], [163, 75], [164, 75], [165, 75], [166, 75], [167, 75], [168, 75], [169, 75], [170, 75], [171, 75], [172, 75], [173, 75], [174, 75], [175, 75], [176, 75], [177, 75], [178, 75], [179, 75], [180, 75], [181, 75], [182, 75], [183, 75], [184, 75], [185, 75], [186, 75], [187, 75], [188, 75], [189, 75], [190, 75], [191, 75], [192, 75], [193, 75], [194, 75], [195, 75], [196, 75], [197, 75], [198, 75], [199, 75], [200, 75], [201, 75], [202, 75], [203, 75], [204, 75], [205, 75], [206, 75], [207, 75], [208, 75], [209, 75], [210, 75], [211, 75], [212, 75], [213, 75], [214, 75], [215, 75], [216, 75], [217, 75], [218, 75], [219, 75], [220, 75], [221, 75], [222, 75], [223, 75], [224, 75], [225, 75], [226, 75], [227, 75], [228, 75], [229, 75], [230, 75], [231, 75], [232, 75], [233, 75], [234, 75], [235, 75], [236, 75], [237, 75], [238, 75], [239, 75], [240, 75], [241, 75], [242, 75], [243, 75], [244, 75], [245, 75], [246, 75], [247, 75], [399, 75], [248, 75], [249, 75], [250, 75], [251, 75], [252, 75], [253, 75], [254, 75], [255, 75], [256, 75], [257, 75], [258, 75], [259, 75], [260, 75], [261, 75], [262, 75], [263, 75], [264, 75], [265, 75], [266, 75], [267, 75], [268, 75], [269, 75], [270, 75], [271, 75], [272, 75], [273, 75], [274, 75], [275, 75], [276, 75], [277, 75], [278, 75], [279, 75], [280, 75], [281, 75], [282, 75], [283, 75], [284, 75], [285, 75], [286, 75], [287, 75], [288, 75], [289, 75], [290, 75], [291, 75], [292, 75], [293, 75], [294, 75], [295, 75], [296, 75], [297, 75], [298, 75], [299, 75], [300, 75], [301, 75], [302, 75], [303, 75], [304, 75], [305, 75], [306, 75], [307, 75], [308, 75], [309, 75], [310, 75], [311, 75], [312, 75], [313, 75], [314, 75], [315, 75], [316, 75], [317, 75], [318, 75], [319, 75], [320, 75], [321, 75], [322, 75], [323, 75], [324, 75], [325, 75], [326, 75], [327, 75], [328, 75], [329, 75], [330, 75], [331, 75], [332, 75], [333, 75], [334, 75], [335, 75], [336, 75], [337, 75], [338, 75], [339, 75], [340, 75], [341, 75], [342, 75], [343, 75], [344, 75], [345, 75], [346, 75], [347, 75], [348, 75], [349, 75], [350, 75], [351, 75], [352, 75], [353, 75], [354, 75], [355, 75], [356, 75], [357, 75], [358, 75], [359, 75], [360, 75], [361, 75], [362, 75], [363, 75], [364, 75], [365, 75], [366, 75], [367, 75], [368, 75], [369, 75], [370, 75], [371, 75], [372, 75], [373, 75], [374, 75], [375, 75], [376, 75], [377, 75], [378, 75], [379, 75], [380, 75], [381, 75], [382, 75], [383, 75], [384, 75], [385, 75], [386, 75], [387, 75], [388, 75], [389, 75], [390, 75], [391, 75], [392, 75], [393, 75], [394, 75], [395, 75], [396, 75], [397, 75], [398, 75], [400, 76], [81, 12], [83, 13], [65, 14], [66, 15], [64, 16], [67, 17], [68, 18], [69, 19], [70, 20], [71, 21], [72, 22], [73, 23], [74, 24], [75, 25], [76, 26], [54, 27], [55, 28], [47, 29], [48, 77], [50, 78], [78, 32], [77, 33], [96, 34], [63, 35], [62, 36], [414, 37], [97, 79], [60, 75], [443, 80], [58, 81], [426, 82], [425, 83], [428, 84], [429, 85], [57, 75], [84, 75], [56, 45], [51, 46], [410, 47], [401, 48], [409, 86], [433, 50], [412, 51], [415, 52], [101, 53], [416, 54], [419, 55], [420, 55], [421, 55], [422, 55], [423, 55], [418, 55], [432, 87], [406, 88], [411, 89], [402, 90], [434, 91], [435, 92], [436, 93], [437, 94], [99, 63], [438, 63], [439, 95], [417, 63], [440, 96], [407, 63], [408, 66], [405, 67], [105, 67], [102, 67], [404, 68], [100, 69], [403, 70], [104, 67], [103, 71]], "semanticDiagnosticsPerFile": [424, 441, 442, 431, 53, 52, 93, 87, 91, 90, 86, 85, 94, 92, 88, 89, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 399, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 400, 81, 83, 80, 82, 65, 66, 64, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 98, 54, 55, 47, 48, 50, 46, 78, 77, 96, 95, 59, 49, 63, 62, 61, 414, 413, 97, 60, 443, 79, 58, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 430, 426, 425, 427, 428, 429, 57, 84, 56, 51, 410, 401, [409, [{"file": "./src/api/request.ts", "start": 1186, "length": 33, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '(config: AxiosRequestConfig) => Promise<any> | AxiosRequestConfig<any>' is not assignable to parameter of type '(value: InternalAxiosRequestConfig<any>) => InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'Promise<any> | AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'headers' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'AxiosHeaders | (Partial<RawAxiosHeaders & { Accept: AxiosHeaderValue; \"Content-Length\": AxiosHeaderValue; \"User-Agent\": AxiosHeaderValue; \"Content-Encoding\": AxiosHeaderValue; Authorization: AxiosHeaderValue; } & { ...; }> & Partial<...>) | undefined' is not assignable to type 'AxiosRequestHeaders'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'AxiosRequestHeaders'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'Partial<RawAxiosHeaders & { Accept: AxiosHeaderValue; \"Content-Length\": AxiosHeaderValue; \"User-Agent\": AxiosHeaderValue; \"Content-Encoding\": AxiosHeaderValue; Authorization: AxiosHeaderValue; } & { ...; }>'.", "category": 1, "code": 2322}]}]}]}]}]}]}]}}]], 433, 412, 415, [101, [{"file": "./src/components/layout.vue", "start": 4179, "length": 6, "messageText": "The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.", "category": 1, "code": 2362}, {"file": "./src/components/layout.vue", "start": 4188, "length": 6, "messageText": "The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.", "category": 1, "code": 2363}]], [416, [{"file": "./src/components/performancemonitor.vue", "start": 3207, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'hit_rate' does not exist on type '{ total: number; expired: number; size: number; }'."}]], 419, 420, 421, 422, [423, [{"file": "./src/components/settings/uisettings.vue", "start": 10581, "length": 25, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | undefined' is not assignable to parameter of type 'string | null'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string | null'.", "category": 1, "code": 2322}]}}]], [418, [{"file": "./src/components/settingsgroup.vue", "start": 233, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'SettingsGroup'."}, {"file": "./src/components/settingsgroup.vue", "start": 289, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'SettingsGroup'."}]], 432, 406, 411, 402, [434, [{"file": "./src/test/profile-navigation.test.ts", "start": 75, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/profile-navigation.test.ts", "start": 106, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/profile-navigation.test.ts", "start": 6058, "length": 14, "code": 2739, "category": 1, "messageText": "Type '{ id: number; username: string; full_name: string; email: string; role: string; is_superuser: boolean; avatar_url: string; }' is missing the following properties from type '{ readonly id: string; readonly username: string; readonly email: string; readonly full_name?: string | undefined; readonly role: \"superadmin\" | \"admin\" | \"editor\" | \"viewer\"; readonly is_active: boolean; ... 5 more ...; readonly updated_at: string; }': is_active, created_at, updated_at"}, {"file": "./src/test/profile-navigation.test.ts", "start": 6098, "length": 15, "messageText": "Cannot assign to 'isAuthenticated' because it is a read-only property.", "category": 1, "code": 2540}]], [435, [{"file": "./src/test/profile.test.ts", "start": 73, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/profile.test.ts", "start": 104, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}]], [436, [{"file": "./src/test/settings.test.ts", "start": 73, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/settings.test.ts", "start": 104, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/settings.test.ts", "start": 843, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'jwt_algorithm' is missing in type '{ app_name: string; app_version: string; app_description: string; timezone: string; language: string; sync_interval_minutes: number; sync_batch_size: number; auto_sync_enabled: false; access_token_expire_minutes: number; ... 35 more ...; updated_by: string; }' but required in type 'SystemSettings'.", "relatedInformation": [{"file": "./src/types/settings.ts", "start": 303, "length": 13, "messageText": "'jwt_algorithm' is declared here.", "category": 3, "code": 2728}]}]], [437, [{"file": "./src/test/setup.ts", "start": 37, "length": 8, "messageText": "Cannot find module 'vitest' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/setup.ts", "start": 69, "length": 17, "messageText": "Cannot find module '@vue/test-utils' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/test/setup.ts", "start": 1929, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 1974, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 2130, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 2288, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 2538, "length": 5, "messageText": "Parameter 'query' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/test/setup.ts", "start": 3200, "length": 2, "messageText": "Parameter 'cb' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/test/setup.ts", "start": 3322, "length": 2, "messageText": "Parameter 'id' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/test/setup.ts", "start": 4648, "length": 9, "messageText": "Cannot find name 'after<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./src/test/setup.ts", "start": 4692, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node` and then add 'node' to the types field in your tsconfig.", "category": 1, "code": 2591}, {"file": "./src/test/setup.ts", "start": 4726, "length": 6, "messageText": "Parameter 'reason' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 99, 438, 439, 417, 440, 407, 408, 405, 105, 102, [404, [{"file": "./src/views/datasync.vue", "start": 7676, "length": 16, "code": 2322, "category": 1, "messageText": "Type 'AxiosResponse<any, any>' is not assignable to type '{ last_sync: null; is_syncing: boolean; android_db_connected: boolean; android_db_path: string; android_db_size: string; total_records: number; sync_config: { auto_sync_enabled: boolean; sync_interval_minutes: number; batch_size: number; }; } | { ...; }'."}, {"file": "./src/views/datasync.vue", "start": 7738, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'tables' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/datasync.vue", "start": 8314, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'message' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/views/datasync.vue", "start": 1624, "length": 20, "code": 2345, "category": 1, "messageText": "Argument of type 'null' is not assignable to parameter of type 'string'."}, {"file": "./src/views/datasync.vue", "start": 1865, "length": 7, "code": 2322, "category": 1, "messageText": "Type '\"success\" | \"active\"' is not assignable to type 'EpPropMergeType<StringConstructor, \"\" | \"success\" | \"warning\" | \"exception\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 229149, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'Partial<{ readonly width: number; readonly color: EpPropMergeType<(new (...args: any[]) => string | ProgressFn | ProgressColor[]) | (() => string | ProgressFn | ProgressColor[]) | ((new (...args: any[]) => string | ... 1 more ... | ProgressColor[]) | (() => string | ... 1 more ... | ProgressColor[]))[], unknown, unk...'", "category": 3, "code": 6500}]}]], 100, [403, [{"file": "./src/views/profile.vue", "start": 14878, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Store<\"user\", Pick<{ profile: Ref<{ id: number; username: string; full_name: string; email: string; phone?: string | undefined; department?: string | undefined; position?: string | undefined; bio?: string | undefined; ... 7 more ...; preferences?: { ...; } | undefined; } | null, UserProfile | ... 1 more ... | null>;...'."}, {"file": "./src/views/profile.vue", "start": 15064, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'token' does not exist on type 'Store<\"user\", Pick<{ profile: Ref<{ id: number; username: string; full_name: string; email: string; phone?: string | undefined; department?: string | undefined; position?: string | undefined; bio?: string | undefined; ... 7 more ...; preferences?: { ...; } | undefined; } | null, UserProfile | ... 1 more ... | null>;...'."}, {"file": "./src/views/profile.vue", "start": 16940, "length": 18, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'LoginHistoryItem[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'LoginHistoryItem' is not assignable to type 'never'.", "category": 1, "code": 2322}]}}, {"file": "./src/views/profile.vue", "start": 18459, "length": 18, "messageText": "Expected 1 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/api/profile.ts", "start": 2509, "length": 51, "messageText": "An argument for 'data' was not provided.", "category": 3, "code": 6210}]}]], [104, [{"file": "./src/views/userdetail.vue", "start": 4294, "length": 100, "code": 2322, "category": 1, "messageText": "Type '{ type: string; description: string; created_at: string; }' is not assignable to type 'never'."}, {"file": "./src/views/userdetail.vue", "start": 4400, "length": 97, "code": 2322, "category": 1, "messageText": "Type '{ type: string; description: string; created_at: string; }' is not assignable to type 'never'."}]], [103, [{"file": "./src/views/users.vue", "start": 5368, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: string; username: string; email: string; full_name: string; gender: string; is_active: boolean; is_premium: boolean; level: number; total_study_time: number; registration_date: string; }[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; username: string; email: string; full_name: string; gender: string; is_active: boolean; is_premium: boolean; level: number; total_study_time: number; registration_date: string; }' is not assignable to type 'never'.", "category": 1, "code": 2322}]}}]]], "affectedFilesPendingEmit": [410, 401, 409, 433, 412, 415, 101, 416, 419, 420, 421, 422, 423, 418, 432, 406, 411, 402, 434, 435, 436, 437, 99, 417, 440, 407, 408, 405, 105, 102, 404, 100, 403, 104, 103], "emitSignatures": [99, 100, 101, 102, 103, 104, 105, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 415, 416, 417, 418, 419, 420, 421, 422, 423, 433, 435, 436, 437]}, "version": "5.3.3"}