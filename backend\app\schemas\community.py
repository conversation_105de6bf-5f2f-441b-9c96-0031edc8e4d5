"""
社区模块数据模式
"""
from datetime import datetime
from typing import List, Optional, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class PagedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    items: List[T]
    total: int
    page: int
    size: int
    total_pages: int
    has_next: bool
    has_prev: bool


# ==================== 学习资源相关 ====================

class LearningResourceBase(BaseModel):
    title: str
    description: Optional[str] = None
    type: str
    url: str
    thumbnail_url: Optional[str] = None
    file_size: Optional[int] = None
    duration: Optional[int] = None
    subject: str
    grade_level: str
    tags: List[str] = []


class LearningResourceCreate(LearningResourceBase):
    author_id: str
    author_name: str


class LearningResourceUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    subject: Optional[str] = None
    grade_level: Optional[str] = None
    tags: Optional[List[str]] = None


class LearningResourceResponse(LearningResourceBase):
    id: str
    author_id: str
    author_name: str
    status: str
    download_count: int
    view_count: int
    like_count: int
    rating: float
    rating_count: int
    is_approved: bool
    created_at: datetime
    updated_at: datetime
    approved_at: Optional[datetime] = None
    approved_by: Optional[str] = None

    class Config:
        from_attributes = True


# ==================== 自习室相关 ====================

class StudyRoomBase(BaseModel):
    name: str
    description: Optional[str] = None
    max_members: int = 50
    is_public: bool = True
    password: Optional[str] = None
    tags: List[str] = []
    rules: Optional[str] = None


class StudyRoomCreate(StudyRoomBase):
    owner_id: str
    owner_name: str


class StudyRoomUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    max_members: Optional[int] = None
    is_public: Optional[bool] = None
    password: Optional[str] = None
    tags: Optional[List[str]] = None
    rules: Optional[str] = None


class StudyRoomResponse(StudyRoomBase):
    id: str
    owner_id: str
    owner_name: str
    current_members: int
    status: str
    total_study_time: int
    average_study_time: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class StudyRoomMemberResponse(BaseModel):
    id: str
    room_id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None
    joined_at: datetime
    total_study_time: int
    weekly_study_time: int
    is_online: bool
    last_active_at: datetime

    class Config:
        from_attributes = True


# ==================== 话题相关 ====================

class TopicBase(BaseModel):
    title: str
    content: str
    category_id: str
    category_name: str
    tags: List[str] = []


class TopicCreate(TopicBase):
    author_id: str
    author_name: str
    author_avatar: Optional[str] = None


class TopicUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    category_id: Optional[str] = None
    category_name: Optional[str] = None
    tags: Optional[List[str]] = None


class TopicResponse(TopicBase):
    id: str
    author_id: str
    author_name: str
    author_avatar: Optional[str] = None
    status: str
    view_count: int
    like_count: int
    reply_count: int
    last_reply_at: Optional[datetime] = None
    last_reply_by: Optional[str] = None
    is_pinned: bool
    is_hot: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TopicReplyResponse(BaseModel):
    id: str
    topic_id: str
    content: str
    author_id: str
    author_name: str
    author_avatar: Optional[str] = None
    like_count: int
    is_liked: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ==================== 部落相关 ====================

class TribeBase(BaseModel):
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    banner: Optional[str] = None
    category: str
    max_members: int = 100
    is_public: bool = True
    join_condition: Optional[str] = None
    tags: List[str] = []
    announcement: Optional[str] = None
    rules: Optional[str] = None


class TribeCreate(TribeBase):
    founder_id: str
    founder_name: str


class TribeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    avatar: Optional[str] = None
    banner: Optional[str] = None
    category: Optional[str] = None
    max_members: Optional[int] = None
    is_public: Optional[bool] = None
    join_condition: Optional[str] = None
    tags: Optional[List[str]] = None
    announcement: Optional[str] = None
    rules: Optional[str] = None


class TribeResponse(TribeBase):
    id: str
    founder_id: str
    founder_name: str
    level: int
    experience: int
    member_count: int
    total_points: int
    weekly_points: int
    rank: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TribeMemberResponse(BaseModel):
    id: str
    tribe_id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None
    role: str
    contribution: int
    weekly_contribution: int
    join_time: datetime
    last_active_time: datetime
    title: Optional[str] = None

    class Config:
        from_attributes = True


# ==================== 师徒结对相关 ====================

class MentorProfileBase(BaseModel):
    subjects: List[str] = []
    experience: Optional[str] = None
    achievements: List[str] = []
    max_students: int = 10
    is_available: bool = True
    introduction: Optional[str] = None
    teaching_style: Optional[str] = None


class MentorProfileCreate(MentorProfileBase):
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None


class MentorProfileUpdate(BaseModel):
    subjects: Optional[List[str]] = None
    experience: Optional[str] = None
    achievements: Optional[List[str]] = None
    max_students: Optional[int] = None
    is_available: Optional[bool] = None
    introduction: Optional[str] = None
    teaching_style: Optional[str] = None


class MentorProfileResponse(MentorProfileBase):
    id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None
    rating: float
    rating_count: int
    student_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MentorshipRelationBase(BaseModel):
    subject: str
    goals: Optional[str] = None


class MentorshipRelationCreate(MentorshipRelationBase):
    mentor_id: str
    mentor_name: str
    student_id: str
    student_name: str


class MentorshipRelationUpdate(BaseModel):
    goals: Optional[str] = None
    status: Optional[str] = None
    progress: Optional[int] = None
    notes: Optional[str] = None
    rating: Optional[float] = None
    feedback: Optional[str] = None


class MentorshipRelationResponse(MentorshipRelationBase):
    id: str
    mentor_id: str
    mentor_name: str
    student_id: str
    student_name: str
    status: str
    progress: int
    start_date: datetime
    end_date: Optional[datetime] = None
    notes: Optional[str] = None
    rating: Optional[float] = None
    feedback: Optional[str] = None

    class Config:
        from_attributes = True


# ==================== 学霸经验相关 ====================

class ExperienceBase(BaseModel):
    title: str
    content: str
    subject: str
    tags: List[str] = []
    images: List[str] = []
    video_url: Optional[str] = None


class ExperienceCreate(ExperienceBase):
    author_id: str
    author_name: str
    author_avatar: Optional[str] = None
    author_level: Optional[str] = None
    author_achievements: List[str] = []


class ExperienceUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    subject: Optional[str] = None
    tags: Optional[List[str]] = None
    images: Optional[List[str]] = None
    video_url: Optional[str] = None
    is_recommended: Optional[bool] = None


class ExperienceResponse(ExperienceBase):
    id: str
    author_id: str
    author_name: str
    author_avatar: Optional[str] = None
    author_level: Optional[str] = None
    author_achievements: List[str] = []
    view_count: int
    like_count: int
    comment_count: int
    share_count: int
    is_recommended: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ==================== 活动竞赛相关 ====================

class ActivityBase(BaseModel):
    title: str
    description: str
    type: str
    subject: Optional[str] = None
    start_date: datetime
    end_date: datetime
    registration_deadline: datetime
    max_participants: Optional[int] = None
    rules: Optional[str] = None
    prizes: List[str] = []
    is_public: bool = True
    tags: List[str] = []


class ActivityCreate(ActivityBase):
    organizer_id: str
    organizer_name: str


class ActivityUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    subject: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    registration_deadline: Optional[datetime] = None
    max_participants: Optional[int] = None
    rules: Optional[str] = None
    prizes: Optional[List[str]] = None
    is_public: Optional[bool] = None
    tags: Optional[List[str]] = None
    status: Optional[str] = None


class ActivityResponse(ActivityBase):
    id: str
    organizer_id: str
    organizer_name: str
    current_participants: int
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ActivityParticipantResponse(BaseModel):
    id: str
    activity_id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None
    registered_at: datetime
    score: Optional[float] = None
    rank: Optional[int] = None
    status: str

    class Config:
        from_attributes = True


# ==================== 心愿相关 ====================

class WishBase(BaseModel):
    content: str
    tags: List[str] = []


class WishCreate(WishBase):
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None


class WishUpdate(BaseModel):
    content: Optional[str] = None
    tags: Optional[List[str]] = None
    status: Optional[str] = None
    is_achieved: Optional[bool] = None


class WishResponse(WishBase):
    id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None
    status: str
    is_achieved: bool
    support_count: int
    comment_count: int
    view_count: int
    hot_score: float
    created_at: datetime
    updated_at: datetime
    achieved_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class WishCommentResponse(BaseModel):
    id: str
    wish_id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None
    content: str
    like_count: int
    is_liked: bool
    created_at: datetime

    class Config:
        from_attributes = True


# ==================== 游戏相关 ====================

class GameActivityBase(BaseModel):
    name: str
    description: Optional[str] = None
    type: str
    rules: Optional[str] = None
    rewards: List[str] = []
    start_date: datetime
    end_date: Optional[datetime] = None


class GameActivityCreate(GameActivityBase):
    pass


class GameActivityUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    rules: Optional[str] = None
    rewards: Optional[List[str]] = None
    is_active: Optional[bool] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class GameActivityResponse(GameActivityBase):
    id: str
    participant_count: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class GameParticipantResponse(BaseModel):
    id: str
    game_id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str] = None
    score: float
    rank: Optional[int] = None
    achievements: List[str] = []
    participated_at: datetime
    last_played_at: datetime

    class Config:
        from_attributes = True


# ==================== 统计数据 ====================

class CommunityStatistics(BaseModel):
    total_resources: int
    total_study_rooms: int
    total_topics: int
    total_tribes: int
    total_mentorships: int
    total_experiences: int
    total_activities: int
    total_wishes: int
    total_games: int
    active_users: int
    daily_active_users: int
    weekly_active_users: int
    monthly_active_users: int


class ModuleStatistics(BaseModel):
    resource_stats: dict
    study_room_stats: dict
    topic_stats: dict
    tribe_stats: dict
    mentorship_stats: dict
    experience_stats: dict
    activity_stats: dict
    wish_stats: dict
    game_stats: dict
