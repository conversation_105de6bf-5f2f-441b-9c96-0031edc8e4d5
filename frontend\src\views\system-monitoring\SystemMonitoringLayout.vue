<template>
  <div class="system-monitoring-layout">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>系统监控中心</h1>
          <p>实时监控系统状态，确保服务稳定运行</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="() => refreshAllData()" :loading="refreshing">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button @click="exportSystemReport" :loading="exporting">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
          <el-dropdown @command="handleQuickAction">
            <el-button>
              快速操作
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="health-check">
                  <el-icon><Monitor /></el-icon>
                  系统健康检查
                </el-dropdown-item>
                <el-dropdown-item command="clear-cache">
                  <el-icon><Delete /></el-icon>
                  清理系统缓存
                </el-dropdown-item>
                <el-dropdown-item command="backup-logs">
                  <el-icon><FolderAdd /></el-icon>
                  备份系统日志
                </el-dropdown-item>
                <el-dropdown-item command="restart-services" divided>
                  <el-icon><RefreshRight /></el-icon>
                  重启系统服务
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 系统性能概览 -->
    <div class="system-overview">
      <PerformanceOverview
        :show-title="true"
        :show-trends="true"
        :show-refresh="true"
        :show-last-update="true"
        :clickable="true"
        :refresh-interval="60"
        :max-metrics="4"
      />
    </div>

    <!-- 子页面内容区域 -->
    <div class="content-area">
      <router-view v-slot="{ Component }">
        <transition name="fade-slide" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Download, ArrowDown, Monitor, Delete, FolderAdd, RefreshRight,
  DataBoard, Document, TrendCharts, CircleCheck, Warning, CircleClose
} from '@element-plus/icons-vue'
import PerformanceOverview from '@/components/PerformanceOverview.vue'



// 响应式数据
const refreshing = ref(false)
const exporting = ref(false)
const lastCheckTime = ref('')





// 事件处理方法
const refreshAllData = async (showMessage = true) => {
  refreshing.value = true
  try {
    // 模拟刷新所有监控数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    lastCheckTime.value = new Date().toLocaleString()
    if (showMessage) {
      ElMessage.success('系统数据刷新成功')
    }
  } catch (error) {
    if (showMessage) {
      ElMessage.error('数据刷新失败')
    }
  } finally {
    refreshing.value = false
  }
}

const exportSystemReport = async () => {
  exporting.value = true
  try {
    // 模拟导出系统报告
    await new Promise(resolve => setTimeout(resolve, 1500))
    ElMessage.success('系统报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  } finally {
    exporting.value = false
  }
}

const handleQuickAction = async (command: string) => {
  switch (command) {
    case 'health-check':
      ElMessage.info('正在执行系统健康检查...')
      // TODO: 实现健康检查逻辑
      break
    case 'clear-cache':
      try {
        await ElMessageBox.confirm('确定要清理系统缓存吗？', '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        ElMessage.success('系统缓存清理成功')
      } catch {
        // 用户取消
      }
      break
    case 'backup-logs':
      ElMessage.info('正在备份系统日志...')
      // TODO: 实现日志备份逻辑
      break
    case 'restart-services':
      try {
        await ElMessageBox.confirm('重启系统服务可能会影响正在进行的操作，确定要继续吗？', '警告', {
          confirmButtonText: '确定重启',
          cancelButtonText: '取消',
          type: 'warning'
        })
        ElMessage.warning('系统服务重启请求已提交')
      } catch {
        // 用户取消
      }
      break
  }
}







// 定时刷新数据
let refreshTimer: number | null = null

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    refreshAllData(false) // 自动刷新不显示提示消息
  }, 60000) // 每分钟刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  refreshAllData()
  startAutoRefresh()
  lastCheckTime.value = new Date().toLocaleString()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.system-monitoring-layout {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
          }
        }
      }
    }
  }

  /* 系统概览指标 */
  .system-overview {
    margin-bottom: var(--spacing-6);

    .metric-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: all 0.3s ease;
      cursor: pointer;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.excellent {
        border-left: 4px solid var(--success-color);
      }

      &.normal {
        border-left: 4px solid var(--primary-color);
      }

      &.warning {
        border-left: 4px solid var(--warning-color);
      }

      &.error {
        border-left: 4px solid var(--error-color);
      }

      .metric-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-5);

        .metric-icon {
          width: 50px;
          height: 50px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);
        }

        .metric-info {
          flex: 1;

          .metric-value {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .metric-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .metric-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.excellent {
              color: var(--success-color);
            }

            &.normal {
              color: var(--primary-color);
            }

            &.warning {
              color: var(--warning-color);
            }

            &.error {
              color: var(--error-color);
            }
          }
        }
      }

      .metric-progress {
        padding: 0 var(--spacing-5) var(--spacing-3);

        .el-progress {
          .el-progress-bar__outer {
            background-color: var(--bg-light);
          }
        }
      }
    }
  }

  /* 内容区域 */
  .content-area {
    min-height: 400px;
    margin-top: var(--spacing-6);
  }

  /* 页面切换动画 */
  .fade-slide-enter-active,
  .fade-slide-leave-active {
    transition: all 0.3s ease;
  }

  .fade-slide-enter-from {
    opacity: 0;
    transform: translateX(20px);
  }

  .fade-slide-leave-to {
    opacity: 0;
    transform: translateX(-20px);
  }

  /* 系统状态指示器 */
  .system-status-indicator {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    z-index: 1000;
    backdrop-filter: blur(10px);

    &.excellent {
      background: var(--success-color);
      color: white;

      .status-dot {
        background: white;
      }
    }

    &.normal {
      background: var(--primary-color);
      color: white;

      .status-dot {
        background: white;
      }
    }

    &.warning {
      background: var(--warning-color);
      color: white;

      .status-dot {
        background: white;
      }
    }

    &.error {
      background: var(--error-color);
      color: white;

      .status-dot {
        background: white;
      }
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    .last-check {
      font-size: var(--font-size-xs);
      opacity: 0.8;
      margin-left: var(--spacing-2);
    }
  }
}

/* 页面过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 动画 */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .system-monitoring-layout {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .system-overview {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }
  }
}

@include respond-to('md') {
  .system-monitoring-layout {
    padding: var(--spacing-4);

    .system-status-indicator {
      bottom: var(--spacing-4);
      right: var(--spacing-4);
      font-size: var(--font-size-xs);
      padding: var(--spacing-2) var(--spacing-3);

      .last-check {
        display: none;
      }
    }
  }
}

@include respond-to('sm') {
  .system-monitoring-layout {
    .content-area {
      margin-top: var(--spacing-4);
    }
  }
}
</style>
