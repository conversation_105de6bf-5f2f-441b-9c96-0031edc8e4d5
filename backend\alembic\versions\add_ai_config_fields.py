"""Add AI configuration fields to settings table

Revision ID: add_ai_config_fields
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_ai_config_fields'
down_revision = None  # 这里应该是上一个迁移的ID
branch_labels = None
depends_on = None


def upgrade():
    """添加AI配置字段"""
    # 添加AI配置相关字段到settings表
    op.add_column('settings', sa.Column('ai_enabled', sa.<PERSON>(), nullable=True, default=False))
    op.add_column('settings', sa.Column('ai_configs', sa.JSON(), nullable=True))
    op.add_column('settings', sa.Column('default_ai_model', sa.String(100), nullable=True))
    
    # 设置默认值
    op.execute("UPDATE settings SET ai_enabled = FALSE WHERE ai_enabled IS NULL")
    op.execute("UPDATE settings SET ai_configs = '[]' WHERE ai_configs IS NULL")
    
    # 修改列为NOT NULL（如果需要）
    op.alter_column('settings', 'ai_enabled', nullable=False, default=False)


def downgrade():
    """移除AI配置字段"""
    op.drop_column('settings', 'default_ai_model')
    op.drop_column('settings', 'ai_configs')
    op.drop_column('settings', 'ai_enabled')
