// Element Plus 主题定制
// Custom Theme for Element Plus Components

// ==================== Element Plus 变量覆盖 ====================
:root {
  // 主色调
  --el-color-primary: var(--primary-color);
  --el-color-primary-light-3: var(--primary-light);
  --el-color-primary-light-5: var(--primary-lighter);
  --el-color-primary-light-7: #a5b4fc;
  --el-color-primary-light-8: #c7d2fe;
  --el-color-primary-light-9: #e0e7ff;
  --el-color-primary-dark-2: var(--primary-dark);

  // 成功色
  --el-color-success: var(--success-color);
  --el-color-success-light-3: var(--success-light);
  --el-color-success-light-5: #6ee7b7;
  --el-color-success-light-7: #a7f3d0;
  --el-color-success-light-8: #d1fae5;
  --el-color-success-light-9: #ecfdf5;

  // 警告色
  --el-color-warning: var(--warning-color);
  --el-color-warning-light-3: var(--warning-light);
  --el-color-warning-light-5: #fcd34d;
  --el-color-warning-light-7: #fde68a;
  --el-color-warning-light-8: #fef3c7;
  --el-color-warning-light-9: #fffbeb;

  // 危险色
  --el-color-danger: var(--error-color);
  --el-color-danger-light-3: var(--error-light);
  --el-color-danger-light-5: #fca5a5;
  --el-color-danger-light-7: #fecaca;
  --el-color-danger-light-8: #fee2e2;
  --el-color-danger-light-9: #fef2f2;

  // 信息色
  --el-color-info: var(--info-color);
  --el-color-info-light-3: var(--info-light);
  --el-color-info-light-5: #93c5fd;
  --el-color-info-light-7: #bfdbfe;
  --el-color-info-light-8: #dbeafe;
  --el-color-info-light-9: #eff6ff;

  // 文本色
  --el-text-color-primary: var(--text-primary);
  --el-text-color-regular: var(--text-secondary);
  --el-text-color-secondary: var(--text-tertiary);
  --el-text-color-placeholder: var(--text-quaternary);

  // 边框色
  --el-border-color: var(--border-light);
  --el-border-color-light: var(--border-light);
  --el-border-color-lighter: #f0f2f5;
  --el-border-color-extra-light: #fafafa;
  --el-border-color-dark: var(--border-medium);
  --el-border-color-darker: var(--border-dark);

  // 背景色
  --el-bg-color: var(--bg-primary);
  --el-bg-color-page: var(--bg-secondary);
  --el-bg-color-overlay: var(--bg-primary);

  // 填充色
  --el-fill-color: var(--gray-100);
  --el-fill-color-light: var(--gray-50);
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fafcff;
  --el-fill-color-dark: var(--gray-200);
  --el-fill-color-darker: var(--gray-300);
  --el-fill-color-blank: transparent;

  // 字体
  --el-font-family: var(--font-family-base);
  --el-font-size-extra-large: var(--font-size-xl);
  --el-font-size-large: var(--font-size-lg);
  --el-font-size-medium: var(--font-size-base);
  --el-font-size-base: var(--font-size-sm);
  --el-font-size-small: var(--font-size-xs);
  --el-font-size-extra-small: 0.6875rem;

  // 圆角
  --el-border-radius-base: var(--radius-md);
  --el-border-radius-small: var(--radius-sm);
  --el-border-radius-round: var(--radius-full);
  --el-border-radius-circle: 50%;

  // 阴影
  --el-box-shadow: var(--shadow-md);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-base: var(--shadow-md);
  --el-box-shadow-dark: var(--shadow-lg);

  // 过渡
  --el-transition-duration: var(--transition-normal);
  --el-transition-duration-fast: var(--transition-fast);
}

// ==================== 组件样式定制 ====================

// 卡片组件
.el-card {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);

  &:hover {
    box-shadow: var(--shadow-md);
  }

  .el-card__header {
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-5) var(--spacing-6);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
  }

  .el-card__body {
    padding: var(--spacing-6);
  }
}

// 按钮组件
.el-button {
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
  border: none;

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    transform: translateY(0);
  }

  &.el-button--primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    
    &:hover {
      background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-lighter) 100%);
    }
  }

  &.el-button--success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
  }

  &.el-button--warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);
  }

  &.el-button--danger {
    background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);
  }
}

// 输入框组件
.el-input {
  .el-input__wrapper {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);

    &:hover {
      box-shadow: var(--shadow-md);
    }

    &.is-focus {
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
  }
}

// 选择器组件
.el-select {
  .el-select__wrapper {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);

    &:hover {
      box-shadow: var(--shadow-md);
    }

    &.is-focus {
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
  }
}

// 表格组件
.el-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);

  .el-table__header-wrapper {
    th {
      background-color: var(--bg-tertiary);
      font-weight: var(--font-weight-semibold);
      color: var(--text-secondary);
      border-bottom: 2px solid var(--border-light);
    }
  }

  .el-table__body-wrapper {
    tr:hover > td {
      background-color: var(--bg-accent) !important;
    }
  }

  .el-table__cell {
    border-bottom: 1px solid var(--border-light);
  }
}

// 分页组件
.el-pagination {
  .el-pager li {
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-1);
    transition: var(--transition-fast);

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }

    &.is-active {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      color: white;
    }
  }

  .btn-prev,
  .btn-next {
    border-radius: var(--radius-md);
    transition: var(--transition-fast);

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
  }
}

// 标签组件
.el-tag {
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  border: none;
  
  &.el-tag--primary {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
  }

  &.el-tag--success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
  }

  &.el-tag--warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
  }

  &.el-tag--danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
  }

  &.el-tag--info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
  }
}

// 对话框组件
.el-dialog {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);

  .el-dialog__header {
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    padding: var(--spacing-6);

    .el-dialog__title {
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
    }
  }

  .el-dialog__body {
    padding: var(--spacing-6);
  }

  .el-dialog__footer {
    border-top: 1px solid var(--border-light);
    padding: var(--spacing-5) var(--spacing-6);
    background-color: var(--bg-secondary);
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  }
}

// 进度条组件
.el-progress {
  .el-progress-bar__outer {
    border-radius: var(--radius-full);
    background-color: var(--gray-200);
  }

  .el-progress-bar__inner {
    border-radius: var(--radius-full);
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
  }
}

// 开关组件
.el-switch {
  .el-switch__core {
    border-radius: var(--radius-full);
    transition: var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
    }
  }
}

// 统计数字组件
.el-statistic {
  .el-statistic__content {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
  }

  .el-statistic__title {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
}
