"""
统一异常处理工具
"""
from typing import Any, Dict, Optional, List
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import logging
import traceback
from datetime import datetime


logger = logging.getLogger(__name__)


class BaseAPIException(HTTPException):
    """基础API异常类"""
    
    def __init__(
        self,
        status_code: int,
        message: str,
        error_code: str = None,
        details: Any = None,
        headers: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code or f"ERR_{status_code}"
        self.details = details
        super().__init__(status_code=status_code, detail=message, headers=headers)


class ValidationError(BaseAPIException):
    """参数验证错误"""
    
    def __init__(self, message: str = "参数验证失败", details: List[Dict] = None):
        super().__init__(
            status_code=422,
            message=message,
            error_code="VALIDATION_ERROR",
            details=details
        )


class AuthenticationError(BaseAPIException):
    """认证错误"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            status_code=401,
            message=message,
            error_code="AUTHENTICATION_ERROR"
        )


class AuthorizationError(BaseAPIException):
    """授权错误"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(
            status_code=403,
            message=message,
            error_code="AUTHORIZATION_ERROR"
        )


class NotFoundError(BaseAPIException):
    """资源不存在错误"""
    
    def __init__(self, message: str = "资源不存在", resource: str = None):
        details = {"resource": resource} if resource else None
        super().__init__(
            status_code=404,
            message=message,
            error_code="NOT_FOUND_ERROR",
            details=details
        )


class ConflictError(BaseAPIException):
    """冲突错误"""
    
    def __init__(self, message: str = "资源冲突", resource: str = None):
        details = {"resource": resource} if resource else None
        super().__init__(
            status_code=409,
            message=message,
            error_code="CONFLICT_ERROR",
            details=details
        )


class RateLimitError(BaseAPIException):
    """频率限制错误"""
    
    def __init__(self, message: str = "请求过于频繁", retry_after: int = None):
        headers = {"Retry-After": str(retry_after)} if retry_after else None
        super().__init__(
            status_code=429,
            message=message,
            error_code="RATE_LIMIT_ERROR",
            headers=headers
        )


class InternalServerError(BaseAPIException):
    """内部服务器错误"""
    
    def __init__(self, message: str = "内部服务器错误", error_id: str = None):
        details = {"error_id": error_id} if error_id else None
        super().__init__(
            status_code=500,
            message=message,
            error_code="INTERNAL_SERVER_ERROR",
            details=details
        )


def create_error_response(
    status_code: int,
    message: str,
    error_code: str = None,
    details: Any = None,
    request_id: str = None
) -> Dict[str, Any]:
    """创建标准错误响应"""
    return {
        "success": False,
        "error": {
            "code": error_code or f"ERR_{status_code}",
            "message": message,
            "details": details,
            "timestamp": datetime.utcnow().isoformat(),
            "request_id": request_id
        }
    }


async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """全局异常处理器"""
    request_id = getattr(request.state, 'request_id', None)
    
    # 记录异常信息
    logger.error(
        f"Unhandled exception: {type(exc).__name__}: {str(exc)}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "traceback": traceback.format_exc()
        }
    )
    
    if isinstance(exc, BaseAPIException):
        return JSONResponse(
            status_code=exc.status_code,
            content=create_error_response(
                status_code=exc.status_code,
                message=exc.message,
                error_code=exc.error_code,
                details=exc.details,
                request_id=request_id
            )
        )
    
    elif isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content=create_error_response(
                status_code=exc.status_code,
                message=exc.detail,
                request_id=request_id
            )
        )
    
    else:
        # 未知异常，返回通用错误
        return JSONResponse(
            status_code=500,
            content=create_error_response(
                status_code=500,
                message="内部服务器错误",
                error_code="INTERNAL_SERVER_ERROR",
                request_id=request_id
            )
        )


def handle_validation_error(errors: List[Dict]) -> ValidationError:
    """处理参数验证错误"""
    error_messages = []
    for error in errors:
        field = ".".join(str(loc) for loc in error.get("loc", []))
        message = error.get("msg", "验证失败")
        error_messages.append(f"{field}: {message}")
    
    return ValidationError(
        message="参数验证失败",
        details={
            "errors": errors,
            "summary": "; ".join(error_messages)
        }
    )


class ErrorLogger:
    """错误日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger("error_tracker")
    
    def log_error(
        self,
        error: Exception,
        request: Request = None,
        user_id: str = None,
        additional_context: Dict = None
    ):
        """记录错误信息"""
        context = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "traceback": traceback.format_exc()
        }
        
        if request:
            context.update({
                "method": request.method,
                "url": str(request.url),
                "headers": dict(request.headers),
                "client_ip": getattr(request.client, 'host', None)
            })
        
        if additional_context:
            context.update(additional_context)
        
        self.logger.error("Application error occurred", extra=context)


# 全局错误记录器实例
error_logger = ErrorLogger()
