<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .route-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .route-card {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            background: #fafafa;
        }
        .route-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .route-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.error {
            background: #dc3545;
        }
        .status {
            font-size: 12px;
            margin-top: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .breadcrumb {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="breadcrumb">
        <strong>WisCude 路由功能测试</strong> - 验证所有页面路由和导航功能
    </div>

    <div class="test-container">
        <h2>🧭 路由功能测试</h2>
        <p>点击下方按钮测试各个页面的路由功能。测试将在新标签页中打开，请检查页面是否正常加载。</p>
        
        <div class="route-grid">
            <!-- 登录页面 -->
            <div class="route-card">
                <h4>🔐 登录页面</h4>
                <div class="route-info">路径: /login | 权限: 无需认证</div>
                <button class="test-button" onclick="testRoute('/login', '登录页面')">测试访问</button>
                <div id="login-status" class="status"></div>
            </div>

            <!-- 仪表板 -->
            <div class="route-card">
                <h4>📊 仪表板</h4>
                <div class="route-info">路径: /dashboard | 权限: 需要认证</div>
                <button class="test-button" onclick="testRoute('/dashboard', '仪表板')">测试访问</button>
                <button class="test-button" onclick="testRoute('/', '根路径重定向')">测试重定向</button>
                <div id="dashboard-status" class="status"></div>
            </div>

            <!-- 用户管理 -->
            <div class="route-card">
                <h4>👥 用户管理</h4>
                <div class="route-info">路径: /users | 权限: 需要认证</div>
                <button class="test-button" onclick="testRoute('/users', '用户管理')">测试访问</button>
                <button class="test-button" onclick="testRoute('/users/user_1', '用户详情')">测试详情页</button>
                <div id="users-status" class="status"></div>
            </div>

            <!-- 数据同步 -->
            <div class="route-card">
                <h4>🔄 数据同步</h4>
                <div class="route-info">路径: /sync | 权限: 需要认证</div>
                <button class="test-button" onclick="testRoute('/sync', '数据同步')">测试访问</button>
                <div id="sync-status" class="status"></div>
            </div>

            <!-- 数据分析 -->
            <div class="route-card">
                <h4>📈 数据分析</h4>
                <div class="route-info">路径: /analytics | 权限: 需要认证</div>
                <button class="test-button" onclick="testRoute('/analytics', '数据分析')">测试访问</button>
                <div id="analytics-status" class="status"></div>
            </div>

            <!-- 系统设置 -->
            <div class="route-card">
                <h4>⚙️ 系统设置</h4>
                <div class="route-info">路径: /settings | 权限: 需要超级管理员</div>
                <button class="test-button" onclick="testRoute('/settings', '系统设置')">测试访问</button>
                <div id="settings-status" class="status"></div>
            </div>

            <!-- 404页面 -->
            <div class="route-card">
                <h4>❌ 404页面</h4>
                <div class="route-info">路径: /404 | 权限: 无需认证</div>
                <button class="test-button" onclick="testRoute('/404', '404页面')">测试访问</button>
                <button class="test-button" onclick="testRoute('/nonexistent', '不存在页面')">测试重定向</button>
                <div id="404-status" class="status"></div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h3>🔍 路由测试说明</h3>
        <ul>
            <li><strong>绿色按钮</strong>：页面加载成功</li>
            <li><strong>红色按钮</strong>：页面加载失败或权限不足</li>
            <li><strong>蓝色按钮</strong>：未测试</li>
        </ul>
        
        <h4>测试步骤：</h4>
        <ol>
            <li>首先测试登录页面，确保可以正常访问</li>
            <li>使用 admin/admin123 登录系统</li>
            <li>依次测试其他页面的访问权限</li>
            <li>测试路由重定向功能</li>
            <li>验证超级管理员权限页面</li>
        </ol>
    </div>

    <script>
        function testRoute(path, name) {
            const fullUrl = `http://localhost:5173${path}`;
            const statusId = getStatusId(path);
            const button = event.target;
            
            // 更新按钮状态
            button.textContent = '测试中...';
            button.disabled = true;
            
            // 在新标签页中打开
            const newWindow = window.open(fullUrl, '_blank');
            
            // 模拟测试结果（实际应该检查页面加载状态）
            setTimeout(() => {
                if (newWindow && !newWindow.closed) {
                    updateStatus(statusId, 'success', `${name} 页面已在新标签页中打开`);
                    button.className = 'test-button success';
                    button.textContent = '✓ 已测试';
                } else {
                    updateStatus(statusId, 'error', `${name} 页面打开失败`);
                    button.className = 'test-button error';
                    button.textContent = '✗ 失败';
                }
                button.disabled = false;
            }, 1000);
        }
        
        function getStatusId(path) {
            const pathMap = {
                '/login': 'login-status',
                '/dashboard': 'dashboard-status',
                '/': 'dashboard-status',
                '/users': 'users-status',
                '/users/user_1': 'users-status',
                '/sync': 'sync-status',
                '/analytics': 'analytics-status',
                '/settings': 'settings-status',
                '/404': '404-status',
                '/nonexistent': '404-status'
            };
            return pathMap[path] || '404-status';
        }
        
        function updateStatus(statusId, type, message) {
            const statusElement = document.getElementById(statusId);
            if (statusElement) {
                statusElement.className = `status ${type}`;
                statusElement.textContent = message;
            }
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('路由测试页面已加载完成');
        });
    </script>
</body>
</html>
