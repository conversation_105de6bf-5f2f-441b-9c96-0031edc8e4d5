"""
广告管理模块Pydantic模型
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

# ==================== 轮播图相关 ====================

class BannerBase(BaseModel):
    title: str
    description: Optional[str] = None
    image_url: str
    link_url: Optional[str] = None
    link_type: str = "external"
    position: str = "home"
    sort_order: int = 0
    status: str = "draft"
    publish_start_time: Optional[datetime] = None
    publish_end_time: Optional[datetime] = None
    target_users: Optional[Dict[str, Any]] = None
    target_regions: Optional[List[str]] = None
    target_devices: Optional[List[str]] = None

class BannerCreate(BannerBase):
    creator_id: str
    creator_name: str

class BannerUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    link_url: Optional[str] = None
    link_type: Optional[str] = None
    position: Optional[str] = None
    sort_order: Optional[int] = None
    status: Optional[str] = None
    publish_start_time: Optional[datetime] = None
    publish_end_time: Optional[datetime] = None
    target_users: Optional[Dict[str, Any]] = None
    target_regions: Optional[List[str]] = None
    target_devices: Optional[List[str]] = None

class BannerResponse(BannerBase):
    id: str
    view_count: int
    click_count: int
    click_rate: float
    creator_id: str
    creator_name: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# ==================== 弹窗相关 ====================

class PopupBase(BaseModel):
    title: str
    content: str
    content_type: str = "text"
    image_url: Optional[str] = None
    video_url: Optional[str] = None
    action_type: str = "none"
    action_url: Optional[str] = None
    action_text: Optional[str] = None
    popup_type: str = "modal"
    size: str = "medium"
    position: str = "center"
    trigger_type: str = "immediate"
    trigger_value: Optional[str] = None
    display_frequency: str = "always"
    target_user_types: Optional[List[str]] = None
    target_user_levels: Optional[List[int]] = None
    target_new_users: bool = False
    target_active_users: bool = False
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    daily_start_hour: Optional[int] = None
    daily_end_hour: Optional[int] = None
    status: str = "draft"
    priority: int = 0
    max_displays_per_user: int = 0

class PopupCreate(PopupBase):
    creator_id: str
    creator_name: str

class PopupUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    content_type: Optional[str] = None
    image_url: Optional[str] = None
    video_url: Optional[str] = None
    action_type: Optional[str] = None
    action_url: Optional[str] = None
    action_text: Optional[str] = None
    popup_type: Optional[str] = None
    size: Optional[str] = None
    position: Optional[str] = None
    trigger_type: Optional[str] = None
    trigger_value: Optional[str] = None
    display_frequency: Optional[str] = None
    target_user_types: Optional[List[str]] = None
    target_user_levels: Optional[List[int]] = None
    target_new_users: Optional[bool] = None
    target_active_users: Optional[bool] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    daily_start_hour: Optional[int] = None
    daily_end_hour: Optional[int] = None
    status: Optional[str] = None
    priority: Optional[int] = None
    max_displays_per_user: Optional[int] = None

class PopupResponse(PopupBase):
    id: str
    total_displays: int
    total_clicks: int
    total_closes: int
    click_rate: float
    close_rate: float
    creator_id: str
    creator_name: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# ==================== 统计相关 ====================

class AdvertisingStatisticsCreate(BaseModel):
    ad_id: str
    ad_type: str
    user_id: Optional[str] = None
    action_type: str
    device_type: Optional[str] = None
    os_version: Optional[str] = None
    app_version: Optional[str] = None
    screen_resolution: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    user_type: Optional[str] = None
    user_level: Optional[int] = None
    registration_days: Optional[int] = None

class AdvertisingStatisticsResponse(BaseModel):
    id: str
    ad_id: str
    ad_type: str
    user_id: Optional[str]
    action_type: str
    action_time: datetime
    device_type: Optional[str]
    os_version: Optional[str]
    app_version: Optional[str]
    screen_resolution: Optional[str]
    region: Optional[str]
    city: Optional[str]
    user_type: Optional[str]
    user_level: Optional[int]
    registration_days: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True

# ==================== 汇总统计 ====================

class AdvertisingOverviewStats(BaseModel):
    total_banners: int
    active_banners: int
    total_popups: int
    active_popups: int
    total_views: int
    total_clicks: int
    average_click_rate: float
    top_performing_ads: List[Dict[str, Any]]

class BannerStats(BaseModel):
    total_banners: int
    active_banners: int
    draft_banners: int
    expired_banners: int
    total_views: int
    total_clicks: int
    average_click_rate: float
    top_positions: List[Dict[str, Any]]

class PopupStats(BaseModel):
    total_popups: int
    active_popups: int
    draft_popups: int
    paused_popups: int
    total_displays: int
    total_clicks: int
    total_closes: int
    average_click_rate: float
    average_close_rate: float

# ==================== 分页响应 ====================

class PagedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    total_pages: int
    has_next: bool
    has_prev: bool
