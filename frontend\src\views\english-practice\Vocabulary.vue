<template>
  <div class="vocabulary-management">
    <div class="page-header">
      <h1>词汇库管理</h1>
      <p>管理英语词汇、释义、发音和例句，支持多种学习模式</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="词汇列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建词汇
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedWords.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedWords.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
            <el-button @click="showImportDialog = true">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
            <el-button @click="exportVocabulary">
              <el-icon><Download /></el-icon>
              导出词汇
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索单词或释义"
              style="width: 200px"
              clearable
              @change="loadVocabulary"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="difficultyFilter" placeholder="难度筛选" style="width: 120px" @change="loadVocabulary">
              <el-option label="全部难度" value="" />
              <el-option label="简单" :value="1" />
              <el-option label="较易" :value="2" />
              <el-option label="中等" :value="3" />
              <el-option label="较难" :value="4" />
              <el-option label="困难" :value="5" />
            </el-select>
            <el-select v-model="typeFilter" placeholder="词性筛选" style="width: 120px" @change="loadVocabulary">
              <el-option label="全部词性" value="" />
              <el-option label="名词" value="noun" />
              <el-option label="动词" value="verb" />
              <el-option label="形容词" value="adjective" />
              <el-option label="副词" value="adverb" />
              <el-option label="介词" value="preposition" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadVocabulary">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>

        <!-- 词汇列表 -->
        <el-table
          :data="vocabulary"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="词汇信息" min-width="300">
            <template #default="{ row }">
              <div class="word-info">
                <div class="word-header">
                  <span class="word-text">{{ row.word }}</span>
                  <el-button
                    text
                    size="small"
                    @click="playPronunciation(row)"
                    v-if="row.pronunciation_url"
                  >
                    <el-icon><VideoPlay /></el-icon>
                  </el-button>
                </div>
                <div class="word-pronunciation" v-if="row.pronunciation">
                  [{{ row.pronunciation }}]
                </div>
                <div class="word-translation">{{ row.translation }}</div>
                <div class="word-meta">
                  <el-tag size="small" :type="getTypeTagType(row.word_type)">
                    {{ getWordTypeName(row.word_type) }}
                  </el-tag>
                  <span class="difficulty-badge" :class="`difficulty-${row.difficulty_level}`">
                    {{ getDifficultyName(row.difficulty_level) }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="例句" min-width="250">
            <template #default="{ row }">
              <div class="examples" v-if="row.example_sentences && row.example_sentences.length">
                <div v-for="(example, index) in row.example_sentences.slice(0, 2)" :key="index" class="example-item">
                  <div class="example-en">{{ example.english }}</div>
                  <div class="example-cn">{{ example.chinese }}</div>
                </div>
                <div v-if="row.example_sentences.length > 2" class="more-examples">
                  +{{ row.example_sentences.length - 2 }}个例句
                </div>
              </div>
              <span v-else class="no-examples">暂无例句</span>
            </template>
          </el-table-column>
          <el-table-column label="学习统计" width="150">
            <template #default="{ row }">
              <div class="learning-stats">
                <div class="stat-item">
                  <span class="stat-label">学习次数:</span>
                  <span class="stat-value">{{ row.learning_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">掌握率:</span>
                  <span class="stat-value mastery-rate">{{ (row.mastery_rate * 100).toFixed(1) }}%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">错误次数:</span>
                  <span class="stat-value error-count">{{ row.error_count }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="标签" width="120">
            <template #default="{ row }">
              <div class="tags" v-if="row.tags && row.tags.length">
                <el-tag 
                  v-for="tag in row.tags.slice(0, 2)" 
                  :key="tag" 
                  size="small" 
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
                <span v-if="row.tags.length > 2" class="more-tags">+{{ row.tags.length - 2 }}</span>
              </div>
              <span v-else class="no-tags">无标签</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="viewWord(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="link" size="small" @click="editWord(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="link"
                size="small"
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="toggleWordStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '归档' : '发布' }}
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deleteWord(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadVocabulary"
            @current-change="loadVocabulary"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="词性分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">词性分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="难度分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">难度分布图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="学习效果分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">学习效果分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑词汇对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingWord ? '编辑词汇' : '新建词汇'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="wordForm" :rules="wordRules" ref="wordFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="单词" prop="word">
              <el-input v-model="wordForm.word" placeholder="请输入英文单词" />
            </el-form-item>
            <el-form-item label="音标" prop="pronunciation">
              <el-input v-model="wordForm.pronunciation" placeholder="请输入音标" />
            </el-form-item>
            <el-form-item label="中文释义" prop="translation">
              <el-input v-model="wordForm.translation" type="textarea" rows="3" placeholder="请输入中文释义" />
            </el-form-item>
            <el-form-item label="词性" prop="word_type">
              <el-select v-model="wordForm.word_type" placeholder="请选择词性">
                <el-option label="名词" value="noun" />
                <el-option label="动词" value="verb" />
                <el-option label="形容词" value="adjective" />
                <el-option label="副词" value="adverb" />
                <el-option label="介词" value="preposition" />
                <el-option label="连词" value="conjunction" />
                <el-option label="代词" value="pronoun" />
                <el-option label="数词" value="numeral" />
                <el-option label="感叹词" value="interjection" />
              </el-select>
            </el-form-item>
            <el-form-item label="难度等级" prop="difficulty_level">
              <el-select v-model="wordForm.difficulty_level" placeholder="请选择难度等级">
                <el-option label="简单" :value="1" />
                <el-option label="较易" :value="2" />
                <el-option label="中等" :value="3" />
                <el-option label="较难" :value="4" />
                <el-option label="困难" :value="5" />
              </el-select>
            </el-form-item>
            <el-form-item label="标签" prop="tags">
              <el-select 
                v-model="wordForm.tags" 
                multiple 
                filterable 
                allow-create 
                placeholder="请选择或输入标签"
              >
                <el-option label="高频词汇" value="高频词汇" />
                <el-option label="考试词汇" value="考试词汇" />
                <el-option label="商务英语" value="商务英语" />
                <el-option label="日常用语" value="日常用语" />
                <el-option label="学术词汇" value="学术词汇" />
              </el-select>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="例句" name="examples">
            <div class="examples-section">
              <div class="section-header">
                <span>例句列表</span>
                <el-button type="primary" size="small" @click="addExample">
                  <el-icon><Plus /></el-icon>
                  添加例句
                </el-button>
              </div>
              <div v-for="(example, index) in wordForm.example_sentences" :key="index" class="example-form">
                <div class="example-header">
                  <span class="example-label">例句 {{ index + 1 }}</span>
                  <el-button type="link" size="small" class="danger" @click="removeExample(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-form-item label="英文例句">
                  <el-input v-model="example.english" placeholder="请输入英文例句" />
                </el-form-item>
                <el-form-item label="中文翻译">
                  <el-input v-model="example.chinese" placeholder="请输入中文翻译" />
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="发音" name="pronunciation">
            <el-form-item label="发音文件">
              <div class="audio-upload">
                <el-upload
                  class="upload-demo"
                  :show-file-list="false"
                  :before-upload="beforeAudioUpload"
                  :http-request="uploadAudio"
                  accept="audio/*"
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    上传发音文件
                  </el-button>
                </el-upload>
                <div v-if="wordForm.pronunciation_url" class="audio-preview">
                  <audio :src="wordForm.pronunciation_url" controls></audio>
                  <el-button type="link" size="small" class="remove-btn" @click="removePronunciation">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="发音说明">
              <el-input v-model="wordForm.pronunciation_notes" type="textarea" rows="3" placeholder="发音要点或注意事项" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveWord" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 词汇详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="词汇详情"
      width="700px"
    >
      <div class="word-detail" v-if="viewingWord">
        <div class="detail-header">
          <h2 class="word-title">{{ viewingWord.word }}</h2>
          <el-button
            type="link"
            @click="playPronunciation(viewingWord)"
            v-if="viewingWord.pronunciation_url"
          >
            <el-icon><VideoPlay /></el-icon>
            播放发音
          </el-button>
        </div>
        
        <div class="detail-section">
          <div class="pronunciation">
            <span class="label">音标：</span>
            <span class="value">[{{ viewingWord.pronunciation }}]</span>
          </div>
          <div class="translation">
            <span class="label">释义：</span>
            <span class="value">{{ viewingWord.translation }}</span>
          </div>
          <div class="word-type">
            <span class="label">词性：</span>
            <el-tag size="small">{{ getWordTypeName(viewingWord.word_type) }}</el-tag>
          </div>
          <div class="difficulty">
            <span class="label">难度：</span>
            <span class="difficulty-badge" :class="`difficulty-${viewingWord.difficulty_level}`">
              {{ getDifficultyName(viewingWord.difficulty_level) }}
            </span>
          </div>
        </div>
        
        <div class="detail-section" v-if="viewingWord.example_sentences && viewingWord.example_sentences.length">
          <h3>例句</h3>
          <div class="examples-detail">
            <div v-for="(example, index) in viewingWord.example_sentences" :key="index" class="example-detail">
              <div class="example-en">{{ example.english }}</div>
              <div class="example-cn">{{ example.chinese }}</div>
            </div>
          </div>
        </div>
        
        <div class="detail-section" v-if="viewingWord.tags && viewingWord.tags.length">
          <h3>标签</h3>
          <div class="tags-detail">
            <el-tag v-for="tag in viewingWord.tags" :key="tag" size="small" class="tag-detail">
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="showImportDialog"
      title="批量导入词汇"
      width="600px"
    >
      <div class="import-section">
        <el-upload
          class="upload-demo"
          drag
          :before-upload="beforeImportUpload"
          :http-request="importVocabulary"
          accept=".xlsx,.xls,.csv"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 Excel (.xlsx, .xls) 和 CSV 格式文件，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        <div class="import-template">
          <el-button type="link" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载导入模板
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, Download, VideoPlay, UploadFilled
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedWords = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const showImportDialog = ref(false)
const editingWord = ref(null)
const viewingWord = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const difficultyFilter = ref('')
const typeFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 词汇列表
const vocabulary = ref([
  {
    id: '1',
    word: 'sophisticated',
    pronunciation: 'səˈfɪstɪkeɪtɪd',
    translation: '复杂的；精密的；老练的；见多识广的',
    word_type: 'adjective',
    difficulty_level: 4,
    tags: ['高频词汇', '考试词汇'],
    example_sentences: [
      {
        english: 'She has sophisticated tastes in art.',
        chinese: '她在艺术方面有着高雅的品味。'
      },
      {
        english: 'This is a sophisticated piece of equipment.',
        chinese: '这是一件精密的设备。'
      }
    ],
    pronunciation_url: '',
    pronunciation_notes: '',
    learning_count: 156,
    mastery_rate: 0.78,
    error_count: 12,
    status: 'published',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const wordForm = reactive({
  word: '',
  pronunciation: '',
  translation: '',
  word_type: '',
  difficulty_level: 1,
  tags: [],
  example_sentences: [
    { english: '', chinese: '' }
  ],
  pronunciation_url: '',
  pronunciation_notes: ''
})

// 表单验证规则
const wordRules = {
  word: [
    { required: true, message: '请输入英文单词', trigger: 'blur' }
  ],
  translation: [
    { required: true, message: '请输入中文释义', trigger: 'blur' }
  ],
  word_type: [
    { required: true, message: '请选择词性', trigger: 'change' }
  ],
  difficulty_level: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ]
}

// 方法
const loadVocabulary = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取词汇列表
    console.log('Loading vocabulary...')
  } catch (error) {
    ElMessage.error('加载词汇列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedWords.value = selection
}

const getWordTypeName = (type) => {
  const types = {
    noun: '名词',
    verb: '动词',
    adjective: '形容词',
    adverb: '副词',
    preposition: '介词',
    conjunction: '连词',
    pronoun: '代词',
    numeral: '数词',
    interjection: '感叹词'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    noun: 'primary',
    verb: 'success',
    adjective: 'warning',
    adverb: 'danger',
    preposition: 'info'
  }
  return types[type] || ''
}

const getDifficultyName = (level) => {
  const names = {
    1: '简单',
    2: '较易',
    3: '中等',
    4: '较难',
    5: '困难'
  }
  return names[level] || `难度${level}`
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const playPronunciation = (word) => {
  if (word.pronunciation_url) {
    const audio = new Audio(word.pronunciation_url)
    audio.play().catch(error => {
      ElMessage.error('播放发音失败')
      console.error('Audio play error:', error)
    })
  } else {
    ElMessage.warning('该词汇暂无发音文件')
  }
}

const viewWord = (word) => {
  viewingWord.value = word
  showViewDialog.value = true
}

const editWord = (word) => {
  editingWord.value = word
  Object.assign(wordForm, word)
  showCreateDialog.value = true
}

const resetForm = () => {
  editingWord.value = null
  Object.assign(wordForm, {
    word: '',
    pronunciation: '',
    translation: '',
    word_type: '',
    difficulty_level: 1,
    tags: [],
    example_sentences: [
      { english: '', chinese: '' }
    ],
    pronunciation_url: '',
    pronunciation_notes: ''
  })
  formActiveTab.value = 'basic'
}

const addExample = () => {
  wordForm.example_sentences.push({ english: '', chinese: '' })
}

const removeExample = (index) => {
  if (wordForm.example_sentences.length > 1) {
    wordForm.example_sentences.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个例句')
  }
}

const beforeAudioUpload = (file) => {
  const isAudio = file.type.startsWith('audio/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAudio) {
    ElMessage.error('只能上传音频文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('音频文件大小不能超过 10MB!')
    return false
  }
  return true
}

const uploadAudio = async (options) => {
  // TODO: 实现音频上传逻辑
  console.log('Uploading audio...', options.file)
  // 模拟上传成功
  wordForm.pronunciation_url = URL.createObjectURL(options.file)
  ElMessage.success('发音文件上传成功')
}

const removePronunciation = () => {
  wordForm.pronunciation_url = ''
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', wordForm)
    showCreateDialog.value = false
    ElMessage.success('词汇已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveWord = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing word...', wordForm)
    showCreateDialog.value = false
    ElMessage.success(editingWord.value ? '词汇更新成功' : '词汇创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleWordStatus = async (word) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling word status...', word)
}

const deleteWord = async (word) => {
  try {
    await ElMessageBox.confirm('确定要删除这个词汇吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting word...', word)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing words...', selectedWords.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving words...', selectedWords.value)
}

const exportVocabulary = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting vocabulary...')
}

const beforeImportUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.type === 'text/csv'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 或 CSV 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

const importVocabulary = async (options) => {
  // TODO: 实现批量导入逻辑
  console.log('Importing vocabulary...', options.file)
  showImportDialog.value = false
  ElMessage.success('词汇导入成功')
}

const downloadTemplate = () => {
  // TODO: 实现下载模板逻辑
  console.log('Downloading import template...')
}

onMounted(() => {
  loadVocabulary()
})
</script>

<style scoped>
.vocabulary-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.word-info {
  padding: 8px 0;
}

.word-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.word-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.word-pronunciation {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-style: italic;
}

.word-translation {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.4;
}

.word-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.difficulty-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.difficulty-badge.difficulty-1 {
  background-color: #67c23a;
}

.difficulty-badge.difficulty-2 {
  background-color: #95d475;
}

.difficulty-badge.difficulty-3 {
  background-color: #e6a23c;
}

.difficulty-badge.difficulty-4 {
  background-color: #f78989;
}

.difficulty-badge.difficulty-5 {
  background-color: #f56c6c;
}

.examples {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-item {
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.example-en {
  font-size: 13px;
  color: #303133;
  margin-bottom: 4px;
  font-style: italic;
}

.example-cn {
  font-size: 12px;
  color: #606266;
}

.more-examples {
  font-size: 12px;
  color: #909399;
  text-align: center;
  padding: 4px;
}

.no-examples {
  font-size: 12px;
  color: #c0c4cc;
}

.learning-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.mastery-rate {
  color: #67c23a !important;
}

.error-count {
  color: #f56c6c !important;
}

.tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tag-item {
  font-size: 11px;
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.more-tags {
  font-size: 11px;
  color: #909399;
}

.no-tags {
  font-size: 12px;
  color: #c0c4cc;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.examples-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #303133;
}

.example-form {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.example-label {
  font-weight: 500;
  color: #303133;
}

.audio-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audio-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.remove-btn {
  color: #f56c6c;
}

.word-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.word-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.detail-section > div {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.detail-section .label {
  font-weight: 500;
  color: #606266;
  min-width: 60px;
}

.detail-section .value {
  color: #303133;
}

.examples-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.example-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.example-detail .example-en {
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
  font-style: italic;
}

.example-detail .example-cn {
  font-size: 13px;
  color: #606266;
}

.tags-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-detail {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.import-section {
  padding: 20px 0;
}

.import-template {
  margin-top: 16px;
  text-align: center;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
