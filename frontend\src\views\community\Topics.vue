<template>
  <div class="topics-management">
    <div class="page-header">
      <h1>话题管理</h1>
      <p>管理社区话题的生命周期、内容风控和运营推广</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="话题生命周期管控" name="lifecycle">
        <!-- 生命周期统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><ChatLineRound /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ lifecycleStats.totalTopics }}</div>
                  <div class="stat-label">总话题数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ lifecycleStats.officialTopics }}</div>
                  <div class="stat-label">官方话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ lifecycleStats.timedTopics }}</div>
                  <div class="stat-label">限时话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><FolderDelete /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ lifecycleStats.archivedTopics }}</div>
                  <div class="stat-label">已归档话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 官方话题创建 -->
        <el-card class="official-topics-card">
          <template #header>
            <div class="card-header">
              <span>官方话题管理</span>
              <el-button type="primary" @click="showCreateOfficialDialog = true">
                <el-icon><Plus /></el-icon>
                创建官方话题
              </el-button>
            </div>
          </template>

          <el-table
            :data="officialTopics"
            v-loading="officialLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="title" label="话题标题" min-width="200">
              <template #default="{ row }">
                <div class="topic-info">
                  <div class="topic-title">{{ row.title }}</div>
                  <div class="topic-meta">
                    <el-tag type="success" size="small">官方</el-tag>
                    <el-tag v-if="row.isPinned" type="danger" size="small">置顶</el-tag>
                    <span class="category">{{ getCategoryName(row.category) }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="participantCount" label="参与人数" width="120">
              <template #default="{ row }">
                <span class="participant-count">{{ row.participantCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="replyCount" label="回复数" width="100">
              <template #default="{ row }">
                <span class="reply-count">{{ row.replyCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="validUntil" label="有效期" width="160">
              <template #default="{ row }">
                <span v-if="row.validUntil" :class="getValidityClass(row.validUntil)">
                  {{ formatDateTime(row.validUntil) }}
                </span>
                <span v-else class="text-success">永久有效</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getTopicStatusTagType(row.status)">
                  {{ getTopicStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewTopicDetails(row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button
                  :type="row.isPinned ? 'warning' : 'success'"
                  size="small"
                  @click="togglePin(row)"
                >
                  <el-icon><Top /></el-icon>
                  {{ row.isPinned ? '取消置顶' : '置顶' }}
                </el-button>
                <el-button type="info" size="small" @click="archiveTopic(row)">
                  <el-icon><FolderDelete /></el-icon>
                  归档
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 话题归档管理 -->
        <el-card class="archive-card">
          <template #header>
            <div class="card-header">
              <span>话题归档管理</span>
              <div class="header-actions">
                <el-button type="warning" @click="handleBatchArchive" :disabled="!selectedTopics.length">
                  <el-icon><FolderDelete /></el-icon>
                  批量归档
                </el-button>
                <el-button type="info" @click="viewArchiveHistory">
                  <el-icon><Clock /></el-icon>
                  归档历史
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="expiredTopics"
            v-loading="expiredLoading"
            @selection-change="handleTopicSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" label="过期话题" min-width="200">
              <template #default="{ row }">
                <div class="topic-info">
                  <div class="topic-title">{{ row.title }}</div>
                  <div class="topic-meta">
                    <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
                    <span class="expired-days">过期{{ getExpiredDays(row.validUntil) }}天</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="participantCount" label="参与人数" width="120" />
            <el-table-column prop="replyCount" label="回复数" width="100" />
            <el-table-column prop="validUntil" label="过期时间" width="160">
              <template #default="{ row }">
                <span class="text-danger">{{ formatDateTime(row.validUntil) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="success" size="small" @click="extendTopic(row)">
                  <el-icon><Refresh /></el-icon>
                  延期
                </el-button>
                <el-button type="warning" size="small" @click="archiveTopic(row)">
                  <el-icon><FolderDelete /></el-icon>
                  立即归档
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="内容风控" name="moderation">
        <!-- 风控统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ moderationStats.pendingReview }}</div>
                  <div class="stat-label">待审核话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><CircleClose /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ moderationStats.blockedTopics }}</div>
                  <div class="stat-label">已屏蔽话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><UserFilled /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ moderationStats.reportedUsers }}</div>
                  <div class="stat-label">被举报用户</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ moderationStats.autoBlocked }}</div>
                  <div class="stat-label">AI自动拦截</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 敏感词管理 -->
        <el-card class="sensitive-words-card">
          <template #header>
            <div class="card-header">
              <span>敏感词库管理</span>
              <div class="header-actions">
                <el-button type="primary" @click="showAddWordDialog = true">
                  <el-icon><Plus /></el-icon>
                  添加敏感词
                </el-button>
                <el-button type="info" @click="importSensitiveWords">
                  <el-icon><Upload /></el-icon>
                  批量导入
                </el-button>
              </div>
            </div>
          </template>

          <div class="sensitive-words-content">
            <div class="words-categories">
              <el-tabs v-model="activeWordCategory" type="card">
                <el-tab-pane label="政治敏感" name="political">
                  <div class="words-list">
                    <el-tag
                      v-for="word in sensitiveWords.political"
                      :key="word.id"
                      closable
                      @close="removeSensitiveWord(word.id)"
                      class="word-tag"
                      type="danger"
                    >
                      {{ word.word }}
                    </el-tag>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="色情暴力" name="violence">
                  <div class="words-list">
                    <el-tag
                      v-for="word in sensitiveWords.violence"
                      :key="word.id"
                      closable
                      @close="removeSensitiveWord(word.id)"
                      class="word-tag"
                      type="warning"
                    >
                      {{ word.word }}
                    </el-tag>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="广告垃圾" name="spam">
                  <div class="words-list">
                    <el-tag
                      v-for="word in sensitiveWords.spam"
                      :key="word.id"
                      closable
                      @close="removeSensitiveWord(word.id)"
                      class="word-tag"
                      type="info"
                    >
                      {{ word.word }}
                    </el-tag>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="自定义" name="custom">
                  <div class="words-list">
                    <el-tag
                      v-for="word in sensitiveWords.custom"
                      :key="word.id"
                      closable
                      @close="removeSensitiveWord(word.id)"
                      class="word-tag"
                    >
                      {{ word.word }}
                    </el-tag>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-card>

        <!-- 待审核话题列表 -->
        <el-card class="review-topics-card">
          <template #header>
            <div class="card-header">
              <span>待审核话题</span>
              <div class="header-actions">
                <el-button type="success" @click="handleBatchApprove" :disabled="!selectedReviewTopics.length">
                  <el-icon><Check /></el-icon>
                  批量通过
                </el-button>
                <el-button type="danger" @click="handleBatchReject" :disabled="!selectedReviewTopics.length">
                  <el-icon><Close /></el-icon>
                  批量拒绝
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="reviewTopics"
            v-loading="reviewLoading"
            @selection-change="handleReviewSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" label="话题标题" min-width="200">
              <template #default="{ row }">
                <div class="topic-info">
                  <div class="topic-title">{{ row.title }}</div>
                  <div class="topic-meta">
                    <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
                    <span class="author">作者：{{ row.authorName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="riskLevel" label="风险等级" width="120">
              <template #default="{ row }">
                <el-tag :type="getRiskLevelTagType(row.riskLevel)">
                  {{ getRiskLevelText(row.riskLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sensitiveWords" label="敏感词" width="200">
              <template #default="{ row }">
                <div class="sensitive-words-display">
                  <el-tag
                    v-for="word in row.sensitiveWords"
                    :key="word"
                    size="small"
                    type="danger"
                    class="sensitive-word-tag"
                  >
                    {{ word }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" label="提交时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.submitTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="reviewTopicDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="approveTopic(row)">
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button type="danger" size="small" @click="rejectTopic(row)">
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="运营推广" name="promotion">
        <!-- 推广统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ promotionStats.hotTopics }}</div>
                  <div class="stat-label">热门话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ promotionStats.featuredTopics }}</div>
                  <div class="stat-label">精选话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Promotion /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ promotionStats.promotedTopics }}</div>
                  <div class="stat-label">推广话题</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ promotionStats.totalEngagement }}</div>
                  <div class="stat-label">总互动量</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 话题推广策略 -->
        <el-card class="promotion-strategy-card">
          <template #header>
            <div class="card-header">
              <span>话题推广策略</span>
              <el-button type="primary" @click="showPromotionDialog = true">
                <el-icon><Plus /></el-icon>
                创建推广计划
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="promotion-strategies">
            <el-col :span="8" v-for="strategy in promotionStrategies" :key="strategy.id">
              <el-card class="strategy-item" :class="{ 'strategy-active': strategy.isActive }">
                <div class="strategy-header">
                  <h3>{{ strategy.name }}</h3>
                  <el-switch v-model="strategy.isActive" @change="toggleStrategy(strategy)" />
                </div>
                <div class="strategy-description">{{ strategy.description }}</div>
                <div class="strategy-metrics">
                  <div class="metric-item">
                    <span class="metric-label">目标话题数：</span>
                    <span class="metric-value">{{ strategy.targetTopics }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">预期互动量：</span>
                    <span class="metric-value">{{ strategy.expectedEngagement }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">执行周期：</span>
                    <span class="metric-value">{{ strategy.duration }}天</span>
                  </div>
                </div>
                <div class="strategy-actions">
                  <el-button type="primary" size="small" @click="editStrategy(strategy)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button type="info" size="small" @click="viewStrategyReport(strategy)">
                    <el-icon><DataAnalysis /></el-icon>
                    报告
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>

        <!-- 热门话题排行 -->
        <el-card class="hot-topics-ranking-card">
          <template #header>
            <div class="card-header">
              <span>热门话题排行榜</span>
              <div class="header-actions">
                <el-radio-group v-model="rankingPeriod" size="small">
                  <el-radio-button label="today">今日</el-radio-button>
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>

          <el-table
            :data="hotTopicsRanking"
            stripe
            style="width: 100%"
          >
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <el-tag :type="getRankTagType($index + 1)" size="small">
                  第{{ $index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="话题标题" min-width="200">
              <template #default="{ row }">
                <div class="topic-info">
                  <div class="topic-title">{{ row.title }}</div>
                  <div class="topic-meta">
                    <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
                    <el-tag v-if="row.isFeatured" type="warning" size="small">精选</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="participantCount" label="参与人数" width="120">
              <template #default="{ row }">
                <span class="participant-count">{{ row.participantCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="replyCount" label="回复数" width="100">
              <template #default="{ row }">
                <span class="reply-count">{{ row.replyCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="viewCount" label="浏览量" width="120">
              <template #default="{ row }">
                <span class="view-count">{{ formatNumber(row.viewCount) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="heatScore" label="热度分数" width="120">
              <template #default="{ row }">
                <el-progress :percentage="row.heatScore" :color="getHeatColor(row.heatScore)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button
                  :type="row.isFeatured ? 'warning' : 'success'"
                  size="small"
                  @click="toggleFeatured(row)"
                >
                  <el-icon><Star /></el-icon>
                  {{ row.isFeatured ? '取消精选' : '设为精选' }}
                </el-button>
                <el-button type="primary" size="small" @click="promoteTopic(row)">
                  <el-icon><Promotion /></el-icon>
                  推广
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 话题分析报告 -->
        <el-card class="analytics-card">
          <template #header>
            <span>话题数据分析</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <h4>话题参与度趋势</h4>
                <div class="chart-placeholder" ref="participationTrendChartRef">
                  <!-- 这里将集成图表组件 -->
                  <div class="chart-mock">参与度趋势图表</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <h4>话题分类分布</h4>
                <div class="chart-placeholder" ref="categoryDistributionChartRef">
                  <!-- 这里将集成图表组件 -->
                  <div class="chart-mock">分类分布饼图</div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <div class="analytics-summary">
                <h4>数据洞察</h4>
                <div class="insights-list">
                  <div class="insight-item">
                    <el-icon><TrendCharts /></el-icon>
                    <span>本周话题参与度较上周提升15%，用户活跃度持续上升</span>
                  </div>
                  <div class="insight-item">
                    <el-icon><Star /></el-icon>
                    <span>学习方法类话题最受欢迎，建议增加相关内容推广</span>
                  </div>
                  <div class="insight-item">
                    <el-icon><Warning /></el-icon>
                    <span>晚上8-10点是话题活跃高峰期，建议在此时段发布重要话题</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatLineRound, Star, Timer, FolderDelete, Plus, View, Top, Warning, CircleClose,
  UserFilled, Upload, Check, Close, TrendCharts, Promotion, DataLine,
  Edit, DataAnalysis, Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('lifecycle')
const officialLoading = ref(false)
const expiredLoading = ref(false)
const reviewLoading = ref(false)
const selectedTopics = ref([])
const selectedReviewTopics = ref([])
const showCreateOfficialDialog = ref(false)
const showAddWordDialog = ref(false)
const showPromotionDialog = ref(false)

// 生命周期统计数据
const lifecycleStats = reactive({
  totalTopics: 1256,
  officialTopics: 45,
  timedTopics: 89,
  archivedTopics: 234
})

// 风控统计数据
const moderationStats = reactive({
  pendingReview: 23,
  blockedTopics: 156,
  reportedUsers: 45,
  autoBlocked: 89
})

// 推广统计数据
const promotionStats = reactive({
  hotTopics: 67,
  featuredTopics: 23,
  promotedTopics: 34,
  totalEngagement: 45678
})

// 官方话题列表
const officialTopics = ref([
  {
    id: 1,
    title: '2024年高考备考经验分享',
    category: 'study',
    participantCount: 1234,
    replyCount: 567,
    validUntil: '2024-06-30 23:59:59',
    status: 'active',
    isPinned: true,
    createTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    title: '学习方法大讨论',
    category: 'experience',
    participantCount: 890,
    replyCount: 345,
    validUntil: null,
    status: 'active',
    isPinned: false,
    createTime: '2024-01-10 14:30:00'
  }
])

// 过期话题列表
const expiredTopics = ref([
  {
    id: 3,
    title: '期末考试复习计划',
    category: 'study',
    participantCount: 456,
    replyCount: 123,
    validUntil: '2024-01-10 23:59:59'
  }
])

// 敏感词库
const sensitiveWords = reactive({
  political: [
    { id: 1, word: '政治敏感词1' },
    { id: 2, word: '政治敏感词2' }
  ],
  violence: [
    { id: 3, word: '暴力词汇1' },
    { id: 4, word: '暴力词汇2' }
  ],
  spam: [
    { id: 5, word: '广告词1' },
    { id: 6, word: '广告词2' }
  ],
  custom: [
    { id: 7, word: '自定义词1' },
    { id: 8, word: '自定义词2' }
  ]
})

const activeWordCategory = ref('political')

// 待审核话题
const reviewTopics = ref([
  {
    id: 4,
    title: '这是一个包含敏感词的话题标题',
    category: 'chat',
    authorName: '用户A',
    riskLevel: 'high',
    sensitiveWords: ['敏感词1', '敏感词2'],
    submitTime: '2024-01-15 16:30:00'
  }
])

// 推广策略
const promotionStrategies = ref([
  {
    id: 1,
    name: '热门话题推广',
    description: '将高质量话题推送到首页热门位置',
    targetTopics: 10,
    expectedEngagement: 5000,
    duration: 7,
    isActive: true
  },
  {
    id: 2,
    name: '新用户引导',
    description: '为新用户推荐适合的入门话题',
    targetTopics: 20,
    expectedEngagement: 2000,
    duration: 30,
    isActive: false
  },
  {
    id: 3,
    name: '学科专题推广',
    description: '按学科分类推广相关话题',
    targetTopics: 15,
    expectedEngagement: 3000,
    duration: 14,
    isActive: true
  }
])

// 热门话题排行
const hotTopicsRanking = ref([
  {
    id: 1,
    title: '高考数学解题技巧分享',
    category: 'study',
    participantCount: 2345,
    replyCount: 567,
    viewCount: 12345,
    heatScore: 95,
    isFeatured: true
  },
  {
    id: 2,
    title: '英语学习方法讨论',
    category: 'experience',
    participantCount: 1890,
    replyCount: 445,
    viewCount: 9876,
    heatScore: 88,
    isFeatured: false
  }
])

const rankingPeriod = ref('today')

// 工具函数
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    study: '学习讨论',
    experience: '经验分享',
    help: '问题求助',
    chat: '闲聊灌水'
  }
  return categoryMap[category] || category
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const getValidityClass = (validUntil: string) => {
  const now = new Date()
  const expiry = new Date(validUntil)
  const diffDays = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'text-danger'
  if (diffDays <= 3) return 'text-warning'
  return 'text-success'
}

const getExpiredDays = (validUntil: string) => {
  const now = new Date()
  const expiry = new Date(validUntil)
  return Math.ceil((now.getTime() - expiry.getTime()) / (1000 * 60 * 60 * 24))
}

const getTopicStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    expired: 'warning',
    archived: 'info'
  }
  return statusMap[status] || 'default'
}

const getTopicStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    expired: '已过期',
    archived: '已归档'
  }
  return statusMap[status] || status
}

const getRiskLevelTagType = (level: string) => {
  const levelMap: Record<string, string> = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return levelMap[level] || 'default'
}

const getRiskLevelText = (level: string) => {
  const levelMap: Record<string, string> = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return levelMap[level] || level
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

const getHeatColor = (score: number) => {
  if (score >= 90) return '#f56c6c'
  if (score >= 70) return '#e6a23c'
  return '#67c23a'
}

// 事件处理函数
const handleTopicSelectionChange = (selection: any[]) => {
  selectedTopics.value = selection
}

const handleReviewSelectionChange = (selection: any[]) => {
  selectedReviewTopics.value = selection
}

const viewTopicDetails = (topic: any) => {
  console.log('查看话题详情:', topic)
  // TODO: 实现话题详情查看
}

const togglePin = (topic: any) => {
  topic.isPinned = !topic.isPinned
  ElMessage.success(`话题"${topic.title}"${topic.isPinned ? '已置顶' : '已取消置顶'}`)
  // TODO: 调用API
}

const archiveTopic = (topic: any) => {
  ElMessageBox.confirm(`确定要归档话题"${topic.title}"吗？`, '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('话题已归档')
    // TODO: 调用API
  })
}

const handleBatchArchive = () => {
  ElMessageBox.confirm('确定要批量归档选中的话题吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量归档成功')
    // TODO: 调用API
  })
}

const viewArchiveHistory = () => {
  console.log('查看归档历史')
  // TODO: 实现归档历史查看
}

const extendTopic = (topic: any) => {
  ElMessageBox.prompt('请输入延期天数', '话题延期', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^\d+$/,
    inputErrorMessage: '请输入有效的天数'
  }).then(({ value }) => {
    ElMessage.success(`话题"${topic.title}"已延期${value}天`)
    // TODO: 调用API
  })
}

const removeSensitiveWord = (wordId: number) => {
  ElMessage.success('敏感词已删除')
  // TODO: 调用API删除敏感词
}

const importSensitiveWords = () => {
  console.log('批量导入敏感词')
  // TODO: 实现敏感词批量导入
}

const handleBatchApprove = () => {
  ElMessageBox.confirm('确定要批量通过选中的话题吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量审核通过成功')
    // TODO: 调用API
  })
}

const handleBatchReject = () => {
  ElMessageBox.confirm('确定要批量拒绝选中的话题吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量拒绝成功')
    // TODO: 调用API
  })
}

const reviewTopicDetails = (topic: any) => {
  console.log('查看审核详情:', topic)
  // TODO: 实现审核详情查看
}

const approveTopic = (topic: any) => {
  ElMessage.success(`话题"${topic.title}"审核通过`)
  // TODO: 调用API
}

const rejectTopic = (topic: any) => {
  ElMessage.warning(`话题"${topic.title}"已拒绝`)
  // TODO: 调用API
}

const toggleStrategy = (strategy: any) => {
  ElMessage.success(`推广策略"${strategy.name}"${strategy.isActive ? '已启用' : '已停用'}`)
  // TODO: 调用API
}

const editStrategy = (strategy: any) => {
  console.log('编辑推广策略:', strategy)
  // TODO: 实现策略编辑
}

const viewStrategyReport = (strategy: any) => {
  console.log('查看策略报告:', strategy)
  // TODO: 实现策略报告查看
}

const toggleFeatured = (topic: any) => {
  topic.isFeatured = !topic.isFeatured
  ElMessage.success(`话题"${topic.title}"${topic.isFeatured ? '已设为精选' : '已取消精选'}`)
  // TODO: 调用API
}

const promoteTopic = (topic: any) => {
  console.log('推广话题:', topic)
  // TODO: 实现话题推广
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.topics-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.info {
  background-color: #409eff;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.danger {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.topic-info {
  display: flex;
  flex-direction: column;
}

.topic-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.topic-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.participant-count, .reply-count, .view-count {
  font-weight: 500;
  color: #303133;
}

.expired-days {
  color: #f56c6c;
  font-weight: 500;
}

.text-danger {
  color: #f56c6c;
}

.text-warning {
  color: #e6a23c;
}

.text-success {
  color: #67c23a;
}

.sensitive-words-content {
  margin-top: 16px;
}

.words-list {
  min-height: 100px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.word-tag {
  margin-bottom: 8px;
}

.sensitive-words-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.sensitive-word-tag {
  margin-bottom: 4px;
}

.promotion-strategies {
  margin-top: 16px;
}

.strategy-item {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.strategy-item:hover {
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.strategy-active {
  border: 2px solid #67c23a;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.strategy-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.strategy-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 16px;
  line-height: 1.4;
}

.strategy-metrics {
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.metric-label {
  color: #909399;
}

.metric-value {
  color: #303133;
  font-weight: 500;
}

.strategy-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.chart-container h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

.chart-mock {
  color: #909399;
  font-size: 14px;
}

.analytics-summary {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.analytics-summary h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.insight-item .el-icon {
  color: #409eff;
  font-size: 16px;
}

@media (max-width: 768px) {
  .topics-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .promotion-strategies .el-col {
    margin-bottom: 10px;
  }
}
</style>

