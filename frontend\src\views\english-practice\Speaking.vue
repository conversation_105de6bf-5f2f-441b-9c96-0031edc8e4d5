<template>
  <div class="speaking-practice">
    <div class="page-header">
      <h1>口语练习管理</h1>
      <p>管理英语口语练习内容、发音评测和口语训练计划</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="练习内容" name="content">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新增练习
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedItems.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedItems.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
            <el-button @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索练习内容"
              style="width: 200px"
              clearable
              @change="loadSpeakingContent"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="levelFilter" placeholder="难度筛选" style="width: 120px" @change="loadSpeakingContent">
              <el-option label="全部难度" value="" />
              <el-option label="初级" value="beginner" />
              <el-option label="中级" value="intermediate" />
              <el-option label="高级" value="advanced" />
            </el-select>
            <el-select v-model="typeFilter" placeholder="类型筛选" style="width: 120px" @change="loadSpeakingContent">
              <el-option label="全部类型" value="" />
              <el-option label="日常对话" value="conversation" />
              <el-option label="演讲练习" value="speech" />
              <el-option label="发音训练" value="pronunciation" />
              <el-option label="情景对话" value="scenario" />
            </el-select>
          </div>
        </div>

        <!-- 口语练习列表 -->
        <el-table
          :data="speakingContent"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="练习信息" min-width="300">
            <template #default="{ row }">
              <div class="content-info">
                <div class="content-title">{{ row.title }}</div>
                <div class="content-description">{{ row.description }}</div>
                <div class="content-meta">
                  <el-tag size="small" :type="getLevelTagType(row.level)">
                    {{ getLevelName(row.level) }}
                  </el-tag>
                  <el-tag size="small" :type="getTypeTagType(row.type)">
                    {{ getTypeName(row.type) }}
                  </el-tag>
                  <span class="duration-info">{{ row.duration }}分钟</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="练习统计" width="150">
            <template #default="{ row }">
              <div class="practice-stats">
                <div class="stat-item">
                  <span class="stat-label">练习次数:</span>
                  <span class="stat-value">{{ row.practice_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">平均分:</span>
                  <span class="stat-value avg-score">{{ row.avg_score }}分</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">完成率:</span>
                  <span class="stat-value completion-rate">{{ (row.completion_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="音频资源" width="120">
            <template #default="{ row }">
              <div class="audio-info">
                <div class="audio-item" v-if="row.audio_url">
                  <el-button type="text" size="small" @click="playAudio(row.audio_url)">
                    <el-icon><VideoPlay /></el-icon>
                    播放示例
                  </el-button>
                </div>
                <div class="audio-duration">{{ formatDuration(row.audio_duration) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewContent(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="text" size="small" @click="editContent(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="text" size="small" @click="previewContent(row)">
                <el-icon><VideoPlay /></el-icon>
                预览
              </el-button>
              <el-button type="text" size="small" @click="viewAnalytics(row)">
                <el-icon><DataAnalysis /></el-icon>
                分析
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="toggleStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '下架' : '发布' }}
              </el-button>
              <el-button type="text" size="small" class="danger" @click="deleteContent(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadSpeakingContent"
            @current-change="loadSpeakingContent"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="练习类型分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">练习类型分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="难度分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">难度分布图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="练习效果分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">练习效果分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑练习对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingContent ? '编辑练习' : '新增练习'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="contentForm" :rules="contentRules" ref="contentFormRef" label-width="100px">
        <el-form-item label="练习标题" prop="title">
          <el-input v-model="contentForm.title" placeholder="请输入练习标题" />
        </el-form-item>
        <el-form-item label="练习描述" prop="description">
          <el-input v-model="contentForm.description" type="textarea" rows="3" placeholder="请输入练习描述" />
        </el-form-item>
        <el-form-item label="练习类型" prop="type">
          <el-select v-model="contentForm.type" placeholder="请选择练习类型">
            <el-option label="日常对话" value="conversation" />
            <el-option label="演讲练习" value="speech" />
            <el-option label="发音训练" value="pronunciation" />
            <el-option label="情景对话" value="scenario" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级" prop="level">
          <el-select v-model="contentForm.level" placeholder="请选择难度等级">
            <el-option label="初级" value="beginner" />
            <el-option label="中级" value="intermediate" />
            <el-option label="高级" value="advanced" />
          </el-select>
        </el-form-item>
        <el-form-item label="练习时长" prop="duration">
          <el-input-number v-model="contentForm.duration" :min="1" :max="60" placeholder="分钟" />
        </el-form-item>
        <el-form-item label="练习内容" prop="content">
          <el-input v-model="contentForm.content" type="textarea" rows="5" placeholder="请输入练习内容" />
        </el-form-item>
        <el-form-item label="音频文件">
          <el-upload
            class="upload-demo"
            :show-file-list="false"
            :before-upload="beforeAudioUpload"
            :http-request="uploadAudio"
            accept="audio/*"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              上传音频
            </el-button>
          </el-upload>
          <div v-if="contentForm.audio_url" class="audio-preview">
            <audio controls :src="contentForm.audio_url"></audio>
            <el-button type="link" size="small" class="remove-btn" @click="removeAudio">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveContent" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, Download, VideoPlay, DataAnalysis
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('content')
const loading = ref(false)
const saving = ref(false)
const selectedItems = ref([])
const showCreateDialog = ref(false)
const editingContent = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const levelFilter = ref('')
const typeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 口语练习列表
const speakingContent = ref([
  {
    id: '1',
    title: '日常购物对话',
    description: '学习在商店购物时的常用英语表达',
    type: 'conversation',
    level: 'beginner',
    duration: 10,
    content: 'A: Can I help you?\nB: Yes, I\'m looking for a shirt.',
    audio_url: '/audio/shopping-conversation.mp3',
    audio_duration: 120,
    practice_count: 256,
    avg_score: 78.5,
    completion_rate: 0.85,
    status: 'published',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const contentForm = reactive({
  title: '',
  description: '',
  type: 'conversation',
  level: 'beginner',
  duration: 10,
  content: '',
  audio_url: ''
})

// 表单验证规则
const contentRules = {
  title: [
    { required: true, message: '请输入练习标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择练习类型', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入练习内容', trigger: 'blur' }
  ]
}

// 方法
const loadSpeakingContent = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取口语练习列表
    console.log('Loading speaking content...')
  } catch (error) {
    ElMessage.error('加载练习内容失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

const getLevelName = (level) => {
  const levels = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级'
  }
  return levels[level] || level
}

const getLevelTagType = (level) => {
  const types = {
    beginner: 'success',
    intermediate: 'warning',
    advanced: 'danger'
  }
  return types[level] || ''
}

const getTypeName = (type) => {
  const types = {
    conversation: '日常对话',
    speech: '演讲练习',
    pronunciation: '发音训练',
    scenario: '情景对话'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    conversation: 'primary',
    speech: 'success',
    pronunciation: 'warning',
    scenario: 'info'
  }
  return types[type] || ''
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const playAudio = (audioUrl) => {
  // TODO: 实现音频播放
  console.log('Playing audio:', audioUrl)
}

const viewContent = (content) => {
  // TODO: 查看练习详情
  console.log('Viewing content:', content)
}

const editContent = (content) => {
  editingContent.value = content
  Object.assign(contentForm, content)
  showCreateDialog.value = true
}

const previewContent = (content) => {
  // TODO: 预览练习内容
  console.log('Previewing content:', content)
}

const viewAnalytics = (content) => {
  // TODO: 查看练习分析
  console.log('Viewing analytics:', content)
}

const resetForm = () => {
  editingContent.value = null
  Object.assign(contentForm, {
    title: '',
    description: '',
    type: 'conversation',
    level: 'beginner',
    duration: 10,
    content: '',
    audio_url: ''
  })
}

const beforeAudioUpload = (file) => {
  const isAudio = file.type.startsWith('audio/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAudio) {
    ElMessage.error('只能上传音频文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('音频文件大小不能超过 10MB!')
    return false
  }
  return true
}

const uploadAudio = async (options) => {
  // TODO: 实现音频上传逻辑
  console.log('Uploading audio...', options.file)
  contentForm.audio_url = URL.createObjectURL(options.file)
  ElMessage.success('音频上传成功')
}

const removeAudio = () => {
  contentForm.audio_url = ''
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', contentForm)
    showCreateDialog.value = false
    ElMessage.success('练习已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveContent = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing content...', contentForm)
    showCreateDialog.value = false
    ElMessage.success(editingContent.value ? '练习更新成功' : '练习创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleStatus = async (content) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling content status...', content)
}

const deleteContent = async (content) => {
  try {
    await ElMessageBox.confirm('确定要删除这个练习吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting content...', content)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing...', selectedItems.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving...', selectedItems.value)
}

const exportData = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting data...')
}

onMounted(() => {
  loadSpeakingContent()
})
</script>

<style scoped>
.speaking-practice {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.content-info {
  padding: 8px 0;
}

.content-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.content-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.content-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.duration-info {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.practice-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.avg-score {
  color: #409eff !important;
}

.completion-rate {
  color: #67c23a !important;
}

.audio-info {
  text-align: center;
}

.audio-duration {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.audio-preview {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.remove-btn {
  color: #f56c6c;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
