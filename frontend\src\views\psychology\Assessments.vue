<template>
  <div class="assessments-management">
    <div class="page-header">
      <h1>心理测评量表管理</h1>
      <p>管理心理测评工具、量表题目和评分标准，提供专业的心理评估服务</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="量表列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建量表
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedAssessments.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedAssessments.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索量表名称"
              style="width: 200px"
              clearable
              @change="loadAssessments"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="typeFilter" placeholder="类型筛选" style="width: 120px" @change="loadAssessments">
              <el-option label="全部类型" value="" />
              <el-option label="抑郁评估" value="depression" />
              <el-option label="焦虑评估" value="anxiety" />
              <el-option label="压力评估" value="stress" />
              <el-option label="人格测试" value="personality" />
              <el-option label="综合评估" value="comprehensive" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadAssessments">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>

        <!-- 量表列表 -->
        <el-table
          :data="assessments"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="量表信息" min-width="300">
            <template #default="{ row }">
              <div class="assessment-info">
                <div class="assessment-title">{{ row.title }}</div>
                <div class="assessment-description">{{ row.description }}</div>
                <div class="assessment-meta">
                  <el-tag size="small" :type="getTypeTagType(row.assessment_type)">
                    {{ getTypeName(row.assessment_type) }}
                  </el-tag>
                  <span class="question-count">{{ row.question_count }}题</span>
                  <span class="time-estimate">约{{ row.estimated_time }}分钟</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="评分标准" width="150">
            <template #default="{ row }">
              <div class="scoring-info">
                <div class="scoring-item">
                  <span class="scoring-label">总分:</span>
                  <span class="scoring-value">{{ row.max_score }}分</span>
                </div>
                <div class="scoring-item">
                  <span class="scoring-label">维度:</span>
                  <span class="scoring-value">{{ row.dimensions.length }}个</span>
                </div>
                <div class="scoring-item">
                  <span class="scoring-label">等级:</span>
                  <span class="scoring-value">{{ row.score_levels.length }}级</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="使用统计" width="120">
            <template #default="{ row }">
              <div class="usage-stats">
                <div class="stat-item">
                  <span class="stat-label">完成:</span>
                  <span class="stat-value">{{ row.completion_count }}次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">平均分:</span>
                  <span class="stat-value">{{ row.average_score.toFixed(1) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">完成率:</span>
                  <span class="stat-value completion-rate">{{ (row.completion_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="专业性" width="100">
            <template #default="{ row }">
              <div class="reliability-info">
                <div class="reliability-item">
                  <span class="reliability-label">信度:</span>
                  <span class="reliability-value">{{ row.reliability }}</span>
                </div>
                <div class="reliability-item">
                  <span class="reliability-label">效度:</span>
                  <span class="reliability-value">{{ row.validity }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="viewAssessment(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="link" size="small" @click="editAssessment(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="link" size="small" @click="showAssessmentPreview(row)">
                <el-icon><Monitor /></el-icon>
                预览
              </el-button>
              <el-button
                type="link"
                size="small"
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="toggleAssessmentStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '归档' : '发布' }}
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deleteAssessment(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadAssessments"
            @current-change="loadAssessments"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="量表类型分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">量表类型分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="完成情况统计">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">完成情况统计图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="评估结果趋势">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">评估结果趋势图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑量表对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingAssessment ? '编辑量表' : '新建量表'"
      width="1000px"
      @close="resetForm"
    >
      <el-form :model="assessmentForm" :rules="assessmentRules" ref="assessmentFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="量表名称" prop="title">
              <el-input v-model="assessmentForm.title" placeholder="请输入量表名称" />
            </el-form-item>
            <el-form-item label="量表描述" prop="description">
              <el-input v-model="assessmentForm.description" type="textarea" rows="3" placeholder="请输入量表描述" />
            </el-form-item>
            <el-form-item label="量表类型" prop="assessment_type">
              <el-select v-model="assessmentForm.assessment_type" placeholder="请选择量表类型">
                <el-option label="抑郁评估" value="depression" />
                <el-option label="焦虑评估" value="anxiety" />
                <el-option label="压力评估" value="stress" />
                <el-option label="人格测试" value="personality" />
                <el-option label="综合评估" value="comprehensive" />
                <el-option label="睡眠质量" value="sleep" />
                <el-option label="社交能力" value="social" />
              </el-select>
            </el-form-item>
            <el-form-item label="预估时间" prop="estimated_time">
              <el-input-number v-model="assessmentForm.estimated_time" :min="1" :max="120" placeholder="分钟" />
            </el-form-item>
            <el-form-item label="适用人群" prop="target_audience">
              <el-checkbox-group v-model="assessmentForm.target_audience">
                <el-checkbox label="大学生">大学生</el-checkbox>
                <el-checkbox label="中学生">中学生</el-checkbox>
                <el-checkbox label="成年人">成年人</el-checkbox>
                <el-checkbox label="老年人">老年人</el-checkbox>
                <el-checkbox label="职场人士">职场人士</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="专业指标">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="信度系数" prop="reliability">
                    <el-input-number v-model="assessmentForm.reliability" :min="0" :max="1" :step="0.01" placeholder="0-1" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="效度系数" prop="validity">
                    <el-input-number v-model="assessmentForm.validity" :min="0" :max="1" :step="0.01" placeholder="0-1" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="评分维度" name="dimensions">
            <div class="dimensions-section">
              <div class="section-header">
                <span>评分维度</span>
                <el-button type="primary" size="small" @click="addDimension">
                  <el-icon><Plus /></el-icon>
                  添加维度
                </el-button>
              </div>
              <div v-for="(dimension, index) in assessmentForm.dimensions" :key="index" class="dimension-form">
                <div class="dimension-header">
                  <span class="dimension-label">维度 {{ index + 1 }}</span>
                  <el-button type="link" size="small" class="danger" @click="removeDimension(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-form-item label="维度名称">
                  <el-input v-model="dimension.name" placeholder="请输入维度名称" />
                </el-form-item>
                <el-form-item label="维度描述">
                  <el-input v-model="dimension.description" placeholder="请输入维度描述" />
                </el-form-item>
                <el-form-item label="权重">
                  <el-input-number v-model="dimension.weight" :min="0" :max="1" :step="0.1" placeholder="0-1" />
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="评分等级" name="levels">
            <div class="levels-section">
              <div class="section-header">
                <span>评分等级</span>
                <el-button type="primary" size="small" @click="addScoreLevel">
                  <el-icon><Plus /></el-icon>
                  添加等级
                </el-button>
              </div>
              <div v-for="(level, index) in assessmentForm.score_levels" :key="index" class="level-form">
                <div class="level-header">
                  <span class="level-label">等级 {{ index + 1 }}</span>
                  <el-button type="link" size="small" class="danger" @click="removeScoreLevel(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="等级名称">
                      <el-input v-model="level.name" placeholder="如：正常、轻度、中度" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="分数范围">
                      <el-input-number v-model="level.min_score" placeholder="最低分" style="width: 45%;" />
                      <span style="margin: 0 5px;">-</span>
                      <el-input-number v-model="level.max_score" placeholder="最高分" style="width: 45%;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="风险等级">
                      <el-select v-model="level.risk_level" placeholder="请选择风险等级">
                        <el-option label="无风险" value="none" />
                        <el-option label="低风险" value="low" />
                        <el-option label="中风险" value="medium" />
                        <el-option label="高风险" value="high" />
                        <el-option label="极高风险" value="critical" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="结果解释">
                  <el-input v-model="level.interpretation" type="textarea" rows="2" placeholder="请输入该等级的结果解释" />
                </el-form-item>
                <el-form-item label="建议">
                  <el-input v-model="level.recommendations" type="textarea" rows="2" placeholder="请输入针对该等级的建议" />
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="题目管理" name="questions">
            <div class="questions-section">
              <div class="section-header">
                <span>量表题目</span>
                <el-button type="primary" size="small" @click="addQuestion">
                  <el-icon><Plus /></el-icon>
                  添加题目
                </el-button>
              </div>
              <div v-for="(question, index) in assessmentForm.questions" :key="index" class="question-form">
                <div class="question-header">
                  <span class="question-label">题目 {{ index + 1 }}</span>
                  <el-button type="link" size="small" class="danger" @click="removeQuestion(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-form-item label="题目内容">
                  <el-input v-model="question.content" placeholder="请输入题目内容" />
                </el-form-item>
                <el-form-item label="所属维度">
                  <el-select v-model="question.dimension" placeholder="请选择所属维度">
                    <el-option 
                      v-for="(dim, dimIndex) in assessmentForm.dimensions" 
                      :key="dimIndex" 
                      :label="dim.name" 
                      :value="dim.name" 
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="选项设置">
                  <div class="options-list">
                    <div v-for="(option, optIndex) in question.options" :key="optIndex" class="option-input">
                      <el-input v-model="option.text" :placeholder="`选项 ${optIndex + 1}`" />
                      <el-input-number v-model="option.score" :min="0" :max="10" placeholder="分值" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="是否反向计分">
                  <el-switch v-model="question.is_reverse" active-text="是" inactive-text="否" />
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveAssessment" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 量表详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="量表详情"
      width="800px"
    >
      <div class="assessment-detail" v-if="viewingAssessment">
        <div class="detail-header">
          <h2 class="assessment-title">{{ viewingAssessment.title }}</h2>
          <el-tag :type="getTypeTagType(viewingAssessment.assessment_type)">
            {{ getTypeName(viewingAssessment.assessment_type) }}
          </el-tag>
        </div>
        
        <div class="detail-section">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">题目数量：</span>
              <span class="value">{{ viewingAssessment.question_count }}题</span>
            </div>
            <div class="info-item">
              <span class="label">预估时间：</span>
              <span class="value">{{ viewingAssessment.estimated_time }}分钟</span>
            </div>
            <div class="info-item">
              <span class="label">信度系数：</span>
              <span class="value">{{ viewingAssessment.reliability }}</span>
            </div>
            <div class="info-item">
              <span class="label">效度系数：</span>
              <span class="value">{{ viewingAssessment.validity }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>量表描述</h3>
          <div class="description-detail">{{ viewingAssessment.description }}</div>
        </div>
        
        <div class="detail-section" v-if="viewingAssessment.dimensions && viewingAssessment.dimensions.length">
          <h3>评分维度</h3>
          <div class="dimensions-detail">
            <div v-for="(dimension, index) in viewingAssessment.dimensions" :key="index" class="dimension-detail">
              <div class="dimension-name">{{ dimension.name }}</div>
              <div class="dimension-description">{{ dimension.description }}</div>
              <div class="dimension-weight">权重: {{ dimension.weight }}</div>
            </div>
          </div>
        </div>
        
        <div class="detail-section" v-if="viewingAssessment.score_levels && viewingAssessment.score_levels.length">
          <h3>评分等级</h3>
          <div class="levels-detail">
            <div v-for="(level, index) in viewingAssessment.score_levels" :key="index" class="level-detail">
              <div class="level-header">
                <span class="level-name">{{ level.name }}</span>
                <span class="level-range">{{ level.min_score }}-{{ level.max_score }}分</span>
                <el-tag size="small" :type="getRiskLevelTagType(level.risk_level)">
                  {{ getRiskLevelName(level.risk_level) }}
                </el-tag>
              </div>
              <div class="level-interpretation">{{ level.interpretation }}</div>
              <div class="level-recommendations">建议: {{ level.recommendations }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 量表预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="量表预览"
      width="700px"
    >
      <div class="assessment-preview" v-if="previewAssessment">
        <div class="preview-header">
          <h2>{{ previewAssessment.title }}</h2>
          <p>{{ previewAssessment.description }}</p>
          <div class="preview-info">
            <span>共{{ previewAssessment.question_count }}题</span>
            <span>预计{{ previewAssessment.estimated_time }}分钟</span>
          </div>
        </div>
        
        <div class="preview-questions">
          <div v-for="(question, index) in previewAssessment.questions" :key="index" class="preview-question">
            <div class="question-title">{{ index + 1 }}. {{ question.content }}</div>
            <div class="question-options">
              <div v-for="(option, optIndex) in question.options" :key="optIndex" class="question-option">
                <el-radio :label="optIndex" name="preview">{{ option.text }}</el-radio>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Monitor
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedAssessments = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const showPreviewDialog = ref(false)
const editingAssessment = ref(null)
const viewingAssessment = ref(null)
const previewAssessment = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const typeFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 量表列表
const assessments = ref([
  {
    id: '1',
    title: '抑郁自评量表(SDS)',
    description: '用于评估个体抑郁症状的严重程度，适用于成年人群体的抑郁筛查和评估',
    assessment_type: 'depression',
    question_count: 20,
    estimated_time: 10,
    max_score: 80,
    dimensions: [
      { name: '情感症状', description: '评估情绪低落、悲伤等情感表现', weight: 0.4 },
      { name: '认知症状', description: '评估思维迟缓、注意力不集中等认知表现', weight: 0.3 },
      { name: '躯体症状', description: '评估睡眠、食欲等躯体表现', weight: 0.3 }
    ],
    score_levels: [
      { name: '正常', min_score: 0, max_score: 49, risk_level: 'none', interpretation: '心理状态正常', recommendations: '保持良好的生活习惯' },
      { name: '轻度抑郁', min_score: 50, max_score: 59, risk_level: 'low', interpretation: '存在轻度抑郁症状', recommendations: '建议关注心理健康，适当调节' },
      { name: '中度抑郁', min_score: 60, max_score: 69, risk_level: 'medium', interpretation: '存在中度抑郁症状', recommendations: '建议寻求专业心理帮助' },
      { name: '重度抑郁', min_score: 70, max_score: 80, risk_level: 'high', interpretation: '存在重度抑郁症状', recommendations: '强烈建议立即寻求专业治疗' }
    ],
    questions: [],
    target_audience: ['大学生', '成年人'],
    reliability: 0.85,
    validity: 0.82,
    completion_count: 234,
    average_score: 45.6,
    completion_rate: 0.89,
    status: 'published',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const assessmentForm = reactive({
  title: '',
  description: '',
  assessment_type: '',
  estimated_time: 10,
  target_audience: [],
  reliability: 0.8,
  validity: 0.8,
  dimensions: [
    { name: '', description: '', weight: 0.5 }
  ],
  score_levels: [
    { name: '', min_score: 0, max_score: 10, risk_level: 'none', interpretation: '', recommendations: '' }
  ],
  questions: [
    {
      content: '',
      dimension: '',
      options: [
        { text: '', score: 1 },
        { text: '', score: 2 },
        { text: '', score: 3 },
        { text: '', score: 4 }
      ],
      is_reverse: false
    }
  ]
})

// 表单验证规则
const assessmentRules = {
  title: [
    { required: true, message: '请输入量表名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入量表描述', trigger: 'blur' }
  ],
  assessment_type: [
    { required: true, message: '请选择量表类型', trigger: 'change' }
  ],
  estimated_time: [
    { required: true, message: '请输入预估时间', trigger: 'blur' }
  ]
}

// 方法
const loadAssessments = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取量表列表
    console.log('Loading assessments...')
  } catch (error) {
    ElMessage.error('加载量表列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedAssessments.value = selection
}

const getTypeName = (type) => {
  const types = {
    depression: '抑郁评估',
    anxiety: '焦虑评估',
    stress: '压力评估',
    personality: '人格测试',
    comprehensive: '综合评估',
    sleep: '睡眠质量',
    social: '社交能力'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    depression: 'danger',
    anxiety: 'warning',
    stress: 'info',
    personality: 'primary',
    comprehensive: 'success'
  }
  return types[type] || ''
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const getRiskLevelName = (level) => {
  const levels = {
    none: '无风险',
    low: '低风险',
    medium: '中风险',
    high: '高风险',
    critical: '极高风险'
  }
  return levels[level] || level
}

const getRiskLevelTagType = (level) => {
  const types = {
    none: 'success',
    low: 'primary',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[level] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewAssessment = (assessment) => {
  viewingAssessment.value = assessment
  showViewDialog.value = true
}

const editAssessment = (assessment) => {
  editingAssessment.value = assessment
  Object.assign(assessmentForm, assessment)
  showCreateDialog.value = true
}

const showAssessmentPreview = (assessment) => {
  previewAssessment.value = assessment
  showPreviewDialog.value = true
}

const resetForm = () => {
  editingAssessment.value = null
  Object.assign(assessmentForm, {
    title: '',
    description: '',
    assessment_type: '',
    estimated_time: 10,
    target_audience: [],
    reliability: 0.8,
    validity: 0.8,
    dimensions: [
      { name: '', description: '', weight: 0.5 }
    ],
    score_levels: [
      { name: '', min_score: 0, max_score: 10, risk_level: 'none', interpretation: '', recommendations: '' }
    ],
    questions: [
      {
        content: '',
        dimension: '',
        options: [
          { text: '', score: 1 },
          { text: '', score: 2 },
          { text: '', score: 3 },
          { text: '', score: 4 }
        ],
        is_reverse: false
      }
    ]
  })
  formActiveTab.value = 'basic'
}

const addDimension = () => {
  assessmentForm.dimensions.push({ name: '', description: '', weight: 0.5 })
}

const removeDimension = (index) => {
  if (assessmentForm.dimensions.length > 1) {
    assessmentForm.dimensions.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个维度')
  }
}

const addScoreLevel = () => {
  assessmentForm.score_levels.push({
    name: '',
    min_score: 0,
    max_score: 10,
    risk_level: 'none',
    interpretation: '',
    recommendations: ''
  })
}

const removeScoreLevel = (index) => {
  if (assessmentForm.score_levels.length > 1) {
    assessmentForm.score_levels.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个评分等级')
  }
}

const addQuestion = () => {
  assessmentForm.questions.push({
    content: '',
    dimension: '',
    options: [
      { text: '', score: 1 },
      { text: '', score: 2 },
      { text: '', score: 3 },
      { text: '', score: 4 }
    ],
    is_reverse: false
  })
}

const removeQuestion = (index) => {
  if (assessmentForm.questions.length > 1) {
    assessmentForm.questions.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个题目')
  }
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', assessmentForm)
    showCreateDialog.value = false
    ElMessage.success('量表已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveAssessment = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing assessment...', assessmentForm)
    showCreateDialog.value = false
    ElMessage.success(editingAssessment.value ? '量表更新成功' : '量表创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleAssessmentStatus = async (assessment) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling assessment status...', assessment)
}

const deleteAssessment = async (assessment) => {
  try {
    await ElMessageBox.confirm('确定要删除这个量表吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting assessment...', assessment)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing assessments...', selectedAssessments.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving assessments...', selectedAssessments.value)
}

onMounted(() => {
  loadAssessments()
})
</script>

<style scoped>
.assessments-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.assessment-info {
  padding: 8px 0;
}

.assessment-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
}

.assessment-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.assessment-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.question-count {
  font-size: 11px;
  color: #409eff;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.time-estimate {
  font-size: 11px;
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.scoring-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.scoring-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.scoring-label {
  color: #909399;
}

.scoring-value {
  color: #606266;
  font-weight: 500;
}

.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.completion-rate {
  color: #67c23a !important;
}

.reliability-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reliability-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.reliability-label {
  color: #909399;
}

.reliability-value {
  color: #606266;
  font-weight: 500;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.dimensions-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #303133;
}

.dimension-form {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.dimension-label {
  font-weight: 500;
  color: #303133;
}

.levels-section {
  margin-top: 20px;
}

.level-form {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.level-label {
  font-weight: 500;
  color: #303133;
}

.questions-section {
  margin-top: 20px;
}

.question-form {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.question-label {
  font-weight: 500;
  color: #303133;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.assessment-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.assessment-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
}

.description-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
}

.dimensions-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dimension-detail {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.dimension-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.dimension-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.dimension-weight {
  font-size: 12px;
  color: #909399;
}

.levels-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.level-detail {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.level-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.level-name {
  font-weight: 500;
  color: #303133;
}

.level-range {
  font-size: 12px;
  color: #606266;
}

.level-interpretation {
  font-size: 13px;
  color: #303133;
  margin-bottom: 4px;
}

.level-recommendations {
  font-size: 12px;
  color: #606266;
}

.assessment-preview {
  padding: 20px 0;
}

.preview-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.preview-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.preview-header p {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.6;
}

.preview-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.preview-questions {
  max-height: 400px;
  overflow-y: auto;
}

.preview-question {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.question-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.question-option {
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
