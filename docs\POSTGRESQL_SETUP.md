
# PostgreSQL 数据库设置说明

## 1. 安装 PostgreSQL

### Windows:
1. 下载并安装 PostgreSQL: https://www.postgresql.org/download/windows/
2. 安装过程中设置 postgres 用户的密码
3. 确保 PostgreSQL 服务正在运行

### macOS:
```bash
brew install postgresql
brew services start postgresql
```

### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
```

## 2. 创建数据库

### 方法一：使用 psql 命令行
```bash
# 连接到 PostgreSQL
psql -U postgres

# 创建数据库
CREATE DATABASE wiscude_admin;

# 退出
\q
```

### 方法二：使用 pgAdmin
1. 打开 pgAdmin
2. 连接到 PostgreSQL 服务器
3. 右键点击 "Databases" -> "Create" -> "Database"
4. 输入数据库名称: wiscude_admin

## 3. 执行初始化 SQL

### 使用 psql 执行 SQL 文件:
```bash
psql -U postgres -d wiscude_admin -f ../sql/database.sql
```

### 或者在 psql 中执行:
```sql
\c wiscude_admin
\i ../sql/database.sql
```

## 4. 更新连接配置

在 `.env` 文件中设置正确的数据库连接信息:
```
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/wiscude_admin
```

## 5. 验证设置

运行以下命令验证数据库连接:
```bash
python -c "from app.core.database import engine; print('数据库连接成功!' if engine else '数据库连接失败!')"
```

## 常见问题

### 连接被拒绝
- 检查 PostgreSQL 服务是否运行
- 检查端口 5432 是否开放
- 检查 pg_hba.conf 配置

### 密码认证失败
- 确认 postgres 用户密码
- 检查 .env 文件中的密码配置

### 权限不足
- 确保用户有创建数据库的权限
- 可能需要使用超级用户权限
