"""
WisCude 后台管理系统 - 数据模型包
"""
from .base import BaseModel, TimestampMixin, SoftDeleteMixin
from .admin import AdminUser, LoginLog, SystemLog, UserRole
from .user import WiscudeUser, StudySession, CheckIn, AILearningRecord, Gender, UserStatus, MembershipType, UserLevel
from .content import (
    CommunityPost, PostComment, SyncLog,
    PostType, PostStatus
)
from .courses import Course, Instructor, CourseChapter, CourseLesson, CourseEnrollment, CourseEvaluation

# 导出所有模型
__all__ = [
    # 基础模型
    "BaseModel",
    "TimestampMixin", 
    "SoftDeleteMixin",
    
    # 管理员模型
    "AdminUser",
    "LoginLog",
    "SystemLog",
    "UserRole",
    
    # 用户模型
    "WiscudeUser",
    "StudySession",
    "CheckIn",
    "AILearningRecord",
    "Gender",
    "UserStatus",
    "MembershipType",
    "UserLevel",
    
    # 内容模型
    "CommunityPost",
    "PostComment",
    "SyncLog",
    "PostType",
    "PostStatus",

    # 课程模型
    "Course",
    "Instructor",
    "CourseChapter",
    "CourseLesson",
    "CourseEnrollment",
    "CourseEvaluation",
]
