# WisCude 应用配置文件
# 统一管理应用的各项配置

# 应用基本信息
app:
  name: "WisCude 后台管理系统"
  version: "1.0.0"
  description: "智慧学习数据管理平台"
  debug: true

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  reload: true
  log_level: "info"
  workers: 1

# 安全配置
security:
  secret_key: "wiscude-secret-key-change-in-production-2024"
  algorithm: "HS256"
  access_token_expire_minutes: 30
  refresh_token_expire_days: 7
  
  # 密码策略
  password:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special: false
    max_attempts: 5
    lockout_minutes: 15

# CORS配置
cors:
  origins:
    - "http://localhost:3000"
    - "http://localhost:5173"
    - "http://localhost:5175"
    - "http://127.0.0.1:3000"
    - "http://127.0.0.1:5173"
    - "http://127.0.0.1:5175"
  allow_credentials: true
  allow_methods: ["*"]
  allow_headers: ["*"]

# 信任主机配置
trusted_hosts:
  - "localhost"
  - "127.0.0.1"
  - "*"  # 开发环境，生产环境应限制

# 文件上传配置
upload:
  max_file_size: 10485760  # 10MB
  upload_dir: "uploads"
  allowed_extensions:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".gif"
    - ".pdf"
    - ".doc"
    - ".docx"
    - ".xls"
    - ".xlsx"

# 分页配置
pagination:
  default_page_size: 20
  max_page_size: 100

# 日志配置
logging:
  level: "INFO"
  file: "logs/wiscude-admin.log"
  max_size_mb: 100
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 数据同步配置
sync:
  interval_minutes: 30
  batch_size: 1000
  auto_sync_enabled: false
  retry_attempts: 3
  retry_delay_seconds: 5

# 邮件配置
email:
  smtp_host: "smtp.gmail.com"
  smtp_port: 587
  smtp_tls: true
  smtp_user: ""
  smtp_password: ""
  from_email: ""
  from_name: "WisCude Admin"

# 监控配置
monitoring:
  enable_metrics: true
  metrics_path: "/metrics"
  health_check_interval: 30
  performance_tracking: true

# 缓存配置
cache:
  redis_url: "redis://localhost:6379/0"
  default_timeout: 300
  key_prefix: "wiscude:"

# 任务队列配置
celery:
  broker_url: "redis://localhost:6379/1"
  result_backend: "redis://localhost:6379/2"
  task_serializer: "json"
  accept_content: ["json"]
  result_serializer: "json"
  timezone: "Asia/Shanghai"

# 开发环境特定配置
development:
  auto_reload: true
  debug_toolbar: true
  sql_echo: false
  fake_data: false

# 生产环境特定配置
production:
  auto_reload: false
  debug_toolbar: false
  sql_echo: false
  ssl_redirect: true
  secure_cookies: true
