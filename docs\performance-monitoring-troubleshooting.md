# 性能监控控制台故障排除指南

## 问题：网络流量监控和系统负载无法显示

### 🚨 系统负载显示问题的紧急修复

如果系统负载部分显示为空白，请按以下步骤操作：

#### 立即修复步骤：
1. **点击"刷新负载"按钮** - 在页面右上角找到橙色的"刷新负载"按钮并点击
2. **检查浏览器控制台** - 按F12打开开发者工具，查看是否有CSS相关错误
3. **强制刷新页面** - 按Ctrl+F5强制刷新页面，清除缓存

#### 根本原因：
系统负载显示问题主要由CSS变量未定义导致。已修复的问题包括：
- CSS自定义属性（--变量名）替换为具体数值
- 确保数据正确初始化
- 添加降级显示机制

### 快速诊断步骤

#### 1. 检查浏览器控制台
打开浏览器开发者工具（F12），查看Console标签页是否有错误信息。

#### 2. 使用调试功能
在性能监控控制台页面，点击右上角的"调试数据"按钮，查看控制台输出的数据状态。

#### 3. 检查数据状态
在控制台中查看以下关键数据：
```javascript
// 系统负载数据
systemLoads: [
  { label: '1分钟', value: 45 },
  { label: '5分钟', value: 38 },
  { label: '15分钟', value: 42 }
]

// 网络图表数据
networkChartData: {
  labels: ['10:00', '10:05', '10:10', '10:15', '10:20'],
  datasets: [
    {
      label: '入站流量',
      data: [5, 8, 6, 9, 7],
      // ...其他配置
    }
  ]
}
```

### 常见问题及解决方案

#### 问题1：图表组件未正确加载
**症状**：图表区域完全空白，没有任何显示

**解决方案**：
1. 检查图表组件是否正确导入
2. 确认ECharts依赖是否正确安装
3. 重新安装依赖：`npm install echarts vue-echarts`

#### 问题2：数据格式不匹配
**症状**：图表显示但没有数据，或显示错误

**解决方案**：
1. 检查数据适配器是否正确转换数据格式
2. 验证原始数据是否有效
3. 查看控制台是否有数据转换错误

#### 问题3：组件Props不匹配
**症状**：图表组件报错或显示异常

**解决方案**：
1. 确认传递给图表组件的props是否正确
2. 检查组件接口定义
3. 移除不必要的props（如已删除的options）

#### 问题4：CSS样式问题
**症状**：图表容器高度为0或样式异常

**解决方案**：
1. 检查`.chart-container`是否有正确的高度设置
2. 确认父容器没有`overflow: hidden`等限制
3. 验证响应式样式是否正确

### 手动修复步骤

#### 步骤1：验证组件导入
确认以下导入是否正确：
```javascript
import { LineChart, DoughnutChart, AreaChart } from '@/components/charts'
```

#### 步骤2：检查数据初始化
确认数据已正确初始化：
```javascript
// 系统负载应该有默认值
const systemLoads = ref([
  { label: '1分钟', value: 45 },
  { label: '5分钟', value: 38 },
  { label: '15分钟', value: 42 }
])

// 网络数据应该有测试数据
const networkChartData = ref({
  labels: ['10:00', '10:05', '10:10', '10:15', '10:20'],
  datasets: [/* 数据集配置 */]
})
```

#### 步骤3：强制刷新数据
在组件中添加强制刷新逻辑：
```javascript
onMounted(async () => {
  // 强制更新图表数据
  await updateChartData()
})
```

#### 步骤4：检查图表组件Props
确认图表组件使用正确的props：
```vue
<!-- AreaChart 正确用法 -->
<AreaChart
  :data="networkChartData"
  height="300px"
  :show-legend="true"
  :show-grid="true"
  :show-tooltip="true"
/>

<!-- LineChart 正确用法 -->
<LineChart
  :data="cpuChartData"
  :x-axis-data="cpuChartLabels"
  height="300px"
  :show-legend="false"
  :show-grid="true"
  :show-tooltip="true"
  :smooth="true"
  :area="true"
/>
```

### 验证修复结果

#### 1. 系统负载显示
应该看到三个圆形进度条，分别显示1分钟、5分钟、15分钟的负载值。

#### 2. 网络流量图表
应该看到一个面积图，显示入站和出站流量的趋势线。

#### 3. 数据更新
点击"刷新数据"按钮，图表应该更新显示新的数据。

### 如果问题仍然存在

#### 1. 重新编译项目
```bash
npm run build
# 或
npm run dev
```

#### 2. 清除缓存
```bash
npm run clean
npm install
```

#### 3. 检查依赖版本
确认以下依赖版本兼容：
- echarts: ^5.x
- vue-echarts: ^6.x
- vue: ^3.x

#### 4. 回滚到工作版本
如果问题严重，可以考虑回滚到之前的工作版本，然后逐步应用修改。

### 联系支持

如果以上步骤都无法解决问题，请提供以下信息：
1. 浏览器控制台的完整错误日志
2. "调试数据"按钮输出的数据状态
3. 浏览器版本和操作系统信息
4. 项目依赖版本信息（package.json）

### 预防措施

1. **定期备份**：在进行重大修改前备份工作版本
2. **渐进式测试**：每次修改后立即测试功能
3. **版本控制**：使用Git等版本控制工具跟踪变更
4. **依赖锁定**：使用package-lock.json锁定依赖版本
