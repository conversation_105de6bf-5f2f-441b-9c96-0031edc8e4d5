# 前端性能监控功能修复计划

## 问题分析

### 当前问题
1. **PerformanceService网络错误**: mockApiCall方法设置了5%的随机错误率
2. **Performance store初始化失败**: 依赖的API调用失败导致store无法正常初始化
3. **store.getFrontendMetrics方法不存在**: PerformanceMonitoring.vue中调用了不存在的方法

### 根本原因
- performanceService.ts中的mockApiCall方法模拟了网络错误
- performance.ts store缺少getFrontendMetrics方法
- 组件中的方法调用不匹配

## 修复方案

### 方案1：完整修复（推荐）
- 降低mockApiCall错误率从5%到1%
- 添加重试机制（3次重试）
- 在performance store中添加getFrontendMetrics方法
- 改进错误处理和用户体验
- 保持代码的专业性和错误处理机制

### 方案2：简化修复
- 完全移除mockApiCall中的随机错误
- 直接返回成功数据
- 简化错误处理逻辑

## 执行计划

### 步骤1：修复performanceService.ts
- **文件**: `frontend/src/services/performanceService.ts`
- **函数**: `mockApiCall`方法
- **逻辑概要**: 
  - 将错误率从5%降低到1%
  - 添加重试机制，最多重试3次
  - 改进错误处理逻辑
- **预期结果**: 大幅减少网络错误，提高系统稳定性

### 步骤2：修复performance store
- **文件**: `frontend/src/stores/performance.ts`
- **函数/类**: 添加`getFrontendMetrics`方法
- **逻辑概要**:
  - 在store的return对象中添加getFrontendMetrics方法
  - 方法直接调用performanceService.getFrontendMetrics()
  - 添加错误处理
- **预期结果**: 解决方法不存在的错误

### 步骤3：修复PerformanceMonitoring.vue
- **文件**: `frontend/src/views/system-monitoring/PerformanceMonitoring.vue`
- **函数**: `refreshFrontendMetrics`方法
- **逻辑概要**:
  - 修正方法调用，使用正确的store方法
  - 添加错误处理和用户反馈
- **预期结果**: 前端性能刷新功能正常工作

### 步骤4：改进错误处理
- **文件**: 所有相关文件
- **逻辑概要**:
  - 添加更好的错误边界处理
  - 改进用户错误提示
  - 添加加载状态指示
- **预期结果**: 更好的用户体验

## 技术细节

### 重试机制实现
```typescript
private async mockApiCall<T>(endpoint: string, mockData: T, retries = 3): Promise<T> {
  for (let i = 0; i <= retries; i++) {
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 100))
      
      // 降低错误率到1%
      if (Math.random() < 0.01) {
        throw new Error('Network error')
      }
      
      return mockData
    } catch (error) {
      if (i === retries) {
        throw error
      }
      // 重试前等待
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

### Store方法添加
```typescript
// 在usePerformanceStore的return对象中添加
getFrontendMetrics() {
  try {
    return performanceService.getFrontendMetrics()
  } catch (error) {
    console.error('获取前端性能数据失败:', error)
    return null
  }
}
```

## 验证计划

1. **功能测试**: 验证前端性能监控页面正常加载
2. **错误处理测试**: 验证网络错误时的重试机制
3. **用户体验测试**: 验证加载状态和错误提示
4. **性能测试**: 验证修复后的性能影响

## 风险评估

- **低风险**: 修改主要集中在错误处理和方法调用
- **向后兼容**: 不影响现有功能
- **可回滚**: 修改相对简单，易于回滚

## 预期效果

- 解决所有当前的错误问题
- 提高系统稳定性和用户体验
- 保持代码的专业性和可维护性
- 为未来扩展奠定良好基础