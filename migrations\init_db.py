"""WisCude User Management System - Database Initialization"""
from sqlalchemy import create_engine, text
from backend.app.core.database import Base, enhanced_db_manager
from backend.app.core.config import settings
from backend.app.models import AdminUser
import sys
import logging

logger = logging.getLogger(__name__)

def init_database():
    """Initialize database with tables"""
    try:
        # 初始化数据库管理器
        print(f"正在连接数据库: {settings.DATABASE_URL}")
        
        # 初始化数据库连接
        if not enhanced_db_manager.init_database():
            print("数据库初始化失败")
            sys.exit(1)
            
        print("数据库连接成功!")
        
        # 创建所有表
        Base.metadata.create_all(bind=enhanced_db_manager.engine)
        print("数据库表创建成功!")
        
        # 创建默认管理员用户
        with next(enhanced_db_manager.get_db()) as db_session:
            existing_admin = db_session.query(AdminUser).filter(
                AdminUser.username == 'admin'
            ).first()
            
            if not existing_admin:
                admin = AdminUser(
                    username='admin',
                    email='<EMAIL>',
                    full_name='系统管理员',
                    is_superuser=True
                )
                admin.set_password('admin123')
                
                db_session.add(admin)
                db_session.commit()
                
                print("\n默认管理员账户创建成功:")
                print("用户名: admin")
                print("密码: admin123")
                print("请在首次登录后修改默认密码!")
            else:
                print("\n默认管理员账户已存在，跳过创建。")
                
    except Exception as e:
        logger.error(f"初始化数据库时出错: {e}")
        print(f"初始化数据库时出错: {e}")
        sys.exit(1)

if __name__ == '__main__':
    print("开始初始化 WisCude 管理系统数据库...")
    init_database()
    print("\n数据库初始化完成! 现在可以运行应用: python run.py")
