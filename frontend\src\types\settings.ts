// 系统设置相关类型定义

export interface SystemSettings {
  // 基本系统配置
  app_name: string
  app_version: string
  app_description: string
  timezone: string
  language: string

  // 数据同步设置
  sync_interval_minutes: number
  sync_batch_size: number
  auto_sync_enabled: boolean
  
  // 安全设置
  secret_key?: string
  jwt_algorithm: string
  access_token_expire_minutes: number
  refresh_token_expire_days: number
  session_timeout_minutes: number
  max_login_attempts: number
  lockout_duration_minutes: number
  password_min_length: number
  password_require_uppercase: boolean
  password_require_lowercase: boolean
  password_require_numbers: boolean
  password_require_special: boolean
  two_factor_auth_enabled: boolean
  
  // 邮件配置
  smtp_host?: string
  smtp_port: number
  smtp_user?: string
  smtp_password?: string
  smtp_tls: boolean
  smtp_ssl: boolean
  email_from?: string
  email_from_name?: string
  email_templates: Record<string, EmailTemplate>
  
  // 日志配置
  log_level: string
  log_file_path: string
  log_retention_days: number
  log_max_size_mb: number
  
  // 用户界面设置
  ui_theme: string
  ui_primary_color: string
  ui_layout_mode: string
  ui_sidebar_collapsed: boolean
  ui_show_breadcrumb: boolean
  ui_show_tags_view: boolean
  default_page_size: number
  max_page_size: number
  dashboard_widgets: string[]
  
  // AI配置
  ai_configs: AIModelConfig[]
  default_ai_model?: string
  ai_enabled: boolean

  // 其他配置
  extra_config?: Record<string, any>

  // 元数据
  created_at?: string
  updated_at?: string
  updated_by?: string
}

export interface EmailTemplate {
  subject: string
  content: string
  variables: string[]
}

export interface LogFile {
  name: string
  size: number
  modified: string
  path: string
}

export interface SettingsCategory {
  key: string
  name: string
  icon: string
  description?: string
}

export interface SettingsTestRequest {
  test_type: 'database' | 'email'
  config: Record<string, any>
}

export interface SettingsTestResult {
  success: boolean
  message: string
}

export interface SettingsImportRequest {
  settings: Record<string, any>
  overwrite: boolean
}

export interface SettingsExportResponse {
  settings: Record<string, any>
  exported_at: string
  version: string
}

// AI模型配置
export interface AIModelConfig {
  id: string
  name: string
  provider: AIProvider
  display_name: string
  description: string
  api_key: string
  api_endpoint: string
  model_name: string
  parameters: AIModelParameters
  enabled: boolean
  is_default: boolean
  created_at?: string
  updated_at?: string
}

// AI服务提供商
export enum AIProvider {
  QWEN = 'qwen',
  DEEPSEEK = 'deepseek',
  OPENAI = 'openai',
  KIMI = 'kimi',
  DOUBAO = 'doubao',
  CLAUDE = 'claude',
  GEMINI = 'gemini',
  BAICHUAN = 'baichuan',
  CHATGLM = 'chatglm',
  WENXIN = 'wenxin',
  SPARK = 'spark',
  MINIMAX = 'minimax',
  CUSTOM = 'custom'
}

// AI模型参数
export interface AIModelParameters {
  temperature: number
  max_tokens: number
  top_p: number
  frequency_penalty: number
  presence_penalty: number
  stream: boolean
  timeout: number
  retry_count: number
  custom_params?: Record<string, any>
}

// AI连接测试请求
export interface AITestRequest {
  model_id: string
  test_message?: string
}

// AI连接测试结果
export interface AITestResult {
  success: boolean
  message: string
  response_time?: number
  model_info?: {
    name: string
    version: string
    capabilities: string[]
  }
  error_details?: string
}

// AI模型预设配置
export interface AIModelPreset {
  provider: AIProvider
  name: string
  display_name: string
  description: string
  api_endpoint: string
  model_name: string
  default_parameters: AIModelParameters
  required_fields: string[]
  documentation_url?: string
}

// 设置表单验证规则
export interface SettingsFormRules {
  [key: string]: Array<{
    required?: boolean
    message: string
    trigger?: string | string[]
    min?: number
    max?: number
    pattern?: RegExp
    validator?: (rule: any, value: any, callback: any) => void
  }>
}

// 设置分组配置
export interface SettingsGroup {
  key: string
  title: string
  icon: string
  fields: SettingsField[]
}

export interface SettingsField {
  key: string
  label: string
  type: 'input' | 'number' | 'switch' | 'select' | 'textarea' | 'password' | 'color' | 'checkbox'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  min?: number
  max?: number
  step?: number
  rows?: number
  sensitive?: boolean
  testable?: boolean
  description?: string
  required?: boolean
  validator?: (value: any) => boolean | string
}

// AI模型预设配置
export const AI_MODEL_PRESETS: AIModelPreset[] = [
  {
    provider: AIProvider.QWEN,
    name: 'qwen-turbo',
    display_name: '通义千问 Turbo',
    description: '阿里云通义千问快速模型，适合日常对话和简单任务',
    api_endpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    model_name: 'qwen-turbo',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://help.aliyun.com/zh/dashscope/'
  },
  {
    provider: AIProvider.DEEPSEEK,
    name: 'deepseek-chat',
    display_name: 'DeepSeek Chat',
    description: 'DeepSeek 对话模型，擅长代码生成和逻辑推理',
    api_endpoint: 'https://api.deepseek.com/v1/chat/completions',
    model_name: 'deepseek-chat',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 4000,
      top_p: 0.95,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://platform.deepseek.com/api-docs/'
  },
  {
    provider: AIProvider.OPENAI,
    name: 'gpt-3.5-turbo',
    display_name: 'OpenAI GPT-3.5 Turbo',
    description: 'OpenAI GPT-3.5 Turbo 模型，平衡性能和成本',
    api_endpoint: 'https://api.openai.com/v1/chat/completions',
    model_name: 'gpt-3.5-turbo',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 4000,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://platform.openai.com/docs/api-reference'
  },
  {
    provider: AIProvider.KIMI,
    name: 'moonshot-v1-8k',
    display_name: 'Kimi Chat 8K',
    description: 'Moonshot AI Kimi 模型，支持长文本处理',
    api_endpoint: 'https://api.moonshot.cn/v1/chat/completions',
    model_name: 'moonshot-v1-8k',
    default_parameters: {
      temperature: 0.3,
      max_tokens: 8000,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://platform.moonshot.cn/docs/api-reference'
  },
  {
    provider: AIProvider.DOUBAO,
    name: 'doubao-lite-4k',
    display_name: '豆包 Lite 4K',
    description: '字节跳动豆包模型，适合轻量级应用',
    api_endpoint: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    model_name: 'doubao-lite-4k',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 4000,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://www.volcengine.com/docs/82379'
  },
  {
    provider: AIProvider.CLAUDE,
    name: 'claude-3-haiku',
    display_name: 'Claude 3 Haiku',
    description: 'Anthropic Claude 3 Haiku 模型，快速响应',
    api_endpoint: 'https://api.anthropic.com/v1/messages',
    model_name: 'claude-3-haiku-20240307',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 4000,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://docs.anthropic.com/claude/reference'
  },
  {
    provider: AIProvider.CLAUDE,
    name: 'claude-3-sonnet',
    display_name: 'Claude 3 Sonnet',
    description: 'Anthropic Claude 3 Sonnet 模型，平衡性能',
    api_endpoint: 'https://api.anthropic.com/v1/messages',
    model_name: 'claude-3-sonnet-20240229',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 4000,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://docs.anthropic.com/claude/reference'
  },
  {
    provider: AIProvider.GEMINI,
    name: 'gemini-pro',
    display_name: 'Google Gemini Pro',
    description: 'Google Gemini Pro 模型，多模态能力',
    api_endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
    model_name: 'gemini-pro',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://ai.google.dev/docs'
  },
  {
    provider: AIProvider.BAICHUAN,
    name: 'baichuan2-turbo',
    display_name: '百川2 Turbo',
    description: '百川智能 Baichuan2 Turbo 模型',
    api_endpoint: 'https://api.baichuan-ai.com/v1/chat/completions',
    model_name: 'Baichuan2-Turbo',
    default_parameters: {
      temperature: 0.3,
      max_tokens: 2048,
      top_p: 0.85,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://platform.baichuan-ai.com/docs/api'
  },
  {
    provider: AIProvider.CHATGLM,
    name: 'chatglm-turbo',
    display_name: 'ChatGLM Turbo',
    description: '智谱AI ChatGLM Turbo 模型',
    api_endpoint: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model_name: 'chatglm_turbo',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.7,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://open.bigmodel.cn/dev/api'
  },
  {
    provider: AIProvider.WENXIN,
    name: 'ernie-bot-turbo',
    display_name: '文心一言 Turbo',
    description: '百度文心一言 ERNIE Bot Turbo 模型',
    api_endpoint: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant',
    model_name: 'ERNIE-Bot-turbo',
    default_parameters: {
      temperature: 0.8,
      max_tokens: 2048,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://cloud.baidu.com/doc/WENXINWORKSHOP/index.html'
  },
  {
    provider: AIProvider.SPARK,
    name: 'spark-lite',
    display_name: '讯飞星火 Lite',
    description: '科大讯飞星火认知大模型 Lite 版本',
    api_endpoint: 'wss://spark-api.xf-yun.com/v1.1/chat',
    model_name: 'general',
    default_parameters: {
      temperature: 0.5,
      max_tokens: 2048,
      top_p: 0.7,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://www.xfyun.cn/doc/spark/Web.html'
  },
  {
    provider: AIProvider.MINIMAX,
    name: 'abab5.5-chat',
    display_name: 'MiniMax abab5.5',
    description: 'MiniMax abab5.5 对话模型',
    api_endpoint: 'https://api.minimax.chat/v1/text/chatcompletion_pro',
    model_name: 'abab5.5-chat',
    default_parameters: {
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.95,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    required_fields: ['api_key'],
    documentation_url: 'https://api.minimax.chat/document/guides/chat-model'
  }
]

// 预定义的设置分组
export const SETTINGS_GROUPS: SettingsGroup[] = [
  {
    key: 'basic',
    title: '基本设置',
    icon: 'Setting',
    fields: [
      {
        key: 'app_name',
        label: '系统名称',
        type: 'input',
        placeholder: '请输入系统名称',
        required: true
      },
      {
        key: 'app_version',
        label: '系统版本',
        type: 'input',
        placeholder: '请输入版本号',
        required: true
      },
      {
        key: 'app_description',
        label: '系统描述',
        type: 'textarea',
        placeholder: '请输入系统描述',
        rows: 3
      },
      {
        key: 'timezone',
        label: '时区设置',
        type: 'select',
        options: [
          { label: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
          { label: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
          { label: '纽约时间 (UTC-5)', value: 'America/New_York' },
          { label: '伦敦时间 (UTC+0)', value: 'Europe/London' },
          { label: 'UTC 标准时间', value: 'UTC' }
        ],
        required: true
      },
      {
        key: 'language',
        label: '默认语言',
        type: 'select',
        options: [
          { label: '简体中文', value: 'zh-CN' },
          { label: 'English', value: 'en-US' }
        ],
        required: true
      }
    ]
  },

  {
    key: 'sync',
    title: '数据同步',
    icon: 'Refresh',
    fields: [
      {
        key: 'sync_interval_minutes',
        label: '同步间隔(分钟)',
        type: 'number',
        min: 1,
        max: 1440,
        description: '自动同步的时间间隔'
      },
      {
        key: 'sync_batch_size',
        label: '批次大小',
        type: 'number',
        min: 100,
        max: 10000,
        description: '每次同步处理的记录数量'
      },
      {
        key: 'auto_sync_enabled',
        label: '启用自动同步',
        type: 'switch',
        description: '是否启用定时自动同步'
      }
    ]
  },
  {
    key: 'security',
    title: '安全设置',
    icon: 'Lock',
    fields: [
      {
        key: 'access_token_expire_minutes',
        label: '访问令牌过期时间(分钟)',
        type: 'number',
        min: 5,
        max: 1440,
        description: '用户登录后访问令牌的有效期'
      },
      {
        key: 'refresh_token_expire_days',
        label: '刷新令牌过期时间(天)',
        type: 'number',
        min: 1,
        max: 30,
        description: '刷新令牌的有效期'
      },
      {
        key: 'session_timeout_minutes',
        label: '会话超时时间(分钟)',
        type: 'number',
        min: 5,
        max: 480,
        description: '用户无操作后自动退出的时间'
      },
      {
        key: 'max_login_attempts',
        label: '最大登录尝试次数',
        type: 'number',
        min: 3,
        max: 10,
        description: '账户锁定前允许的最大失败登录次数'
      },
      {
        key: 'lockout_duration_minutes',
        label: '账户锁定时长(分钟)',
        type: 'number',
        min: 5,
        max: 1440,
        description: '账户被锁定后的解锁时间'
      },
      {
        key: 'password_min_length',
        label: '密码最小长度',
        type: 'number',
        min: 6,
        max: 32,
        description: '用户密码的最小字符数'
      },
      {
        key: 'password_require_uppercase',
        label: '密码需要大写字母',
        type: 'switch',
        description: '密码必须包含至少一个大写字母'
      },
      {
        key: 'password_require_lowercase',
        label: '密码需要小写字母',
        type: 'switch',
        description: '密码必须包含至少一个小写字母'
      },
      {
        key: 'password_require_numbers',
        label: '密码需要数字',
        type: 'switch',
        description: '密码必须包含至少一个数字'
      },
      {
        key: 'password_require_special',
        label: '密码需要特殊字符',
        type: 'switch',
        description: '密码必须包含至少一个特殊字符'
      },
      {
        key: 'two_factor_auth_enabled',
        label: '启用双因素认证',
        type: 'switch',
        description: '为管理员账户启用双因素认证'
      }
    ]
  },
  {
    key: 'email',
    title: '邮件服务',
    icon: 'Message',
    fields: [
      {
        key: 'smtp_host',
        label: 'SMTP服务器',
        type: 'input',
        placeholder: 'smtp.gmail.com',
        testable: true,
        description: 'SMTP服务器地址'
      },
      {
        key: 'smtp_port',
        label: 'SMTP端口',
        type: 'number',
        min: 1,
        max: 65535,
        placeholder: '587',
        description: 'SMTP服务器端口号'
      },
      {
        key: 'smtp_user',
        label: 'SMTP用户名',
        type: 'input',
        placeholder: '请输入邮箱地址',
        description: '用于SMTP认证的用户名'
      },
      {
        key: 'smtp_password',
        label: 'SMTP密码',
        type: 'password',
        placeholder: '请输入邮箱密码或应用密码',
        sensitive: true,
        description: '用于SMTP认证的密码'
      },
      {
        key: 'smtp_tls',
        label: '启用TLS',
        type: 'switch',
        description: '使用TLS加密连接'
      },
      {
        key: 'smtp_ssl',
        label: '启用SSL',
        type: 'switch',
        description: '使用SSL加密连接'
      },
      {
        key: 'email_from',
        label: '发件人邮箱',
        type: 'input',
        placeholder: '<EMAIL>',
        description: '系统邮件的发件人地址'
      },
      {
        key: 'email_from_name',
        label: '发件人名称',
        type: 'input',
        placeholder: 'WisCude 系统',
        description: '系统邮件的发件人显示名称'
      }
    ]
  },
  {
    key: 'logging',
    title: '日志信息',
    icon: 'Document',
    fields: [
      {
        key: 'log_level',
        label: '日志级别',
        type: 'select',
        options: [
          { label: 'DEBUG - 调试信息', value: 'DEBUG' },
          { label: 'INFO - 一般信息', value: 'INFO' },
          { label: 'WARNING - 警告信息', value: 'WARNING' },
          { label: 'ERROR - 错误信息', value: 'ERROR' },
          { label: 'CRITICAL - 严重错误', value: 'CRITICAL' }
        ],
        description: '设置系统记录的最低日志级别'
      },
      {
        key: 'log_file_path',
        label: '日志文件路径',
        type: 'input',
        placeholder: 'logs/wiscude-admin.log',
        description: '日志文件的存储路径'
      },
      {
        key: 'log_retention_days',
        label: '日志保留天数',
        type: 'number',
        min: 1,
        max: 365,
        description: '日志文件的保留时间，超期自动删除'
      },
      {
        key: 'log_max_size_mb',
        label: '单个日志文件最大大小(MB)',
        type: 'number',
        min: 1,
        max: 1000,
        description: '单个日志文件的最大大小，超过后自动轮转'
      }
    ]
  },
  {
    key: 'ui',
    title: '界面设置',
    icon: 'Monitor',
    fields: [
      {
        key: 'ui_theme',
        label: '界面主题',
        type: 'select',
        options: [
          { label: '浅色主题', value: 'light' },
          { label: '深色主题', value: 'dark' },
          { label: '自动切换', value: 'auto' }
        ],
        description: '选择系统界面的主题风格'
      },
      {
        key: 'ui_primary_color',
        label: '主题色彩',
        type: 'color',
        description: '自定义系统的主题颜色'
      },
      {
        key: 'ui_layout_mode',
        label: '布局模式',
        type: 'select',
        options: [
          { label: '经典布局', value: 'classic' },
          { label: '顶部导航', value: 'top-nav' },
          { label: '混合布局', value: 'mix' }
        ],
        description: '选择系统的整体布局模式'
      },
      {
        key: 'ui_sidebar_collapsed',
        label: '默认收起侧边栏',
        type: 'switch',
        description: '系统启动时是否默认收起侧边栏'
      },
      {
        key: 'ui_show_breadcrumb',
        label: '显示面包屑导航',
        type: 'switch',
        description: '是否在页面顶部显示面包屑导航'
      },
      {
        key: 'ui_show_tags_view',
        label: '显示标签页',
        type: 'switch',
        description: '是否显示页面标签页功能'
      },
      {
        key: 'default_page_size',
        label: '默认分页大小',
        type: 'number',
        min: 10,
        max: 100,
        description: '列表页面的默认每页显示条数'
      },
      {
        key: 'max_page_size',
        label: '最大分页大小',
        type: 'number',
        min: 50,
        max: 500,
        description: '列表页面允许的最大每页显示条数'
      },
      {
        key: 'dashboard_widgets',
        label: '首页显示组件',
        type: 'checkbox',
        options: [
          { label: '用户统计', value: 'user-stats' },
          { label: '学习数据', value: 'study-stats' },
          { label: '系统状态', value: 'system-status' },
          { label: '最近活动', value: 'recent-activities' },
          { label: '数据图表', value: 'charts' },
          { label: '快速操作', value: 'quick-actions' }
        ],
        description: '选择在首页显示的功能组件'
      }
    ]
  },
  {
    key: 'ai',
    title: 'AI配置',
    icon: 'ChatDotRound',
    fields: [
      {
        key: 'ai_enabled',
        label: '启用AI功能',
        type: 'switch',
        description: '是否启用系统AI功能模块'
      },
      {
        key: 'default_ai_model',
        label: '默认AI模型',
        type: 'select',
        options: [], // 动态加载已配置的模型
        description: '系统默认使用的AI模型'
      }
    ]
  }
]
