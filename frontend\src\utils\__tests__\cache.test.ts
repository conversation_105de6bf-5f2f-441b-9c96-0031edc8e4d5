/**
 * 缓存工具测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { cache, PersistentCache } from '../cache'

describe('Cache', () => {
  beforeEach(() => {
    cache.clear()
  })

  describe('基本功能', () => {
    it('应该能够设置和获取缓存', () => {
      const key = 'test-key'
      const value = { data: 'test-data' }
      
      cache.set(key, value)
      const result = cache.get(key)
      
      expect(result).toEqual(value)
    })

    it('应该在过期后返回null', async () => {
      const key = 'test-key'
      const value = 'test-value'
      const ttl = 100 // 100ms
      
      cache.set(key, value, ttl)
      
      // 立即获取应该有值
      expect(cache.get(key)).toBe(value)
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 150))
      
      // 过期后应该返回null
      expect(cache.get(key)).toBeNull()
    })

    it('应该能够删除缓存', () => {
      const key = 'test-key'
      const value = 'test-value'
      
      cache.set(key, value)
      expect(cache.get(key)).toBe(value)
      
      cache.delete(key)
      expect(cache.get(key)).toBeNull()
    })

    it('应该能够清空所有缓存', () => {
      cache.set('key1', 'value1')
      cache.set('key2', 'value2')
      
      expect(cache.get('key1')).toBe('value1')
      expect(cache.get('key2')).toBe('value2')
      
      cache.clear()
      
      expect(cache.get('key1')).toBeNull()
      expect(cache.get('key2')).toBeNull()
    })
  })

  describe('统计功能', () => {
    it('应该返回正确的统计信息', () => {
      cache.set('key1', 'value1', 1000)
      cache.set('key2', 'value2', 100) // 很快过期
      
      const stats = cache.getStats()
      
      expect(stats.total).toBe(2)
      expect(stats.size).toBeGreaterThan(0)
    })

    it('应该正确检查缓存是否存在', () => {
      const key = 'test-key'
      
      expect(cache.has(key)).toBe(false)
      
      cache.set(key, 'value')
      expect(cache.has(key)).toBe(true)
      
      cache.delete(key)
      expect(cache.has(key)).toBe(false)
    })
  })

  describe('清理功能', () => {
    it('应该能够清理过期缓存', async () => {
      cache.set('key1', 'value1', 1000) // 不会过期
      cache.set('key2', 'value2', 50)   // 很快过期
      
      // 等待key2过期
      await new Promise(resolve => setTimeout(resolve, 100))
      
      cache.cleanup()
      
      expect(cache.has('key1')).toBe(true)
      expect(cache.has('key2')).toBe(false)
    })
  })
})

describe('PersistentCache', () => {
  let persistentCache: PersistentCache

  beforeEach(() => {
    // 模拟localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn()
    }
    
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock
    })

    persistentCache = new PersistentCache('test_')
  })

  it('应该能够设置持久化缓存', () => {
    const key = 'test-key'
    const value = { data: 'test-data' }
    
    persistentCache.set(key, value)
    
    expect(localStorage.setItem).toHaveBeenCalledWith(
      'test_' + key,
      expect.stringContaining(JSON.stringify(value))
    )
  })

  it('应该能够获取持久化缓存', () => {
    const key = 'test-key'
    const value = { data: 'test-data' }
    const cacheItem = {
      data: value,
      timestamp: Date.now(),
      expiry: Date.now() + 60000
    }
    
    vi.mocked(localStorage.getItem).mockReturnValue(JSON.stringify(cacheItem))
    
    const result = persistentCache.get(key)
    
    expect(result).toEqual(value)
    expect(localStorage.getItem).toHaveBeenCalledWith('test_' + key)
  })

  it('应该在获取过期缓存时返回null', () => {
    const key = 'test-key'
    const expiredCacheItem = {
      data: 'test-data',
      timestamp: Date.now() - 120000,
      expiry: Date.now() - 60000 // 已过期
    }
    
    vi.mocked(localStorage.getItem).mockReturnValue(JSON.stringify(expiredCacheItem))
    
    const result = persistentCache.get(key)
    
    expect(result).toBeNull()
    expect(localStorage.removeItem).toHaveBeenCalledWith('test_' + key)
  })

  it('应该能够删除持久化缓存', () => {
    const key = 'test-key'
    
    persistentCache.delete(key)
    
    expect(localStorage.removeItem).toHaveBeenCalledWith('test_' + key)
  })

  it('应该能够清空所有持久化缓存', () => {
    // 模拟localStorage中有多个缓存项
    Object.defineProperty(window, 'localStorage', {
      value: {
        ...localStorage,
        keys: vi.fn().mockReturnValue(['test_key1', 'test_key2', 'other_key'])
      }
    })
    
    Object.keys = vi.fn().mockReturnValue(['test_key1', 'test_key2', 'other_key'])
    
    persistentCache.clear()
    
    expect(localStorage.removeItem).toHaveBeenCalledWith('test_key1')
    expect(localStorage.removeItem).toHaveBeenCalledWith('test_key2')
    expect(localStorage.removeItem).not.toHaveBeenCalledWith('other_key')
  })
})
