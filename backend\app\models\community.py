"""
社区模块数据模型
"""
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from app.core.database import Base


class LearningResource(Base):
    """学习资源模型"""
    __tablename__ = "learning_resources"

    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="资源标题")
    description = Column(Text, comment="资源描述")
    type = Column(String(20), nullable=False, comment="资源类型")
    url = Column(String(500), nullable=False, comment="资源链接")
    thumbnail_url = Column(String(500), comment="缩略图链接")
    file_size = Column(Integer, comment="文件大小(字节)")
    duration = Column(Integer, comment="时长(秒)")
    subject = Column(String(50), nullable=False, comment="学科")
    grade_level = Column(String(20), nullable=False, comment="年级")
    tags = Column(JSON, comment="标签列表")
    author_id = Column(String(36), nullable=False, comment="作者ID")
    author_name = Column(String(100), nullable=False, comment="作者姓名")
    status = Column(String(20), default="pending", comment="审核状态")
    download_count = Column(Integer, default=0, comment="下载次数")
    view_count = Column(Integer, default=0, comment="浏览次数")
    like_count = Column(Integer, default=0, comment="点赞次数")
    rating = Column(Float, default=0.0, comment="评分")
    rating_count = Column(Integer, default=0, comment="评分人数")
    is_approved = Column(Boolean, default=False, comment="是否已审核")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    approved_at = Column(DateTime, comment="审核时间")
    approved_by = Column(String(36), comment="审核人ID")


class StudyRoom(Base):
    """自习室模型"""
    __tablename__ = "study_rooms"

    id = Column(String(36), primary_key=True)
    name = Column(String(100), nullable=False, comment="自习室名称")
    description = Column(Text, comment="自习室描述")
    owner_id = Column(String(36), nullable=False, comment="房主ID")
    owner_name = Column(String(100), nullable=False, comment="房主姓名")
    max_members = Column(Integer, default=50, comment="最大成员数")
    current_members = Column(Integer, default=0, comment="当前成员数")
    is_public = Column(Boolean, default=True, comment="是否公开")
    password = Column(String(100), comment="房间密码")
    tags = Column(JSON, comment="标签列表")
    rules = Column(Text, comment="房间规则")
    status = Column(String(20), default="active", comment="房间状态")
    total_study_time = Column(Integer, default=0, comment="总学习时长(分钟)")
    average_study_time = Column(Integer, default=0, comment="平均学习时长(分钟)")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class StudyRoomMember(Base):
    """自习室成员模型"""
    __tablename__ = "study_room_members"

    id = Column(String(36), primary_key=True)
    room_id = Column(String(36), ForeignKey("study_rooms.id"), nullable=False, comment="自习室ID")
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_avatar = Column(String(500), comment="用户头像")
    joined_at = Column(DateTime, default=datetime.utcnow, comment="加入时间")
    total_study_time = Column(Integer, default=0, comment="总学习时长(分钟)")
    weekly_study_time = Column(Integer, default=0, comment="本周学习时长(分钟)")
    is_online = Column(Boolean, default=False, comment="是否在线")
    last_active_at = Column(DateTime, default=datetime.utcnow, comment="最后活跃时间")

    # 关系
    room = relationship("StudyRoom", backref="members")


class Topic(Base):
    """话题模型"""
    __tablename__ = "topics"

    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="话题标题")
    content = Column(Text, nullable=False, comment="话题内容")
    author_id = Column(String(36), nullable=False, comment="作者ID")
    author_name = Column(String(100), nullable=False, comment="作者姓名")
    author_avatar = Column(String(500), comment="作者头像")
    category_id = Column(String(36), nullable=False, comment="分类ID")
    category_name = Column(String(100), nullable=False, comment="分类名称")
    tags = Column(JSON, comment="标签列表")
    status = Column(String(20), default="active", comment="话题状态")
    view_count = Column(Integer, default=0, comment="浏览次数")
    like_count = Column(Integer, default=0, comment="点赞次数")
    reply_count = Column(Integer, default=0, comment="回复次数")
    last_reply_at = Column(DateTime, comment="最后回复时间")
    last_reply_by = Column(String(100), comment="最后回复人")
    is_pinned = Column(Boolean, default=False, comment="是否置顶")
    is_hot = Column(Boolean, default=False, comment="是否热门")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class TopicReply(Base):
    """话题回复模型"""
    __tablename__ = "topic_replies"

    id = Column(String(36), primary_key=True)
    topic_id = Column(String(36), ForeignKey("topics.id"), nullable=False, comment="话题ID")
    content = Column(Text, nullable=False, comment="回复内容")
    author_id = Column(String(36), nullable=False, comment="作者ID")
    author_name = Column(String(100), nullable=False, comment="作者姓名")
    author_avatar = Column(String(500), comment="作者头像")
    like_count = Column(Integer, default=0, comment="点赞次数")
    is_liked = Column(Boolean, default=False, comment="是否已点赞")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    topic = relationship("Topic", backref="replies")


class Tribe(Base):
    """部落模型"""
    __tablename__ = "tribes"

    id = Column(String(36), primary_key=True)
    name = Column(String(100), nullable=False, comment="部落名称")
    description = Column(Text, comment="部落描述")
    avatar = Column(String(500), comment="部落头像")
    banner = Column(String(500), comment="部落横幅")
    founder_id = Column(String(36), nullable=False, comment="创始人ID")
    founder_name = Column(String(100), nullable=False, comment="创始人姓名")
    category = Column(String(50), nullable=False, comment="部落分类")
    level = Column(Integer, default=1, comment="部落等级")
    experience = Column(Integer, default=0, comment="经验值")
    member_count = Column(Integer, default=0, comment="成员数量")
    max_members = Column(Integer, default=100, comment="最大成员数")
    is_public = Column(Boolean, default=True, comment="是否公开")
    join_condition = Column(String(200), comment="加入条件")
    tags = Column(JSON, comment="标签列表")
    announcement = Column(Text, comment="部落公告")
    rules = Column(Text, comment="部落规则")
    total_points = Column(Integer, default=0, comment="总积分")
    weekly_points = Column(Integer, default=0, comment="本周积分")
    rank = Column(Integer, default=0, comment="部落排名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class TribeMember(Base):
    """部落成员模型"""
    __tablename__ = "tribe_members"

    id = Column(String(36), primary_key=True)
    tribe_id = Column(String(36), ForeignKey("tribes.id"), nullable=False, comment="部落ID")
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_avatar = Column(String(500), comment="用户头像")
    role = Column(String(20), default="member", comment="角色")
    contribution = Column(Integer, default=0, comment="贡献度")
    weekly_contribution = Column(Integer, default=0, comment="本周贡献")
    join_time = Column(DateTime, default=datetime.utcnow, comment="加入时间")
    last_active_time = Column(DateTime, default=datetime.utcnow, comment="最后活跃时间")
    title = Column(String(50), comment="部落内称号")

    # 关系
    tribe = relationship("Tribe", backref="members")


class MentorProfile(Base):
    """导师档案模型"""
    __tablename__ = "mentor_profiles"

    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, unique=True, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_avatar = Column(String(500), comment="用户头像")
    subjects = Column(JSON, comment="擅长学科")
    experience = Column(Text, comment="教学经验")
    achievements = Column(JSON, comment="成就列表")
    rating = Column(Float, default=0.0, comment="评分")
    rating_count = Column(Integer, default=0, comment="评分人数")
    student_count = Column(Integer, default=0, comment="学生数量")
    max_students = Column(Integer, default=10, comment="最大学生数")
    is_available = Column(Boolean, default=True, comment="是否可接收学生")
    introduction = Column(Text, comment="个人介绍")
    teaching_style = Column(Text, comment="教学风格")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class MentorshipRelation(Base):
    """师徒关系模型"""
    __tablename__ = "mentorship_relations"

    id = Column(String(36), primary_key=True)
    mentor_id = Column(String(36), ForeignKey("mentor_profiles.id"), nullable=False, comment="导师ID")
    mentor_name = Column(String(100), nullable=False, comment="导师姓名")
    student_id = Column(String(36), nullable=False, comment="学生ID")
    student_name = Column(String(100), nullable=False, comment="学生姓名")
    subject = Column(String(50), nullable=False, comment="学科")
    goals = Column(Text, comment="学习目标")
    status = Column(String(20), default="pending", comment="关系状态")
    progress = Column(Integer, default=0, comment="进度百分比")
    start_date = Column(DateTime, default=datetime.utcnow, comment="开始时间")
    end_date = Column(DateTime, comment="结束时间")
    notes = Column(Text, comment="备注")
    rating = Column(Float, comment="评分")
    feedback = Column(Text, comment="反馈")

    # 关系
    mentor = relationship("MentorProfile", backref="students")


class Experience(Base):
    """学霸经验模型"""
    __tablename__ = "experiences"

    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="经验标题")
    content = Column(Text, nullable=False, comment="经验内容")
    author_id = Column(String(36), nullable=False, comment="作者ID")
    author_name = Column(String(100), nullable=False, comment="作者姓名")
    author_avatar = Column(String(500), comment="作者头像")
    author_level = Column(String(50), comment="作者等级")
    author_achievements = Column(JSON, comment="作者成就")
    subject = Column(String(50), nullable=False, comment="学科")
    tags = Column(JSON, comment="标签列表")
    images = Column(JSON, comment="图片列表")
    video_url = Column(String(500), comment="视频链接")
    view_count = Column(Integer, default=0, comment="浏览次数")
    like_count = Column(Integer, default=0, comment="点赞次数")
    comment_count = Column(Integer, default=0, comment="评论次数")
    share_count = Column(Integer, default=0, comment="分享次数")
    is_recommended = Column(Boolean, default=False, comment="是否推荐")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class Activity(Base):
    """活动竞赛模型"""
    __tablename__ = "activities"

    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="活动标题")
    description = Column(Text, nullable=False, comment="活动描述")
    type = Column(String(20), nullable=False, comment="活动类型")
    organizer_id = Column(String(36), nullable=False, comment="组织者ID")
    organizer_name = Column(String(100), nullable=False, comment="组织者姓名")
    subject = Column(String(50), comment="学科")
    start_date = Column(DateTime, nullable=False, comment="开始时间")
    end_date = Column(DateTime, nullable=False, comment="结束时间")
    registration_deadline = Column(DateTime, nullable=False, comment="报名截止时间")
    max_participants = Column(Integer, comment="最大参与人数")
    current_participants = Column(Integer, default=0, comment="当前参与人数")
    rules = Column(Text, comment="活动规则")
    prizes = Column(JSON, comment="奖品设置")
    status = Column(String(20), default="draft", comment="活动状态")
    is_public = Column(Boolean, default=True, comment="是否公开")
    tags = Column(JSON, comment="标签列表")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class ActivityParticipant(Base):
    """活动参与者模型"""
    __tablename__ = "activity_participants"

    id = Column(String(36), primary_key=True)
    activity_id = Column(String(36), ForeignKey("activities.id"), nullable=False, comment="活动ID")
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_avatar = Column(String(500), comment="用户头像")
    registered_at = Column(DateTime, default=datetime.utcnow, comment="报名时间")
    score = Column(Float, comment="得分")
    rank = Column(Integer, comment="排名")
    status = Column(String(20), default="registered", comment="参与状态")

    # 关系
    activity = relationship("Activity", backref="participants")


class Wish(Base):
    """心愿模型"""
    __tablename__ = "wishes"

    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_avatar = Column(String(500), comment="用户头像")
    content = Column(Text, nullable=False, comment="心愿内容")
    tags = Column(JSON, comment="标签列表")
    status = Column(String(20), default="active", comment="心愿状态")
    is_achieved = Column(Boolean, default=False, comment="是否已实现")
    support_count = Column(Integer, default=0, comment="支持次数")
    comment_count = Column(Integer, default=0, comment="评论次数")
    view_count = Column(Integer, default=0, comment="浏览次数")
    hot_score = Column(Float, default=0.0, comment="热度分数")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    achieved_at = Column(DateTime, comment="实现时间")


class WishComment(Base):
    """心愿评论模型"""
    __tablename__ = "wish_comments"

    id = Column(String(36), primary_key=True)
    wish_id = Column(String(36), ForeignKey("wishes.id"), nullable=False, comment="心愿ID")
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_avatar = Column(String(500), comment="用户头像")
    content = Column(Text, nullable=False, comment="评论内容")
    like_count = Column(Integer, default=0, comment="点赞次数")
    is_liked = Column(Boolean, default=False, comment="是否已点赞")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    # 关系
    wish = relationship("Wish", backref="comments")


class GameActivity(Base):
    """游戏活动模型"""
    __tablename__ = "game_activities"

    id = Column(String(36), primary_key=True)
    name = Column(String(100), nullable=False, comment="游戏名称")
    description = Column(Text, comment="游戏描述")
    type = Column(String(20), nullable=False, comment="游戏类型")
    rules = Column(Text, comment="游戏规则")
    rewards = Column(JSON, comment="奖励设置")
    participant_count = Column(Integer, default=0, comment="参与人数")
    is_active = Column(Boolean, default=True, comment="是否活跃")
    start_date = Column(DateTime, nullable=False, comment="开始时间")
    end_date = Column(DateTime, comment="结束时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class GameParticipant(Base):
    """游戏参与者模型"""
    __tablename__ = "game_participants"

    id = Column(String(36), primary_key=True)
    game_id = Column(String(36), ForeignKey("game_activities.id"), nullable=False, comment="游戏ID")
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_avatar = Column(String(500), comment="用户头像")
    score = Column(Float, default=0.0, comment="得分")
    rank = Column(Integer, comment="排名")
    achievements = Column(JSON, comment="成就列表")
    participated_at = Column(DateTime, default=datetime.utcnow, comment="参与时间")
    last_played_at = Column(DateTime, default=datetime.utcnow, comment="最后游戏时间")

    # 关系
    game = relationship("GameActivity", backref="participants")
