<template>
  <div class="questions-management">
    <div class="page-header">
      <h1>题目管理</h1>
      <p>管理题目内容、题型和答案解析，支持多种题型编辑</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="题目列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建题目
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedQuestions.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedQuestions.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
            <el-button @click="showImportDialog = true">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
            <el-button @click="exportQuestions">
              <el-icon><Download /></el-icon>
              导出题目
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索题目标题或内容"
              style="width: 200px"
              clearable
              @change="loadQuestions"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="categoryFilter" placeholder="分类筛选" style="width: 150px" @change="loadQuestions">
              <el-option label="全部分类" value="" />
              <el-option label="数学基础" value="math_basic" />
              <el-option label="语文阅读" value="chinese_reading" />
              <el-option label="英语语法" value="english_grammar" />
            </el-select>
            <el-select v-model="typeFilter" placeholder="题型筛选" style="width: 120px" @change="loadQuestions">
              <el-option label="全部题型" value="" />
              <el-option label="单选题" value="single_choice" />
              <el-option label="多选题" value="multiple_choice" />
              <el-option label="填空题" value="fill_blank" />
              <el-option label="简答题" value="essay" />
              <el-option label="判断题" value="true_false" />
            </el-select>
            <el-select v-model="difficultyFilter" placeholder="难度筛选" style="width: 100px" @change="loadQuestions">
              <el-option label="全部难度" value="" />
              <el-option label="简单" :value="1" />
              <el-option label="较易" :value="2" />
              <el-option label="中等" :value="3" />
              <el-option label="较难" :value="4" />
              <el-option label="困难" :value="5" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadQuestions">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>

        <!-- 题目列表 -->
        <el-table
          :data="questions"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="题目信息" min-width="350">
            <template #default="{ row }">
              <div class="question-info">
                <div class="question-title">{{ row.title }}</div>
                <div class="question-content">{{ row.content }}</div>
                <div class="question-meta">
                  <el-tag size="small" :type="getTypeTagType(row.question_type)">
                    {{ getTypeName(row.question_type) }}
                  </el-tag>
                  <el-tag size="small" type="info">{{ row.subject }}</el-tag>
                  <el-tag size="small" type="warning">{{ row.grade_level }}</el-tag>
                  <span class="difficulty-badge" :class="`difficulty-${row.difficulty}`">
                    {{ getDifficultyName(row.difficulty) }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="知识点" width="150">
            <template #default="{ row }">
              <div class="knowledge-points">
                <el-tag 
                  v-for="point in row.knowledge_points" 
                  :key="point" 
                  size="small" 
                  class="knowledge-tag"
                >
                  {{ point }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="分值" width="80">
            <template #default="{ row }">
              <span class="score-value">{{ row.score }}分</span>
            </template>
          </el-table-column>
          <el-table-column label="使用统计" width="120">
            <template #default="{ row }">
              <div class="usage-stats">
                <div class="stat-item">
                  <span class="stat-label">使用:</span>
                  <span class="stat-value">{{ row.usage_count }}次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">正确率:</span>
                  <span class="stat-value correct-rate">{{ (row.correct_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="质量评分" width="100">
            <template #default="{ row }">
              <div class="quality-score">
                <el-rate 
                  v-model="row.quality_score" 
                  :max="5" 
                  disabled 
                  show-score 
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getReviewStatusTagType(row.review_status)">
                {{ getReviewStatusName(row.review_status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="viewQuestion(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="link" size="small" @click="editQuestion(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="link" size="small" @click="reviewQuestion(row)" v-if="row.review_status === 'pending'">
                <el-icon><Check /></el-icon>
                审核
              </el-button>
              <el-button
                type="link"
                size="small"
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="toggleQuestionStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '归档' : '发布' }}
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deleteQuestion(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadQuestions"
            @current-change="loadQuestions"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="题型分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">题型分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="难度分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">难度分布图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="正确率趋势">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">正确率趋势图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑题目对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingQuestion ? '编辑题目' : '新建题目'"
      width="1000px"
      @close="resetForm"
    >
      <el-form :model="questionForm" :rules="questionRules" ref="questionFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="题目标题" prop="title">
              <el-input v-model="questionForm.title" placeholder="请输入题目标题" />
            </el-form-item>
            <el-form-item label="题目内容" prop="content">
              <el-input v-model="questionForm.content" type="textarea" rows="4" placeholder="请输入题目内容" />
            </el-form-item>
            <el-form-item label="题目类型" prop="question_type">
              <el-select v-model="questionForm.question_type" placeholder="请选择题目类型" @change="onQuestionTypeChange">
                <el-option label="单选题" value="single_choice" />
                <el-option label="多选题" value="multiple_choice" />
                <el-option label="填空题" value="fill_blank" />
                <el-option label="简答题" value="essay" />
                <el-option label="判断题" value="true_false" />
              </el-select>
            </el-form-item>
            <el-form-item label="分类" prop="category_id">
              <el-select v-model="questionForm.category_id" placeholder="请选择分类">
                <el-option label="数学基础" value="math_basic" />
                <el-option label="语文阅读" value="chinese_reading" />
                <el-option label="英语语法" value="english_grammar" />
              </el-select>
            </el-form-item>
            <el-form-item label="学科" prop="subject">
              <el-select v-model="questionForm.subject" placeholder="请选择学科">
                <el-option label="数学" value="数学" />
                <el-option label="语文" value="语文" />
                <el-option label="英语" value="英语" />
                <el-option label="物理" value="物理" />
                <el-option label="化学" value="化学" />
              </el-select>
            </el-form-item>
            <el-form-item label="年级" prop="grade_level">
              <el-select v-model="questionForm.grade_level" placeholder="请选择年级">
                <el-option label="小学一年级" value="小学一年级" />
                <el-option label="小学二年级" value="小学二年级" />
                <el-option label="小学三年级" value="小学三年级" />
                <el-option label="初中一年级" value="初中一年级" />
                <el-option label="初中二年级" value="初中二年级" />
                <el-option label="初中三年级" value="初中三年级" />
                <el-option label="高中一年级" value="高中一年级" />
                <el-option label="高中二年级" value="高中二年级" />
                <el-option label="高中三年级" value="高中三年级" />
              </el-select>
            </el-form-item>
            <el-form-item label="难度等级" prop="difficulty">
              <el-select v-model="questionForm.difficulty" placeholder="请选择难度等级">
                <el-option label="简单" :value="1" />
                <el-option label="较易" :value="2" />
                <el-option label="中等" :value="3" />
                <el-option label="较难" :value="4" />
                <el-option label="困难" :value="5" />
              </el-select>
            </el-form-item>
            <el-form-item label="知识点标签" prop="knowledge_points">
              <el-select 
                v-model="questionForm.knowledge_points" 
                multiple 
                filterable 
                allow-create 
                placeholder="请选择或输入知识点标签"
              >
                <el-option label="函数" value="函数" />
                <el-option label="方程" value="方程" />
                <el-option label="几何" value="几何" />
                <el-option label="概率" value="概率" />
              </el-select>
            </el-form-item>
            <el-form-item label="题目分值" prop="score">
              <el-input-number v-model="questionForm.score" :min="0.5" :max="100" :step="0.5" />
            </el-form-item>
            <el-form-item label="答题时间限制">
              <el-input-number v-model="questionForm.time_limit" :min="0" placeholder="秒，0表示无限制" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="选项设置" name="options" v-if="isChoiceQuestion">
            <div class="options-section">
              <div class="section-header">
                <span>题目选项</span>
                <el-button type="primary" size="small" @click="addOption">
                  <el-icon><Plus /></el-icon>
                  添加选项
                </el-button>
              </div>
              <div v-for="(option, index) in questionForm.options" :key="index" class="option-item">
                <div class="option-header">
                  <span class="option-label">选项 {{ String.fromCharCode(65 + index) }}</span>
                  <el-button type="link" size="small" class="danger" @click="removeOption(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-input v-model="option.text" placeholder="请输入选项内容" />
                <div class="option-actions">
                  <el-checkbox 
                    v-model="option.is_correct" 
                    @change="onOptionCorrectChange(index, $event)"
                  >
                    正确答案
                  </el-checkbox>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="答案解析" name="answer">
            <el-form-item label="正确答案" prop="correct_answer" v-if="!isChoiceQuestion">
              <el-input v-model="questionForm.correct_answer" type="textarea" rows="3" placeholder="请输入正确答案" />
            </el-form-item>
            <el-form-item label="答案解析" prop="answer_analysis">
              <el-input v-model="questionForm.answer_analysis" type="textarea" rows="5" placeholder="请输入答案解析" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="多媒体资源" name="media">
            <el-form-item label="题目图片">
              <div class="media-upload">
                <el-upload
                  class="image-uploader"
                  :show-file-list="false"
                  :before-upload="beforeImageUpload"
                  :http-request="uploadImage"
                  multiple
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    上传图片
                  </el-button>
                </el-upload>
                <div class="uploaded-images">
                  <div v-for="(image, index) in questionForm.images" :key="index" class="image-item">
                    <img :src="image" class="preview-image" />
                    <el-button type="link" size="small" class="remove-btn" @click="removeImage(index)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="音频资源">
              <el-input v-model="questionForm.audio_url" placeholder="音频文件URL" />
            </el-form-item>
            <el-form-item label="视频资源">
              <el-input v-model="questionForm.video_url" placeholder="视频文件URL" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveQuestion" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 题目详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="题目详情"
      width="800px"
    >
      <div class="question-detail" v-if="viewingQuestion">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">题目标题：</span>
              <span class="value">{{ viewingQuestion.title }}</span>
            </div>
            <div class="info-item">
              <span class="label">题目类型：</span>
              <span class="value">{{ getTypeName(viewingQuestion.question_type) }}</span>
            </div>
            <div class="info-item">
              <span class="label">学科：</span>
              <span class="value">{{ viewingQuestion.subject }}</span>
            </div>
            <div class="info-item">
              <span class="label">年级：</span>
              <span class="value">{{ viewingQuestion.grade_level }}</span>
            </div>
            <div class="info-item">
              <span class="label">难度：</span>
              <span class="value">{{ getDifficultyName(viewingQuestion.difficulty) }}</span>
            </div>
            <div class="info-item">
              <span class="label">分值：</span>
              <span class="value">{{ viewingQuestion.score }}分</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>题目内容</h3>
          <div class="question-content-detail">{{ viewingQuestion.content }}</div>
        </div>
        
        <div class="detail-section" v-if="viewingQuestion.options && viewingQuestion.options.length">
          <h3>选项</h3>
          <div class="options-detail">
            <div v-for="(option, index) in viewingQuestion.options" :key="index" class="option-detail">
              <span class="option-letter">{{ String.fromCharCode(65 + index) }}.</span>
              <span class="option-text">{{ option.text }}</span>
              <el-tag v-if="option.is_correct" type="success" size="small">正确答案</el-tag>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>答案解析</h3>
          <div class="answer-analysis-detail">{{ viewingQuestion.answer_analysis }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="showImportDialog"
      title="批量导入题目"
      width="600px"
    >
      <div class="import-section">
        <el-upload
          class="upload-demo"
          drag
          :before-upload="beforeImportUpload"
          :http-request="importQuestions"
          accept=".xlsx,.xls,.csv"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 Excel (.xlsx, .xls) 和 CSV 格式文件，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        <div class="import-template">
          <el-button type="link" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载导入模板
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Check, Upload, Download, UploadFilled
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedQuestions = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const showImportDialog = ref(false)
const editingQuestion = ref(null)
const viewingQuestion = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const categoryFilter = ref('')
const typeFilter = ref('')
const difficultyFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 题目列表
const questions = ref([
  {
    id: '1',
    title: '二次函数的图像与性质',
    content: '已知二次函数 f(x) = ax² + bx + c (a ≠ 0)，若函数的图像开口向上，且对称轴为 x = 2，最小值为 -1，求该函数的解析式。',
    question_type: 'single_choice',
    category_id: 'math_basic',
    subject: '数学',
    grade_level: '高中二年级',
    difficulty: 3,
    knowledge_points: ['二次函数', '函数图像', '对称轴'],
    score: 5.0,
    time_limit: 300,
    options: [
      { text: 'f(x) = x² - 4x + 3', is_correct: true },
      { text: 'f(x) = x² - 4x + 5', is_correct: false },
      { text: 'f(x) = 2x² - 8x + 7', is_correct: false },
      { text: 'f(x) = x² + 4x + 3', is_correct: false }
    ],
    correct_answer: 'A',
    answer_analysis: '根据题意，开口向上说明a>0，对称轴x=2，最小值-1。利用顶点式可得f(x)=a(x-2)²-1，将点代入求得a=1，所以f(x)=x²-4x+3。',
    images: [],
    audio_url: '',
    video_url: '',
    status: 'published',
    usage_count: 45,
    correct_rate: 0.78,
    quality_score: 4.2,
    review_status: 'approved',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const questionForm = reactive({
  title: '',
  content: '',
  question_type: 'single_choice',
  category_id: '',
  subject: '',
  grade_level: '',
  difficulty: 1,
  knowledge_points: [],
  score: 1.0,
  time_limit: 0,
  options: [
    { text: '', is_correct: false },
    { text: '', is_correct: false },
    { text: '', is_correct: false },
    { text: '', is_correct: false }
  ],
  correct_answer: '',
  answer_analysis: '',
  images: [],
  audio_url: '',
  video_url: ''
})

// 表单验证规则
const questionRules = {
  title: [
    { required: true, message: '请输入题目标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入题目内容', trigger: 'blur' }
  ],
  question_type: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '请选择学科', trigger: 'change' }
  ],
  grade_level: [
    { required: true, message: '请选择年级', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ]
}

// 计算属性
const isChoiceQuestion = computed(() => {
  return ['single_choice', 'multiple_choice', 'true_false'].includes(questionForm.question_type)
})

// 方法
const loadQuestions = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取题目列表
    console.log('Loading questions...')
  } catch (error) {
    ElMessage.error('加载题目列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedQuestions.value = selection
}

const getTypeName = (type) => {
  const types = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    fill_blank: '填空题',
    essay: '简答题',
    true_false: '判断题'
  }
  return types[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    single_choice: 'primary',
    multiple_choice: 'success',
    fill_blank: 'warning',
    essay: 'danger',
    true_false: 'info'
  }
  return types[type] || ''
}

const getDifficultyName = (level) => {
  const names = {
    1: '简单',
    2: '较易',
    3: '中等',
    4: '较难',
    5: '困难'
  }
  return names[level] || `难度${level}`
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const getReviewStatusName = (status) => {
  const statuses = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return statuses[status] || status
}

const getReviewStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewQuestion = (question) => {
  viewingQuestion.value = question
  showViewDialog.value = true
}

const editQuestion = (question) => {
  editingQuestion.value = question
  Object.assign(questionForm, question)
  showCreateDialog.value = true
}

const resetForm = () => {
  editingQuestion.value = null
  Object.assign(questionForm, {
    title: '',
    content: '',
    question_type: 'single_choice',
    category_id: '',
    subject: '',
    grade_level: '',
    difficulty: 1,
    knowledge_points: [],
    score: 1.0,
    time_limit: 0,
    options: [
      { text: '', is_correct: false },
      { text: '', is_correct: false },
      { text: '', is_correct: false },
      { text: '', is_correct: false }
    ],
    correct_answer: '',
    answer_analysis: '',
    images: [],
    audio_url: '',
    video_url: ''
  })
  formActiveTab.value = 'basic'
}

const onQuestionTypeChange = (type) => {
  if (type === 'true_false') {
    questionForm.options = [
      { text: '正确', is_correct: false },
      { text: '错误', is_correct: false }
    ]
  } else if (['single_choice', 'multiple_choice'].includes(type)) {
    questionForm.options = [
      { text: '', is_correct: false },
      { text: '', is_correct: false },
      { text: '', is_correct: false },
      { text: '', is_correct: false }
    ]
  }
}

const addOption = () => {
  questionForm.options.push({ text: '', is_correct: false })
}

const removeOption = (index) => {
  if (questionForm.options.length > 2) {
    questionForm.options.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留两个选项')
  }
}

const onOptionCorrectChange = (index, isCorrect) => {
  if (questionForm.question_type === 'single_choice' && isCorrect) {
    // 单选题只能有一个正确答案
    questionForm.options.forEach((option, i) => {
      if (i !== index) {
        option.is_correct = false
      }
    })
  }
}

const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const uploadImage = async (options) => {
  // TODO: 实现图片上传逻辑
  console.log('Uploading image...', options.file)
  // 模拟上传成功
  const imageUrl = URL.createObjectURL(options.file)
  questionForm.images.push(imageUrl)
}

const removeImage = (index) => {
  questionForm.images.splice(index, 1)
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', questionForm)
    showCreateDialog.value = false
    ElMessage.success('题目已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveQuestion = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing question...', questionForm)
    showCreateDialog.value = false
    ElMessage.success(editingQuestion.value ? '题目更新成功' : '题目创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const reviewQuestion = async (question) => {
  // TODO: 实现审核逻辑
  console.log('Reviewing question...', question)
}

const toggleQuestionStatus = async (question) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling question status...', question)
}

const deleteQuestion = async (question) => {
  try {
    await ElMessageBox.confirm('确定要删除这个题目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting question...', question)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing questions...', selectedQuestions.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving questions...', selectedQuestions.value)
}

const exportQuestions = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting questions...')
}

const beforeImportUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.type === 'text/csv'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 或 CSV 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

const importQuestions = async (options) => {
  // TODO: 实现批量导入逻辑
  console.log('Importing questions...', options.file)
  showImportDialog.value = false
  ElMessage.success('题目导入成功')
}

const downloadTemplate = () => {
  // TODO: 实现下载模板逻辑
  console.log('Downloading import template...')
}

onMounted(() => {
  loadQuestions()
})
</script>

<style scoped>
.questions-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.question-info {
  padding: 8px 0;
}

.question-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.question-content {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.question-meta {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.difficulty-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.difficulty-badge.difficulty-1 {
  background-color: #67c23a;
}

.difficulty-badge.difficulty-2 {
  background-color: #95d475;
}

.difficulty-badge.difficulty-3 {
  background-color: #e6a23c;
}

.difficulty-badge.difficulty-4 {
  background-color: #f78989;
}

.difficulty-badge.difficulty-5 {
  background-color: #f56c6c;
}

.knowledge-points {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.knowledge-tag {
  font-size: 11px;
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.score-value {
  font-weight: 600;
  color: #409eff;
}

.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.correct-rate {
  color: #67c23a !important;
}

.quality-score {
  display: flex;
  align-items: center;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.options-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #303133;
}

.option-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  font-weight: 500;
  color: #303133;
}

.option-actions {
  margin-top: 8px;
}

.media-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.image-item {
  position: relative;
  width: 100px;
  height: 100px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.question-detail {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
}

.question-content-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
}

.options-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-detail {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.option-letter {
  font-weight: 600;
  color: #409eff;
  min-width: 20px;
}

.option-text {
  flex: 1;
  color: #303133;
}

.answer-analysis-detail {
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
  border-left: 4px solid #409eff;
}

.import-section {
  padding: 20px 0;
}

.import-template {
  margin-top: 16px;
  text-align: center;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
