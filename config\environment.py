"""
WisCude 环境配置管理器
统一管理不同环境的配置加载和验证
"""
import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class Environment(Enum):
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

@dataclass
class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.environment = self._detect_environment()
        self._config_cache: Dict[str, Any] = {}
    
    def _detect_environment(self) -> Environment:
        """检测当前运行环境"""
        env_name = os.getenv("WISCUDE_ENV", "development").lower()
        
        try:
            return Environment(env_name)
        except ValueError:
            print(f"警告: 未知环境 '{env_name}'，使用默认环境 'development'")
            return Environment.DEVELOPMENT
    
    def load_config(self, config_name: str) -> Dict[str, Any]:
        """加载配置文件"""
        if config_name in self._config_cache:
            return self._config_cache[config_name]
        
        config_file = self.config_dir / f"{config_name}.yaml"
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 应用环境特定配置
            config = self._apply_environment_config(config)
            
            # 应用环境变量覆盖
            config = self._apply_env_overrides(config)
            
            self._config_cache[config_name] = config
            return config
            
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误 {config_file}: {e}")
    
    def _apply_environment_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境特定配置"""
        if "environments" not in config:
            return config
        
        env_config = config.get("environments", {}).get(self.environment.value, {})
        
        # 深度合并环境配置
        return self._deep_merge(config, env_config)
    
    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境变量覆盖"""
        # 定义环境变量映射
        env_mappings = {
            "WISCUDE_DATABASE_URL": ["database", "url"],
            "WISCUDE_SECRET_KEY": ["security", "secret_key"],
            "WISCUDE_DEBUG": ["app", "debug"],
            "WISCUDE_HOST": ["server", "host"],
            "WISCUDE_PORT": ["server", "port"],
            "WISCUDE_LOG_LEVEL": ["logging", "level"],
            "WISCUDE_REDIS_URL": ["cache", "redis_url"],
            "WISCUDE_SMTP_HOST": ["email", "smtp_host"],
            "WISCUDE_SMTP_USER": ["email", "smtp_user"],
            "WISCUDE_SMTP_PASSWORD": ["email", "smtp_password"],
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # 类型转换
                if env_var in ["WISCUDE_DEBUG"]:
                    env_value = env_value.lower() in ("true", "1", "yes", "on")
                elif env_var in ["WISCUDE_PORT"]:
                    env_value = int(env_value)
                
                # 设置配置值
                self._set_nested_value(config, config_path, env_value)
        
        return config
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _set_nested_value(self, config: Dict[str, Any], path: list, value: Any):
        """设置嵌套配置值"""
        current = config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        return self.load_config("app")
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.load_config("database")
    
    def validate_config(self, config_name: str) -> bool:
        """验证配置完整性"""
        try:
            config = self.load_config(config_name)
            
            if config_name == "app":
                return self._validate_app_config(config)
            elif config_name == "database":
                return self._validate_database_config(config)
            
            return True
            
        except Exception as e:
            print(f"配置验证失败 {config_name}: {e}")
            return False
    
    def _validate_app_config(self, config: Dict[str, Any]) -> bool:
        """验证应用配置"""
        required_keys = [
            ["app", "name"],
            ["app", "version"],
            ["server", "host"],
            ["server", "port"],
            ["security", "secret_key"]
        ]
        
        for key_path in required_keys:
            if not self._has_nested_key(config, key_path):
                print(f"缺少必需的配置项: {'.'.join(key_path)}")
                return False
        
        return True
    
    def _validate_database_config(self, config: Dict[str, Any]) -> bool:
        """验证数据库配置"""
        required_keys = [
            ["database", "url"],
            ["android_database", "path"]
        ]
        
        for key_path in required_keys:
            if not self._has_nested_key(config, key_path):
                print(f"缺少必需的配置项: {'.'.join(key_path)}")
                return False
        
        return True
    
    def _has_nested_key(self, config: Dict[str, Any], key_path: list) -> bool:
        """检查嵌套键是否存在"""
        current = config
        for key in key_path:
            if not isinstance(current, dict) or key not in current:
                return False
            current = current[key]
        return True
    
    def get_environment(self) -> Environment:
        """获取当前环境"""
        return self.environment
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == Environment.PRODUCTION
    
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == Environment.TESTING

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config(config_name: str) -> Dict[str, Any]:
    """获取配置的便捷函数"""
    return config_manager.load_config(config_name)

def get_app_config() -> Dict[str, Any]:
    """获取应用配置的便捷函数"""
    return config_manager.get_app_config()

def get_database_config() -> Dict[str, Any]:
    """获取数据库配置的便捷函数"""
    return config_manager.get_database_config()
