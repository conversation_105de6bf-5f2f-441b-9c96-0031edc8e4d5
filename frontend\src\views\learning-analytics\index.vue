<template>
  <div class="learning-analytics-dashboard">
    <div class="page-header">
      <h1>学习分析</h1>
      <p>深度分析学习数据，提供个性化学习建议和智能推荐</p>
    </div>

    <!-- 核心指标概览 -->
    <el-row :gutter="20" class="metrics-overview">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.totalUsers }}</div>
              <div class="metric-label">活跃用户</div>
              <div class="metric-trend up">↑ 12.5%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon sessions">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.totalSessions }}</div>
              <div class="metric-label">学习会话</div>
              <div class="metric-trend up">↑ 8.3%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon duration">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ formatDuration(metrics.avgDuration) }}</div>
              <div class="metric-label">平均时长</div>
              <div class="metric-trend up">↑ 15.2%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon completion">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ (metrics.completionRate * 100).toFixed(1) }}%</div>
              <div class="metric-label">完成率</div>
              <div class="metric-trend down">↓ 2.1%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能模块导航 -->
    <el-card class="modules-card">
      <template #header>
        <div class="card-header">
          <span>分析模块</span>
        </div>
      </template>
      
      <el-row :gutter="20" class="modules-grid">
        <el-col :span="6" v-for="module in modules" :key="module.name">
          <div class="module-item" @click="navigateToModule(module.path)">
            <div class="module-icon">
              <el-icon><component :is="iconComponents[module.icon]" /></el-icon>
            </div>
            <div class="module-info">
              <h3>{{ module.title }}</h3>
              <p>{{ module.description }}</p>
              <div class="module-stats">
                <span class="stat-item">
                  <el-icon><DataLine /></el-icon>
                  {{ module.count }}
                </span>
                <span class="stat-item" :class="module.trend">
                  <el-icon><TrendCharts /></el-icon>
                  {{ module.trendText }}
                </span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实时学习活动 -->
    <el-row :gutter="20" class="activity-section">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习活动趋势</span>
              <el-select v-model="timeRange" size="small" style="width: 120px;">
                <el-option label="今日" value="today" />
                <el-option label="本周" value="week" />
                <el-option label="本月" value="month" />
                <el-option label="本年" value="year" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加图表组件 -->
            <div class="chart-placeholder">学习活动趋势图表</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习热点</span>
            </div>
          </template>
          <div class="hotspots-list">
            <div v-for="(hotspot, index) in learningHotspots" :key="index" class="hotspot-item">
              <div class="hotspot-rank">{{ index + 1 }}</div>
              <div class="hotspot-content">
                <div class="hotspot-title">{{ hotspot.title }}</div>
                <div class="hotspot-stats">
                  <span class="hotspot-users">{{ hotspot.users }}人学习</span>
                  <span class="hotspot-growth" :class="hotspot.growth > 0 ? 'positive' : 'negative'">
                    {{ hotspot.growth > 0 ? '+' : '' }}{{ hotspot.growth }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 学习效果分析 -->
    <el-row :gutter="20" class="effectiveness-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习效果分布</span>
            </div>
          </template>
          <div class="effectiveness-chart">
            <div class="effectiveness-stats">
              <div v-for="level in effectivenessLevels" :key="level.name" class="effectiveness-item">
                <div class="effectiveness-info">
                  <span class="level-name">{{ level.name }}</span>
                  <span class="level-percentage">{{ level.percentage }}%</span>
                </div>
                <div class="effectiveness-bar">
                  <div 
                    class="effectiveness-progress" 
                    :style="{ width: level.percentage + '%', backgroundColor: level.color }"
                  ></div>
                </div>
                <span class="level-count">{{ level.count }}人</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学习路径分析</span>
            </div>
          </template>
          <div class="pathway-analysis">
            <div class="pathway-flow">
              <div v-for="(step, index) in learningPathway" :key="index" class="pathway-step">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">
                  <div class="step-title">{{ step.title }}</div>
                  <div class="step-stats">
                    <span class="step-users">{{ step.users }}人</span>
                    <span class="step-rate">{{ (step.rate * 100).toFixed(1) }}%</span>
                  </div>
                </div>
                <div v-if="index < learningPathway.length - 1" class="step-arrow">
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 个性化推荐 -->
    <el-card class="recommendations-card">
      <template #header>
        <div class="card-header">
          <span>智能推荐</span>
          <el-button type="primary" size="small" @click="refreshRecommendations">
            <el-icon><Refresh /></el-icon>
            刷新推荐
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20" class="recommendations-grid">
        <el-col :span="8" v-for="category in recommendationCategories" :key="category.name">
          <div class="recommendation-category">
            <div class="category-header">
              <div class="category-icon" :style="{ backgroundColor: category.color }">
                <el-icon><component :is="iconComponents[category.icon]" /></el-icon>
              </div>
              <div class="category-info">
                <h4>{{ category.title }}</h4>
                <p>{{ category.description }}</p>
              </div>
            </div>
            <div class="category-items">
              <div v-for="item in category.items" :key="item.id" class="recommendation-item">
                <div class="item-content">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-meta">
                    <span class="item-score">匹配度: {{ item.score }}%</span>
                    <span class="item-reason">{{ item.reason }}</span>
                  </div>
                </div>
                <el-button type="text" size="small" @click="applyRecommendation(item)">
                  应用
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 预警和异常检测 -->
    <el-card class="alerts-card" v-if="alerts.length > 0">
      <template #header>
        <div class="card-header">
          <span class="alerts-title">
            <el-icon class="alert-icon"><Warning /></el-icon>
            学习预警
          </span>
          <el-button type="danger" size="small" @click="$router.push('/learning-analytics/alerts')">
            查看全部
          </el-button>
        </div>
      </template>
      
      <div class="alerts-list">
        <div v-for="alert in alerts.slice(0, 3)" :key="alert.id" class="alert-item">
          <div class="alert-level" :class="`level-${alert.level}`">
            {{ getAlertLevelName(alert.level) }}
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-description">{{ alert.description }}</div>
            <div class="alert-time">{{ formatTime(alert.created_at) }}</div>
          </div>
          <div class="alert-actions">
            <el-button type="primary" size="small" @click="handleAlert(alert)">
              处理
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 快速操作面板 -->
    <el-card class="quick-actions-card">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      
      <el-row :gutter="20" class="quick-actions">
        <el-col :span="6" v-for="action in quickActions" :key="action.name">
          <div class="action-item" @click="executeAction(action)">
            <div class="action-icon" :style="{ backgroundColor: action.color }">
              <el-icon><component :is="iconComponents[action.icon]" /></el-icon>
            </div>
            <div class="action-info">
              <div class="action-title">{{ action.title }}</div>
              <div class="action-description">{{ action.description }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  User, Clock, Timer, CircleCheck, DataLine, TrendCharts, ArrowRight, 
  Refresh, Warning, Document, Reading, TrendCharts as TrendChartsIcon
} from '@element-plus/icons-vue'

// 注册图标组件
const iconComponents = {
  User,
  Document,
  Reading,
  TrendCharts: TrendChartsIcon,
  DataLine
}

const router = useRouter()

// 核心指标
const metrics = reactive({
  totalUsers: 2847,
  totalSessions: 15632,
  avgDuration: 2340, // 秒
  completionRate: 0.847
})

// 时间范围
const timeRange = ref('week')

// 功能模块
const modules = ref([
  {
    name: 'behavior',
    title: '行为分析',
    description: '分析用户学习行为模式',
    icon: 'User',
    path: '/learning-analytics/behavior',
    count: '15.6K',
    trend: 'up',
    trendText: '↑18%'
  },
  {
    name: 'performance',
    title: '成绩分析',
    description: '分析学习成绩和进度',
    icon: 'TrendCharts',
    path: '/learning-analytics/performance',
    count: '8.9K',
    trend: 'up',
    trendText: '↑12%'
  },
  {
    name: 'content',
    title: '内容分析',
    description: '分析学习内容效果',
    icon: 'Document',
    path: '/learning-analytics/content',
    count: '5.2K',
    trend: 'up',
    trendText: '↑25%'
  },
  {
    name: 'recommendations',
    title: '智能推荐',
    description: '个性化学习推荐',
    icon: 'Reading',
    path: '/learning-analytics/recommendations',
    count: '12.3K',
    trend: 'up',
    trendText: '↑8%'
  }
])

// 学习热点
const learningHotspots = ref([
  { title: '数学函数专题', users: 1234, growth: 25.6 },
  { title: '英语语法练习', users: 987, growth: 18.3 },
  { title: '物理力学基础', users: 756, growth: 12.7 },
  { title: '化学方程式', users: 654, growth: -5.2 },
  { title: '历史时间轴', users: 543, growth: 8.9 }
])

// 学习效果等级
const effectivenessLevels = ref([
  { name: '优秀', percentage: 28, count: 798, color: '#67c23a' },
  { name: '良好', percentage: 35, count: 996, color: '#409eff' },
  { name: '一般', percentage: 25, count: 712, color: '#e6a23c' },
  { name: '待提升', percentage: 12, count: 341, color: '#f56c6c' }
])

// 学习路径
const learningPathway = ref([
  { title: '基础知识', users: 2847, rate: 1.0 },
  { title: '练习巩固', users: 2410, rate: 0.847 },
  { title: '进阶学习', users: 1923, rate: 0.675 },
  { title: '综合应用', users: 1456, rate: 0.511 },
  { title: '掌握精通', users: 1098, rate: 0.386 }
])

// 推荐分类
const recommendationCategories = ref([
  {
    name: 'content',
    title: '内容推荐',
    description: '基于学习进度的内容推荐',
    icon: 'Document',
    color: '#409eff',
    items: [
      { id: 1, title: '高等数学进阶', score: 92, reason: '基于当前学习进度' },
      { id: 2, title: '线性代数基础', score: 87, reason: '知识关联度高' }
    ]
  },
  {
    name: 'study',
    title: '学习方法',
    description: '个性化学习策略推荐',
    icon: 'Reading',
    color: '#67c23a',
    items: [
      { id: 3, title: '间隔重复学习法', score: 89, reason: '适合记忆型学习' },
      { id: 4, title: '费曼学习技巧', score: 84, reason: '提升理解深度' }
    ]
  },
  {
    name: 'schedule',
    title: '时间安排',
    description: '优化学习时间分配',
    icon: 'TrendCharts',
    color: '#e6a23c',
    items: [
      { id: 5, title: '早晨专注时段', score: 91, reason: '基于活跃时间分析' },
      { id: 6, title: '碎片时间利用', score: 78, reason: '提高学习效率' }
    ]
  }
])

// 预警信息
const alerts = ref([
  {
    id: 1,
    level: 'high',
    title: '学习进度异常',
    description: '用户ID: U123456 连续7天未完成学习任务',
    created_at: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    level: 'medium',
    title: '成绩下降趋势',
    description: '数学科目平均分较上周下降15%',
    created_at: '2024-01-15 13:45:00'
  }
])

// 快速操作
const quickActions = ref([
  {
    name: 'export',
    title: '导出报告',
    description: '生成学习分析报告',
    icon: 'Document',
    color: '#409eff'
  },
  {
    name: 'alert',
    title: '设置预警',
    description: '配置学习预警规则',
    icon: 'Warning',
    color: '#f56c6c'
  },
  {
    name: 'recommend',
    title: '批量推荐',
    description: '为用户生成推荐',
    icon: 'Reading',
    color: '#67c23a'
  },
  {
    name: 'analyze',
    title: '深度分析',
    description: '启动深度学习分析',
    icon: 'TrendCharts',
    color: '#e6a23c'
  }
])

// 方法
const navigateToModule = (path: string) => {
  router.push(path)
}

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}h ${minutes}m`
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const getAlertLevelName = (level: string) => {
  const levels = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return levels[level] || level
}

const refreshRecommendations = () => {
  // TODO: 刷新推荐逻辑
  console.log('Refreshing recommendations...')
}

const applyRecommendation = (item: any) => {
  // TODO: 应用推荐逻辑
  console.log('Applying recommendation...', item)
}

const handleAlert = (alert: any) => {
  // TODO: 处理预警逻辑
  console.log('Handling alert...', alert)
}

const executeAction = (action: any) => {
  // TODO: 执行快速操作逻辑
  console.log('Executing action...', action)
}

// 加载数据
const loadData = async () => {
  // TODO: 调用API获取分析数据
  console.log('Loading learning analytics data...')
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.learning-analytics-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.metrics-overview {
  margin-bottom: 24px;
}

.metric-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.metric-icon.users {
  background-color: #409eff;
}

.metric-icon.sessions {
  background-color: #67c23a;
}

.metric-icon.duration {
  background-color: #e6a23c;
}

.metric-icon.completion {
  background-color: #f56c6c;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin: 4px 0;
}

.metric-trend {
  font-size: 12px;
  font-weight: 500;
}

.metric-trend.up {
  color: #67c23a;
}

.metric-trend.down {
  color: #f56c6c;
}

.modules-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modules-grid {
  margin-top: 16px;
}

.module-item {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
  height: 140px;
  display: flex;
  flex-direction: column;
}

.module-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.module-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-bottom: 12px;
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.module-info p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  flex: 1;
}

.module-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.stat-item.up {
  color: #67c23a;
}

.activity-section {
  margin-bottom: 24px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.hotspots-list {
  max-height: 300px;
  overflow-y: auto;
}

.hotspot-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.hotspot-item:last-child {
  border-bottom: none;
}

.hotspot-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
}

.hotspot-content {
  flex: 1;
}

.hotspot-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.hotspot-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.hotspot-users {
  color: #606266;
}

.hotspot-growth.positive {
  color: #67c23a;
}

.hotspot-growth.negative {
  color: #f56c6c;
}

.effectiveness-section {
  margin-bottom: 24px;
}

.effectiveness-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 10px 0;
}

.effectiveness-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.effectiveness-info {
  min-width: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-name {
  font-weight: 500;
  color: #303133;
}

.level-percentage {
  font-size: 12px;
  color: #909399;
}

.effectiveness-bar {
  flex: 1;
  height: 8px;
  background-color: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
}

.effectiveness-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.level-count {
  min-width: 50px;
  font-size: 12px;
  color: #606266;
  text-align: right;
}

.pathway-flow {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 0;
  overflow-x: auto;
}

.pathway-step {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.step-stats {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.step-users {
  color: #606266;
}

.step-rate {
  color: #409eff;
  font-weight: 500;
}

.step-arrow {
  color: #c0c4cc;
  font-size: 16px;
}

.recommendations-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.recommendations-grid {
  margin-top: 16px;
}

.recommendation-category {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  height: 100%;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.category-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.category-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.category-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.item-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.item-score {
  color: #67c23a;
  font-weight: 500;
}

.item-reason {
  color: #909399;
}

.alerts-card {
  margin-bottom: 24px;
  border: 1px solid #f56c6c;
  box-shadow: 0 2px 12px 0 rgba(245, 108, 108, 0.2);
}

.alerts-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f56c6c;
  font-weight: 600;
}

.alert-icon {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background-color: #fef0f0;
  border-radius: 6px;
  border-left: 4px solid #f56c6c;
}

.alert-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  min-width: 40px;
  text-align: center;
}

.alert-level.level-high {
  background-color: #f56c6c;
}

.alert-level.level-medium {
  background-color: #e6a23c;
}

.alert-level.level-low {
  background-color: #67c23a;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.alert-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 11px;
  color: #909399;
}

.quick-actions-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.quick-actions {
  margin-top: 16px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px 0 rgba(64, 158, 255, 0.2);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.action-info {
  flex: 1;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.action-description {
  font-size: 12px;
  color: #909399;
}
</style>
