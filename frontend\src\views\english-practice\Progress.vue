<template>
  <div class="progress-tracking">
    <div class="page-header">
      <h1>学习进度</h1>
      <p>跟踪学生英语学习进度、成绩分析和学习路径规划</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-students">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalStudents }}</div>
              <div class="stat-label">学习学生</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon avg-progress">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.avgProgress }}%</div>
              <div class="stat-label">平均进度</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completion-rate">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ (statistics.completionRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active-learners">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.activeLearners }}</div>
              <div class="stat-label">活跃学习者</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="学生进度" name="students">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="generateReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
            <el-button type="success" @click="exportProgress">
              <el-icon><Download /></el-icon>
              导出进度
            </el-button>
            <el-button type="warning" @click="sendReminders">
              <el-icon><Bell /></el-icon>
              发送提醒
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索学生姓名"
              style="width: 200px"
              clearable
              @change="loadStudentProgress"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="classFilter" placeholder="班级筛选" style="width: 120px" @change="loadStudentProgress">
              <el-option label="全部班级" value="" />
              <el-option label="初级班" value="beginner" />
              <el-option label="中级班" value="intermediate" />
              <el-option label="高级班" value="advanced" />
            </el-select>
            <el-select v-model="progressFilter" placeholder="进度筛选" style="width: 120px" @change="loadStudentProgress">
              <el-option label="全部进度" value="" />
              <el-option label="优秀(90%+)" value="excellent" />
              <el-option label="良好(70-89%)" value="good" />
              <el-option label="一般(50-69%)" value="average" />
              <el-option label="待提高(<50%)" value="poor" />
            </el-select>
          </div>
        </div>

        <!-- 学生进度列表 -->
        <el-table
          :data="studentProgress"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column label="学生信息" min-width="200">
            <template #default="{ row }">
              <div class="student-info">
                <div class="student-avatar">
                  <img v-if="row.avatar" :src="row.avatar" :alt="row.name" />
                  <div v-else class="avatar-placeholder">{{ row.name.charAt(0) }}</div>
                </div>
                <div class="student-details">
                  <div class="student-name">{{ row.name }}</div>
                  <div class="student-class">{{ row.class_name }}</div>
                  <div class="student-id">学号: {{ row.student_id }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习进度" width="200">
            <template #default="{ row }">
              <div class="progress-info">
                <div class="progress-header">
                  <span class="progress-text">{{ row.progress }}%</span>
                  <span class="progress-level" :class="getProgressClass(row.progress)">
                    {{ getProgressLevel(row.progress) }}
                  </span>
                </div>
                <el-progress 
                  :percentage="row.progress" 
                  :status="getProgressStatus(row.progress)"
                  :stroke-width="8"
                />
                <div class="progress-details">
                  <span>已完成: {{ row.completed_lessons }}/{{ row.total_lessons }}课</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习统计" width="150">
            <template #default="{ row }">
              <div class="learning-stats">
                <div class="stat-item">
                  <span class="stat-label">学习时长:</span>
                  <span class="stat-value">{{ formatDuration(row.study_time) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">平均分:</span>
                  <span class="stat-value avg-score">{{ row.avg_score }}分</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">连续天数:</span>
                  <span class="stat-value streak">{{ row.streak_days }}天</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="最近活动" width="150">
            <template #default="{ row }">
              <div class="recent-activity">
                <div class="activity-item">
                  <span class="activity-label">最后学习:</span>
                  <span class="activity-time">{{ formatTime(row.last_activity) }}</span>
                </div>
                <div class="activity-item">
                  <span class="activity-label">当前课程:</span>
                  <span class="activity-lesson">{{ row.current_lesson }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewDetails(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button type="text" size="small" @click="viewAnalytics(row)">
                <el-icon><DataAnalysis /></el-icon>
                分析
              </el-button>
              <el-button type="text" size="small" @click="sendMessage(row)">
                <el-icon><ChatDotRound /></el-icon>
                消息
              </el-button>
              <el-button type="text" size="small" @click="adjustPlan(row)">
                <el-icon><Setting /></el-icon>
                调整
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadStudentProgress"
            @current-change="loadStudentProgress"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="进度分析" name="analytics">
        <!-- 分析图表区域 -->
        <div class="analytics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>进度分布分析</span>
                    <el-select v-model="timeRange" size="small" style="width: 120px;">
                      <el-option label="最近7天" value="7d" />
                      <el-option label="最近30天" value="30d" />
                      <el-option label="最近90天" value="90d" />
                    </el-select>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加进度分布图表 -->
                  <div class="chart-placeholder">进度分布图表</div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>学习活跃度</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加学习活跃度图表 -->
                  <div class="chart-placeholder">学习活跃度图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>学习趋势分析</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加学习趋势图表 -->
                  <div class="chart-placeholder">学习趋势分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>

      <el-tab-pane label="学习路径" name="paths">
        <!-- 学习路径规划 -->
        <div class="learning-paths">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>学习路径管理</span>
                <el-button type="primary" @click="createLearningPath">
                  <el-icon><Plus /></el-icon>
                  创建路径
                </el-button>
              </div>
            </template>
            
            <div class="paths-grid">
              <div v-for="path in learningPaths" :key="path.id" class="path-card">
                <div class="path-header">
                  <h3>{{ path.name }}</h3>
                  <el-tag :type="path.difficulty === 'beginner' ? 'success' : path.difficulty === 'intermediate' ? 'warning' : 'danger'">
                    {{ getDifficultyName(path.difficulty) }}
                  </el-tag>
                </div>
                <div class="path-description">{{ path.description }}</div>
                <div class="path-stats">
                  <div class="stat-item">
                    <span class="stat-label">课程数:</span>
                    <span class="stat-value">{{ path.lesson_count }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">预计时长:</span>
                    <span class="stat-value">{{ path.estimated_hours }}小时</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">学习人数:</span>
                    <span class="stat-value">{{ path.student_count }}人</span>
                  </div>
                </div>
                <div class="path-actions">
                  <el-button type="text" size="small" @click="editPath(path)">编辑</el-button>
                  <el-button type="text" size="small" @click="viewPathDetails(path)">详情</el-button>
                  <el-button type="text" size="small" class="danger" @click="deletePath(path)">删除</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, TrendCharts, CircleCheck, Clock, Document, Download, Bell, Search, 
  View, DataAnalysis, ChatDotRound, Setting, Plus
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('students')
const loading = ref(false)
const timeRange = ref('30d')
const searchKeyword = ref('')
const classFilter = ref('')
const progressFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = reactive({
  totalStudents: 1234,
  avgProgress: 76.5,
  completionRate: 0.68,
  activeLearners: 892
})

// 学生进度列表
const studentProgress = ref([
  {
    id: '1',
    name: '张小明',
    student_id: 'S2024001',
    class_name: '中级班A',
    avatar: '',
    progress: 85,
    completed_lessons: 17,
    total_lessons: 20,
    study_time: 3600, // 秒
    avg_score: 88.5,
    streak_days: 15,
    last_activity: '2024-01-15 14:30:00',
    current_lesson: '第18课：商务英语对话',
    status: 'active'
  }
])

// 学习路径
const learningPaths = ref([
  {
    id: '1',
    name: '基础英语入门',
    description: '适合零基础学员的英语入门课程',
    difficulty: 'beginner',
    lesson_count: 20,
    estimated_hours: 40,
    student_count: 156
  },
  {
    id: '2',
    name: '商务英语进阶',
    description: '提升职场英语沟通能力',
    difficulty: 'intermediate',
    lesson_count: 30,
    estimated_hours: 60,
    student_count: 89
  }
])

// 方法
const loadStudentProgress = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取学生进度数据
    console.log('Loading student progress...')
  } catch (error) {
    ElMessage.error('加载学生进度失败')
  } finally {
    loading.value = false
  }
}

const getProgressClass = (progress) => {
  if (progress >= 90) return 'excellent'
  if (progress >= 70) return 'good'
  if (progress >= 50) return 'average'
  return 'poor'
}

const getProgressLevel = (progress) => {
  if (progress >= 90) return '优秀'
  if (progress >= 70) return '良好'
  if (progress >= 50) return '一般'
  return '待提高'
}

const getProgressStatus = (progress) => {
  if (progress >= 90) return 'success'
  if (progress >= 70) return ''
  if (progress >= 50) return 'warning'
  return 'exception'
}

const getStatusName = (status) => {
  const statuses = {
    active: '学习中',
    paused: '暂停',
    completed: '已完成',
    inactive: '未活跃'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    paused: 'warning',
    completed: 'primary',
    inactive: 'info'
  }
  return types[status] || ''
}

const getDifficultyName = (difficulty) => {
  const difficulties = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级'
  }
  return difficulties[difficulty] || difficulty
}

const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}小时${minutes}分钟`
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const viewDetails = (student) => {
  // TODO: 查看学生详情
  console.log('Viewing student details:', student)
}

const viewAnalytics = (student) => {
  // TODO: 查看学生分析
  console.log('Viewing student analytics:', student)
}

const sendMessage = (student) => {
  // TODO: 发送消息给学生
  console.log('Sending message to student:', student)
}

const adjustPlan = (student) => {
  // TODO: 调整学习计划
  console.log('Adjusting learning plan for student:', student)
}

const generateReport = () => {
  // TODO: 生成进度报告
  console.log('Generating progress report...')
}

const exportProgress = () => {
  // TODO: 导出进度数据
  console.log('Exporting progress data...')
}

const sendReminders = () => {
  // TODO: 发送学习提醒
  console.log('Sending learning reminders...')
}

const createLearningPath = () => {
  // TODO: 创建学习路径
  console.log('Creating learning path...')
}

const editPath = (path) => {
  // TODO: 编辑学习路径
  console.log('Editing learning path:', path)
}

const viewPathDetails = (path) => {
  // TODO: 查看路径详情
  console.log('Viewing path details:', path)
}

const deletePath = async (path) => {
  try {
    await ElMessageBox.confirm('确定要删除这个学习路径吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting learning path:', path)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  loadStudentProgress()
})
</script>

<style scoped>
.progress-tracking {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.total-students {
  background-color: #409eff;
}

.stat-icon.avg-progress {
  background-color: #67c23a;
}

.stat-icon.completion-rate {
  background-color: #e6a23c;
}

.stat-icon.active-learners {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.student-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.student-details {
  flex: 1;
}

.student-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.student-class {
  font-size: 12px;
  color: #606266;
  margin-bottom: 2px;
}

.student-id {
  font-size: 11px;
  color: #909399;
}

.progress-info {
  text-align: center;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.progress-level {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
}

.progress-level.excellent {
  background-color: #67c23a;
}

.progress-level.good {
  background-color: #409eff;
}

.progress-level.average {
  background-color: #e6a23c;
}

.progress-level.poor {
  background-color: #f56c6c;
}

.progress-details {
  font-size: 11px;
  color: #909399;
  margin-top: 8px;
}

.learning-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
  font-size: 12px;
}

.stat-value {
  color: #606266;
  font-weight: 600;
  font-size: 14px;
}

.avg-score {
  color: #409eff !important;
}

.streak {
  color: #67c23a !important;
}

.recent-activity {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-item {
  display: flex;
  flex-direction: column;
  font-size: 12px;
}

.activity-label {
  color: #909399;
  margin-bottom: 2px;
  font-size: 12px;
}

.activity-time,
.activity-lesson {
  color: #606266;
  font-weight: 600;
  font-size: 14px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.analytics-section {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.learning-paths {
  padding: 20px 0;
}

.paths-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.path-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.path-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.path-description {
  font-size: 13px;
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.5;
}

.path-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.path-actions {
  display: flex;
  gap: 12px;
}

.danger {
  color: #f56c6c;
}
</style>
