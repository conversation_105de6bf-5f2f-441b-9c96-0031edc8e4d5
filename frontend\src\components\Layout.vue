<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth">
        <div class="sidebar">
          <div class="logo">
            <el-icon size="32" color="#409EFF">
              <Odometer />
            </el-icon>
            <span v-if="!isCollapsed" class="logo-text">WisCude</span>
          </div>
          
          <el-menu
            :default-active="activeMenu"
            :collapse="isCollapsed"
            :unique-opened="true"
            class="sidebar-menu"
            @select="handleMenuSelect"
          >
            <template v-for="route in menuRoutes" :key="route.path">
              <!-- 有子菜单的路由 -->
              <el-sub-menu
                v-if="!route.meta?.hidden && getSortedChildren(route).length > 0"
                :index="route.path"
              >
                <template #title>
                  <el-icon>
                    <component :is="route.meta?.icon || 'Document'" />
                  </el-icon>
                  <span>{{ route.meta?.title }}</span>
                </template>
                <el-menu-item
                  v-for="child in getSortedChildren(route)"
                  :key="child.path"
                  :index="`${route.path}/${child.path}`"
                >
                  <el-icon>
                    <component :is="child.meta?.icon || 'Document'" />
                  </el-icon>
                  <template #title>{{ child.meta?.title }}</template>
                </el-menu-item>
              </el-sub-menu>

              <!-- 没有子菜单的路由 -->
              <el-menu-item
                v-else-if="!route.meta?.hidden"
                :index="route.path"
              >
                <el-icon>
                  <component :is="route.meta?.icon || 'Document'" />
                </el-icon>
                <template #title>{{ route.meta?.title }}</template>
              </el-menu-item>
            </template>
          </el-menu>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              text
              @click="toggleSidebar"
            >
              <el-icon size="20">
                <Fold v-if="!isCollapsed" />
                <Expand v-else />
              </el-icon>
            </el-button>
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="authStore.user?.avatar_url">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ authStore.user?.full_name || authStore.user?.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    系统设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏折叠状态
const isCollapsed = ref(false)

// 排序子菜单的函数
const sortChildren = (children: any[]) => {
  return children
    .filter(child => !child.meta?.hidden)
    .sort((a, b) => {
      const orderA = typeof a.meta?.order === 'number' ? a.meta.order : 999
      const orderB = typeof b.meta?.order === 'number' ? b.meta.order : 999
      return orderA - orderB
    })
}

// 获取排序后的子菜单
const getSortedChildren = (route: any) => {
  if (!route.children || route.children.length === 0) return []
  const sorted = sortChildren(route.children)



  return sorted
}

// 计算属性
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '200px')
const activeMenu = computed(() => {
  const currentPath = route.path
  console.log(`[Menu] 当前激活路径: ${currentPath}`)
  return currentPath
})
const currentPageTitle = computed(() => route.meta?.title as string || '未知页面')

// 菜单路由
const menuRoutes = computed(() => {
  // 直接从路由配置中获取数据
  const allRoutes = router.getRoutes()
  const layoutRoute = allRoutes.find(r => r.name === 'Layout')
  if (!layoutRoute?.children) return []

  return layoutRoute.children
    .filter(route => !route.meta?.hidden) // 过滤隐藏的路由
    .filter(route => {
      // 如果需要超级用户权限，检查当前用户权限
      if (route.meta?.requiresSuperuser) {
        return authStore.user?.is_superuser
      }
      return true
    })
    .sort((a, b) => {
      // 按order字段排序，没有order的放在最后
      const orderA = a.meta?.order || 999
      const orderB = b.meta?.order || 999
      return orderA - orderB
    })
})



// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 防抖处理路由跳转
let navigationTimeout: NodeJS.Timeout | null = null

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  console.log(`[Menu] 选择菜单项: ${index}`)

  // 清除之前的导航定时器
  if (navigationTimeout) {
    clearTimeout(navigationTimeout)
  }

  // 确保路径格式正确
  const targetPath = index.startsWith('/') ? index : `/${index}`

  // 如果当前路径与目标路径相同，不进行跳转
  if (route.path === targetPath) {
    console.log(`[Menu] 已在目标页面，跳过跳转: ${targetPath}`)
    return
  }

  // 使用防抖机制避免频繁导航
  navigationTimeout = setTimeout(() => {
    // 再次检查路径，防止在防抖期间路径已经改变
    if (route.path !== targetPath) {
      router.push(targetPath).catch(error => {
        // 忽略冗余导航错误
        if (!error.message.includes('Avoided redundant navigation')) {
          console.error(`[Menu] 路由跳转失败:`, error)
        }
      })
    }
  }, 100) // 100ms防抖
}

// 处理用户菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      try {
        await router.push('/profile')
      } catch (error) {
        console.error('跳转到个人资料页面失败:', error)
        ElMessage.error('跳转到个人资料页面失败')
      }
      break
    case 'settings':
      try {
        await router.push('/settings')
      } catch (error) {
        console.error('跳转到系统设置页面失败:', error)
        ElMessage.error('跳转到系统设置页面失败')
      }
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await authStore.logout()
        router.push('/login')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

// 监听路由变化
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log(`[Route] 路由变化: ${oldPath} -> ${newPath}`)
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

:deep(.el-container) {
  height: 100vh;
}

:deep(.el-aside) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100vh;
  overflow: hidden;
}

:deep(.el-main) {
  padding: 0;
  height: calc(100vh - 60px);
  overflow: hidden;
}

.sidebar {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    color: white;
    
    .logo-text {
      margin-left: 10px;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .sidebar-menu {
    border-right: none;
    background: transparent;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100vh - 60px);

    :deep(.el-menu-item) {
      color: rgba(255, 255, 255, 0.8);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }

      &.is-active {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
      }
    }

    :deep(.el-sub-menu) {
      .el-sub-menu__title {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          color: white;
        }
      }

      .el-menu {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 确保Element Plus菜单组件的背景透明
:deep(.el-menu) {
  background-color: transparent !important;
}

:deep(.el-menu--vertical) {
  background-color: transparent !important;
}

:deep(.el-sub-menu .el-menu) {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

:deep(.el-sub-menu .el-menu .el-menu-item) {
  color: rgba(255, 255, 255, 0.7);
  padding-left: 50px !important;

  &:hover {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
  }

  &.is-active {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
  }
}

.header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      .username {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.main-content {
  background: #f5f7fa;
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

// 确保滚动区域独立
:deep(.el-container--vertical) {
  height: 100vh;
}

// 优化滚动条样式
.sidebar-menu {
  // 自定义侧边栏滚动条
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.main-content {
  // 自定义主内容区滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>
