<template>
  <div class="banners-management">
    <div class="page-header">
      <h1>轮播图管理</h1>
      <p>管理首页轮播图和广告横幅的展示、排序和统计</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="轮播图列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建轮播图
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedBanners.length === 0"
              @click="batchActivate"
            >
              批量启用
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedBanners.length === 0"
              @click="batchDeactivate"
            >
              批量停用
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索轮播图标题"
              style="width: 200px"
              clearable
              @change="loadBanners"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="loadBanners">
              <el-option label="全部" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="active" />
              <el-option label="已停用" value="inactive" />
              <el-option label="已过期" value="expired" />
            </el-select>
            <el-select v-model="positionFilter" placeholder="位置筛选" style="width: 120px" @change="loadBanners">
              <el-option label="全部" value="" />
              <el-option label="首页" value="home" />
              <el-option label="课程页" value="course" />
              <el-option label="社区页" value="community" />
            </el-select>
          </div>
        </div>

        <!-- 轮播图列表 -->
        <el-table
          :data="banners"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="轮播图" width="200">
            <template #default="{ row }">
              <div class="banner-preview">
                <img :src="row.image_url" :alt="row.title" class="banner-image" />
                <div class="banner-info">
                  <div class="banner-title">{{ row.title }}</div>
                  <div class="banner-position">{{ getPositionName(row.position) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="排序" width="80">
            <template #default="{ row }">
              <span class="sort-order">{{ row.sort_order }}</span>
            </template>
          </el-table-column>
          <el-table-column label="统计数据" width="150">
            <template #default="{ row }">
              <div class="stats-info">
                <div class="stat-item">
                  <el-icon><View /></el-icon>
                  <span>{{ row.view_count }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Mouse /></el-icon>
                  <span>{{ row.click_count }}</span>
                </div>
                <div class="stat-item">
                  <span class="click-rate">{{ (row.click_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="发布时间" width="180">
            <template #default="{ row }">
              <div v-if="row.publish_start_time" class="time-info">
                <div>开始：{{ formatTime(row.publish_start_time) }}</div>
                <div v-if="row.publish_end_time">结束：{{ formatTime(row.publish_end_time) }}</div>
              </div>
              <span v-else class="no-time">未设置</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="editBanner(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="link"
                size="small"
                :class="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleBannerStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'active' ? '停用' : '启用' }}
              </el-button>
              <el-button type="link" size="small" @click="viewStats(row)">
                <el-icon><TrendCharts /></el-icon>
                统计
              </el-button>
              <el-button type="link" size="small" class="danger" @click="deleteBanner(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadBanners"
            @current-change="loadBanners"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="点击率趋势">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">点击率趋势图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="位置分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">位置分布图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑轮播图对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingBanner ? '编辑轮播图' : '新建轮播图'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="bannerForm" :rules="bannerRules" ref="bannerFormRef" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="bannerForm.title" placeholder="请输入轮播图标题" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="bannerForm.description" type="textarea" rows="3" placeholder="请输入轮播图描述" />
        </el-form-item>
        <el-form-item label="图片" prop="image_url">
          <div class="image-upload">
            <el-upload
              class="image-uploader"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="uploadImage"
            >
              <img v-if="bannerForm.image_url" :src="bannerForm.image_url" class="uploaded-image" />
              <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div class="upload-tips">
              <p>支持 JPG、PNG 格式，建议尺寸 1200x400px，大小不超过 2MB</p>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="跳转链接" prop="link_url">
          <el-input v-model="bannerForm.link_url" placeholder="请输入跳转链接（可选）" />
        </el-form-item>
        <el-form-item label="展示位置" prop="position">
          <el-select v-model="bannerForm.position" placeholder="请选择展示位置">
            <el-option label="首页轮播" value="home" />
            <el-option label="课程页面" value="course" />
            <el-option label="社区页面" value="community" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序顺序" prop="sort_order">
          <el-input-number v-model="bannerForm.sort_order" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker
            v-model="publishTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveBanner" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, TrendCharts, View, Mouse, ArrowUp
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const loading = ref(false)
const saving = ref(false)
const selectedBanners = ref([])
const showCreateDialog = ref(false)
const editingBanner = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('')
const positionFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 轮播图列表
const banners = ref([
  {
    id: '1',
    title: '新学期课程推广',
    description: '全新课程体系，助力学习成长',
    image_url: '/images/banner-placeholder.jpg',
    link_url: '/courses',
    position: 'home',
    sort_order: 1,
    status: 'active',
    view_count: 1234,
    click_count: 89,
    click_rate: 0.072,
    publish_start_time: '2024-01-01 00:00:00',
    publish_end_time: '2024-12-31 23:59:59'
  }
])

// 表单数据
const bannerForm = reactive({
  title: '',
  description: '',
  image_url: '',
  link_url: '',
  position: 'home',
  sort_order: 0
})

const publishTimeRange = ref([])

// 表单验证规则
const bannerRules = {
  title: [
    { required: true, message: '请输入轮播图标题', trigger: 'blur' }
  ],
  image_url: [
    { required: true, message: '请上传轮播图图片', trigger: 'change' }
  ],
  position: [
    { required: true, message: '请选择展示位置', trigger: 'change' }
  ]
}

// 方法
const loadBanners = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取轮播图列表
    console.log('Loading banners...')
  } catch (error) {
    ElMessage.error('加载轮播图列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedBanners.value = selection
}

const getPositionName = (position) => {
  const positions = {
    home: '首页',
    course: '课程页',
    community: '社区页'
  }
  return positions[position] || position
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    active: '已发布',
    inactive: '已停用',
    expired: '已过期'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: '',
    active: 'success',
    inactive: 'warning',
    expired: 'danger'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const editBanner = (banner) => {
  editingBanner.value = banner
  Object.assign(bannerForm, banner)
  if (banner.publish_start_time && banner.publish_end_time) {
    publishTimeRange.value = [banner.publish_start_time, banner.publish_end_time]
  }
  showCreateDialog.value = true
}

const resetForm = () => {
  editingBanner.value = null
  Object.assign(bannerForm, {
    title: '',
    description: '',
    image_url: '',
    link_url: '',
    position: 'home',
    sort_order: 0
  })
  publishTimeRange.value = []
}

const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const uploadImage = async (options) => {
  // TODO: 实现图片上传逻辑
  console.log('Uploading image...', options.file)
  // 模拟上传成功
  bannerForm.image_url = URL.createObjectURL(options.file)
}

const saveBanner = async () => {
  // TODO: 实现保存逻辑
  console.log('Saving banner...', bannerForm)
  showCreateDialog.value = false
  ElMessage.success(editingBanner.value ? '轮播图更新成功' : '轮播图创建成功')
}

const toggleBannerStatus = async (banner) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling banner status...', banner)
}

const deleteBanner = async (banner) => {
  try {
    await ElMessageBox.confirm('确定要删除这个轮播图吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting banner...', banner)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchActivate = async () => {
  // TODO: 实现批量启用逻辑
  console.log('Batch activating banners...', selectedBanners.value)
}

const batchDeactivate = async () => {
  // TODO: 实现批量停用逻辑
  console.log('Batch deactivating banners...', selectedBanners.value)
}

const viewStats = (banner) => {
  // TODO: 实现查看统计逻辑
  console.log('Viewing banner stats...', banner)
}

// 工具函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

onMounted(() => {
  loadBanners()
})
</script>

<style scoped>
.banners-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.banner-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.banner-image {
  width: 80px;
  height: 30px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.banner-info {
  flex: 1;
}

.banner-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.banner-position {
  font-size: 12px;
  color: #909399;
}

.sort-order {
  font-weight: 600;
  color: #409eff;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.click-rate {
  font-weight: 600;
  color: #67c23a;
}

.time-info {
  font-size: 12px;
  color: #606266;
}

.time-info div {
  margin-bottom: 2px;
}

.no-time {
  font-size: 12px;
  color: #c0c4cc;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.image-upload {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.uploaded-image {
  width: 200px;
  height: 100px;
  display: block;
  object-fit: cover;
}

.upload-tips {
  font-size: 12px;
  color: #909399;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
