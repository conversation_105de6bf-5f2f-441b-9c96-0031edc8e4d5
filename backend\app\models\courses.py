"""
课程管理模块数据模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, JSON, Float, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class Instructor(BaseModel):
    """讲师模型"""
    __tablename__ = "instructors"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), unique=True, comment="关联用户ID")
    name = Column(String(100), nullable=False, comment="讲师姓名")
    avatar = Column(String(500), comment="头像")
    
    # 基本信息
    title = Column(String(100), comment="职称")
    bio = Column(Text, comment="个人简介")
    specialties = Column(JSON, comment="专业领域")
    experience_years = Column(Integer, comment="教学经验年限")
    
    # 教育背景
    education_background = Column(JSON, comment="教育背景")
    certifications = Column(JSON, comment="资质认证")
    achievements = Column(JSON, comment="成就奖项")
    
    # 联系信息
    email = Column(String(100), comment="邮箱")
    phone = Column(String(20), comment="电话")
    social_links = Column(JSON, comment="社交媒体链接")
    
    # 教学统计
    course_count = Column(Integer, default=0, comment="课程数量")
    student_count = Column(Integer, default=0, comment="学生总数")
    total_hours = Column(Float, default=0.0, comment="总教学时长")
    
    # 评价统计
    rating = Column(Float, default=0.0, comment="评分")
    rating_count = Column(Integer, default=0, comment="评价数量")
    
    # 状态
    status = Column(String(20), default="active", comment="状态: active, inactive, suspended")
    is_verified = Column(Boolean, default=False, comment="是否已认证")
    is_featured = Column(Boolean, default=False, comment="是否为推荐讲师")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class Course(BaseModel):
    """课程模型"""
    __tablename__ = "courses"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="课程标题")
    subtitle = Column(String(300), comment="副标题")
    description = Column(Text, comment="课程描述")
    
    # 基本信息
    category = Column(String(50), comment="课程分类")
    subject = Column(String(50), comment="学科")
    grade_levels = Column(JSON, comment="适用年级")
    difficulty_level = Column(Integer, default=1, comment="难度等级 1-5")
    
    # 讲师信息
    instructor_id = Column(String(36), ForeignKey('instructors.id'), comment="主讲师ID")
    co_instructors = Column(JSON, comment="协作讲师列表")
    
    # 课程内容
    cover_image = Column(String(500), comment="封面图片")
    trailer_video = Column(String(500), comment="预告视频")
    syllabus = Column(Text, comment="课程大纲")
    learning_objectives = Column(JSON, comment="学习目标")
    prerequisites = Column(JSON, comment="先修要求")
    
    # 课程设置
    total_duration = Column(Integer, comment="总时长（分钟）")
    chapter_count = Column(Integer, default=0, comment="章节数量")
    lesson_count = Column(Integer, default=0, comment="课时数量")
    
    # 定价信息
    price = Column(Float, default=0.0, comment="课程价格")
    original_price = Column(Float, comment="原价")
    is_free = Column(Boolean, default=False, comment="是否免费")
    discount_rate = Column(Float, comment="折扣率")
    
    # 发布设置
    status = Column(String(20), default="draft", comment="状态: draft, published, archived")
    publish_time = Column(DateTime, comment="发布时间")
    enrollment_start = Column(DateTime, comment="报名开始时间")
    enrollment_end = Column(DateTime, comment="报名结束时间")
    
    # 学习统计
    enrollment_count = Column(Integer, default=0, comment="报名人数")
    completion_count = Column(Integer, default=0, comment="完成人数")
    completion_rate = Column(Float, default=0.0, comment="完成率")
    
    # 评价统计
    rating = Column(Float, default=0.0, comment="评分")
    rating_count = Column(Integer, default=0, comment="评价数量")
    
    # 推荐设置
    is_featured = Column(Boolean, default=False, comment="是否为推荐课程")
    is_trending = Column(Boolean, default=False, comment="是否为热门课程")
    recommendation_score = Column(Float, default=0.0, comment="推荐分数")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class CourseChapter(BaseModel):
    """课程章节模型"""
    __tablename__ = "course_chapters"
    
    id = Column(String(36), primary_key=True)
    course_id = Column(String(36), ForeignKey('courses.id'), comment="课程ID")
    title = Column(String(200), nullable=False, comment="章节标题")
    description = Column(Text, comment="章节描述")
    
    # 排序和结构
    sort_order = Column(Integer, default=0, comment="排序顺序")
    parent_id = Column(String(36), comment="父章节ID")
    level = Column(Integer, default=1, comment="章节层级")
    
    # 内容统计
    lesson_count = Column(Integer, default=0, comment="课时数量")
    total_duration = Column(Integer, default=0, comment="总时长（分钟）")
    
    # 状态
    is_published = Column(Boolean, default=False, comment="是否已发布")
    is_free = Column(Boolean, default=False, comment="是否免费")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class CourseLesson(BaseModel):
    """课程课时模型"""
    __tablename__ = "course_lessons"
    
    id = Column(String(36), primary_key=True)
    course_id = Column(String(36), ForeignKey('courses.id'), comment="课程ID")
    chapter_id = Column(String(36), ForeignKey('course_chapters.id'), comment="章节ID")
    title = Column(String(200), nullable=False, comment="课时标题")
    description = Column(Text, comment="课时描述")
    
    # 内容信息
    lesson_type = Column(String(20), default="video", comment="课时类型: video, audio, text, quiz, assignment")
    video_url = Column(String(500), comment="视频链接")
    audio_url = Column(String(500), comment="音频链接")
    content = Column(Text, comment="文本内容")
    duration = Column(Integer, comment="时长（分钟）")
    
    # 资料附件
    attachments = Column(JSON, comment="附件列表")
    resources = Column(JSON, comment="学习资源")
    
    # 排序和访问
    sort_order = Column(Integer, default=0, comment="排序顺序")
    is_published = Column(Boolean, default=False, comment="是否已发布")
    is_free = Column(Boolean, default=False, comment="是否免费")
    
    # 学习统计
    view_count = Column(Integer, default=0, comment="观看次数")
    completion_count = Column(Integer, default=0, comment="完成人数")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class CourseEnrollment(BaseModel):
    """课程报名记录模型"""
    __tablename__ = "course_enrollments"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(Integer, ForeignKey('user.id'), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    course_id = Column(String(36), ForeignKey('courses.id'), comment="课程ID")
    
    # 报名信息
    enrollment_time = Column(DateTime, default=datetime.utcnow, comment="报名时间")
    payment_amount = Column(Float, default=0.0, comment="支付金额")
    payment_status = Column(String(20), default="unpaid", comment="支付状态")
    
    # 学习进度
    progress_percentage = Column(Float, default=0.0, comment="学习进度百分比")
    completed_lessons = Column(JSON, comment="已完成课时列表")
    current_lesson_id = Column(String(36), comment="当前学习课时ID")
    total_study_time = Column(Integer, default=0, comment="总学习时间（分钟）")
    
    # 学习状态
    status = Column(String(20), default="active", comment="状态: active, completed, dropped, suspended")
    completion_time = Column(DateTime, comment="完成时间")
    certificate_issued = Column(Boolean, default=False, comment="是否已颁发证书")
    
    # 最后活动
    last_access_time = Column(DateTime, comment="最后访问时间")
    last_lesson_id = Column(String(36), comment="最后学习课时ID")
    
    # 关系
    user = relationship("WiscudeUser", back_populates="course_enrollments")
    course = relationship("Course")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class CourseEvaluation(BaseModel):
    """课程评价模型"""
    __tablename__ = "course_evaluations"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, comment="用户ID")
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    course_id = Column(String(36), ForeignKey('courses.id'), comment="课程ID")
    instructor_id = Column(String(36), ForeignKey('instructors.id'), comment="讲师ID")
    
    # 评分
    overall_rating = Column(Integer, nullable=False, comment="总体评分 1-5")
    content_rating = Column(Integer, comment="内容质量评分")
    instructor_rating = Column(Integer, comment="讲师评分")
    difficulty_rating = Column(Integer, comment="难度评分")
    value_rating = Column(Integer, comment="性价比评分")
    
    # 评价内容
    title = Column(String(200), comment="评价标题")
    content = Column(Text, comment="评价内容")
    pros = Column(Text, comment="优点")
    cons = Column(Text, comment="缺点")
    
    # 推荐度
    would_recommend = Column(Boolean, comment="是否推荐")
    recommendation_reason = Column(Text, comment="推荐理由")
    
    # 互动统计
    helpful_count = Column(Integer, default=0, comment="有用数")
    reply_count = Column(Integer, default=0, comment="回复数")
    
    # 状态
    status = Column(String(20), default="published", comment="状态: draft, published, hidden")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    
    # 创建信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
