<template>
  <div class="user-detail">
    <el-page-header @back="$router.go(-1)">
      <template #content>
        <span class="text-large font-600 mr-3">用户详情</span>
      </template>
    </el-page-header>
    
    <div class="content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card title="基本信息" v-loading="loading">
            <div class="user-info">
              <div class="avatar-section">
                <el-avatar :size="80" :src="userInfo.avatar_url">
                  <el-icon size="40"><User /></el-icon>
                </el-avatar>
                <h3>{{ userInfo.full_name || userInfo.nickname }}</h3>
                <p>{{ userInfo.email }}</p>
                <el-tag 
                  :type="userInfo.status === 'ACTIVE' ? 'success' : userInfo.status === 'INACTIVE' ? 'warning' : 'danger'"
                  size="small"
                >
                  {{ userInfo.status === 'ACTIVE' ? '活跃' : userInfo.status === 'INACTIVE' ? '非活跃' : '已删除' }}
                </el-tag>
                <el-tag 
                  :type="userInfo.membership === 'VIP' ? 'danger' : 'info'"
                  size="small"
                  style="margin-left: 8px"
                >
                  {{ userInfo.membership === 'VIP' ? 'VIP会员' : '普通会员' }}
                </el-tag>
              </div>
              
              <div class="info-list">
                <div class="info-item">
                  <span class="label">昵称：</span>
                  <span class="value">{{ userInfo.nickname }}</span>
                </div>
                <div class="info-item">
                  <span class="label">性别：</span>
                  <span class="value">{{ getGenderText(userInfo.gender) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">等级：</span>
                  <span class="value">{{ userInfo.level }}</span>
                </div>
                <div class="info-item">
                  <span class="label">经验值：</span>
                  <span class="value">{{ userInfo.experience_points }}</span>
                </div>
                <div class="info-item">
                  <span class="label">注册时间：</span>
                  <span class="value">{{ formatDate(userInfo.registration_time) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="16">
          <el-card title="学习统计">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ userInfo.total_study_time }}</div>
                <div class="stat-label">总学习时长(分钟)</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userInfo.total_focus_sessions }}</div>
                <div class="stat-label">专注会话数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userInfo.total_check_ins }}</div>
                <div class="stat-label">打卡次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">85%</div>
                <div class="stat-label">完成率</div>
              </div>
            </div>
          </el-card>
          
          <el-card title="最近活动" class="mt-20">
            <el-table :data="recentActivities" style="width: 100%">
              <el-table-column prop="type" label="活动类型" width="120" />
              <el-table-column prop="description" label="描述" />
              <el-table-column prop="created_at" label="时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { userApi } from '@/api/user'

const route = useRoute()
const userId = parseInt(route.params.id as string)

const loading = ref(false)
const userInfo = ref({
  id: 0,
  nickname: '',
  email: '',
  full_name: '',
  avatar_url: '',
  gender: 'UNKNOWN',
  level: 0,
  experience_points: 0,
  total_study_time: 0,
  total_focus_sessions: 0,
  total_check_ins: 0,
  registration_time: '',
  status: 'ACTIVE',
  membership: 'REGULAR',
  is_premium: false
})

const recentActivities = ref([])

onMounted(() => {
  loadUserDetail()
  loadRecentActivities()
})

const loadUserDetail = async () => {
  try {
    loading.value = true
    const response = await userApi.getUserById(userId)
    userInfo.value = response
  } catch (error) {
    console.error('加载用户详情失败:', error)
    ElMessage.error('加载用户详情失败')
  } finally {
    loading.value = false
  }
}

const loadRecentActivities = async () => {
  try {
    // TODO: 调用API获取最近活动
    // 模拟数据
    recentActivities.value = [
      {
        type: '学习会话',
        description: '完成了数学学习任务',
        created_at: '2024-01-20T10:30:00Z'
      },
      {
        type: '每日打卡',
        description: '完成每日打卡',
        created_at: '2024-01-20T08:00:00Z'
      }
    ]
  } catch (error) {
    console.error('加载用户活动失败:', error)
  }
}

const getGenderText = (gender: string) => {
  const genderMap: Record<string, string> = {
    MALE: '男',
    FEMALE: '女',
    OTHER: '其他',
    UNKNOWN: '未知'
  }
  return genderMap[gender] || '未知'
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.user-detail {
  .content {
    margin-top: 20px;
  }
  
  .user-info {
    .avatar-section {
      text-align: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 10px 0 5px;
        color: #303133;
      }
      
      p {
        color: #909399;
        margin: 0 0 10px 0;
      }
      
      .el-tag {
        margin: 2px;
      }
    }
    
    .info-list {
      .info-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .label {
          color: #606266;
          font-weight: 500;
        }
        
        .value {
          color: #303133;
        }
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    
    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>
