<template>
  <div class="base-chart" :style="{ width: width, height: height }">
    <div ref="chartContainer" class="chart-container"></div>
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-if="error" class="chart-error">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
    </div>
    <div v-if="!loading && !error && isEmpty" class="chart-empty">
      <el-icon><DocumentDelete /></el-icon>
      <span>暂无数据</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import { Loading, Warning, DocumentDelete } from '@element-plus/icons-vue'

// Props定义
interface Props {
  width?: string
  height?: string
  options?: echarts.EChartsOption
  loading?: boolean
  error?: string
  theme?: string
  autoResize?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  loading: false,
  error: '',
  theme: 'default',
  autoResize: true
})

// Emits定义
const emit = defineEmits<{
  chartReady: [chart: echarts.ECharts]
  chartClick: [params: any]
  chartHover: [params: any]
}>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()

// 计算属性
const isEmpty = computed(() => {
  if (!props.options || !props.options.series) return true
  
  const series = Array.isArray(props.options.series) 
    ? props.options.series 
    : [props.options.series]
  
  return series.every(s => !s.data || (Array.isArray(s.data) && s.data.length === 0))
})

// 初始化图表
const initChart = async () => {
  if (!chartContainer.value) return

  try {
    // 销毁已存在的图表实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }

    // 创建新的图表实例
    chartInstance.value = echarts.init(chartContainer.value, props.theme)

    // 设置图表配置
    if (props.options) {
      chartInstance.value.setOption(props.options, true)
    }

    // 绑定事件
    chartInstance.value.on('click', (params) => {
      emit('chartClick', params)
    })

    chartInstance.value.on('mouseover', (params) => {
      emit('chartHover', params)
    })

    // 触发图表就绪事件
    emit('chartReady', chartInstance.value)

    // 自动调整大小
    if (props.autoResize) {
      window.addEventListener('resize', handleResize)
    }
  } catch (error) {
    console.error('Chart initialization failed:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value || !props.options) return

  try {
    chartInstance.value.setOption(props.options, true)
  } catch (error) {
    console.error('Chart update failed:', error)
  }
}

// 调整图表大小
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 显示加载状态
const showLoading = () => {
  if (chartInstance.value) {
    chartInstance.value.showLoading('default', {
      text: '加载中...',
      color: '#409eff',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    })
  }
}

// 隐藏加载状态
const hideLoading = () => {
  if (chartInstance.value) {
    chartInstance.value.hideLoading()
  }
}

// 获取图表实例
const getChartInstance = () => {
  return chartInstance.value
}

// 导出图片
const exportImage = (type: 'png' | 'jpeg' = 'png') => {
  if (!chartInstance.value) return null
  
  return chartInstance.value.getDataURL({
    type: `image/${type}`,
    pixelRatio: 2,
    backgroundColor: '#fff'
  })
}

// 清空图表
const clear = () => {
  if (chartInstance.value) {
    chartInstance.value.clear()
  }
}

// 销毁图表
const dispose = () => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = undefined
  }
  
  if (props.autoResize) {
    window.removeEventListener('resize', handleResize)
  }
}

// 监听配置变化
watch(
  () => props.options,
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (loading) => {
    if (loading) {
      showLoading()
    } else {
      hideLoading()
    }
  }
)

// 监听主题变化
watch(
  () => props.theme,
  () => {
    nextTick(() => {
      initChart()
    })
  }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  dispose()
})

// 暴露方法给父组件
defineExpose({
  getChartInstance,
  updateChart,
  exportImage,
  clear,
  dispose,
  showLoading,
  hideLoading
})
</script>

<style scoped>
.base-chart {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart-loading,
.chart-error,
.chart-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
}

.chart-loading .el-icon {
  font-size: 24px;
  color: #409eff;
}

.chart-error .el-icon {
  font-size: 24px;
  color: #f56c6c;
}

.chart-empty .el-icon {
  font-size: 24px;
  color: #c0c4cc;
}

.chart-loading span,
.chart-error span,
.chart-empty span {
  margin-top: 4px;
}
</style>
