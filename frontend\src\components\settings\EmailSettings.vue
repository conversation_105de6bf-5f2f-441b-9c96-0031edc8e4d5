<template>
  <el-card class="settings-card">
    <template #header>
      <div class="card-header">
        <el-icon><Message /></el-icon>
        <span>邮件服务</span>
        <el-tag v-if="isConfigured" type="success" size="small">已配置</el-tag>
        <el-tag v-else type="info" size="small">未配置</el-tag>
      </div>
    </template>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      class="settings-form"
    >
      <!-- SMTP服务器配置 -->
      <div class="settings-section">
        <h4 class="section-title">SMTP服务器配置</h4>
        
        <el-form-item label="SMTP服务器" prop="smtp_host" required>
          <el-input
            v-model="formData.smtp_host"
            placeholder="如：smtp.gmail.com"
            clearable
          />
          <div class="field-description">
            SMTP服务器地址，用于发送系统邮件
          </div>
        </el-form-item>
        
        <el-form-item label="SMTP端口" prop="smtp_port" required>
          <el-input-number
            v-model="formData.smtp_port"
            :min="1"
            :max="65535"
            controls-position="right"
            style="width: 200px"
          />
          <div class="port-hints">
            <el-button
              v-for="port in commonPorts"
              :key="port.value"
              size="small"
              text
              @click="formData.smtp_port = port.value"
            >
              {{ port.label }}
            </el-button>
          </div>
          <div class="field-description">
            SMTP服务器端口号，常用端口：25(不加密)、587(TLS)、465(SSL)
          </div>
        </el-form-item>
        
        <el-form-item label="用户名" prop="smtp_user" required>
          <el-input
            v-model="formData.smtp_user"
            placeholder="请输入邮箱地址"
            clearable
          />
          <div class="field-description">
            用于SMTP认证的用户名，通常是完整的邮箱地址
          </div>
        </el-form-item>
        
        <el-form-item label="密码" prop="smtp_password" required>
          <el-input
            v-model="formData.smtp_password"
            type="password"
            placeholder="请输入邮箱密码或应用密码"
            show-password
            clearable
          />
          <div class="field-description">
            用于SMTP认证的密码，建议使用应用专用密码
          </div>
        </el-form-item>
        
        <el-form-item label="加密方式">
          <div class="encryption-options">
            <el-checkbox
              v-model="formData.smtp_tls"
              label="启用TLS"
            />
            <el-checkbox
              v-model="formData.smtp_ssl"
              label="启用SSL"
            />
          </div>
          <div class="field-description">
            选择SMTP连接的加密方式，TLS适用于587端口，SSL适用于465端口
          </div>
        </el-form-item>
      </div>
      
      <!-- 发件人设置 -->
      <div class="settings-section">
        <h4 class="section-title">发件人设置</h4>
        
        <el-form-item label="发件人邮箱" prop="email_from" required>
          <el-input
            v-model="formData.email_from"
            placeholder="如：<EMAIL>"
            clearable
          />
          <div class="field-description">
            系统邮件的发件人地址，通常使用noreply前缀
          </div>
        </el-form-item>
        
        <el-form-item label="发件人名称" prop="email_from_name">
          <el-input
            v-model="formData.email_from_name"
            placeholder="如：WisCude 系统"
            clearable
          />
          <div class="field-description">
            系统邮件的发件人显示名称
          </div>
        </el-form-item>
      </div>
      
      <!-- 邮件模板管理 -->
      <div class="settings-section">
        <h4 class="section-title">邮件模板管理</h4>
        
        <el-tabs v-model="activeTemplate" type="border-card">
          <el-tab-pane
            v-for="(template, key) in emailTemplates"
            :key="key"
            :label="template.name"
            :name="key"
          >
            <el-form-item label="邮件主题">
              <el-input
                v-model="template.subject"
                placeholder="请输入邮件主题"
              />
            </el-form-item>
            
            <el-form-item label="邮件内容">
              <el-input
                v-model="template.content"
                type="textarea"
                :rows="8"
                placeholder="请输入邮件内容，支持HTML格式"
              />
            </el-form-item>
            
            <el-form-item label="可用变量">
              <div class="template-variables">
                <el-tag
                  v-for="variable in template.variables"
                  :key="variable"
                  size="small"
                  @click="insertVariable(variable)"
                  style="cursor: pointer; margin-right: 8px; margin-bottom: 4px;"
                >
                  {{ variable }}
                </el-tag>
              </div>
              <div class="field-description">
                点击变量名可插入到邮件内容中
              </div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <!-- 测试邮件 -->
      <div class="settings-section">
        <h4 class="section-title">测试邮件</h4>
        
        <el-form-item label="测试邮箱">
          <el-input
            v-model="testEmail"
            placeholder="请输入测试邮箱地址"
            clearable
            style="width: 300px"
          />
          <el-button
            type="primary"
            @click="sendTestEmail"
            :loading="testing"
            style="margin-left: 12px"
          >
            发送测试邮件
          </el-button>
        </el-form-item>
        
        <div v-if="testResult" class="test-result">
          <el-alert
            :type="testResult.success ? 'success' : 'error'"
            :title="testResult.message"
            :closable="false"
            show-icon
          />
        </div>
      </div>
      
      <el-form-item class="form-actions">
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存设置
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button
          type="success"
          @click="testConnection"
          :loading="testing"
        >
          测试连接
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Message } from '@element-plus/icons-vue'
import type { SystemSettings } from '@/types/settings'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  settings: SystemSettings
}

interface Emits {
  (e: 'update', settings: Partial<SystemSettings>): void
  (e: 'save', settings: Partial<SystemSettings>): void
  (e: 'test', config: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const saving = ref(false)
const testing = ref(false)
const testEmail = ref('')
const testResult = ref<any>(null)
const activeTemplate = ref('welcome')

const formData = reactive<Partial<SystemSettings>>({
  smtp_host: '',
  smtp_port: 587,
  smtp_user: '',
  smtp_password: '',
  smtp_tls: true,
  smtp_ssl: false,
  email_from: '',
  email_from_name: '',
  email_templates: {}
})

const emailTemplates = reactive({
  welcome: {
    name: '欢迎邮件',
    subject: '欢迎使用 WisCude 系统',
    content: `<h2>欢迎，{{username}}！</h2>
<p>您的账户已成功创建。</p>
<p>登录地址：{{login_url}}</p>
<p>如有问题，请联系管理员。</p>`,
    variables: ['{{username}}', '{{email}}', '{{login_url}}', '{{system_name}}']
  },
  reset_password: {
    name: '密码重置',
    subject: '密码重置请求',
    content: `<h2>密码重置</h2>
<p>您好，{{username}}！</p>
<p>我们收到了您的密码重置请求。</p>
<p>请点击以下链接重置密码：</p>
<p><a href="{{reset_url}}">重置密码</a></p>
<p>如果您没有请求重置密码，请忽略此邮件。</p>`,
    variables: ['{{username}}', '{{email}}', '{{reset_url}}', '{{expire_time}}']
  },
  notification: {
    name: '系统通知',
    subject: '系统通知',
    content: `<h2>系统通知</h2>
<p>您好，{{username}}！</p>
<p>{{message}}</p>
<p>时间：{{timestamp}}</p>`,
    variables: ['{{username}}', '{{message}}', '{{timestamp}}', '{{system_name}}']
  }
})

const commonPorts = [
  { label: '587 (TLS)', value: 587 },
  { label: '465 (SSL)', value: 465 },
  { label: '25 (无加密)', value: 25 }
]

const formRules: FormRules = {
  smtp_host: [
    { required: true, message: '请输入SMTP服务器地址', trigger: 'blur' }
  ],
  smtp_port: [
    { required: true, message: '请输入SMTP端口', trigger: 'blur' }
  ],
  smtp_user: [
    { required: true, message: '请输入SMTP用户名', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  smtp_password: [
    { required: true, message: '请输入SMTP密码', trigger: 'blur' }
  ],
  email_from: [
    { required: true, message: '请输入发件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

const isConfigured = computed(() => {
  return !!(formData.smtp_host && formData.smtp_user && formData.smtp_password)
})

// 监听props变化
watch(() => props.settings, (newSettings) => {
  Object.assign(formData, newSettings)
  if (newSettings.email_templates) {
    Object.assign(emailTemplates, newSettings.email_templates)
  }
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newData) => {
  emit('update', { ...newData, email_templates: emailTemplates })
}, { deep: true })

const insertVariable = (variable: string) => {
  const template = emailTemplates[activeTemplate.value as keyof typeof emailTemplates]
  if (template) {
    template.content += variable
  }
}

const sendTestEmail = async () => {
  if (!testEmail.value) {
    ElMessage.warning('请输入测试邮箱地址')
    return
  }
  
  if (!isConfigured.value) {
    ElMessage.warning('请先配置SMTP服务器')
    return
  }
  
  testing.value = true
  testResult.value = null
  
  try {
    // 模拟发送测试邮件
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    testResult.value = {
      success: true,
      message: `测试邮件已发送到 ${testEmail.value}`
    }
    
    ElMessage.success('测试邮件发送成功')
  } catch (error) {
    testResult.value = {
      success: false,
      message: '测试邮件发送失败：' + (error as Error).message
    }
    
    ElMessage.error('测试邮件发送失败')
  } finally {
    testing.value = false
  }
}

const testConnection = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    testing.value = true
    emit('test', formData)
    
    setTimeout(() => {
      testing.value = false
    }, 2000)
    
  } catch (error) {
    ElMessage.error('请先完善邮件配置')
  }
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    emit('save', { ...formData, email_templates: emailTemplates })
    
    setTimeout(() => {
      saving.value = false
    }, 1000)
    
  } catch (error) {
    ElMessage.error('表单验证失败，请检查输入')
  }
}

const handleReset = () => {
  ElMessageBox.confirm('确定要重置邮件设置吗？', '确认重置', {
    type: 'warning'
  }).then(() => {
    formRef.value?.resetFields()
    Object.assign(formData, props.settings)
    ElMessage.success('邮件设置已重置')
  }).catch(() => {
    // 用户取消
  })
}

onMounted(() => {
  Object.assign(formData, props.settings)
  if (props.settings.email_templates) {
    Object.assign(emailTemplates, props.settings.email_templates)
  }
})
</script>

<style lang="scss" scoped>
.settings-card {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .settings-form {
    .settings-section {
      margin-bottom: 32px;
      
      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }
    
    .field-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
      line-height: 1.4;
    }
    
    .port-hints {
      margin-top: 8px;
      display: flex;
      gap: 8px;
    }
    
    .encryption-options {
      display: flex;
      gap: 16px;
    }
    
    .template-variables {
      margin-bottom: 8px;
    }
    
    .test-result {
      margin-top: 16px;
    }
    
    .form-actions {
      margin-top: 32px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
</style>
