/**
 * 操作日志记录系统
 * 用于记录用户操作、系统事件和错误信息
 */

import { useAuthStore } from '@/store/auth'

// 日志级别
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

// 操作类型
export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  EXPORT = 'export',
  IMPORT = 'import',
  LOGIN = 'login',
  LOGOUT = 'logout',
  UPLOAD = 'upload',
  DOWNLOAD = 'download',
  PUBLISH = 'publish',
  UNPUBLISH = 'unpublish'
}

// 模块类型
export enum ModuleType {
  ADVERTISEMENT = 'advertisement',
  QUESTION_BANK = 'question_bank',
  ENGLISH_PRACTICE = 'english_practice',
  PSYCHOLOGY = 'psychology',
  COURSE_MANAGEMENT = 'course_management',
  SOFTWARE_UPDATE = 'software_update',
  LEARNING_ANALYTICS = 'learning_analytics',
  COMMUNITY = 'community',
  SYSTEM = 'system',
  USER = 'user'
}

// 日志条目接口
export interface LogEntry {
  id?: string
  timestamp: string
  level: LogLevel
  module: ModuleType
  operation: OperationType
  userId?: string
  username?: string
  userRole?: string
  resourceId?: string
  resourceType?: string
  description: string
  details?: Record<string, any>
  ip?: string
  userAgent?: string
  duration?: number
  success: boolean
  errorMessage?: string
  stackTrace?: string
}

// 日志配置
export interface LoggerConfig {
  enabled: boolean
  level: LogLevel
  maxEntries: number
  enableConsole: boolean
  enableRemote: boolean
  remoteEndpoint?: string
  batchSize: number
  flushInterval: number
}

/**
 * 日志记录器类
 */
export class Logger {
  private config: LoggerConfig
  private logBuffer: LogEntry[] = []
  private flushTimer?: number

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      enabled: true,
      level: LogLevel.INFO,
      maxEntries: 1000,
      enableConsole: true,
      enableRemote: true,
      batchSize: 10,
      flushInterval: 5000,
      ...config
    }

    // 启动定时刷新
    if (this.config.enabled && this.config.enableRemote) {
      this.startFlushTimer()
    }

    // 监听页面卸载事件，确保日志被发送
    window.addEventListener('beforeunload', () => {
      this.flush()
    })
  }

  /**
   * 记录日志
   */
  log(entry: Partial<LogEntry>): void {
    if (!this.config.enabled) return

    const authStore = useAuthStore()
    const currentUser = authStore.user

    const logEntry: LogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: entry.level || LogLevel.INFO,
      module: entry.module || ModuleType.SYSTEM,
      operation: entry.operation || OperationType.VIEW,
      userId: currentUser?.id,
      username: currentUser?.username,
      userRole: currentUser?.role,
      description: entry.description || '',
      ip: this.getClientIP(),
      userAgent: navigator.userAgent,
      success: entry.success !== false,
      ...entry
    }

    // 检查日志级别
    if (!this.shouldLog(logEntry.level)) return

    // 添加到缓冲区
    this.logBuffer.push(logEntry)

    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(logEntry)
    }

    // 检查是否需要立即刷新
    if (this.logBuffer.length >= this.config.batchSize) {
      this.flush()
    }

    // 限制缓冲区大小
    if (this.logBuffer.length > this.config.maxEntries) {
      this.logBuffer = this.logBuffer.slice(-this.config.maxEntries)
    }
  }

  /**
   * 记录操作日志
   */
  logOperation(
    module: ModuleType,
    operation: OperationType,
    description: string,
    options: Partial<LogEntry> = {}
  ): void {
    this.log({
      level: LogLevel.INFO,
      module,
      operation,
      description,
      ...options
    })
  }

  /**
   * 记录错误日志
   */
  logError(
    module: ModuleType,
    error: Error | string,
    options: Partial<LogEntry> = {}
  ): void {
    const errorMessage = error instanceof Error ? error.message : error
    const stackTrace = error instanceof Error ? error.stack : undefined

    this.log({
      level: LogLevel.ERROR,
      module,
      operation: OperationType.VIEW,
      description: `错误: ${errorMessage}`,
      success: false,
      errorMessage,
      stackTrace,
      ...options
    })
  }

  /**
   * 记录性能日志
   */
  logPerformance(
    module: ModuleType,
    operation: OperationType,
    duration: number,
    description: string,
    options: Partial<LogEntry> = {}
  ): void {
    this.log({
      level: LogLevel.INFO,
      module,
      operation,
      description: `${description} (耗时: ${duration}ms)`,
      duration,
      ...options
    })
  }

  /**
   * 记录用户行为
   */
  logUserAction(
    module: ModuleType,
    action: string,
    resourceType?: string,
    resourceId?: string,
    details?: Record<string, any>
  ): void {
    this.log({
      level: LogLevel.INFO,
      module,
      operation: OperationType.VIEW,
      description: `用户操作: ${action}`,
      resourceType,
      resourceId,
      details
    })
  }

  /**
   * 刷新日志到远程服务器
   */
  async flush(): Promise<void> {
    if (!this.config.enableRemote || this.logBuffer.length === 0) return

    const logsToSend = [...this.logBuffer]
    this.logBuffer = []

    try {
      if (this.config.remoteEndpoint) {
        await fetch(this.config.remoteEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ logs: logsToSend })
        })
      }
    } catch (error) {
      console.error('Failed to send logs to remote server:', error)
      // 将失败的日志重新加入缓冲区
      this.logBuffer.unshift(...logsToSend)
    }
  }

  /**
   * 获取本地日志
   */
  getLocalLogs(): LogEntry[] {
    return [...this.logBuffer]
  }

  /**
   * 清空本地日志
   */
  clearLocalLogs(): void {
    this.logBuffer = []
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config }
    
    if (this.config.enabled && this.config.enableRemote) {
      this.startFlushTimer()
    } else {
      this.stopFlushTimer()
    }
  }

  /**
   * 检查是否应该记录该级别的日志
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.FATAL]
    const currentLevelIndex = levels.indexOf(this.config.level)
    const logLevelIndex = levels.indexOf(level)
    return logLevelIndex >= currentLevelIndex
  }

  /**
   * 输出到控制台
   */
  private logToConsole(entry: LogEntry): void {
    const message = `[${entry.timestamp}] ${entry.level.toUpperCase()} ${entry.module}:${entry.operation} - ${entry.description}`
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry)
        break
      case LogLevel.INFO:
        console.info(message, entry)
        break
      case LogLevel.WARN:
        console.warn(message, entry)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(message, entry)
        break
    }
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    this.stopFlushTimer()
    this.flushTimer = window.setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  /**
   * 停止定时刷新
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
      this.flushTimer = undefined
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 获取客户端IP（简化版）
   */
  private getClientIP(): string {
    // 在实际应用中，可能需要通过API获取真实IP
    return 'unknown'
  }
}

// 创建全局日志记录器实例
export const logger = new Logger({
  remoteEndpoint: '/api/logs'
})

/**
 * 性能监控装饰器
 */
export function logPerformance(module: ModuleType, operation: OperationType) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const startTime = performance.now()
      let success = true
      let error: any = null
      
      try {
        const result = await originalMethod.apply(this, args)
        return result
      } catch (e) {
        success = false
        error = e
        throw e
      } finally {
        const duration = performance.now() - startTime
        
        logger.logPerformance(
          module,
          operation,
          duration,
          `${target.constructor.name}.${propertyKey}`,
          {
            success,
            errorMessage: error?.message,
            details: { args: args.length }
          }
        )
      }
    }
    
    return descriptor
  }
}

/**
 * 操作日志装饰器
 */
export function logOperation(module: ModuleType, operation: OperationType, description?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const desc = description || `${target.constructor.name}.${propertyKey}`
      
      try {
        const result = await originalMethod.apply(this, args)
        
        logger.logOperation(module, operation, desc, {
          success: true,
          details: { args: args.length }
        })
        
        return result
      } catch (error) {
        logger.logOperation(module, operation, desc, {
          success: false,
          errorMessage: error instanceof Error ? error.message : String(error)
        })
        
        throw error
      }
    }
    
    return descriptor
  }
}

/**
 * 日志工具函数
 */
export const log = {
  // 快捷方法
  debug: (module: ModuleType, message: string, details?: any) => 
    logger.log({ level: LogLevel.DEBUG, module, description: message, details }),
  
  info: (module: ModuleType, message: string, details?: any) => 
    logger.log({ level: LogLevel.INFO, module, description: message, details }),
  
  warn: (module: ModuleType, message: string, details?: any) => 
    logger.log({ level: LogLevel.WARN, module, description: message, details }),
  
  error: (module: ModuleType, error: Error | string, details?: any) => 
    logger.logError(module, error, { details }),
  
  // 操作日志
  operation: logger.logOperation.bind(logger),
  
  // 性能日志
  performance: logger.logPerformance.bind(logger),
  
  // 用户行为
  userAction: logger.logUserAction.bind(logger)
}

export default logger
