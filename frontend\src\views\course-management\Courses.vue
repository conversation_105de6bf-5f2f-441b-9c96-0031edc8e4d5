<template>
  <div class="courses-management">
    <div class="page-header">
      <h1>课程管理</h1>
      <p>管理在线课程内容、章节结构、学习资源和发布状态</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="课程列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建课程
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedCourses.length === 0"
              @click="batchPublish"
            >
              批量发布
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedCourses.length === 0"
              @click="batchArchive"
            >
              批量归档
            </el-button>
            <el-button @click="exportCourses">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索课程名称"
              style="width: 200px"
              clearable
              @change="loadCourses"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="categoryFilter" placeholder="分类筛选" style="width: 120px" @change="loadCourses">
              <el-option label="全部分类" value="" />
              <el-option label="编程开发" value="programming" />
              <el-option label="设计创意" value="design" />
              <el-option label="商业管理" value="business" />
              <el-option label="语言学习" value="language" />
              <el-option label="职业技能" value="skills" />
            </el-select>
            <el-select v-model="levelFilter" placeholder="难度筛选" style="width: 100px" @change="loadCourses">
              <el-option label="全部难度" value="" />
              <el-option label="入门" value="beginner" />
              <el-option label="初级" value="elementary" />
              <el-option label="中级" value="intermediate" />
              <el-option label="高级" value="advanced" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadCourses">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>

        <!-- 课程列表 -->
        <el-table
          :data="courses"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="课程信息" min-width="350">
            <template #default="{ row }">
              <div class="course-info">
                <div class="course-cover">
                  <img :src="row.cover_image || '/default-course.png'" :alt="row.title" />
                </div>
                <div class="course-details">
                  <div class="course-title">{{ row.title }}</div>
                  <div class="course-description">{{ row.description }}</div>
                  <div class="course-meta">
                    <el-tag size="small" :type="getCategoryTagType(row.category)">
                      {{ getCategoryName(row.category) }}
                    </el-tag>
                    <span class="difficulty-badge" :class="`difficulty-${row.difficulty_level}`">
                      {{ getDifficultyName(row.difficulty_level) }}
                    </span>
                    <span class="duration-info">{{ formatDuration(row.duration) }}</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="讲师信息" width="150">
            <template #default="{ row }">
              <div class="instructor-info">
                <div class="instructor-avatar">
                  <img :src="row.instructor.avatar || '/default-avatar.png'" :alt="row.instructor.name" />
                </div>
                <div class="instructor-details">
                  <div class="instructor-name">{{ row.instructor.name }}</div>
                  <div class="instructor-title">{{ row.instructor.title }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习数据" width="120">
            <template #default="{ row }">
              <div class="learning-stats">
                <div class="stat-item">
                  <span class="stat-label">学员:</span>
                  <span class="stat-value">{{ row.student_count }}人</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">完成:</span>
                  <span class="stat-value completion-rate">{{ (row.completion_rate * 100).toFixed(1) }}%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">评分:</span>
                  <span class="stat-value rating">{{ row.rating }}★</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="课程结构" width="100">
            <template #default="{ row }">
              <div class="course-structure">
                <div class="structure-item">
                  <span class="structure-label">章节:</span>
                  <span class="structure-value">{{ row.chapter_count }}</span>
                </div>
                <div class="structure-item">
                  <span class="structure-label">课时:</span>
                  <span class="structure-value">{{ row.lesson_count }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="价格" width="100">
            <template #default="{ row }">
              <div class="price-info">
                <span v-if="row.price === 0" class="free-price">免费</span>
                <span v-else class="paid-price">¥{{ row.price }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="create-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button text size="small" @click="viewCourse(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button text size="small" @click="editCourse(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button text size="small" @click="manageLessons(row)">
                <el-icon><Menu /></el-icon>
                课时
              </el-button>
              <el-button text size="small" @click="viewStudents(row)">
                <el-icon><UserFilled /></el-icon>
                学员
              </el-button>
              <el-button
                text
                size="small"
                :class="row.status === 'published' ? 'warning' : 'success'"
                @click="toggleCourseStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '归档' : '发布' }}
              </el-button>
              <el-button text size="small" class="danger" @click="deleteCourse(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadCourses"
            @current-change="loadCourses"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="分类分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">课程分类分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="难度分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">课程难度分布图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="学习效果分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">学习效果分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑课程对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingCourse ? '编辑课程' : '新建课程'"
      width="900px"
      @close="resetForm"
    >
      <el-form :model="courseForm" :rules="courseRules" ref="courseFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="课程名称" prop="title">
              <el-input v-model="courseForm.title" placeholder="请输入课程名称" />
            </el-form-item>
            <el-form-item label="课程描述" prop="description">
              <el-input v-model="courseForm.description" type="textarea" rows="3" placeholder="请输入课程描述" />
            </el-form-item>
            <el-form-item label="课程分类" prop="category">
              <el-select v-model="courseForm.category" placeholder="请选择课程分类">
                <el-option label="编程开发" value="programming" />
                <el-option label="设计创意" value="design" />
                <el-option label="商业管理" value="business" />
                <el-option label="语言学习" value="language" />
                <el-option label="职业技能" value="skills" />
              </el-select>
            </el-form-item>
            <el-form-item label="难度等级" prop="difficulty_level">
              <el-select v-model="courseForm.difficulty_level" placeholder="请选择难度等级">
                <el-option label="入门" value="beginner" />
                <el-option label="初级" value="elementary" />
                <el-option label="中级" value="intermediate" />
                <el-option label="高级" value="advanced" />
              </el-select>
            </el-form-item>
            <el-form-item label="课程价格" prop="price">
              <el-input-number v-model="courseForm.price" :min="0" :step="1" placeholder="0表示免费" />
            </el-form-item>
            <el-form-item label="预计时长" prop="duration">
              <el-input-number v-model="courseForm.duration" :min="1" placeholder="分钟" />
            </el-form-item>
            <el-form-item label="课程标签" prop="tags">
              <el-select 
                v-model="courseForm.tags" 
                multiple 
                filterable 
                allow-create 
                placeholder="请选择或输入标签"
              >
                <el-option label="热门" value="热门" />
                <el-option label="推荐" value="推荐" />
                <el-option label="新课" value="新课" />
                <el-option label="实战" value="实战" />
                <el-option label="基础" value="基础" />
              </el-select>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="讲师信息" name="instructor">
            <el-form-item label="选择讲师" prop="instructor_id">
              <el-select v-model="courseForm.instructor_id" placeholder="请选择讲师" filterable>
                <el-option 
                  v-for="instructor in instructors" 
                  :key="instructor.id" 
                  :label="instructor.name" 
                  :value="instructor.id"
                >
                  <div class="instructor-option">
                    <span class="instructor-name">{{ instructor.name }}</span>
                    <span class="instructor-title">{{ instructor.title }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="讲师介绍">
              <el-input v-model="courseForm.instructor_intro" type="textarea" rows="3" placeholder="讲师介绍" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="课程封面" name="cover">
            <el-form-item label="课程封面">
              <div class="cover-upload">
                <el-upload
                  class="upload-demo"
                  :show-file-list="false"
                  :before-upload="beforeCoverUpload"
                  :http-request="uploadCover"
                  accept="image/*"
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    上传封面图片
                  </el-button>
                </el-upload>
                <div v-if="courseForm.cover_image" class="cover-preview">
                  <img :src="courseForm.cover_image" alt="课程封面" />
                  <el-button text size="small" class="remove-btn" @click="removeCover">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="封面说明">
              <el-input v-model="courseForm.cover_description" placeholder="封面图片说明" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="课程大纲" name="outline">
            <div class="outline-section">
              <div class="section-header">
                <span>课程大纲</span>
                <el-button type="primary" size="small" @click="addChapter">
                  <el-icon><Plus /></el-icon>
                  添加章节
                </el-button>
              </div>
              <div v-for="(chapter, index) in courseForm.chapters" :key="index" class="chapter-form">
                <div class="chapter-header">
                  <span class="chapter-label">第{{ index + 1 }}章</span>
                  <el-button text size="small" class="danger" @click="removeChapter(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-form-item label="章节标题">
                  <el-input v-model="chapter.title" placeholder="请输入章节标题" />
                </el-form-item>
                <el-form-item label="章节描述">
                  <el-input v-model="chapter.description" placeholder="请输入章节描述" />
                </el-form-item>
                <el-form-item label="课时列表">
                  <div class="lessons-list">
                    <div v-for="(lesson, lessonIndex) in chapter.lessons" :key="lessonIndex" class="lesson-item">
                      <el-input v-model="lesson.title" placeholder="课时标题" />
                      <el-input-number v-model="lesson.duration" :min="1" placeholder="时长(分钟)" />
                      <el-button text size="small" class="danger" @click="removeLesson(index, lessonIndex)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                    <el-button text size="small" @click="addLesson(index)">
                      <el-icon><Plus /></el-icon>
                      添加课时
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveCourse" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 课程详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="课程详情"
      width="800px"
    >
      <div class="course-detail" v-if="viewingCourse">
        <div class="detail-header">
          <div class="course-cover-large">
            <img :src="viewingCourse.cover_image || '/default-course.png'" :alt="viewingCourse.title" />
          </div>
          <div class="course-info-large">
            <h2 class="course-title-large">{{ viewingCourse.title }}</h2>
            <div class="course-meta-large">
              <el-tag :type="getCategoryTagType(viewingCourse.category)">
                {{ getCategoryName(viewingCourse.category) }}
              </el-tag>
              <span class="difficulty-badge" :class="`difficulty-${viewingCourse.difficulty_level}`">
                {{ getDifficultyName(viewingCourse.difficulty_level) }}
              </span>
              <span class="price-large">
                {{ viewingCourse.price === 0 ? '免费' : `¥${viewingCourse.price}` }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>课程描述</h3>
          <div class="description-detail">{{ viewingCourse.description }}</div>
        </div>
        
        <div class="detail-section">
          <h3>讲师信息</h3>
          <div class="instructor-detail">
            <div class="instructor-avatar-large">
              <img :src="viewingCourse.instructor.avatar || '/default-avatar.png'" :alt="viewingCourse.instructor.name" />
            </div>
            <div class="instructor-info-large">
              <div class="instructor-name-large">{{ viewingCourse.instructor.name }}</div>
              <div class="instructor-title-large">{{ viewingCourse.instructor.title }}</div>
              <div class="instructor-intro">{{ viewingCourse.instructor_intro }}</div>
            </div>
          </div>
        </div>
        
        <div class="detail-section" v-if="viewingCourse.chapters && viewingCourse.chapters.length">
          <h3>课程大纲</h3>
          <div class="chapters-detail">
            <div v-for="(chapter, index) in viewingCourse.chapters" :key="index" class="chapter-detail">
              <div class="chapter-title">第{{ index + 1 }}章 {{ chapter.title }}</div>
              <div class="chapter-description">{{ chapter.description }}</div>
              <div class="lessons-detail">
                <div v-for="(lesson, lessonIndex) in chapter.lessons" :key="lessonIndex" class="lesson-detail">
                  {{ lessonIndex + 1 }}. {{ lesson.title }} ({{ lesson.duration }}分钟)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, Download, Menu, UserFilled
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedCourses = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const editingCourse = ref(null)
const viewingCourse = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const categoryFilter = ref('')
const levelFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 课程列表
const courses = ref([
  {
    id: '1',
    title: 'Vue.js 3.0 完整教程',
    description: '从零开始学习Vue.js 3.0，包含组合式API、响应式系统、路由管理等核心概念',
    category: 'programming',
    difficulty_level: 'intermediate',
    price: 299,
    duration: 1200,
    cover_image: '',
    instructor: {
      id: '1',
      name: '张老师',
      title: '高级前端工程师',
      avatar: ''
    },
    instructor_intro: '10年前端开发经验，Vue.js核心贡献者',
    student_count: 1234,
    completion_rate: 0.85,
    rating: 4.8,
    chapter_count: 8,
    lesson_count: 45,
    chapters: [],
    tags: ['热门', '推荐'],
    status: 'published',
    created_at: '2024-01-15 10:30:00'
  }
])

// 讲师列表
const instructors = ref([
  { id: '1', name: '张老师', title: '高级前端工程师' },
  { id: '2', name: '李老师', title: '资深后端工程师' },
  { id: '3', name: '王老师', title: 'UI/UX设计专家' }
])

// 表单数据
const courseForm = reactive({
  title: '',
  description: '',
  category: '',
  difficulty_level: 'beginner',
  price: 0,
  duration: 60,
  cover_image: '',
  cover_description: '',
  instructor_id: '',
  instructor_intro: '',
  tags: [],
  chapters: [
    {
      title: '',
      description: '',
      lessons: [
        { title: '', duration: 10 }
      ]
    }
  ]
})

// 表单验证规则
const courseRules = {
  title: [
    { required: true, message: '请输入课程名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入课程描述', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择课程分类', trigger: 'change' }
  ],
  difficulty_level: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],
  instructor_id: [
    { required: true, message: '请选择讲师', trigger: 'change' }
  ]
}

// 方法
const loadCourses = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取课程列表
    console.log('Loading courses...')
  } catch (error) {
    ElMessage.error('加载课程列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedCourses.value = selection
}

const getCategoryName = (category) => {
  const categories = {
    programming: '编程开发',
    design: '设计创意',
    business: '商业管理',
    language: '语言学习',
    skills: '职业技能'
  }
  return categories[category] || category
}

const getCategoryTagType = (category) => {
  const types = {
    programming: 'primary',
    design: 'success',
    business: 'warning',
    language: 'danger',
    skills: 'info'
  }
  return types[category] || ''
}

const getDifficultyName = (level) => {
  const levels = {
    beginner: '入门',
    elementary: '初级',
    intermediate: '中级',
    advanced: '高级'
  }
  return levels[level] || level
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const formatDuration = (minutes) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return hours > 0 ? `${hours}h${mins}m` : `${mins}m`
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewCourse = (course) => {
  viewingCourse.value = course
  showViewDialog.value = true
}

const editCourse = (course) => {
  editingCourse.value = course
  Object.assign(courseForm, course)
  showCreateDialog.value = true
}

const manageLessons = (course) => {
  // TODO: 跳转到课时管理页面
  console.log('Managing lessons for course:', course)
}

const viewStudents = (course) => {
  // TODO: 跳转到学员管理页面
  console.log('Viewing students for course:', course)
}

const resetForm = () => {
  editingCourse.value = null
  Object.assign(courseForm, {
    title: '',
    description: '',
    category: '',
    difficulty_level: 'beginner',
    price: 0,
    duration: 60,
    cover_image: '',
    cover_description: '',
    instructor_id: '',
    instructor_intro: '',
    tags: [],
    chapters: [
      {
        title: '',
        description: '',
        lessons: [
          { title: '', duration: 10 }
        ]
      }
    ]
  })
  formActiveTab.value = 'basic'
}

const addChapter = () => {
  courseForm.chapters.push({
    title: '',
    description: '',
    lessons: [
      { title: '', duration: 10 }
    ]
  })
}

const removeChapter = (index) => {
  if (courseForm.chapters.length > 1) {
    courseForm.chapters.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个章节')
  }
}

const addLesson = (chapterIndex) => {
  courseForm.chapters[chapterIndex].lessons.push({ title: '', duration: 10 })
}

const removeLesson = (chapterIndex, lessonIndex) => {
  const chapter = courseForm.chapters[chapterIndex]
  if (chapter.lessons.length > 1) {
    chapter.lessons.splice(lessonIndex, 1)
  } else {
    ElMessage.warning('每个章节至少需要保留一个课时')
  }
}

const beforeCoverUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const uploadCover = async (options) => {
  // TODO: 实现封面上传逻辑
  console.log('Uploading cover...', options.file)
  // 模拟上传成功
  courseForm.cover_image = URL.createObjectURL(options.file)
  ElMessage.success('封面上传成功')
}

const removeCover = () => {
  courseForm.cover_image = ''
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', courseForm)
    showCreateDialog.value = false
    ElMessage.success('课程已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveCourse = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing course...', courseForm)
    showCreateDialog.value = false
    ElMessage.success(editingCourse.value ? '课程更新成功' : '课程创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleCourseStatus = async (course) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling course status...', course)
}

const deleteCourse = async (course) => {
  try {
    await ElMessageBox.confirm('确定要删除这个课程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting course...', course)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing courses...', selectedCourses.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving courses...', selectedCourses.value)
}

const exportCourses = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting courses...')
}

onMounted(() => {
  loadCourses()
})
</script>

<style scoped>
.courses-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.course-info {
  display: flex;
  gap: 12px;
  padding: 8px 0;
}

.course-cover {
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.course-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-details {
  flex: 1;
}

.course-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
}

.course-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.difficulty-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.difficulty-badge.difficulty-beginner {
  background-color: #67c23a;
}

.difficulty-badge.difficulty-elementary {
  background-color: #409eff;
}

.difficulty-badge.difficulty-intermediate {
  background-color: #e6a23c;
}

.difficulty-badge.difficulty-advanced {
  background-color: #f56c6c;
}

.duration-info {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.instructor-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.instructor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.instructor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.instructor-details {
  flex: 1;
}

.instructor-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.instructor-title {
  font-size: 11px;
  color: #909399;
}

.learning-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.completion-rate {
  color: #67c23a !important;
}

.rating {
  color: #f56c6c !important;
}

.course-structure {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.structure-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.structure-label {
  color: #909399;
}

.structure-value {
  color: #606266;
  font-weight: 500;
}

.price-info {
  text-align: center;
}

.free-price {
  color: #67c23a;
  font-weight: 600;
}

.paid-price {
  color: #f56c6c;
  font-weight: 600;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.instructor-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.instructor-name {
  font-weight: 500;
  color: #303133;
}

.instructor-title {
  font-size: 12px;
  color: #909399;
}

.cover-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cover-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.cover-preview img {
  width: 200px;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
}

.remove-btn {
  color: #f56c6c;
}

.outline-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #303133;
}

.chapter-form {
  margin-bottom: 24px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chapter-label {
  font-weight: 500;
  color: #303133;
}

.lessons-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.lesson-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.course-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.course-cover-large {
  width: 200px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.course-cover-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-info-large {
  flex: 1;
}

.course-title-large {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.course-meta-large {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.price-large {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.description-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
}

.instructor-detail {
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.instructor-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.instructor-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.instructor-info-large {
  flex: 1;
}

.instructor-name-large {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.instructor-title-large {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.instructor-intro {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

.chapters-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chapter-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.chapter-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.chapter-description {
  font-size: 13px;
  color: #606266;
  margin-bottom: 12px;
}

.lessons-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.lesson-detail {
  font-size: 12px;
  color: #606266;
  padding: 4px 0;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
