"""
WisCude 后台管理系统 - 系统设置API
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
import json
import logging
import asyncio

from app.core.database import get_db
from app.core.security import get_current_user, get_current_active_superuser
from app.models.settings import (
    SystemSettingsResponse, SystemSettingsUpdate,
    SettingsTestRequest, SettingsImportRequest, SettingsExportResponse,
    AIConfigRequest, AIConfigResponse, AITestRequest, AITestResult,
    AIModelConfig
)
from app.schemas.settings import LogFileInfo, SystemInfo
from app.services.settings_service import get_settings_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/settings", tags=["系统设置"])

@router.get("/", response_model=SystemSettingsResponse, summary="获取系统设置")
async def get_settings(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取当前系统设置"""
    try:
        settings_service = get_settings_service(db)
        settings = settings_service.get_settings()
        
        # 对非超级管理员隐藏敏感信息
        if not current_user.get('is_superuser', False):
            settings_dict = settings.dict()
            sensitive_fields = ['secret_key', 'smtp_password', 'database_url']
            for field in sensitive_fields:
                if field in settings_dict:
                    settings_dict[field] = "***HIDDEN***"
            return settings_dict
        
        return settings
    except Exception as e:
        logger.error(f"获取系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统设置失败"
        )

@router.put("/", response_model=SystemSettingsResponse, summary="更新系统设置")
async def update_settings(
    settings_update: SystemSettingsUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """更新系统设置（仅超级管理员）"""
    try:
        settings_service = get_settings_service(db)
        updated_settings = settings_service.update_settings(
            settings_update, 
            current_user['id']
        )
        
        logger.info(f"管理员 {current_user['username']} 更新了系统设置")
        
        return updated_settings
    except Exception as e:
        logger.error(f"更新系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新系统设置失败: {str(e)}"
        )

@router.post("/reset", response_model=SystemSettingsResponse, summary="重置系统设置")
async def reset_settings(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """重置为默认系统设置（仅超级管理员）"""
    try:
        settings_service = get_settings_service(db)
        reset_settings = settings_service.reset_to_default(current_user['id'])
        
        logger.warning(f"管理员 {current_user['username']} 重置了系统设置")
        
        return reset_settings
    except Exception as e:
        logger.error(f"重置系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置系统设置失败"
        )

@router.get("/export", response_model=SettingsExportResponse, summary="导出系统设置")
async def export_settings(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """导出系统设置（仅超级管理员）"""
    try:
        settings_service = get_settings_service(db)
        export_data = settings_service.export_settings()
        
        logger.info(f"管理员 {current_user['username']} 导出了系统设置")
        
        return export_data
    except Exception as e:
        logger.error(f"导出系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="导出系统设置失败"
        )

@router.post("/import", response_model=SystemSettingsResponse, summary="导入系统设置")
async def import_settings(
    import_request: SettingsImportRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """导入系统设置（仅超级管理员）"""
    try:
        settings_service = get_settings_service(db)
        imported_settings = settings_service.import_settings(
            import_request.settings,
            current_user['id'],
            import_request.overwrite
        )
        
        logger.warning(f"管理员 {current_user['username']} 导入了系统设置")
        
        return imported_settings
    except Exception as e:
        logger.error(f"导入系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"导入系统设置失败: {str(e)}"
        )

@router.post("/import-file", response_model=SystemSettingsResponse, summary="从文件导入系统设置")
async def import_settings_from_file(
    file: UploadFile = File(...),
    overwrite: bool = False,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """从JSON文件导入系统设置（仅超级管理员）"""
    try:
        # 验证文件类型
        if not file.filename.endswith('.json'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持JSON格式的配置文件"
            )
        
        # 读取文件内容
        content = await file.read()
        try:
            import_data = json.loads(content.decode('utf-8'))
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="JSON文件格式错误"
            )
        
        # 提取设置数据
        settings_data = import_data.get('settings', import_data)
        
        settings_service = get_settings_service(db)
        imported_settings = settings_service.import_settings(
            settings_data,
            current_user['id'],
            overwrite
        )
        
        logger.warning(f"管理员 {current_user['username']} 从文件导入了系统设置")
        
        return imported_settings
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从文件导入系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"从文件导入系统设置失败: {str(e)}"
        )

@router.post("/test-connection", summary="测试数据库连接")
async def test_database_connection(
    test_request: SettingsTestRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """测试数据库连接（仅超级管理员）"""
    try:
        if test_request.test_type != "database":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="测试类型必须为 'database'"
            )
        
        settings_service = get_settings_service(db)
        result = settings_service.test_database_connection(test_request.config)
        
        logger.info(f"管理员 {current_user['username']} 测试了数据库连接")
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试数据库连接失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试数据库连接失败"
        )

@router.post("/test-email", summary="测试邮件配置")
async def test_email_configuration(
    test_request: SettingsTestRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """测试邮件配置（仅超级管理员）"""
    try:
        if test_request.test_type != "email":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="测试类型必须为 'email'"
            )
        
        settings_service = get_settings_service(db)
        result = settings_service.test_email_configuration(test_request.config)
        
        logger.info(f"管理员 {current_user['username']} 测试了邮件配置")
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试邮件配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试邮件配置失败"
        )

@router.get("/categories", summary="获取设置分类")
async def get_settings_categories(
    current_user: dict = Depends(get_current_user)
):
    """获取设置分类信息"""
    categories = [
        {
            "key": "basic",
            "name": "基本设置",
            "icon": "Setting",
            "description": "应用基本信息配置"
        },
        {
            "key": "database",
            "name": "数据库设置",
            "icon": "Coin",
            "description": "数据库连接配置"
        },
        {
            "key": "sync",
            "name": "数据同步",
            "icon": "Refresh",
            "description": "数据同步相关配置"
        },
        {
            "key": "security",
            "name": "安全设置",
            "icon": "Lock",
            "description": "安全和认证配置"
        },
        {
            "key": "email",
            "name": "邮件服务",
            "icon": "Message",
            "description": "邮件服务配置"
        },
        {
            "key": "logging",
            "name": "日志配置",
            "icon": "Document",
            "description": "日志记录配置"
        },
        {
            "key": "ui",
            "name": "界面设置",
            "icon": "Monitor",
            "description": "用户界面配置"
        }
    ]
    
    return {"categories": categories}

@router.get("/logs/files", summary="获取日志文件列表")
async def get_log_files(
    current_user: dict = Depends(get_current_user)
):
    """获取系统日志文件列表"""
    try:
        import os
        import glob
        from datetime import datetime

        log_dir = "logs"
        if not os.path.exists(log_dir):
            return []

        log_files = []
        for file_path in glob.glob(os.path.join(log_dir, "*.log*")):
            if os.path.isfile(file_path):
                stat = os.stat(file_path)
                log_files.append({
                    "name": os.path.basename(file_path),
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "path": file_path
                })

        # 按修改时间排序
        log_files.sort(key=lambda x: x["modified"], reverse=True)
        return log_files
    except Exception as e:
        logger.error(f"获取日志文件列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取日志文件列表失败"
        )

@router.get("/logs/content/{filename}", summary="获取日志文件内容")
async def get_log_file_content(
    filename: str,
    lines: int = 1000,
    current_user: dict = Depends(get_current_user)
):
    """获取指定日志文件的内容"""
    try:
        import os

        log_path = os.path.join("logs", filename)
        if not os.path.exists(log_path) or not os.path.isfile(log_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志文件不存在"
            )

        # 读取文件最后N行
        with open(log_path, 'r', encoding='utf-8') as f:
            file_lines = f.readlines()
            if len(file_lines) > lines:
                content = ''.join(file_lines[-lines:])
            else:
                content = ''.join(file_lines)

        return {"content": content}
    except Exception as e:
        logger.error(f"读取日志文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="读取日志文件失败"
        )

@router.delete("/logs/file/{filename}", summary="删除日志文件")
async def delete_log_file(
    filename: str,
    current_user: dict = Depends(get_current_active_superuser)
):
    """删除指定的日志文件"""
    try:
        import os

        log_path = os.path.join("logs", filename)
        if not os.path.exists(log_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志文件不存在"
            )

        os.remove(log_path)
        logger.info(f"用户 {current_user.get('username')} 删除了日志文件: {filename}")

        return {"success": True, "message": f"日志文件 {filename} 已删除"}
    except Exception as e:
        logger.error(f"删除日志文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除日志文件失败"
        )

@router.post("/logs/cleanup", summary="清理过期日志")
async def cleanup_old_logs(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """清理过期的日志文件"""
    try:
        import os
        import glob
        from datetime import datetime, timedelta

        settings_service = get_settings_service(db)
        settings = settings_service.get_settings()

        retention_days = settings.log_retention_days or 30
        cutoff_date = datetime.now() - timedelta(days=retention_days)

        log_dir = "logs"
        deleted_count = 0

        if os.path.exists(log_dir):
            for file_path in glob.glob(os.path.join(log_dir, "*.log*")):
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        deleted_count += 1

        logger.info(f"用户 {current_user.get('username')} 清理了 {deleted_count} 个过期日志文件")

        return {
            "deleted": deleted_count,
            "message": f"已清理 {deleted_count} 个过期日志文件"
        }
    except Exception as e:
        logger.error(f"清理过期日志失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理过期日志失败"
        )

@router.post("/send-test-email", summary="发送测试邮件")
async def send_test_email(
    email: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """发送测试邮件"""
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from datetime import datetime

        settings_service = get_settings_service(db)
        settings = settings_service.get_settings()

        if not settings.smtp_host:
            return {"success": False, "message": "邮件服务未配置"}

        # 创建邮件
        msg = MIMEMultipart()
        msg['From'] = f"{settings.email_from_name} <{settings.email_from}>"
        msg['To'] = email
        msg['Subject'] = "WisCude 系统测试邮件"

        body = f"""
        <h2>WisCude 系统测试邮件</h2>
        <p>这是一封来自 WisCude 后台管理系统的测试邮件。</p>
        <p>如果您收到此邮件，说明邮件服务配置正确。</p>
        <p>发送时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>发送者：{current_user.get('username')}</p>
        """

        msg.attach(MIMEText(body, 'html'))

        # 发送邮件
        if settings.smtp_ssl:
            server = smtplib.SMTP_SSL(settings.smtp_host, settings.smtp_port)
        else:
            server = smtplib.SMTP(settings.smtp_host, settings.smtp_port)
            if settings.smtp_tls:
                server.starttls()

        server.login(settings.smtp_user, settings.smtp_password)
        server.send_message(msg)
        server.quit()

        logger.info(f"用户 {current_user.get('username')} 发送测试邮件到 {email}")
        return {"success": True, "message": f"测试邮件已发送到 {email}"}
    except Exception as e:
        logger.error(f"发送测试邮件失败: {e}")
        return {"success": False, "message": f"发送测试邮件失败: {str(e)}"}

@router.get("/defaults", summary="获取默认设置")
async def get_default_settings(
    group: str = None,
    current_user: dict = Depends(get_current_user)
):
    """获取默认设置值"""
    defaults = {
        "basic": {
            "app_name": "WisCude 后台管理系统",
            "app_version": "1.0.0",
            "app_description": "智能学习数据管理系统",
            "timezone": "Asia/Shanghai",
            "language": "zh-CN",
            "sync_interval_minutes": 30,
            "sync_batch_size": 1000,
            "auto_sync_enabled": False
        },
        "security": {
            "access_token_expire_minutes": 30,
            "refresh_token_expire_days": 7,
            "session_timeout_minutes": 60,
            "max_login_attempts": 5,
            "lockout_duration_minutes": 15,
            "password_min_length": 8,
            "password_require_uppercase": True,
            "password_require_lowercase": True,
            "password_require_numbers": True,
            "password_require_special": True,
            "two_factor_auth_enabled": False
        },
        "email": {
            "smtp_host": "",
            "smtp_port": 587,
            "smtp_user": "",
            "smtp_password": "",
            "smtp_tls": True,
            "smtp_ssl": False,
            "email_from": "",
            "email_from_name": "WisCude 系统"
        },
        "logging": {
            "log_level": "INFO",
            "log_file_path": "logs/wiscude-admin.log",
            "log_retention_days": 30,
            "log_max_size_mb": 100
        },
        "ui": {
            "ui_theme": "light",
            "ui_primary_color": "#409EFF",
            "ui_layout_mode": "classic",
            "ui_sidebar_collapsed": False,
            "ui_show_breadcrumb": True,
            "ui_show_tags_view": True,
            "default_page_size": 20,
            "max_page_size": 100,
            "dashboard_widgets": ["user-stats", "study-stats", "system-status", "recent-activities"]
        }
    }

    if group:
        return defaults.get(group, {})

    # 合并所有默认值
    all_defaults = {}
    for group_defaults in defaults.values():
        all_defaults.update(group_defaults)

    return all_defaults

# AI配置相关API
@router.get("/ai", response_model=AIConfigResponse, summary="获取AI配置")
async def get_ai_config(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取AI配置"""
    try:
        settings_service = get_settings_service(db)
        settings = settings_service.get_settings()

        # 对非超级管理员隐藏API密钥
        ai_configs = settings.ai_configs or []
        if not current_user.get('is_superuser', False):
            for config in ai_configs:
                if 'api_key' in config:
                    config['api_key'] = "***HIDDEN***"

        return AIConfigResponse(
            ai_enabled=settings.ai_enabled,
            default_ai_model=settings.default_ai_model,
            models=[AIModelConfig(**config) for config in ai_configs]
        )
    except Exception as e:
        logger.error(f"获取AI配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取AI配置失败"
        )

@router.put("/ai", response_model=AIConfigResponse, summary="更新AI配置")
async def update_ai_config(
    ai_config: AIConfigRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """更新AI配置（仅超级管理员）"""
    try:
        settings_service = get_settings_service(db)

        # 验证默认模型是否存在
        if ai_config.default_ai_model:
            model_ids = [model.id for model in ai_config.models]
            if ai_config.default_ai_model not in model_ids:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="默认AI模型不存在于配置列表中"
                )

        # 确保只有一个默认模型
        default_count = sum(1 for model in ai_config.models if model.is_default)
        if default_count > 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能设置一个默认AI模型"
            )

        # 更新设置
        update_data = {
            'ai_enabled': ai_config.ai_enabled,
            'default_ai_model': ai_config.default_ai_model,
            'ai_configs': [model.dict() for model in ai_config.models]
        }

        updated_settings = settings_service.update_settings(
            update_data,
            current_user['id']
        )

        logger.info(f"管理员 {current_user['username']} 更新了AI配置")

        return AIConfigResponse(
            ai_enabled=updated_settings.ai_enabled,
            default_ai_model=updated_settings.default_ai_model,
            models=[AIModelConfig(**config) for config in updated_settings.ai_configs or []]
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新AI配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI配置失败: {str(e)}"
        )

@router.post("/ai/test", response_model=AITestResult, summary="测试AI模型连接")
async def test_ai_model(
    test_request: AITestRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """测试AI模型连接"""
    try:
        settings_service = get_settings_service(db)
        settings = settings_service.get_settings()

        # 查找指定的AI模型配置
        ai_configs = settings.ai_configs or []
        model_config = None
        for config in ai_configs:
            if config.get('id') == test_request.model_id:
                model_config = config
                break

        if not model_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI模型配置不存在"
            )

        # TODO: 实际的AI模型连接测试逻辑
        # 这里应该根据不同的AI提供商实现具体的连接测试
        import time
        import random

        start_time = time.time()

        # 模拟测试过程
        await asyncio.sleep(random.uniform(0.5, 2.0))

        response_time = time.time() - start_time

        # 模拟测试结果
        success = random.choice([True, True, True, False])  # 75%成功率

        if success:
            return AITestResult(
                success=True,
                message="AI模型连接测试成功",
                response_time=response_time,
                model_info={
                    "name": model_config.get('model_name'),
                    "provider": model_config.get('provider'),
                    "version": "1.0.0",
                    "capabilities": ["text-generation", "chat"]
                }
            )
        else:
            return AITestResult(
                success=False,
                message="AI模型连接测试失败",
                response_time=response_time,
                error_details="连接超时或API密钥无效"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI模型测试失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI模型测试失败: {str(e)}"
        )

@router.get("/ai/presets", summary="获取AI模型预设配置")
async def get_ai_presets(
    provider: str = None,
    current_user: dict = Depends(get_current_user)
):
    """获取AI模型预设配置"""
    presets = {
        "qwen": [
            {
                "name": "qwen-turbo",
                "display_name": "通义千问 Turbo",
                "description": "阿里云通义千问快速模型，适合日常对话和简单任务",
                "api_endpoint": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
                "model_name": "qwen-turbo",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 2000,
                    "top_p": 0.9,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            },
            {
                "name": "qwen-plus",
                "display_name": "通义千问 Plus",
                "description": "阿里云通义千问增强模型，性能更强",
                "api_endpoint": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
                "model_name": "qwen-plus",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "top_p": 0.9,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "deepseek": [
            {
                "name": "deepseek-chat",
                "display_name": "DeepSeek Chat",
                "description": "DeepSeek 对话模型，擅长代码生成和逻辑推理",
                "api_endpoint": "https://api.deepseek.com/v1/chat/completions",
                "model_name": "deepseek-chat",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "top_p": 0.95,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "openai": [
            {
                "name": "gpt-3.5-turbo",
                "display_name": "OpenAI GPT-3.5 Turbo",
                "description": "OpenAI GPT-3.5 Turbo 模型，平衡性能和成本",
                "api_endpoint": "https://api.openai.com/v1/chat/completions",
                "model_name": "gpt-3.5-turbo",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "top_p": 1,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            },
            {
                "name": "gpt-4",
                "display_name": "OpenAI GPT-4",
                "description": "OpenAI GPT-4 模型，最强性能",
                "api_endpoint": "https://api.openai.com/v1/chat/completions",
                "model_name": "gpt-4",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 8000,
                    "top_p": 1,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 60,
                    "retry_count": 3
                }
            }
        ],
        "kimi": [
            {
                "name": "moonshot-v1-8k",
                "display_name": "Kimi Chat 8K",
                "description": "Moonshot AI Kimi 模型，支持长文本处理",
                "api_endpoint": "https://api.moonshot.cn/v1/chat/completions",
                "model_name": "moonshot-v1-8k",
                "default_parameters": {
                    "temperature": 0.3,
                    "max_tokens": 8000,
                    "top_p": 1,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "doubao": [
            {
                "name": "doubao-lite-4k",
                "display_name": "豆包 Lite 4K",
                "description": "字节跳动豆包模型，适合轻量级应用",
                "api_endpoint": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
                "model_name": "doubao-lite-4k",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "top_p": 0.9,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "claude": [
            {
                "name": "claude-3-haiku",
                "display_name": "Claude 3 Haiku",
                "description": "Anthropic Claude 3 Haiku 模型，快速响应",
                "api_endpoint": "https://api.anthropic.com/v1/messages",
                "model_name": "claude-3-haiku-20240307",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "top_p": 1,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            },
            {
                "name": "claude-3-sonnet",
                "display_name": "Claude 3 Sonnet",
                "description": "Anthropic Claude 3 Sonnet 模型，平衡性能",
                "api_endpoint": "https://api.anthropic.com/v1/messages",
                "model_name": "claude-3-sonnet-20240229",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "top_p": 1,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "gemini": [
            {
                "name": "gemini-pro",
                "display_name": "Google Gemini Pro",
                "description": "Google Gemini Pro 模型，多模态能力",
                "api_endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                "model_name": "gemini-pro",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 2048,
                    "top_p": 0.8,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "baichuan": [
            {
                "name": "baichuan2-turbo",
                "display_name": "百川2 Turbo",
                "description": "百川智能 Baichuan2 Turbo 模型",
                "api_endpoint": "https://api.baichuan-ai.com/v1/chat/completions",
                "model_name": "Baichuan2-Turbo",
                "default_parameters": {
                    "temperature": 0.3,
                    "max_tokens": 2048,
                    "top_p": 0.85,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "chatglm": [
            {
                "name": "chatglm-turbo",
                "display_name": "ChatGLM Turbo",
                "description": "智谱AI ChatGLM Turbo 模型",
                "api_endpoint": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
                "model_name": "chatglm_turbo",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 2048,
                    "top_p": 0.7,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "wenxin": [
            {
                "name": "ernie-bot-turbo",
                "display_name": "文心一言 Turbo",
                "description": "百度文心一言 ERNIE Bot Turbo 模型",
                "api_endpoint": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant",
                "model_name": "ERNIE-Bot-turbo",
                "default_parameters": {
                    "temperature": 0.8,
                    "max_tokens": 2048,
                    "top_p": 0.8,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "spark": [
            {
                "name": "spark-lite",
                "display_name": "讯飞星火 Lite",
                "description": "科大讯飞星火认知大模型 Lite 版本",
                "api_endpoint": "wss://spark-api.xf-yun.com/v1.1/chat",
                "model_name": "general",
                "default_parameters": {
                    "temperature": 0.5,
                    "max_tokens": 2048,
                    "top_p": 0.7,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ],
        "minimax": [
            {
                "name": "abab5.5-chat",
                "display_name": "MiniMax abab5.5",
                "description": "MiniMax abab5.5 对话模型",
                "api_endpoint": "https://api.minimax.chat/v1/text/chatcompletion_pro",
                "model_name": "abab5.5-chat",
                "default_parameters": {
                    "temperature": 0.7,
                    "max_tokens": 2048,
                    "top_p": 0.95,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "stream": False,
                    "timeout": 30,
                    "retry_count": 3
                }
            }
        ]
    }

    if provider:
        return presets.get(provider, [])

    return presets
