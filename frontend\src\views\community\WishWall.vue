<template>
  <div class="wish-wall-management">
    <div class="page-header">
      <h1>心愿墙管理</h1>
      <p>管理学习心愿的收集、匹配、实现和激励机制</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="心愿收集与审核" name="collection">
        <!-- 收集统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ collectionStats.pendingWishes }}</div>
                  <div class="stat-label">待审核心愿</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ collectionStats.approvedToday }}</div>
                  <div class="stat-label">今日已通过</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ collectionStats.featuredWishes }}</div>
                  <div class="stat-label">精选心愿</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Heart /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ collectionStats.urgentWishes }}</div>
                  <div class="stat-label">紧急心愿</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 心愿分类管理 -->
        <el-card class="wish-categories-card">
          <template #header>
            <div class="card-header">
              <span>心愿分类管理</span>
              <el-button type="primary" @click="showCategoryDialog = true">
                <el-icon><Plus /></el-icon>
                添加分类
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="categories-grid">
            <el-col :span="6" v-for="category in wishCategories" :key="category.id">
              <el-card class="category-item" :class="{ 'category-active': category.isActive }">
                <div class="category-header">
                  <div class="category-icon" :style="{ backgroundColor: category.color }">
                    <el-icon><component :is="getCategoryIcon(category.type)" /></el-icon>
                  </div>
                  <div class="category-info">
                    <h3>{{ category.name }}</h3>
                    <div class="category-meta">
                      <span class="wish-count">{{ category.wishCount }}个心愿</span>
                      <span class="completion-rate">完成率{{ category.completionRate }}%</span>
                    </div>
                  </div>
                </div>
                <div class="category-description">{{ category.description }}</div>
                <div class="category-stats">
                  <div class="stat-item">
                    <span class="stat-label">本月新增：</span>
                    <span class="stat-value">{{ category.monthlyNew }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">平均实现时间：</span>
                    <span class="stat-value">{{ category.avgCompletionTime }}天</span>
                  </div>
                </div>
                <div class="category-actions">
                  <el-button type="primary" size="small" @click="viewCategoryWishes(category)">
                    <el-icon><View /></el-icon>
                    查看心愿
                  </el-button>
                  <el-button 
                    :type="category.isActive ? 'warning' : 'success'" 
                    size="small" 
                    @click="toggleCategory(category)"
                  >
                    <el-icon><Switch /></el-icon>
                    {{ category.isActive ? '禁用' : '启用' }}
                  </el-button>
                  <el-button type="info" size="small" @click="editCategory(category)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>

        <!-- 待审核心愿列表 -->
        <el-card class="pending-wishes-card">
          <template #header>
            <div class="card-header">
              <span>待审核心愿</span>
              <div class="header-actions">
                <el-select v-model="wishFilter" placeholder="筛选分类" clearable>
                  <el-option
                    v-for="category in wishCategories"
                    :key="category.id"
                    :label="category.name"
                    :value="category.id"
                  />
                </el-select>
                <el-button type="success" @click="handleBatchApprove" :disabled="!selectedWishes.length">
                  <el-icon><Check /></el-icon>
                  批量通过
                </el-button>
                <el-button type="danger" @click="handleBatchReject" :disabled="!selectedWishes.length">
                  <el-icon><Close /></el-icon>
                  批量拒绝
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="pendingWishes"
            v-loading="wishesLoading"
            @selection-change="handleWishSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" label="心愿标题" min-width="200">
              <template #default="{ row }">
                <div class="wish-info">
                  <div class="wish-title">{{ row.title }}</div>
                  <div class="wish-meta">
                    <el-tag :type="getCategoryTagType(row.categoryId)" size="small">
                      {{ getCategoryName(row.categoryId) }}
                    </el-tag>
                    <el-tag :type="getUrgencyTagType(row.urgency)" size="small">
                      {{ getUrgencyText(row.urgency) }}
                    </el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="authorName" label="许愿人" width="120">
              <template #default="{ row }">
                <div class="author-info">
                  <el-avatar :src="row.authorAvatar" :size="24">{{ row.authorName.charAt(0) }}</el-avatar>
                  <span class="author-name">{{ row.authorName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="心愿描述" min-width="250" show-overflow-tooltip />
            <el-table-column prop="targetDate" label="期望实现时间" width="140">
              <template #default="{ row }">
                {{ formatDate(row.targetDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" label="提交时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.submitTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewWishDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="approveWish(row)">
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button type="warning" size="small" @click="setFeatured(row)">
                  <el-icon><Star /></el-icon>
                  精选
                </el-button>
                <el-button type="danger" size="small" @click="rejectWish(row)">
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="wishPagination.page"
              v-model:page-size="wishPagination.size"
              :total="wishPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleWishSizeChange"
              @current-change="handleWishPageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="心愿匹配" name="matching">
        <!-- 匹配统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.totalMatches }}</div>
                  <div class="stat-label">总匹配数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.successfulMatches }}</div>
                  <div class="stat-label">成功匹配</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.avgMatchTime }}</div>
                  <div class="stat-label">平均匹配时长(小时)</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.matchSuccessRate }}%</div>
                  <div class="stat-label">匹配成功率</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 智能匹配算法配置 -->
        <el-card class="matching-algorithm-card">
          <template #header>
            <div class="card-header">
              <span>智能匹配算法配置</span>
              <el-button type="primary" @click="showAlgorithmDialog = true">
                <el-icon><Setting /></el-icon>
                调整算法
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="algorithm-content">
            <el-col :span="8">
              <div class="algorithm-section">
                <h4>匹配权重设置</h4>
                <div class="weight-settings">
                  <div class="weight-item">
                    <span class="weight-label">地理位置匹配：</span>
                    <el-slider v-model="algorithmConfig.locationWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">兴趣相似度：</span>
                    <el-slider v-model="algorithmConfig.interestWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">能力匹配度：</span>
                    <el-slider v-model="algorithmConfig.abilityWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">时间可用性：</span>
                    <el-slider v-model="algorithmConfig.timeWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">历史成功率：</span>
                    <el-slider v-model="algorithmConfig.historyWeight" :max="100" show-input />
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="algorithm-section">
                <h4>匹配规则设置</h4>
                <div class="rule-settings">
                  <div class="rule-item">
                    <span class="rule-label">最低匹配分数：</span>
                    <el-input-number v-model="algorithmConfig.minMatchScore" :min="0" :max="100" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">最大推荐数量：</span>
                    <el-input-number v-model="algorithmConfig.maxRecommendations" :min="1" :max="20" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">自动匹配：</span>
                    <el-switch v-model="algorithmConfig.autoMatch" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">跨地区匹配：</span>
                    <el-switch v-model="algorithmConfig.crossRegionMatch" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">匹配冷却期：</span>
                    <el-input-number v-model="algorithmConfig.cooldownHours" :min="0" :max="72" />
                    <span class="unit">小时</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="algorithm-section">
                <h4>特殊规则</h4>
                <div class="special-rules">
                  <div class="rule-item">
                    <el-checkbox v-model="algorithmConfig.prioritizeUrgent">优先匹配紧急心愿</el-checkbox>
                  </div>
                  <div class="rule-item">
                    <el-checkbox v-model="algorithmConfig.considerHistory">考虑历史互助记录</el-checkbox>
                  </div>
                  <div class="rule-item">
                    <el-checkbox v-model="algorithmConfig.balanceLoad">平衡助人者负载</el-checkbox>
                  </div>
                  <div class="rule-item">
                    <el-checkbox v-model="algorithmConfig.enableFeedback">启用反馈学习</el-checkbox>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div class="algorithm-actions">
            <el-button type="primary" @click="saveAlgorithmConfig">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button type="info" @click="testAlgorithm">
              <el-icon><DataAnalysis /></el-icon>
              测试算法
            </el-button>
            <el-button @click="resetAlgorithmConfig">
              <el-icon><Refresh /></el-icon>
              重置默认
            </el-button>
          </div>
        </el-card>

        <!-- 匹配队列管理 -->
        <el-card class="matching-queue-card">
          <template #header>
            <div class="card-header">
              <span>匹配队列管理</span>
              <div class="header-actions">
                <el-button type="primary" @click="runMatching">
                  <el-icon><Connection /></el-icon>
                  执行匹配
                </el-button>
                <el-button type="info" @click="refreshQueue">
                  <el-icon><Refresh /></el-icon>
                  刷新队列
                </el-button>
              </div>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="queue-section">
                <h4>等待匹配的心愿 ({{ waitingWishes.length }})</h4>
                <div class="queue-list">
                  <div
                    v-for="wish in waitingWishes"
                    :key="wish.id"
                    class="queue-item"
                  >
                    <div class="wish-info">
                      <div class="wish-header">
                        <span class="wish-title">{{ wish.title }}</span>
                        <el-tag :type="getUrgencyTagType(wish.urgency)" size="small">
                          {{ getUrgencyText(wish.urgency) }}
                        </el-tag>
                      </div>
                      <div class="wish-details">
                        <span class="author">{{ wish.authorName }}</span>
                        <span class="category">{{ getCategoryName(wish.categoryId) }}</span>
                        <span class="waiting-time">等待{{ formatWaitingTime(wish.waitingTime) }}</span>
                      </div>
                    </div>
                    <div class="queue-actions">
                      <el-button type="primary" size="small" @click="manualMatch(wish)">
                        <el-icon><Connection /></el-icon>
                        手动匹配
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="queue-section">
                <h4>可提供帮助的用户 ({{ availableHelpers.length }})</h4>
                <div class="queue-list">
                  <div
                    v-for="helper in availableHelpers"
                    :key="helper.id"
                    class="queue-item"
                  >
                    <div class="helper-info">
                      <div class="helper-header">
                        <el-avatar :src="helper.avatar" :size="32">{{ helper.name.charAt(0) }}</el-avatar>
                        <div class="helper-details">
                          <div class="helper-name">{{ helper.name }}</div>
                          <div class="helper-meta">
                            <el-tag :type="getLevelTagType(helper.level)" size="small">{{ getLevelName(helper.level) }}</el-tag>
                            <span class="help-count">已帮助{{ helper.helpCount }}人</span>
                          </div>
                        </div>
                      </div>
                      <div class="helper-skills">
                        <el-tag
                          v-for="skill in helper.skills"
                          :key="skill"
                          size="small"
                          class="skill-tag"
                        >
                          {{ skill }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="queue-actions">
                      <el-button type="success" size="small" @click="viewHelperProfile(helper)">
                        <el-icon><User /></el-icon>
                        查看详情
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="实现跟踪" name="tracking">
        <!-- 跟踪统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><List /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ trackingStats.activeWishes }}</div>
                  <div class="stat-label">进行中心愿</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ trackingStats.completedWishes }}</div>
                  <div class="stat-label">已完成心愿</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ trackingStats.overdueWishes }}</div>
                  <div class="stat-label">逾期心愿</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ trackingStats.avgCompletionTime }}</div>
                  <div class="stat-label">平均完成时间(天)</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 心愿进度跟踪 -->
        <el-card class="progress-tracking-card">
          <template #header>
            <div class="card-header">
              <span>心愿进度跟踪</span>
              <div class="header-actions">
                <el-select v-model="trackingFilter" placeholder="筛选状态" clearable>
                  <el-option label="进行中" value="active" />
                  <el-option label="即将逾期" value="due_soon" />
                  <el-option label="已逾期" value="overdue" />
                  <el-option label="已完成" value="completed" />
                </el-select>
                <el-button type="warning" @click="sendReminders" :disabled="!selectedTracking.length">
                  <el-icon><Bell /></el-icon>
                  发送提醒
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="trackingWishes"
            v-loading="trackingLoading"
            @selection-change="handleTrackingSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="心愿信息" min-width="200">
              <template #default="{ row }">
                <div class="wish-tracking-info">
                  <div class="wish-title">{{ row.title }}</div>
                  <div class="wish-participants">
                    <el-avatar :src="row.authorAvatar" :size="24">{{ row.authorName.charAt(0) }}</el-avatar>
                    <span class="participant-name">{{ row.authorName }}</span>
                    <el-icon><ArrowRight /></el-icon>
                    <el-avatar :src="row.helperAvatar" :size="24">{{ row.helperName.charAt(0) }}</el-avatar>
                    <span class="participant-name">{{ row.helperName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="category" label="分类" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getCategoryName(row.categoryId) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="完成进度" width="150">
              <template #default="{ row }">
                <el-progress :percentage="row.progress" :color="getProgressColor(row.progress)" />
              </template>
            </el-table-column>
            <el-table-column prop="targetDate" label="目标时间" width="120">
              <template #default="{ row }">
                <span :class="getDateClass(row.targetDate)">
                  {{ formatDate(row.targetDate) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="lastUpdate" label="最后更新" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.lastUpdate) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getTrackingStatusTagType(row.status)">
                  {{ getTrackingStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewTrackingDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="info" size="small" @click="updateProgress(row)">
                  <el-icon><Edit /></el-icon>
                  更新
                </el-button>
                <el-button type="warning" size="small" @click="sendReminder(row)">
                  <el-icon><Bell /></el-icon>
                  提醒
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="激励评价" name="incentive">
        <!-- 激励统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Trophy /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ incentiveStats.totalRewards }}</div>
                  <div class="stat-label">总奖励发放</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Medal /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ incentiveStats.activeHelpers }}</div>
                  <div class="stat-label">活跃助人者</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ incentiveStats.avgSatisfaction }}</div>
                  <div class="stat-label">平均满意度</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Coin /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ formatNumber(incentiveStats.totalPoints) }}</div>
                  <div class="stat-label">总积分发放</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 助人者排行榜 -->
        <el-card class="helpers-ranking-card">
          <template #header>
            <div class="card-header">
              <span>助人者排行榜</span>
              <div class="header-actions">
                <el-radio-group v-model="rankingPeriod" size="small">
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="quarter">本季度</el-radio-button>
                  <el-radio-button label="year">本年度</el-radio-button>
                </el-radio-group>
                <el-button type="primary" @click="showRewardDialog = true">
                  <el-icon><Gift /></el-icon>
                  发放奖励
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="topHelpers" stripe style="width: 100%">
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <el-tag :type="getRankTagType($index + 1)" size="small">
                  第{{ $index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="助人者信息" min-width="200">
              <template #default="{ row }">
                <div class="helper-ranking-info">
                  <el-avatar :src="row.avatar" :size="40">{{ row.name.charAt(0) }}</el-avatar>
                  <div class="helper-details">
                    <div class="helper-name">{{ row.name }}</div>
                    <div class="helper-level">
                      <el-tag :type="getLevelTagType(row.level)" size="small">{{ getLevelName(row.level) }}</el-tag>
                      <span class="join-time">{{ formatRelativeTime(row.joinTime) }}加入</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="helpCount" label="帮助次数" width="120">
              <template #default="{ row }">
                <span class="help-count">{{ row.helpCount }}次</span>
              </template>
            </el-table-column>
            <el-table-column prop="successRate" label="成功率" width="100">
              <template #default="{ row }">
                <span class="success-rate">{{ row.successRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="avgRating" label="平均评分" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.avgRating" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="earnedPoints" label="获得积分" width="120">
              <template #default="{ row }">
                <span class="earned-points">{{ formatNumber(row.earnedPoints) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="specialties" label="擅长领域" min-width="200">
              <template #default="{ row }">
                <div class="specialties">
                  <el-tag
                    v-for="specialty in row.specialties"
                    :key="specialty"
                    size="small"
                    class="specialty-tag"
                  >
                    {{ specialty }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewHelperProfile(row)">
                  <el-icon><User /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="rewardHelper(row)">
                  <el-icon><Gift /></el-icon>
                  奖励
                </el-button>
                <el-button type="warning" size="small" @click="sendAppreciation(row)">
                  <el-icon><ChatDotRound /></el-icon>
                  感谢
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock, Check, Star, Heart, Plus, View, Switch, Edit, Close, Connection, CircleCheck,
  Timer, DataLine, Setting, DataAnalysis, Refresh, User, List, Warning, Bell, ArrowRight,
  Trophy, Medal, Coin, Gift, ChatDotRound
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('collection')
const wishesLoading = ref(false)
const trackingLoading = ref(false)
const selectedWishes = ref([])
const selectedTracking = ref([])
const showCategoryDialog = ref(false)
const showAlgorithmDialog = ref(false)
const showRewardDialog = ref(false)

// 收集统计数据
const collectionStats = reactive({
  pendingWishes: 28,
  approvedToday: 15,
  featuredWishes: 12,
  urgentWishes: 6
})

// 匹配统计数据
const matchingStats = reactive({
  totalMatches: 456,
  successfulMatches: 389,
  avgMatchTime: 4.5,
  matchSuccessRate: 85
})

// 跟踪统计数据
const trackingStats = reactive({
  activeWishes: 234,
  completedWishes: 189,
  overdueWishes: 12,
  avgCompletionTime: 8.5
})

// 激励统计数据
const incentiveStats = reactive({
  totalRewards: 234,
  activeHelpers: 89,
  avgSatisfaction: 4.7,
  totalPoints: 125000
})

// 匹配算法配置
const algorithmConfig = reactive({
  locationWeight: 30,
  interestWeight: 25,
  abilityWeight: 20,
  timeWeight: 15,
  historyWeight: 10,
  minMatchScore: 70,
  maxRecommendations: 5,
  autoMatch: false,
  crossRegionMatch: true,
  cooldownHours: 24,
  prioritizeUrgent: true,
  considerHistory: true,
  balanceLoad: false,
  enableFeedback: true
})

// 心愿分类
const wishCategories = ref([
  {
    id: 1,
    name: '学习资源',
    type: 'study',
    color: '#409eff',
    description: '需要学习资料、课程、书籍等资源帮助',
    wishCount: 156,
    completionRate: 78,
    monthlyNew: 23,
    avgCompletionTime: 7,
    isActive: true
  },
  {
    id: 2,
    name: '学习伙伴',
    type: 'partner',
    color: '#67c23a',
    description: '寻找学习伙伴、组队学习、互相监督',
    wishCount: 89,
    completionRate: 85,
    monthlyNew: 18,
    avgCompletionTime: 3,
    isActive: true
  }
])

// 待审核心愿
const pendingWishes = ref([
  {
    id: 1,
    title: '寻找数学学习伙伴',
    categoryId: 2,
    authorName: '小明',
    authorAvatar: '',
    description: '希望找到一起学习高等数学的伙伴，可以互相讨论问题，共同进步',
    urgency: 'normal',
    targetDate: '2024-02-28',
    submitTime: '2024-01-15 10:30:00'
  }
])

// 等待匹配的心愿
const waitingWishes = ref([
  {
    id: 1,
    title: '需要英语口语练习伙伴',
    categoryId: 2,
    authorName: '小红',
    urgency: 'high',
    waitingTime: 180
  },
  {
    id: 2,
    title: '寻找编程学习资料',
    categoryId: 1,
    authorName: '小李',
    urgency: 'normal',
    waitingTime: 120
  }
])

// 可提供帮助的用户
const availableHelpers = ref([
  {
    id: 1,
    name: '张老师',
    avatar: '',
    level: 'gold',
    helpCount: 25,
    skills: ['数学', '物理', '编程']
  },
  {
    id: 2,
    name: '李同学',
    avatar: '',
    level: 'silver',
    helpCount: 12,
    skills: ['英语', '文学']
  }
])

// 跟踪中的心愿
const trackingWishes = ref([
  {
    id: 1,
    title: '寻找数学学习伙伴',
    categoryId: 2,
    authorName: '小明',
    authorAvatar: '',
    helperName: '张老师',
    helperAvatar: '',
    progress: 75,
    targetDate: '2024-02-28',
    lastUpdate: '2024-01-20 14:30:00',
    status: 'active'
  }
])

// 助人者排行榜
const topHelpers = ref([
  {
    id: 1,
    name: '张老师',
    avatar: '',
    level: 'gold',
    joinTime: '2023-09-01',
    helpCount: 45,
    successRate: 95,
    avgRating: 4.9,
    earnedPoints: 15600,
    specialties: ['数学', '物理', '编程']
  },
  {
    id: 2,
    name: '李同学',
    avatar: '',
    level: 'silver',
    joinTime: '2023-10-15',
    helpCount: 28,
    successRate: 89,
    avgRating: 4.6,
    earnedPoints: 9800,
    specialties: ['英语', '文学']
  }
])

// 分页数据
const wishPagination = reactive({
  page: 1,
  size: 20,
  total: 100
})

// 筛选条件
const wishFilter = ref('')
const trackingFilter = ref('')
const rankingPeriod = ref('month')

// 工具函数
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatRelativeTime = (date: string) => {
  const now = new Date()
  const target = new Date(date)
  const diffMonths = Math.floor((now.getTime() - target.getTime()) / (1000 * 60 * 60 * 24 * 30))

  if (diffMonths === 0) return '本月'
  if (diffMonths < 12) return `${diffMonths}个月前`
  return `${Math.floor(diffMonths / 12)}年前`
}

const formatWaitingTime = (minutes: number) => {
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins}分钟`
}

const getCategoryIcon = (type: string) => {
  return Star // 简化处理
}

const getCategoryTagType = (categoryId: number) => {
  return 'primary'
}

const getCategoryName = (categoryId: number) => {
  const category = wishCategories.value.find(c => c.id === categoryId)
  return category?.name || '未知分类'
}

const getUrgencyTagType = (urgency: string) => {
  const urgencyMap: Record<string, string> = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return urgencyMap[urgency] || 'default'
}

const getUrgencyText = (urgency: string) => {
  const urgencyMap: Record<string, string> = {
    low: '不急',
    normal: '一般',
    high: '较急',
    urgent: '紧急'
  }
  return urgencyMap[urgency] || urgency
}

const getLevelTagType = (level: string) => {
  const levelMap: Record<string, string> = {
    gold: 'danger',
    silver: 'warning',
    bronze: 'success'
  }
  return levelMap[level] || 'info'
}

const getLevelName = (level: string) => {
  const levelMap: Record<string, string> = {
    gold: '金牌助人者',
    silver: '银牌助人者',
    bronze: '铜牌助人者'
  }
  return levelMap[level] || level
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getDateClass = (date: string) => {
  const now = new Date()
  const target = new Date(date)
  const diffDays = Math.floor((target.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'text-danger' // 已逾期
  if (diffDays <= 3) return 'text-warning' // 即将逾期
  return 'text-success' // 正常
}

const getTrackingStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'primary',
    due_soon: 'warning',
    overdue: 'danger',
    completed: 'success'
  }
  return statusMap[status] || 'default'
}

const getTrackingStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '进行中',
    due_soon: '即将逾期',
    overdue: '已逾期',
    completed: '已完成'
  }
  return statusMap[status] || status
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

// 事件处理函数
const handleWishSelectionChange = (selection: any[]) => {
  selectedWishes.value = selection
}

const handleBatchApprove = () => {
  ElMessageBox.confirm('确定要批量通过选中的心愿吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量审核通过成功')
    // TODO: 调用API
  })
}

const handleBatchReject = () => {
  ElMessageBox.confirm('确定要批量拒绝选中的心愿吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量拒绝成功')
    // TODO: 调用API
  })
}

const viewCategoryWishes = (category: any) => {
  console.log('查看分类心愿:', category)
  // TODO: 实现分类心愿查看
}

const toggleCategory = (category: any) => {
  category.isActive = !category.isActive
  ElMessage.success(`分类已${category.isActive ? '启用' : '禁用'}`)
  // TODO: 调用API
}

const editCategory = (category: any) => {
  console.log('编辑分类:', category)
  // TODO: 实现分类编辑
}

const viewWishDetails = (wish: any) => {
  console.log('查看心愿详情:', wish)
  // TODO: 实现心愿详情查看
}

const approveWish = (wish: any) => {
  ElMessage.success(`心愿"${wish.title}"已通过审核`)
  // TODO: 调用API
}

const setFeatured = (wish: any) => {
  ElMessage.success(`心愿"${wish.title}"已设为精选`)
  // TODO: 调用API
}

const rejectWish = (wish: any) => {
  ElMessage.warning(`心愿"${wish.title}"已被拒绝`)
  // TODO: 调用API
}

const handleWishSizeChange = (size: number) => {
  wishPagination.size = size
  // TODO: 重新加载数据
}

const handleWishPageChange = (page: number) => {
  wishPagination.page = page
  // TODO: 重新加载数据
}

const saveAlgorithmConfig = () => {
  ElMessage.success('匹配算法配置已保存')
  // TODO: 调用API保存配置
}

const testAlgorithm = () => {
  ElMessage.info('正在测试匹配算法...')
  // TODO: 实现算法测试
}

const resetAlgorithmConfig = () => {
  ElMessageBox.confirm('确定要重置为默认配置吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    // 重置配置
    ElMessage.success('配置已重置为默认值')
  })
}

const runMatching = () => {
  ElMessage.info('正在执行智能匹配...')
  // TODO: 调用匹配API
}

const refreshQueue = () => {
  ElMessage.success('匹配队列已刷新')
  // TODO: 刷新队列数据
}

const manualMatch = (wish: any) => {
  console.log('手动匹配心愿:', wish)
  // TODO: 实现手动匹配
}

const viewHelperProfile = (helper: any) => {
  console.log('查看助人者详情:', helper)
  // TODO: 实现助人者详情查看
}

const handleTrackingSelectionChange = (selection: any[]) => {
  selectedTracking.value = selection
}

const sendReminders = () => {
  ElMessage.success(`已向${selectedTracking.value.length}个心愿发送提醒`)
  // TODO: 调用API发送提醒
}

const viewTrackingDetails = (wish: any) => {
  console.log('查看跟踪详情:', wish)
  // TODO: 实现跟踪详情查看
}

const updateProgress = (wish: any) => {
  console.log('更新进度:', wish)
  // TODO: 实现进度更新
}

const sendReminder = (wish: any) => {
  ElMessage.success(`已向心愿"${wish.title}"发送提醒`)
  // TODO: 调用API发送提醒
}

const rewardHelper = (helper: any) => {
  console.log('奖励助人者:', helper)
  // TODO: 实现助人者奖励
}

const sendAppreciation = (helper: any) => {
  ElMessage.success(`已向${helper.name}发送感谢消息`)
  // TODO: 调用API发送感谢
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.wish-wall-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.info {
  background-color: #409eff;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.danger {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.categories-grid {
  margin-top: 16px;
}

.category-item {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.category-item:hover {
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.15);
}

.category-item.category-active {
  border-left: 4px solid #67c23a;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.category-info {
  flex: 1;
}

.category-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.category-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.wish-count {
  color: #409eff;
}

.completion-rate {
  color: #67c23a;
}

.category-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.category-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

.category-actions {
  display: flex;
  gap: 8px;
}

.wish-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.wish-title {
  font-weight: 500;
  color: #303133;
}

.wish-meta {
  display: flex;
  gap: 8px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-weight: 500;
  color: #303133;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.algorithm-content {
  margin-top: 16px;
}

.algorithm-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.algorithm-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.weight-settings, .rule-settings, .special-rules {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.weight-item, .rule-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.weight-label, .rule-label {
  min-width: 120px;
  font-size: 14px;
  color: #606266;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 8px;
}

.algorithm-actions {
  margin-top: 20px;
  text-align: center;
}

.queue-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 400px;
}

.queue-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.queue-list {
  max-height: 320px;
  overflow-y: auto;
}

.queue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.wish-info, .helper-info {
  flex: 1;
}

.wish-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.wish-title {
  font-weight: 500;
  color: #303133;
}

.wish-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.author {
  color: #606266;
}

.category {
  color: #409eff;
}

.waiting-time {
  color: #e6a23c;
}

.helper-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.helper-details {
  flex: 1;
}

.helper-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.helper-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.help-count {
  color: #606266;
}

.helper-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.skill-tag {
  margin-bottom: 4px;
}

.wish-tracking-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.wish-participants {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.participant-name {
  color: #606266;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}

.text-warning {
  color: #e6a23c;
  font-weight: 600;
}

.text-success {
  color: #67c23a;
  font-weight: 600;
}

.helper-ranking-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.helper-level {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.join-time {
  color: #909399;
}

.help-count {
  color: #409eff;
  font-weight: 600;
}

.success-rate {
  color: #67c23a;
  font-weight: 600;
}

.earned-points {
  color: #e6a23c;
  font-weight: 600;
}

.specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.specialty-tag {
  margin-bottom: 4px;
}

@media (max-width: 768px) {
  .wish-wall-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .categories-grid .el-col,
  .algorithm-content .el-col {
    margin-bottom: 10px;
  }

  .category-actions {
    flex-direction: column;
    gap: 4px;
  }

  .queue-section {
    height: auto;
    min-height: 300px;
  }

  .algorithm-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}
</style>
