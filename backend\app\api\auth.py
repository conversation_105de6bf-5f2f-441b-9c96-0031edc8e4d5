"""
WisCude 后台管理系统 - 认证API
"""
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import (
    create_access_token, create_refresh_token, decode_token,
    get_current_user, get_current_active_superuser
)
from app.models.admin import AdminUser, LoginLog
from .schemas import (
    Token, LoginRequest, RefreshTokenRequest, AdminUserResponse,
    ChangePasswordRequest
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["认证"])

@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """用户登录"""
    # 查找用户
    user = db.query(AdminUser).filter(AdminUser.username == form_data.username).first()
    
    if not user or not user.verify_password(form_data.password):
        # 记录登录失败日志
        login_log = LoginLog(
            username=form_data.username,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            status=False,
            message="用户名或密码错误"
        )
        db.add(login_log)
        db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    
    try:
        # 创建令牌
        access_token = create_access_token(subject=user.id)
        refresh_token = create_refresh_token(subject=user.id)
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        
        # 记录登录成功日志
        login_log = LoginLog(
            user_id=user.id,
            username=user.username,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            status=True,
            message="登录成功"
        )
        db.add(login_log)
        db.commit()
        
        logger.info(f"用户 {user.username} 登录成功")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    except Exception as e:
        logger.error(f"创建令牌失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )

@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        payload = decode_token(refresh_request.refresh_token)
        user_id = payload.get("sub")
        token_type = payload.get("type")
        
        if token_type != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        # 验证用户是否存在且活跃
        user = db.query(AdminUser).filter(AdminUser.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已被禁用"
            )
        
        # 创建新令牌
        access_token = create_access_token(subject=user_id)
        new_refresh_token = create_refresh_token(subject=user_id)
        
        logger.info(f"刷新令牌成功: {user.username}")
        
        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer"
        }
        
    except Exception as e:
        logger.error(f"刷新令牌失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )

@router.get("/me", response_model=AdminUserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: AdminUser = Depends(get_current_user)
):
    """获取当前用户信息"""
    return current_user

@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改当前用户密码"""
    # 验证当前密码
    if not current_user.verify_password(password_data.current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 设置新密码
    current_user.set_password(password_data.new_password)
    current_user.updated_at = datetime.utcnow()
    
    db.commit()
    
    logger.info(f"用户 {current_user.username} 修改密码成功")
    
    return {"message": "密码修改成功"}

@router.post("/logout", summary="用户登出")
async def logout(
    request: Request,
    db: Session = Depends(get_db),
    current_user: AdminUser = Depends(get_current_user)
):
    """用户登出"""
    try:
        # 记录登出日志
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        logout_log = LoginLog(
            user_id=current_user.id,
            username=current_user.username,
            ip_address=client_ip,
            user_agent=user_agent,
            status=True,
            message="登出成功"
        )
        db.add(logout_log)
        db.commit()
        
        logger.info(f"用户登出成功: {current_user.username}")
        
        return {"message": "登出成功"}
    except Exception as e:
        logger.error(f"登出失败: {e}")
        return {"message": "登出成功"}

@router.get("/check", summary="验证令牌")
async def check_token(
    current_user: AdminUser = Depends(get_current_user)
):
    """验证令牌有效性"""
    return {
        "valid": True,
        "user_id": current_user.id,
        "username": current_user.username,
        "message": "令牌有效"
    }
