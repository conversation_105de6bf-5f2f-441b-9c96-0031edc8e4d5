/**
 * 题库管理API接口
 */
import request from './request'

// ==================== 题目分类管理 ====================

/**
 * 获取题目分类列表
 */
export const getQuestionCategories = (params: {
  page?: number
  size?: number
  keyword?: string
  subject?: string
  parent_id?: string
}) => {
  return request({
    url: '/v1/question-bank/categories',
    method: 'get',
    params
  })
}

/**
 * 创建题目分类
 */
export const createQuestionCategory = (data: {
  name: string
  description?: string
  subject: string
  parent_id?: string
  sort_order?: number
  icon?: string
  color?: string
}) => {
  return request({
    url: '/api/v1/question-bank/categories',
    method: 'post',
    data
  })
}

/**
 * 更新题目分类
 */
export const updateQuestionCategory = (categoryId: string, data: any) => {
  return request({
    url: `/api/v1/question-bank/categories/${categoryId}`,
    method: 'put',
    data
  })
}

/**
 * 删除题目分类
 */
export const deleteQuestionCategory = (categoryId: string) => {
  return request({
    url: `/api/v1/question-bank/categories/${categoryId}`,
    method: 'delete'
  })
}

// ==================== 题目管理 ====================

/**
 * 获取题目列表
 */
export const getQuestions = (params: {
  page?: number
  size?: number
  keyword?: string
  category_id?: string
  subject?: string
  question_type?: string
  difficulty?: number
  status?: string
}) => {
  return request({
    url: '/v1/question-bank/questions',
    method: 'get',
    params
  })
}

/**
 * 获取题目详情
 */
export const getQuestion = (questionId: string) => {
  return request({
    url: `/api/v1/question-bank/questions/${questionId}`,
    method: 'get'
  })
}

/**
 * 创建题目
 */
export const createQuestion = (data: {
  title: string
  content: string
  question_type: string
  category_id: string
  subject: string
  difficulty: number
  options?: any[]
  correct_answer: any
  explanation?: string
  tags?: string[]
  points?: number
  time_limit?: number
  attachments?: any[]
  status?: string
}) => {
  return request({
    url: '/api/v1/question-bank/questions',
    method: 'post',
    data
  })
}

/**
 * 更新题目
 */
export const updateQuestion = (questionId: string, data: any) => {
  return request({
    url: `/api/v1/question-bank/questions/${questionId}`,
    method: 'put',
    data
  })
}

/**
 * 删除题目
 */
export const deleteQuestion = (questionId: string) => {
  return request({
    url: `/api/v1/question-bank/questions/${questionId}`,
    method: 'delete'
  })
}

/**
 * 审核题目
 */
export const reviewQuestion = (questionId: string, data: {
  status: string
  review_comment?: string
}) => {
  return request({
    url: `/api/v1/question-bank/questions/${questionId}/review`,
    method: 'post',
    data
  })
}

// ==================== 试卷管理 ====================

/**
 * 获取试卷列表
 */
export const getPapers = (params: {
  page?: number
  size?: number
  keyword?: string
  subject?: string
  paper_type?: string
  status?: string
}) => {
  return request({
    url: '/v1/question-bank/papers',
    method: 'get',
    params
  })
}

/**
 * 获取试卷详情
 */
export const getPaper = (paperId: string) => {
  return request({
    url: `/api/v1/question-bank/papers/${paperId}`,
    method: 'get'
  })
}

/**
 * 创建试卷
 */
export const createPaper = (data: {
  title: string
  description?: string
  subject: string
  paper_type: string
  total_score: number
  time_limit: number
  question_count: number
  questions: any[]
  settings?: any
  status?: string
}) => {
  return request({
    url: '/api/v1/question-bank/papers',
    method: 'post',
    data
  })
}

/**
 * 更新试卷
 */
export const updatePaper = (paperId: string, data: any) => {
  return request({
    url: `/api/v1/question-bank/papers/${paperId}`,
    method: 'put',
    data
  })
}

/**
 * 删除试卷
 */
export const deletePaper = (paperId: string) => {
  return request({
    url: `/api/v1/question-bank/papers/${paperId}`,
    method: 'delete'
  })
}

/**
 * 发布试卷
 */
export const publishPaper = (paperId: string) => {
  return request({
    url: `/api/v1/question-bank/papers/${paperId}/publish`,
    method: 'post'
  })
}

/**
 * 撤回试卷
 */
export const unpublishPaper = (paperId: string) => {
  return request({
    url: `/api/v1/question-bank/papers/${paperId}/unpublish`,
    method: 'post'
  })
}

// ==================== 答题记录管理 ====================

/**
 * 获取答题记录列表
 */
export const getAnswerRecords = (params: {
  page?: number
  size?: number
  user_id?: string
  question_id?: string
  paper_id?: string
  is_correct?: boolean
}) => {
  return request({
    url: '/v1/question-bank/answer-records',
    method: 'get',
    params
  })
}

/**
 * 提交答案
 */
export const submitAnswer = (data: {
  question_id: string
  paper_id?: string
  user_answer: any
  time_spent?: number
}) => {
  return request({
    url: '/api/v1/question-bank/answer-records',
    method: 'post',
    data
  })
}

// ==================== 试卷记录管理 ====================

/**
 * 获取试卷记录列表
 */
export const getPaperRecords = (params: {
  page?: number
  size?: number
  user_id?: string
  paper_id?: string
  status?: string
}) => {
  return request({
    url: '/v1/question-bank/paper-records',
    method: 'get',
    params
  })
}

/**
 * 开始答题
 */
export const startPaper = (paperId: string) => {
  return request({
    url: `/api/v1/question-bank/papers/${paperId}/start`,
    method: 'post'
  })
}

/**
 * 提交试卷
 */
export const submitPaper = (paperId: string, data: {
  answers: any[]
  time_spent: number
}) => {
  return request({
    url: `/api/v1/question-bank/papers/${paperId}/submit`,
    method: 'post',
    data
  })
}

/**
 * 获取试卷结果
 */
export const getPaperResult = (recordId: string) => {
  return request({
    url: `/api/v1/question-bank/paper-records/${recordId}/result`,
    method: 'get'
  })
}

// ==================== 统计分析 ====================

/**
 * 获取题库统计概览
 */
export const getQuestionBankOverview = () => {
  return request({
    url: '/v1/question-bank/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取题目统计
 */
export const getQuestionStatistics = (questionId: string) => {
  return request({
    url: `/api/v1/question-bank/statistics/questions/${questionId}`,
    method: 'get'
  })
}

/**
 * 获取试卷统计
 */
export const getPaperStatistics = (paperId: string) => {
  return request({
    url: `/api/v1/question-bank/statistics/papers/${paperId}`,
    method: 'get'
  })
}

// ==================== 数据类型定义 ====================

export interface QuestionCategory {
  id: string
  name: string
  description?: string
  subject: string
  parent_id?: string
  sort_order: number
  icon?: string
  color?: string
  question_count: number
  created_at: string
  updated_at: string
}

export interface Question {
  id: string
  title: string
  content: string
  question_type: string
  category_id: string
  subject: string
  difficulty: number
  options?: any[]
  correct_answer: any
  explanation?: string
  tags: string[]
  points: number
  time_limit?: number
  attachments: any[]
  status: string
  answer_count: number
  correct_count: number
  accuracy_rate: number
  created_at: string
  updated_at: string
}

export interface Paper {
  id: string
  title: string
  description?: string
  subject: string
  paper_type: string
  total_score: number
  time_limit: number
  question_count: number
  questions: any[]
  settings: any
  status: string
  attempt_count: number
  average_score: number
  pass_rate: number
  created_at: string
  updated_at: string
}

export interface AnswerRecord {
  id: string
  user_id: string
  question_id: string
  paper_id?: string
  user_answer: any
  correct_answer: any
  is_correct: boolean
  score: number
  time_spent: number
  created_at: string
}

export interface PaperRecord {
  id: string
  user_id: string
  paper_id: string
  start_time: string
  end_time?: string
  time_spent: number
  total_score: number
  user_score: number
  accuracy_rate: number
  status: string
  answers: any[]
  created_at: string
}

export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}