#!/usr/bin/env python3
"""
测试API端点的脚本
"""
import requests
import json

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8000/api"

    # 首先尝试登录获取token
    print("=== 尝试登录获取token ===")
    login_url = base_url + "/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    headers = {}

    try:
        # 使用表单数据而不是JSON
        login_response = requests.post(login_url, data=login_data, timeout=10)
        print(f"登录状态码: {login_response.status_code}")

        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get("access_token")
            if token:
                headers["Authorization"] = f"Bearer {token}"
                print("✓ 登录成功，获取到token")
            else:
                print("✗ 登录响应中没有token")
        else:
            print(f"✗ 登录失败: {login_response.text}")
    except requests.exceptions.RequestException as e:
        print(f"✗ 登录请求失败: {e}")

    # 测试端点列表
    endpoints = [
        "/system/logs/modules",
        "/system/logs?page=1&page_size=10",
        "/system/logs/statistics",
        "/system/logs/realtime"
    ]

    print("\n=== API端点测试 ===")

    for endpoint in endpoints:
        url = base_url + endpoint
        print(f"\n测试: {url}")

        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
            else:
                print(f"错误响应: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_api_endpoints()
