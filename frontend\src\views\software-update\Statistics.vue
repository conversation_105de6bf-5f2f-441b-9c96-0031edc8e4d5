<template>
  <div class="update-statistics">
    <div class="page-header">
      <h1>更新统计</h1>
      <p>查看软件更新下载、安装数据和用户反馈统计</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon downloads">
              <el-icon><Download /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalDownloads }}</div>
              <div class="stat-label">总下载数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon installs">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalInstalls }}</div>
              <div class="stat-label">总安装数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success-rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ (statistics.successRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon failures">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalFailures }}</div>
              <div class="stat-label">失败次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>下载趋势分析</span>
              <el-select v-model="timeRange" size="small" style="width: 120px;">
                <el-option label="最近7天" value="7d" />
                <el-option label="最近30天" value="30d" />
                <el-option label="最近90天" value="90d" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加下载趋势图表 -->
            <div class="chart-placeholder">下载趋势图表</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>平台分布统计</span>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加平台分布饼图 -->
            <div class="chart-placeholder">平台分布饼图</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>版本更新统计</span>
            </div>
          </template>
          <div class="chart-container">
            <!-- TODO: 添加版本更新统计图表 -->
            <div class="chart-placeholder">版本更新统计图表</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="table-section">
      <template #header>
        <div class="card-header">
          <span>版本统计详情</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索版本"
              style="width: 200px"
              clearable
              @change="loadStatistics"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="exportStatistics">
              <el-icon><Download /></el-icon>
              导出统计
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="versionStats"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column label="版本号" prop="version" width="120" />
        <el-table-column label="发布时间" width="150">
          <template #default="{ row }">
            <span>{{ formatDate(row.release_date) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下载统计" width="150">
          <template #default="{ row }">
            <div class="download-stats">
              <div class="stat-item">
                <span class="stat-label">下载:</span>
                <span class="stat-value">{{ row.downloads }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">安装:</span>
                <span class="stat-value install-count">{{ row.installs }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="成功率" width="120">
          <template #default="{ row }">
            <div class="success-rate-info">
              <div class="rate-value" :class="getRateClass(row.success_rate)">
                {{ (row.success_rate * 100).toFixed(1) }}%
              </div>
              <el-progress 
                :percentage="row.success_rate * 100" 
                :status="getProgressStatus(row.success_rate)"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平台分布" min-width="200">
          <template #default="{ row }">
            <div class="platform-stats">
              <div v-for="platform in row.platforms" :key="platform.name" class="platform-item">
                <span class="platform-name">{{ platform.name }}:</span>
                <span class="platform-count">{{ platform.count }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户反馈" width="120">
          <template #default="{ row }">
            <div class="feedback-stats">
              <div class="feedback-item">
                <span class="feedback-label">好评:</span>
                <span class="feedback-value positive">{{ row.positive_feedback }}</span>
              </div>
              <div class="feedback-item">
                <span class="feedback-label">差评:</span>
                <span class="feedback-value negative">{{ row.negative_feedback }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button type="text" size="small" @click="viewFeedback(row)">
              <el-icon><ChatDotRound /></el-icon>
              反馈
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadStatistics"
          @current-change="loadStatistics"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download, CircleCheck, TrendCharts, Warning, Search, View, ChatDotRound, ArrowUp
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const timeRange = ref('30d')
const searchKeyword = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = reactive({
  totalDownloads: 25678,
  totalInstalls: 23456,
  successRate: 0.914,
  totalFailures: 2222
})

// 版本统计列表
const versionStats = ref([
  {
    id: '1',
    version: 'v2.1.3',
    release_date: '2024-01-15',
    downloads: 5678,
    installs: 5234,
    success_rate: 0.922,
    platforms: [
      { name: 'Windows', count: 2345 },
      { name: 'macOS', count: 1567 },
      { name: 'Linux', count: 1322 }
    ],
    positive_feedback: 89,
    negative_feedback: 12
  }
])

// 方法
const loadStatistics = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取统计数据
    console.log('Loading statistics...')
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const getRateClass = (rate) => {
  if (rate >= 0.9) return 'excellent'
  if (rate >= 0.8) return 'good'
  if (rate >= 0.7) return 'average'
  return 'poor'
}

const getProgressStatus = (rate) => {
  if (rate >= 0.9) return 'success'
  if (rate >= 0.8) return ''
  if (rate >= 0.7) return 'warning'
  return 'exception'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const viewDetails = (version) => {
  // TODO: 查看版本详情
  console.log('Viewing version details:', version)
}

const viewFeedback = (version) => {
  // TODO: 查看用户反馈
  console.log('Viewing user feedback:', version)
}

const exportStatistics = () => {
  // TODO: 导出统计数据
  console.log('Exporting statistics...')
}

// 工具函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.update-statistics {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.downloads {
  background-color: #409eff;
}

.stat-icon.installs {
  background-color: #67c23a;
}

.stat-icon.success-rate {
  background-color: #e6a23c;
}

.stat-icon.failures {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.table-section {
  margin-bottom: 24px;
}

.download-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.install-count {
  color: #67c23a !important;
}

.success-rate-info {
  text-align: center;
}

.rate-value {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.rate-value.excellent {
  color: #67c23a;
}

.rate-value.good {
  color: #409eff;
}

.rate-value.average {
  color: #e6a23c;
}

.rate-value.poor {
  color: #f56c6c;
}

.platform-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.platform-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.platform-name {
  color: #909399;
}

.platform-count {
  color: #606266;
  font-weight: 500;
}

.feedback-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feedback-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.feedback-label {
  color: #909399;
}

.feedback-value {
  font-weight: 500;
}

.feedback-value.positive {
  color: #67c23a;
}

.feedback-value.negative {
  color: #f56c6c;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
