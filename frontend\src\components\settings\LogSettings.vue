<template>
  <el-card class="settings-card">
    <template #header>
      <div class="card-header">
        <el-icon><Document /></el-icon>
        <span>日志信息</span>
        <el-tag type="info" size="small">当前级别: {{ currentLogLevel }}</el-tag>
      </div>
    </template>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="160px"
      class="settings-form"
    >
      <!-- 日志配置 -->
      <div class="settings-section">
        <h4 class="section-title">日志配置</h4>
        
        <el-form-item label="日志级别" prop="log_level" required>
          <el-select
            v-model="formData.log_level"
            placeholder="请选择日志级别"
            style="width: 100%"
          >
            <el-option
              v-for="level in logLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            >
              <div class="log-level-option">
                <span>{{ level.label }}</span>
                <span class="level-description">{{ level.description }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="field-description">
            设置系统记录的最低日志级别，级别越高记录的信息越少
          </div>
        </el-form-item>
        
        <el-form-item label="日志文件路径" prop="log_file_path">
          <el-input
            v-model="formData.log_file_path"
            placeholder="如：logs/wiscude-admin.log"
            clearable
          />
          <div class="field-description">
            日志文件的存储路径，相对于项目根目录
          </div>
        </el-form-item>
        
        <el-form-item label="日志保留天数" prop="log_retention_days">
          <el-input-number
            v-model="formData.log_retention_days"
            :min="1"
            :max="365"
            :step="1"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">天</span>
          <div class="field-description">
            日志文件的保留时间，超期的日志文件将被自动删除
          </div>
        </el-form-item>
        
        <el-form-item label="单个文件最大大小" prop="log_max_size_mb">
          <el-input-number
            v-model="formData.log_max_size_mb"
            :min="1"
            :max="1000"
            :step="1"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit-text">MB</span>
          <div class="field-description">
            单个日志文件的最大大小，超过后将自动轮转创建新文件
          </div>
        </el-form-item>
      </div>
      
      <!-- 日志查看 -->
      <div class="settings-section">
        <h4 class="section-title">日志查看</h4>
        
        <div class="log-files-list">
          <el-table :data="logFiles" style="width: 100%">
            <el-table-column prop="name" label="文件名" />
            <el-table-column prop="size" label="大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="modified" label="修改时间" width="180">
              <template #default="{ row }">
                {{ formatTime(row.modified) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button
                  size="small"
                  @click="viewLogFile(row)"
                >
                  查看
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click="downloadLogFile(row)"
                >
                  下载
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteLogFile(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="log-actions">
            <el-button @click="refreshLogFiles" :loading="loadingLogs">
              <el-icon><Refresh /></el-icon>
              刷新列表
            </el-button>
            <el-button type="warning" @click="clearOldLogs">
              <el-icon><Delete /></el-icon>
              清理过期日志
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 实时日志 -->
      <div class="settings-section">
        <h4 class="section-title">实时日志</h4>
        
        <div class="real-time-log">
          <div class="log-controls">
            <el-button
              :type="isWatching ? 'danger' : 'success'"
              @click="toggleLogWatch"
            >
              {{ isWatching ? '停止监控' : '开始监控' }}
            </el-button>
            <el-select
              v-model="watchLogLevel"
              placeholder="选择监控级别"
              style="width: 150px; margin-left: 12px"
            >
              <el-option
                v-for="level in logLevels"
                :key="level.value"
                :label="level.value"
                :value="level.value"
              />
            </el-select>
            <el-button @click="clearLogDisplay" style="margin-left: 12px">
              清空显示
            </el-button>
          </div>
          
          <div class="log-display" ref="logDisplayRef">
            <div
              v-for="(log, index) in realtimeLogs"
              :key="index"
              class="log-entry"
              :class="log.level.toLowerCase()"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-level">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <el-form-item class="form-actions">
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存设置
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
        <el-button
          type="info"
          @click="exportLogs"
        >
          导出所有日志
        </el-button>
      </el-form-item>
    </el-form>
    
    <!-- 日志查看对话框 -->
    <el-dialog
      v-model="logViewDialogVisible"
      :title="`查看日志 - ${currentLogFile?.name}`"
      width="80%"
      top="5vh"
    >
      <div class="log-viewer">
        <div class="log-viewer-controls">
          <el-input
            v-model="logSearchKeyword"
            placeholder="搜索日志内容..."
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="searchInLog">搜索</el-button>
        </div>
        
        <div class="log-content">
          <pre>{{ logFileContent }}</pre>
        </div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Refresh, Delete, Search } from '@element-plus/icons-vue'
import type { SystemSettings, LogFile } from '@/types/settings'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  settings: SystemSettings
}

interface Emits {
  (e: 'update', settings: Partial<SystemSettings>): void
  (e: 'save', settings: Partial<SystemSettings>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const logDisplayRef = ref<HTMLElement>()
const saving = ref(false)
const loadingLogs = ref(false)
const isWatching = ref(false)
const watchLogLevel = ref('INFO')
const logViewDialogVisible = ref(false)
const currentLogFile = ref<LogFile | null>(null)
const logFileContent = ref('')
const logSearchKeyword = ref('')
const realtimeLogs = ref<any[]>([])
let logWatchInterval: number | null = null

const formData = reactive<Partial<SystemSettings>>({
  log_level: 'INFO',
  log_file_path: 'logs/wiscude-admin.log',
  log_retention_days: 30,
  log_max_size_mb: 100
})

const logFiles = ref<LogFile[]>([
  {
    name: 'wiscude-admin.log',
    size: 2048576,
    modified: '2024-01-20T10:30:00Z',
    path: 'logs/wiscude-admin.log'
  },
  {
    name: 'wiscude-admin.log.1',
    size: 10485760,
    modified: '2024-01-19T23:59:59Z',
    path: 'logs/wiscude-admin.log.1'
  }
])

const logLevels = [
  { value: 'DEBUG', label: 'DEBUG - 调试信息', description: '详细的调试信息' },
  { value: 'INFO', label: 'INFO - 一般信息', description: '一般的运行信息' },
  { value: 'WARNING', label: 'WARNING - 警告信息', description: '警告但不影响运行' },
  { value: 'ERROR', label: 'ERROR - 错误信息', description: '错误但系统可继续运行' },
  { value: 'CRITICAL', label: 'CRITICAL - 严重错误', description: '严重错误，系统可能停止' }
]

const formRules: FormRules = {
  log_level: [
    { required: true, message: '请选择日志级别', trigger: 'change' }
  ],
  log_retention_days: [
    { required: true, message: '请设置日志保留天数', trigger: 'blur' }
  ],
  log_max_size_mb: [
    { required: true, message: '请设置日志文件最大大小', trigger: 'blur' }
  ]
}

const currentLogLevel = computed(() => {
  return formData.log_level || 'INFO'
})

// 监听props变化
watch(() => props.settings, (newSettings) => {
  Object.assign(formData, newSettings)
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newData) => {
  emit('update', newData)
}, { deep: true })

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

const refreshLogFiles = async () => {
  loadingLogs.value = true
  try {
    // 模拟获取日志文件列表
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('日志文件列表已刷新')
  } catch (error) {
    ElMessage.error('刷新日志文件列表失败')
  } finally {
    loadingLogs.value = false
  }
}

const viewLogFile = async (file: LogFile) => {
  currentLogFile.value = file
  logViewDialogVisible.value = true
  
  try {
    // 模拟加载日志文件内容
    logFileContent.value = '正在加载日志内容...'
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    logFileContent.value = `[2024-01-20 10:30:00] INFO: 系统启动成功
[2024-01-20 10:30:01] INFO: 数据库连接已建立
[2024-01-20 10:30:02] INFO: 用户 admin 登录成功
[2024-01-20 10:35:15] WARNING: 缓存命中率较低: 45%
[2024-01-20 10:40:30] ERROR: 邮件发送失败: SMTP连接超时
[2024-01-20 10:45:00] INFO: 数据同步完成，处理 1234 条记录`
  } catch (error) {
    ElMessage.error('加载日志文件失败')
  }
}

const downloadLogFile = (file: LogFile) => {
  // 创建下载链接
  const link = document.createElement('a')
  link.href = `data:text/plain;charset=utf-8,${encodeURIComponent(logFileContent.value || '日志内容')}`
  link.download = file.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  ElMessage.success(`日志文件 ${file.name} 下载成功`)
}

const deleteLogFile = (file: LogFile) => {
  ElMessageBox.confirm(
    `确定要删除日志文件 ${file.name} 吗？此操作不可恢复。`,
    '确认删除',
    { type: 'warning' }
  ).then(() => {
    const index = logFiles.value.findIndex(f => f.name === file.name)
    if (index > -1) {
      logFiles.value.splice(index, 1)
      ElMessage.success('日志文件删除成功')
    }
  }).catch(() => {
    // 用户取消
  })
}

const clearOldLogs = () => {
  ElMessageBox.confirm(
    '确定要清理过期的日志文件吗？将删除超过保留期限的所有日志文件。',
    '确认清理',
    { type: 'warning' }
  ).then(() => {
    ElMessage.success('过期日志清理完成')
  }).catch(() => {
    // 用户取消
  })
}

const toggleLogWatch = () => {
  if (isWatching.value) {
    stopLogWatch()
  } else {
    startLogWatch()
  }
}

const startLogWatch = () => {
  isWatching.value = true
  logWatchInterval = window.setInterval(() => {
    // 模拟实时日志
    const levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
    const messages = [
      '用户登录成功',
      '数据同步完成',
      '缓存更新',
      '邮件发送',
      '数据库查询',
      '文件上传完成'
    ]
    
    const newLog = {
      time: new Date().toLocaleTimeString(),
      level: levels[Math.floor(Math.random() * levels.length)],
      message: messages[Math.floor(Math.random() * messages.length)]
    }
    
    if (newLog.level === watchLogLevel.value || watchLogLevel.value === 'ALL') {
      realtimeLogs.value.push(newLog)
      
      // 限制显示的日志条数
      if (realtimeLogs.value.length > 100) {
        realtimeLogs.value.shift()
      }
      
      // 自动滚动到底部
      nextTick(() => {
        if (logDisplayRef.value) {
          logDisplayRef.value.scrollTop = logDisplayRef.value.scrollHeight
        }
      })
    }
  }, 2000)
}

const stopLogWatch = () => {
  isWatching.value = false
  if (logWatchInterval) {
    clearInterval(logWatchInterval)
    logWatchInterval = null
  }
}

const clearLogDisplay = () => {
  realtimeLogs.value = []
}

const searchInLog = () => {
  if (!logSearchKeyword.value) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  // 简单的搜索高亮
  const keyword = logSearchKeyword.value
  const content = logFileContent.value
  const highlightedContent = content.replace(
    new RegExp(keyword, 'gi'),
    `<mark>${keyword}</mark>`
  )
  
  logFileContent.value = highlightedContent
  ElMessage.success(`找到 ${(content.match(new RegExp(keyword, 'gi')) || []).length} 个匹配项`)
}

const exportLogs = () => {
  ElMessageBox.confirm('确定要导出所有日志文件吗？', '确认导出', {
    type: 'info'
  }).then(() => {
    ElMessage.success('日志导出功能开发中...')
  }).catch(() => {
    // 用户取消
  })
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    emit('save', formData)
    
    setTimeout(() => {
      saving.value = false
    }, 1000)
    
  } catch (error) {
    ElMessage.error('表单验证失败，请检查输入')
  }
}

const handleReset = () => {
  ElMessageBox.confirm('确定要重置日志设置吗？', '确认重置', {
    type: 'warning'
  }).then(() => {
    formRef.value?.resetFields()
    Object.assign(formData, props.settings)
    ElMessage.success('日志设置已重置')
  }).catch(() => {
    // 用户取消
  })
}

onMounted(() => {
  Object.assign(formData, props.settings)
  refreshLogFiles()
})

onUnmounted(() => {
  stopLogWatch()
})
</script>

<style lang="scss" scoped>
.settings-card {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .settings-form {
    .settings-section {
      margin-bottom: 32px;
      
      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }
    
    .field-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
      line-height: 1.4;
    }
    
    .unit-text {
      margin-left: 8px;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
    
    .log-level-option {
      display: flex;
      justify-content: space-between;
      
      .level-description {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
    
    .log-files-list {
      .log-actions {
        margin-top: 16px;
        display: flex;
        gap: 12px;
      }
    }
    
    .real-time-log {
      .log-controls {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
      }
      
      .log-display {
        height: 300px;
        overflow-y: auto;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
        padding: 12px;
        background: #000;
        color: #fff;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        
        .log-entry {
          margin-bottom: 4px;
          
          .log-time {
            color: #888;
            margin-right: 8px;
          }
          
          .log-level {
            margin-right: 8px;
            font-weight: bold;
            
            &.debug { color: #888; }
            &.info { color: #4CAF50; }
            &.warning { color: #FF9800; }
            &.error { color: #F44336; }
            &.critical { color: #E91E63; }
          }
          
          .log-message {
            color: #fff;
          }
        }
      }
    }
    
    .form-actions {
      margin-top: 32px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}

.log-viewer {
  .log-viewer-controls {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .log-content {
    height: 500px;
    overflow: auto;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 12px;
    background: #f8f9fa;
    
    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style>
