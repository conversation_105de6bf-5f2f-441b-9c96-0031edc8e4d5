<template>
  <div class="counselors-management">
    <div class="page-header">
      <h1>咨询师管理</h1>
      <p>管理心理咨询师资源、资质认证、预约安排和服务评价</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="咨询师列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新增咨询师
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedCounselors.length === 0"
              @click="batchActivate"
            >
              批量激活
            </el-button>
            <el-button 
              type="warning" 
              :disabled="selectedCounselors.length === 0"
              @click="batchSuspend"
            >
              批量暂停
            </el-button>
            <el-button @click="exportCounselors">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索咨询师姓名"
              style="width: 200px"
              clearable
              @change="loadCounselors"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="specializationFilter" placeholder="专业领域" style="width: 120px" @change="loadCounselors">
              <el-option label="全部领域" value="" />
              <el-option label="青少年心理" value="adolescent" />
              <el-option label="情感咨询" value="relationship" />
              <el-option label="职场心理" value="workplace" />
              <el-option label="学习压力" value="academic" />
              <el-option label="家庭治疗" value="family" />
            </el-select>
            <el-select v-model="levelFilter" placeholder="资质等级" style="width: 100px" @change="loadCounselors">
              <el-option label="全部等级" value="" />
              <el-option label="初级" value="junior" />
              <el-option label="中级" value="intermediate" />
              <el-option label="高级" value="senior" />
              <el-option label="专家" value="expert" />
            </el-select>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadCounselors">
              <el-option label="全部状态" value="" />
              <el-option label="活跃" value="active" />
              <el-option label="暂停" value="suspended" />
              <el-option label="离职" value="inactive" />
            </el-select>
          </div>
        </div>

        <!-- 咨询师列表 -->
        <el-table
          :data="counselors"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="咨询师信息" min-width="300">
            <template #default="{ row }">
              <div class="counselor-info">
                <div class="counselor-avatar">
                  <img :src="row.avatar || '/default-avatar.png'" :alt="row.name" />
                  <div class="online-status" :class="row.online_status"></div>
                </div>
                <div class="counselor-details">
                  <div class="counselor-name">{{ row.name }}</div>
                  <div class="counselor-title">{{ row.title }}</div>
                  <div class="counselor-meta">
                    <el-tag size="small" :type="getLevelTagType(row.level)">
                      {{ getLevelName(row.level) }}
                    </el-tag>
                    <span class="experience">{{ row.experience }}年经验</span>
                    <span class="rating">{{ row.rating }}★</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="专业领域" width="150">
            <template #default="{ row }">
              <div class="specializations">
                <el-tag 
                  v-for="spec in row.specializations.slice(0, 2)" 
                  :key="spec" 
                  size="small" 
                  class="spec-tag"
                >
                  {{ getSpecializationName(spec) }}
                </el-tag>
                <span v-if="row.specializations.length > 2" class="more-specs">
                  +{{ row.specializations.length - 2 }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="服务数据" width="120">
            <template #default="{ row }">
              <div class="service-stats">
                <div class="stat-item">
                  <span class="stat-label">咨询:</span>
                  <span class="stat-value">{{ row.consultation_count }}次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">预约:</span>
                  <span class="stat-value appointment-count">{{ row.appointment_count }}个</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">满意度:</span>
                  <span class="stat-value satisfaction-rate">{{ (row.satisfaction_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="资质认证" width="120">
            <template #default="{ row }">
              <div class="certifications">
                <div class="cert-item">
                  <span class="cert-label">执业证:</span>
                  <span class="cert-status" :class="row.license_status">
                    {{ getLicenseStatusName(row.license_status) }}
                  </span>
                </div>
                <div class="cert-item">
                  <span class="cert-label">到期:</span>
                  <span class="cert-date">{{ formatDate(row.license_expiry) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="工作时间" width="100">
            <template #default="{ row }">
              <div class="work-schedule">
                <div class="schedule-item">
                  <span class="schedule-label">工作日:</span>
                  <span class="schedule-value">{{ row.work_days }}</span>
                </div>
                <div class="schedule-item">
                  <span class="schedule-label">时间:</span>
                  <span class="schedule-value">{{ row.work_hours }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="加入时间" width="120">
            <template #default="{ row }">
              <span class="join-time">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewCounselor(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="text" size="small" @click="editCounselor(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="text" size="small" @click="viewSchedule(row)">
                <el-icon><Calendar /></el-icon>
                排班
              </el-button>
              <el-button type="text" size="small" @click="viewAppointments(row)">
                <el-icon><Clock /></el-icon>
                预约
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                :class="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleCounselorStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'active' ? '暂停' : '激活' }}
              </el-button>
              <el-button type="text" size="small" class="danger" @click="deleteCounselor(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadCounselors"
            @current-change="loadCounselors"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="专业领域分布">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">专业领域分布图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="服务质量分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">服务质量分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card title="咨询师工作负荷分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">工作负荷分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑咨询师对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingCounselor ? '编辑咨询师' : '新增咨询师'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="counselorForm" :rules="counselorRules" ref="counselorFormRef" label-width="100px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="counselorForm.name" placeholder="请输入咨询师姓名" />
            </el-form-item>
            <el-form-item label="职称" prop="title">
              <el-input v-model="counselorForm.title" placeholder="请输入职称" />
            </el-form-item>
            <el-form-item label="资质等级" prop="level">
              <el-select v-model="counselorForm.level" placeholder="请选择资质等级">
                <el-option label="初级" value="junior" />
                <el-option label="中级" value="intermediate" />
                <el-option label="高级" value="senior" />
                <el-option label="专家" value="expert" />
              </el-select>
            </el-form-item>
            <el-form-item label="工作经验" prop="experience">
              <el-input-number v-model="counselorForm.experience" :min="0" :max="50" placeholder="年" />
            </el-form-item>
            <el-form-item label="专业领域" prop="specializations">
              <el-select 
                v-model="counselorForm.specializations" 
                multiple 
                placeholder="请选择专业领域"
              >
                <el-option label="青少年心理" value="adolescent" />
                <el-option label="情感咨询" value="relationship" />
                <el-option label="职场心理" value="workplace" />
                <el-option label="学习压力" value="academic" />
                <el-option label="家庭治疗" value="family" />
                <el-option label="焦虑抑郁" value="anxiety" />
              </el-select>
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="counselorForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="counselorForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="资质认证" name="certification">
            <el-form-item label="执业证号" prop="license_number">
              <el-input v-model="counselorForm.license_number" placeholder="请输入执业证号" />
            </el-form-item>
            <el-form-item label="发证机构" prop="license_authority">
              <el-input v-model="counselorForm.license_authority" placeholder="请输入发证机构" />
            </el-form-item>
            <el-form-item label="证书到期日" prop="license_expiry">
              <el-date-picker
                v-model="counselorForm.license_expiry"
                type="date"
                placeholder="选择到期日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="教育背景" prop="education">
              <el-input v-model="counselorForm.education" type="textarea" rows="3" placeholder="请输入教育背景" />
            </el-form-item>
            <el-form-item label="专业培训" prop="training">
              <el-input v-model="counselorForm.training" type="textarea" rows="3" placeholder="请输入专业培训经历" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="工作安排" name="schedule">
            <el-form-item label="工作日" prop="work_days">
              <el-checkbox-group v-model="counselorForm.work_days_array">
                <el-checkbox label="周一">周一</el-checkbox>
                <el-checkbox label="周二">周二</el-checkbox>
                <el-checkbox label="周三">周三</el-checkbox>
                <el-checkbox label="周四">周四</el-checkbox>
                <el-checkbox label="周五">周五</el-checkbox>
                <el-checkbox label="周六">周六</el-checkbox>
                <el-checkbox label="周日">周日</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="工作时间" prop="work_hours">
              <el-time-picker
                v-model="counselorForm.work_time_range"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
            <el-form-item label="咨询费用" prop="consultation_fee">
              <el-input-number v-model="counselorForm.consultation_fee" :min="0" placeholder="元/小时" />
            </el-form-item>
            <el-form-item label="服务方式">
              <el-checkbox-group v-model="counselorForm.service_methods">
                <el-checkbox label="面对面">面对面</el-checkbox>
                <el-checkbox label="视频通话">视频通话</el-checkbox>
                <el-checkbox label="语音通话">语音通话</el-checkbox>
                <el-checkbox label="文字咨询">文字咨询</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="个人简介" name="profile">
            <el-form-item label="头像">
              <div class="avatar-upload">
                <el-upload
                  class="upload-demo"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                  :http-request="uploadAvatar"
                  accept="image/*"
                >
                  <el-button type="primary">
                    <el-icon><Upload /></el-icon>
                    上传头像
                  </el-button>
                </el-upload>
                <div v-if="counselorForm.avatar" class="avatar-preview">
                  <img :src="counselorForm.avatar" alt="咨询师头像" />
                  <el-button type="text" size="small" class="remove-btn" @click="removeAvatar">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="个人简介" prop="bio">
              <el-input v-model="counselorForm.bio" type="textarea" rows="5" placeholder="请输入个人简介" />
            </el-form-item>
            <el-form-item label="咨询理念" prop="philosophy">
              <el-input v-model="counselorForm.philosophy" type="textarea" rows="3" placeholder="请输入咨询理念" />
            </el-form-item>
            <el-form-item label="擅长领域" prop="expertise">
              <el-input v-model="counselorForm.expertise" type="textarea" rows="3" placeholder="请输入擅长领域详细描述" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCounselor" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 咨询师详情查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="咨询师详情"
      width="800px"
    >
      <div class="counselor-detail" v-if="viewingCounselor">
        <div class="detail-header">
          <div class="counselor-avatar-large">
            <img :src="viewingCounselor.avatar || '/default-avatar.png'" :alt="viewingCounselor.name" />
            <div class="online-status-large" :class="viewingCounselor.online_status"></div>
          </div>
          <div class="counselor-info-large">
            <h2 class="counselor-name-large">{{ viewingCounselor.name }}</h2>
            <div class="counselor-title-large">{{ viewingCounselor.title }}</div>
            <div class="counselor-meta-large">
              <el-tag :type="getLevelTagType(viewingCounselor.level)">
                {{ getLevelName(viewingCounselor.level) }}
              </el-tag>
              <span class="experience-large">{{ viewingCounselor.experience }}年经验</span>
              <span class="rating-large">{{ viewingCounselor.rating }}★ ({{ viewingCounselor.review_count }}评价)</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>专业领域</h3>
          <div class="specializations-detail">
            <el-tag 
              v-for="spec in viewingCounselor.specializations" 
              :key="spec" 
              size="small" 
              class="spec-tag-detail"
            >
              {{ getSpecializationName(spec) }}
            </el-tag>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>个人简介</h3>
          <div class="bio-detail">{{ viewingCounselor.bio }}</div>
        </div>
        
        <div class="detail-section">
          <h3>咨询理念</h3>
          <div class="philosophy-detail">{{ viewingCounselor.philosophy }}</div>
        </div>
        
        <div class="detail-section">
          <h3>服务信息</h3>
          <div class="service-info">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">咨询费用：</span>
                <span class="value">¥{{ viewingCounselor.consultation_fee }}/小时</span>
              </div>
              <div class="info-item">
                <span class="label">工作时间：</span>
                <span class="value">{{ viewingCounselor.work_days }} {{ viewingCounselor.work_hours }}</span>
              </div>
              <div class="info-item">
                <span class="label">服务方式：</span>
                <span class="value">{{ viewingCounselor.service_methods?.join('、') }}</span>
              </div>
              <div class="info-item">
                <span class="label">满意度：</span>
                <span class="value">{{ (viewingCounselor.satisfaction_rate * 100).toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>资质认证</h3>
          <div class="certification-info">
            <div class="cert-grid">
              <div class="cert-item">
                <span class="cert-label">执业证号：</span>
                <span class="cert-value">{{ viewingCounselor.license_number }}</span>
              </div>
              <div class="cert-item">
                <span class="cert-label">发证机构：</span>
                <span class="cert-value">{{ viewingCounselor.license_authority }}</span>
              </div>
              <div class="cert-item">
                <span class="cert-label">证书到期：</span>
                <span class="cert-value">{{ formatDate(viewingCounselor.license_expiry) }}</span>
              </div>
              <div class="cert-item">
                <span class="cert-label">证书状态：</span>
                <span class="cert-value" :class="viewingCounselor.license_status">
                  {{ getLicenseStatusName(viewingCounselor.license_status) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, View, Upload, Download, Calendar, Clock
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedCounselors = ref([])
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const editingCounselor = ref(null)
const viewingCounselor = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const specializationFilter = ref('')
const levelFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 咨询师列表
const counselors = ref([
  {
    id: '1',
    name: '张心理师',
    title: '临床心理学博士',
    level: 'expert',
    experience: 10,
    avatar: '',
    specializations: ['adolescent', 'anxiety', 'academic'],
    consultation_count: 456,
    appointment_count: 23,
    satisfaction_rate: 0.95,
    rating: 4.8,
    review_count: 128,
    license_number: 'PSY20240001',
    license_authority: '中国心理学会',
    license_expiry: '2025-12-31',
    license_status: 'valid',
    work_days: '周一至周五',
    work_hours: '09:00-17:00',
    consultation_fee: 300,
    service_methods: ['面对面', '视频通话'],
    phone: '13800138000',
    email: '<EMAIL>',
    education: '北京大学心理学博士',
    training: '认知行为治疗、家庭系统治疗',
    bio: '专注于青少年心理健康，擅长处理学习压力、人际关系等问题',
    philosophy: '以人为本，关注个体的成长和发展',
    expertise: '青少年心理咨询、学习障碍、情绪调节',
    online_status: 'online',
    status: 'active',
    created_at: '2024-01-15 10:30:00'
  }
])

// 表单数据
const counselorForm = reactive({
  name: '',
  title: '',
  level: 'junior',
  experience: 0,
  specializations: [],
  phone: '',
  email: '',
  license_number: '',
  license_authority: '',
  license_expiry: '',
  education: '',
  training: '',
  work_days_array: [],
  work_time_range: [],
  consultation_fee: 200,
  service_methods: [],
  avatar: '',
  bio: '',
  philosophy: '',
  expertise: ''
})

// 表单验证规则
const counselorRules = {
  name: [
    { required: true, message: '请输入咨询师姓名', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入职称', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择资质等级', trigger: 'change' }
  ],
  specializations: [
    { required: true, message: '请选择专业领域', trigger: 'change' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 方法
const loadCounselors = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取咨询师列表
    console.log('Loading counselors...')
  } catch (error) {
    ElMessage.error('加载咨询师列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedCounselors.value = selection
}

const getLevelName = (level) => {
  const levels = {
    junior: '初级',
    intermediate: '中级',
    senior: '高级',
    expert: '专家'
  }
  return levels[level] || level
}

const getLevelTagType = (level) => {
  const types = {
    junior: 'success',
    intermediate: 'primary',
    senior: 'warning',
    expert: 'danger'
  }
  return types[level] || ''
}

const getSpecializationName = (spec) => {
  const specs = {
    adolescent: '青少年心理',
    relationship: '情感咨询',
    workplace: '职场心理',
    academic: '学习压力',
    family: '家庭治疗',
    anxiety: '焦虑抑郁'
  }
  return specs[spec] || spec
}

const getStatusName = (status) => {
  const statuses = {
    active: '活跃',
    suspended: '暂停',
    inactive: '离职'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    suspended: 'warning',
    inactive: 'info'
  }
  return types[status] || ''
}

const getLicenseStatusName = (status) => {
  const statuses = {
    valid: '有效',
    expired: '已过期',
    suspended: '已暂停'
  }
  return statuses[status] || status
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const viewCounselor = (counselor) => {
  viewingCounselor.value = counselor
  showViewDialog.value = true
}

const editCounselor = (counselor) => {
  editingCounselor.value = counselor
  Object.assign(counselorForm, counselor)
  // 处理工作日数组
  counselorForm.work_days_array = counselor.work_days?.split('、') || []
  showCreateDialog.value = true
}

const viewSchedule = (counselor) => {
  // TODO: 跳转到排班管理页面
  console.log('Viewing schedule for counselor:', counselor)
}

const viewAppointments = (counselor) => {
  // TODO: 跳转到预约管理页面
  console.log('Viewing appointments for counselor:', counselor)
}

const resetForm = () => {
  editingCounselor.value = null
  Object.assign(counselorForm, {
    name: '',
    title: '',
    level: 'junior',
    experience: 0,
    specializations: [],
    phone: '',
    email: '',
    license_number: '',
    license_authority: '',
    license_expiry: '',
    education: '',
    training: '',
    work_days_array: [],
    work_time_range: [],
    consultation_fee: 200,
    service_methods: [],
    avatar: '',
    bio: '',
    philosophy: '',
    expertise: ''
  })
  formActiveTab.value = 'basic'
}

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const uploadAvatar = async (options) => {
  // TODO: 实现头像上传逻辑
  console.log('Uploading avatar...', options.file)
  // 模拟上传成功
  counselorForm.avatar = URL.createObjectURL(options.file)
  ElMessage.success('头像上传成功')
}

const removeAvatar = () => {
  counselorForm.avatar = ''
}

const saveCounselor = async () => {
  saving.value = true
  try {
    // 处理工作日
    counselorForm.work_days = counselorForm.work_days_array.join('、')
    // 处理工作时间
    if (counselorForm.work_time_range && counselorForm.work_time_range.length === 2) {
      counselorForm.work_hours = `${counselorForm.work_time_range[0]}-${counselorForm.work_time_range[1]}`
    }

    // TODO: 实现保存逻辑
    console.log('Saving counselor...', counselorForm)
    showCreateDialog.value = false
    ElMessage.success(editingCounselor.value ? '咨询师信息更新成功' : '咨询师添加成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleCounselorStatus = async (counselor) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling counselor status...', counselor)
}

const deleteCounselor = async (counselor) => {
  try {
    await ElMessageBox.confirm('确定要删除这个咨询师吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting counselor...', counselor)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const batchActivate = async () => {
  // TODO: 实现批量激活逻辑
  console.log('Batch activating counselors...', selectedCounselors.value)
}

const batchSuspend = async () => {
  // TODO: 实现批量暂停逻辑
  console.log('Batch suspending counselors...', selectedCounselors.value)
}

const exportCounselors = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting counselors...')
}

onMounted(() => {
  loadCounselors()
})
</script>

<style scoped>
.counselors-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.counselor-info {
  display: flex;
  gap: 12px;
  padding: 8px 0;
}

.counselor-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.counselor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.online-status.online {
  background-color: #67c23a;
}

.online-status.offline {
  background-color: #909399;
}

.online-status.busy {
  background-color: #f56c6c;
}

.counselor-details {
  flex: 1;
}

.counselor-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.counselor-title {
  font-size: 12px;
  color: #606266;
  margin-bottom: 6px;
}

.counselor-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.experience {
  font-size: 11px;
  color: #409eff;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.rating {
  font-size: 11px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.specializations {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.spec-tag {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.more-specs {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
}

.service-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.appointment-count {
  color: #409eff !important;
}

.satisfaction-rate {
  color: #67c23a !important;
}

.certifications {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cert-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.cert-label {
  color: #909399;
}

.cert-status {
  font-weight: 500;
}

.cert-status.valid {
  color: #67c23a;
}

.cert-status.expired {
  color: #f56c6c;
}

.cert-status.suspended {
  color: #e6a23c;
}

.cert-date {
  color: #606266;
  font-weight: 500;
}

.work-schedule {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.schedule-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.schedule-label {
  color: #909399;
}

.schedule-value {
  color: #606266;
  font-weight: 500;
}

.join-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.avatar-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.avatar-preview img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 50%;
}

.remove-btn {
  color: #f56c6c;
}

.counselor-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.counselor-avatar-large {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.counselor-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-status-large {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid white;
}

.online-status-large.online {
  background-color: #67c23a;
}

.online-status-large.offline {
  background-color: #909399;
}

.online-status-large.busy {
  background-color: #f56c6c;
}

.counselor-info-large {
  flex: 1;
}

.counselor-name-large {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.counselor-title-large {
  font-size: 16px;
  color: #606266;
  margin-bottom: 12px;
}

.counselor-meta-large {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.experience-large {
  font-size: 14px;
  color: #409eff;
  background-color: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.rating-large {
  font-size: 14px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.specializations-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-tag-detail {
  background-color: #f0f2f5;
  color: #606266;
  border: none;
}

.bio-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
}

.philosophy-detail {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
  border-left: 4px solid #409eff;
}

.service-info {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  color: #606266;
  font-weight: 500;
}

.value {
  color: #303133;
}

.certification-info {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.cert-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.cert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cert-label {
  color: #606266;
  font-weight: 500;
}

.cert-value {
  color: #303133;
}

.cert-value.valid {
  color: #67c23a;
}

.cert-value.expired {
  color: #f56c6c;
}

.cert-value.suspended {
  color: #e6a23c;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
