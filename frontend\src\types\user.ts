// 用户相关类型定义

export type Gender = 'MALE' | 'FEMALE' | 'OTHER' | 'UNKNOWN'

export type UserStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'DELETED'

export type MembershipType = 'REGULAR' | 'VIP'

export type UserLevel = 'LEVEL_ONE' | 'LEVEL_TWO' | 'LEVEL_THREE' | 'LEVEL_FOUR' | 'LEVEL_FIVE' | 'LEVEL_SIX'

export interface WiscudeUser {
  id: number
  email: string
  phone?: string
  nickname: string
  full_name?: string
  avatar_url?: string
  gender: Gender
  birth_date?: string
  bio?: string
  location?: string
  status: UserStatus
  membership: MembershipType
  level: UserLevel
  registration_time: string
  last_login?: string
  is_premium: boolean
  premium_expires_at?: string
  total_study_time: number
  total_focus_sessions: number
  total_check_ins: number
  experience_points: number
  created_at: string
  updated_at: string
  // 兼容性属性
  is_active?: boolean
  username?: string
}

export interface UserStats {
  study_stats: {
    total_sessions: number
    completed_sessions: number
    completion_rate: number
    avg_session_duration: number
    total_study_time: number
  }
  checkin_stats: {
    total_checkins: number
    total_check_ins: number
  }
  community_stats: {
    total_posts: number
    total_likes: number
  }
  general_stats: {
    level: number
    experience_points: number
    registration_days: number
  }
}

export interface UserOverview {
  total_users: number
  active_users: number
  premium_users: number
  inactive_users: number
  free_users: number
  gender_distribution: Array<{
    gender: Gender
    count: number
  }>
  level_distribution: Array<{
    level: number
    count: number
  }>
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  pages: number
}
