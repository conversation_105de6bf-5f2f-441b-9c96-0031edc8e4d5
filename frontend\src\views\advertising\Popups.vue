<template>
  <div class="popups-management">
    <div class="page-header">
      <h1>弹窗管理</h1>
      <p>管理应用内弹窗广告的展示条件、用户定向和统计分析</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="弹窗列表" name="list">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建弹窗
            </el-button>
            <el-button
              type="success"
              :disabled="selectedPopups.length === 0"
              @click="batchActivate"
            >
              批量启用
            </el-button>
            <el-button
              type="warning"
              :disabled="selectedPopups.length === 0"
              @click="batchPause"
            >
              批量暂停
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索弹窗标题"
              style="width: 200px"
              clearable
              @change="loadPopups"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="loadPopups">
              <el-option label="全部" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="active" />
              <el-option label="已暂停" value="paused" />
              <el-option label="已过期" value="expired" />
            </el-select>
            <el-select v-model="typeFilter" placeholder="类型筛选" style="width: 120px" @change="loadPopups">
              <el-option label="全部" value="" />
              <el-option label="模态框" value="modal" />
              <el-option label="提示框" value="toast" />
              <el-option label="横幅" value="banner" />
            </el-select>
          </div>
        </div>

        <!-- 弹窗列表 -->
        <el-table
          :data="popups"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="弹窗信息" min-width="250">
            <template #default="{ row }">
              <div class="popup-info">
                <div class="popup-title">{{ row.title }}</div>
                <div class="popup-content">{{ row.content }}</div>
                <div class="popup-meta">
                  <el-tag size="small">{{ getPopupTypeName(row.popup_type) }}</el-tag>
                  <el-tag size="small" type="info">{{ getTriggerTypeName(row.trigger_type) }}</el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="优先级" width="80">
            <template #default="{ row }">
              <span class="priority" :class="getPriorityClass(row.priority)">{{ row.priority }}</span>
            </template>
          </el-table-column>
          <el-table-column label="目标用户" width="150">
            <template #default="{ row }">
              <div class="target-info">
                <div v-if="row.target_new_users" class="target-tag">新用户</div>
                <div v-if="row.target_active_users" class="target-tag">活跃用户</div>
                <div v-if="row.target_user_types && row.target_user_types.length" class="target-tag">
                  {{ row.target_user_types.join(', ') }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="统计数据" width="180">
            <template #default="{ row }">
              <div class="stats-info">
                <div class="stat-item">
                  <el-icon><View /></el-icon>
                  <span>展示: {{ row.total_displays }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Mouse /></el-icon>
                  <span>点击: {{ row.total_clicks }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Close /></el-icon>
                  <span>关闭: {{ row.total_closes }}</span>
                </div>
                <div class="stat-item">
                  <span class="click-rate">点击率: {{ (row.click_rate * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="时间设置" width="180">
            <template #default="{ row }">
              <div v-if="row.start_time" class="time-info">
                <div>开始：{{ formatTime(row.start_time) }}</div>
                <div v-if="row.end_time">结束：{{ formatTime(row.end_time) }}</div>
                <div v-if="row.daily_start_hour !== null && row.daily_end_hour !== null">
                  每日：{{ row.daily_start_hour }}:00 - {{ row.daily_end_hour }}:00
                </div>
              </div>
              <span v-else class="no-time">未设置</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button text size="small" @click="editPopup(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                text
                size="small"
                :class="row.status === 'active' ? 'warning' : 'success'"
                @click="togglePopupStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'active' ? '暂停' : '启用' }}
              </el-button>
              <el-button text size="small" @click="previewPopup(row)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button text size="small" @click="viewStats(row)">
                <el-icon><TrendCharts /></el-icon>
                统计
              </el-button>
              <el-button text size="small" class="danger" @click="deletePopup(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadPopups"
            @current-change="loadPopups"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <!-- 统计图表区域 -->
        <div class="statistics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card title="展示效果分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">展示效果分析图表</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card title="用户行为分析">
                <div class="chart-container">
                  <!-- TODO: 添加图表组件 -->
                  <div class="chart-placeholder">用户行为分析图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑弹窗对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingPopup ? '编辑弹窗' : '新建弹窗'"
      width="900px"
      @close="resetForm"
    >
      <el-form :model="popupForm" :rules="popupRules" ref="popupFormRef" label-width="120px">
        <el-tabs v-model="formActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="弹窗标题" prop="title">
              <el-input v-model="popupForm.title" placeholder="请输入弹窗标题" />
            </el-form-item>
            <el-form-item label="弹窗内容" prop="content">
              <el-input v-model="popupForm.content" type="textarea" rows="4" placeholder="请输入弹窗内容" />
            </el-form-item>
            <el-form-item label="内容类型" prop="content_type">
              <el-select v-model="popupForm.content_type" placeholder="请选择内容类型">
                <el-option label="纯文本" value="text" />
                <el-option label="HTML" value="html" />
                <el-option label="图片" value="image" />
                <el-option label="视频" value="video" />
              </el-select>
            </el-form-item>
            <el-form-item label="弹窗类型" prop="popup_type">
              <el-select v-model="popupForm.popup_type" placeholder="请选择弹窗类型">
                <el-option label="模态框" value="modal" />
                <el-option label="提示框" value="toast" />
                <el-option label="横幅" value="banner" />
              </el-select>
            </el-form-item>
            <el-form-item label="弹窗大小" prop="size">
              <el-select v-model="popupForm.size" placeholder="请选择弹窗大小">
                <el-option label="小" value="small" />
                <el-option label="中" value="medium" />
                <el-option label="大" value="large" />
                <el-option label="全屏" value="fullscreen" />
              </el-select>
            </el-form-item>
            <el-form-item label="优先级" prop="priority">
              <el-input-number v-model="popupForm.priority" :min="0" :max="999" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="触发条件" name="trigger">
            <el-form-item label="触发类型" prop="trigger_type">
              <el-select v-model="popupForm.trigger_type" placeholder="请选择触发类型">
                <el-option label="立即显示" value="immediate" />
                <el-option label="延时显示" value="delay" />
                <el-option label="滚动触发" value="scroll" />
                <el-option label="退出意图" value="exit" />
              </el-select>
            </el-form-item>
            <el-form-item label="触发条件值" prop="trigger_value">
              <el-input v-model="popupForm.trigger_value" placeholder="如延时秒数、滚动百分比等" />
            </el-form-item>
            <el-form-item label="显示频率" prop="display_frequency">
              <el-select v-model="popupForm.display_frequency" placeholder="请选择显示频率">
                <el-option label="每次都显示" value="always" />
                <el-option label="仅显示一次" value="once" />
                <el-option label="每日一次" value="daily" />
                <el-option label="每周一次" value="weekly" />
              </el-select>
            </el-form-item>
            <el-form-item label="每用户最大展示次数">
              <el-input-number v-model="popupForm.max_displays_per_user" :min="0" />
              <div class="form-tip">0表示无限制</div>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="用户定向" name="targeting">
            <el-form-item label="目标用户类型">
              <el-checkbox-group v-model="popupForm.target_user_types">
                <el-checkbox label="student">学生</el-checkbox>
                <el-checkbox label="teacher">教师</el-checkbox>
                <el-checkbox label="parent">家长</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="用户特征">
              <el-checkbox v-model="popupForm.target_new_users">新用户</el-checkbox>
              <el-checkbox v-model="popupForm.target_active_users">活跃用户</el-checkbox>
            </el-form-item>
            <el-form-item label="目标用户等级">
              <el-checkbox-group v-model="popupForm.target_user_levels">
                <el-checkbox :label="1">等级1</el-checkbox>
                <el-checkbox :label="2">等级2</el-checkbox>
                <el-checkbox :label="3">等级3</el-checkbox>
                <el-checkbox :label="4">等级4</el-checkbox>
                <el-checkbox :label="5">等级5</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="时间设置" name="timing">
            <el-form-item label="展示时间段">
              <el-date-picker
                v-model="displayTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="每日展示时间">
              <el-time-picker
                v-model="dailyTimeRange"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="savePopup" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 弹窗预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="弹窗预览"
      width="600px"
    >
      <div class="popup-preview" v-if="previewPopup">
        <div class="preview-popup" :class="previewPopup.popup_type">
          <div class="popup-header">
            <h3>{{ previewPopup.title }}</h3>
            <el-button text class="close-btn">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="popup-body">
            <p>{{ previewPopup.content }}</p>
          </div>
          <div class="popup-footer" v-if="previewPopup.action_type !== 'none'">
            <el-button type="primary">{{ previewPopup.action_text || '确定' }}</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Edit, Delete, Switch, TrendCharts, View, Mouse, Close, ArrowUp
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('list')
const formActiveTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const selectedPopups = ref([])
const showCreateDialog = ref(false)
const showPreviewDialog = ref(false)
const editingPopup = ref(null)
const previewPopupData = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('')
const typeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 弹窗列表
const popups = ref([
  {
    id: '1',
    title: '新用户注册优惠',
    content: '新用户注册即可获得专属优惠券，快来体验吧！',
    content_type: 'text',
    popup_type: 'modal',
    size: 'medium',
    trigger_type: 'immediate',
    display_frequency: 'once',
    target_new_users: true,
    target_active_users: false,
    target_user_types: ['student'],
    target_user_levels: [1, 2],
    status: 'active',
    priority: 10,
    max_displays_per_user: 1,
    total_displays: 1234,
    total_clicks: 456,
    total_closes: 678,
    click_rate: 0.37,
    close_rate: 0.55,
    start_time: '2024-01-01 00:00:00',
    end_time: '2024-12-31 23:59:59',
    daily_start_hour: 9,
    daily_end_hour: 21
  }
])

// 表单数据
const popupForm = reactive({
  title: '',
  content: '',
  content_type: 'text',
  popup_type: 'modal',
  size: 'medium',
  trigger_type: 'immediate',
  trigger_value: '',
  display_frequency: 'always',
  target_user_types: [],
  target_user_levels: [],
  target_new_users: false,
  target_active_users: false,
  priority: 0,
  max_displays_per_user: 0
})

const displayTimeRange = ref([])
const dailyTimeRange = ref([])

// 表单验证规则
const popupRules = {
  title: [
    { required: true, message: '请输入弹窗标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入弹窗内容', trigger: 'blur' }
  ],
  content_type: [
    { required: true, message: '请选择内容类型', trigger: 'change' }
  ],
  popup_type: [
    { required: true, message: '请选择弹窗类型', trigger: 'change' }
  ]
}

// 方法
const loadPopups = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取弹窗列表
    console.log('Loading popups...')
  } catch (error) {
    ElMessage.error('加载弹窗列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedPopups.value = selection
}

const getPopupTypeName = (type) => {
  const types = {
    modal: '模态框',
    toast: '提示框',
    banner: '横幅'
  }
  return types[type] || type
}

const getTriggerTypeName = (type) => {
  const types = {
    immediate: '立即',
    delay: '延时',
    scroll: '滚动',
    exit: '退出'
  }
  return types[type] || type
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    active: '已发布',
    paused: '已暂停',
    expired: '已过期'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: '',
    active: 'success',
    paused: 'warning',
    expired: 'danger'
  }
  return types[status] || ''
}

const getPriorityClass = (priority) => {
  if (priority >= 50) return 'high'
  if (priority >= 20) return 'medium'
  return 'low'
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const editPopup = (popup) => {
  editingPopup.value = popup
  Object.assign(popupForm, popup)
  if (popup.start_time && popup.end_time) {
    displayTimeRange.value = [popup.start_time, popup.end_time]
  }
  if (popup.daily_start_hour !== null && popup.daily_end_hour !== null) {
    dailyTimeRange.value = [`${popup.daily_start_hour}:00`, `${popup.daily_end_hour}:00`]
  }
  showCreateDialog.value = true
}

const resetForm = () => {
  editingPopup.value = null
  Object.assign(popupForm, {
    title: '',
    content: '',
    content_type: 'text',
    popup_type: 'modal',
    size: 'medium',
    trigger_type: 'immediate',
    trigger_value: '',
    display_frequency: 'always',
    target_user_types: [],
    target_user_levels: [],
    target_new_users: false,
    target_active_users: false,
    priority: 0,
    max_displays_per_user: 0
  })
  displayTimeRange.value = []
  dailyTimeRange.value = []
  formActiveTab.value = 'basic'
}

const savePopup = async () => {
  // TODO: 实现保存逻辑
  console.log('Saving popup...', popupForm)
  showCreateDialog.value = false
  ElMessage.success(editingPopup.value ? '弹窗更新成功' : '弹窗创建成功')
}

const togglePopupStatus = async (popup) => {
  // TODO: 实现状态切换逻辑
  console.log('Toggling popup status...', popup)
}

const deletePopup = async (popup) => {
  try {
    await ElMessageBox.confirm('确定要删除这个弹窗吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting popup...', popup)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const previewPopup = (popup) => {
  previewPopupData.value = popup
  showPreviewDialog.value = true
}

const batchActivate = async () => {
  // TODO: 实现批量启用逻辑
  console.log('Batch activating popups...', selectedPopups.value)
}

const batchPause = async () => {
  // TODO: 实现批量暂停逻辑
  console.log('Batch pausing popups...', selectedPopups.value)
}

const viewStats = (popup) => {
  // TODO: 实现查看统计逻辑
  console.log('Viewing popup stats...', popup)
}

// 工具函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

onMounted(() => {
  loadPopups()
})
</script>

<style scoped>
.popups-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.popup-info {
  padding: 8px 0;
}

.popup-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
}

.popup-content {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.popup-meta {
  display: flex;
  gap: 6px;
}

.priority {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.priority.high {
  background-color: #fef0f0;
  color: #f56c6c;
}

.priority.medium {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.priority.low {
  background-color: #f0f9ff;
  color: #409eff;
}

.target-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.target-tag {
  font-size: 11px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
  width: fit-content;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.click-rate {
  font-weight: 600;
  color: #67c23a;
}

.time-info {
  font-size: 12px;
  color: #606266;
}

.time-info div {
  margin-bottom: 2px;
}

.no-time {
  font-size: 12px;
  color: #c0c4cc;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  padding: 20px 0;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.popup-preview {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.preview-popup {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  margin: 0 auto;
}

.preview-popup.modal {
  border: 1px solid #ebeef5;
}

.preview-popup.toast {
  border-left: 4px solid #409eff;
}

.preview-popup.banner {
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.close-btn {
  padding: 0;
  color: #909399;
}

.popup-body {
  padding: 20px;
}

.popup-body p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.popup-footer {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>