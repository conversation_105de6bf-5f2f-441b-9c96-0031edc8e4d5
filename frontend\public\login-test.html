<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WisCude 登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .login-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .login-button:hover {
            background: #0056b3;
        }
        .login-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🔐 WisCude 登录测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong><br>
            • 前端服务器：http://localhost:5173<br>
            • 后端服务器：http://localhost:8000<br>
            • 默认账户：admin / admin123<br>
            • 测试登录API和前端路由功能
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名：</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码：</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit" class="login-button" id="loginButton">
                登录测试
            </button>
        </form>

        <div class="test-buttons">
            <button class="test-button" onclick="testBackendHealth()">测试后端健康</button>
            <button class="test-button" onclick="testFrontendAPI()">测试前端代理</button>
            <button class="test-button" onclick="openMainApp()">打开主应用</button>
            <button class="test-button" onclick="testDirectLogin()">直接登录测试</button>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const resultDiv = document.getElementById('result');
        const loginButton = document.getElementById('loginButton');
        const loginForm = document.getElementById('loginForm');

        function showResult(message, isSuccess = false) {
            resultDiv.textContent = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
        }

        function setLoading(loading) {
            loginButton.disabled = loading;
            loginButton.textContent = loading ? '登录中...' : '登录测试';
        }

        // 登录表单提交
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            setLoading(true);
            
            try {
                // 测试前端代理的登录API
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`登录成功！\n\n响应数据：\n${JSON.stringify(data, null, 2)}`, true);
                    
                    // 保存令牌
                    if (data.access_token) {
                        localStorage.setItem('access_token', data.access_token);
                        localStorage.setItem('refresh_token', data.refresh_token || data.access_token);
                    }
                    
                    // 3秒后跳转到主应用
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                } else {
                    showResult(`登录失败！\n\n错误信息：\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                showResult(`网络错误：\n${error.message}`);
            } finally {
                setLoading(false);
            }
        });

        // 测试后端健康状态
        async function testBackendHealth() {
            try {
                const response = await fetch('http://localhost:8000/api/health');
                const data = await response.json();
                showResult(`后端健康检查成功！\n\n响应数据：\n${JSON.stringify(data, null, 2)}`, true);
            } catch (error) {
                showResult(`后端健康检查失败：\n${error.message}`);
            }
        }

        // 测试前端API代理
        async function testFrontendAPI() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                showResult(`前端API代理成功！\n\n响应数据：\n${JSON.stringify(data, null, 2)}`, true);
            } catch (error) {
                showResult(`前端API代理失败：\n${error.message}`);
            }
        }

        // 打开主应用
        function openMainApp() {
            window.open('/', '_blank');
        }

        // 直接登录测试（绕过前端代理）
        async function testDirectLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:8000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`直接登录成功！\n\n响应数据：\n${JSON.stringify(data, null, 2)}`, true);
                } else {
                    showResult(`直接登录失败！\n\n错误信息：\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                showResult(`直接登录网络错误：\n${error.message}`);
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('登录测试页面已加载完成');
            console.log('前端服务器：http://localhost:5173');
            console.log('后端服务器：http://localhost:8000');
        });
    </script>
</body>
</html>
