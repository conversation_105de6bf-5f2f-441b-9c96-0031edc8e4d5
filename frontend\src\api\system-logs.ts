/**
 * 系统日志管理API
 */
import request from './request'

// 日志级别枚举
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

// 日志模块枚举
export enum LogModule {
  SYSTEM = 'SYSTEM',
  USER = 'USER',
  AUTH = 'AUTH',
  API = 'API',
  DATABASE = 'DATABASE',
  CACHE = 'CACHE',
  EMAIL = 'EMAIL',
  FILE = 'FILE',
  SYNC = 'SYNC'
}

// 系统日志接口
export interface SystemLog {
  id: string
  user_id?: string
  username?: string
  action: string
  module: string
  level: string
  message: string
  details?: string
  ip_address?: string
  created_at: string
  updated_at?: string
}

// 日志查询参数
export interface LogQueryParams {
  page?: number
  page_size?: number
  level?: string
  module?: string
  user_id?: string
  start_date?: string
  end_date?: string
  keyword?: string
}

// 日志统计信息
export interface LogStatistics {
  total_logs: number
  recent_logs_24h: number
  level_distribution: Record<string, number>
  module_distribution: Record<string, number>
}

// 日志配置
export interface LogConfig {
  log_level: string
  log_file_path: string
  log_retention_days: number
  log_max_size_mb: number
  log_rotation_enabled: boolean
  log_format: string
}

// 实时日志数据
export interface RealtimeLog {
  id: string
  timestamp: string
  level: string
  module: string
  message: string
  username?: string
  action: string
  ip_address?: string
}

export const systemLogsApi = {
  /**
   * 获取系统日志列表
   */
  getSystemLogs(params: LogQueryParams = {}): Promise<{
    items: SystemLog[]
    total: number
    page: number
    page_size: number
    pages: number
  }> {
    return request({
      url: '/system/logs',
      method: 'get',
      params
    })
  },

  /**
   * 获取系统日志详情
   */
  getSystemLog(logId: string): Promise<SystemLog> {
    return request({
      url: `/system/logs/${logId}`,
      method: 'get'
    })
  },

  /**
   * 创建系统日志
   */
  createSystemLog(logData: {
    action: string
    module: string
    level?: string
    message: string
    details?: string
    user_id?: string
    username?: string
    ip_address?: string
  }): Promise<{
    id: string
    message: string
    created_at: string
  }> {
    return request({
      url: '/system/logs',
      method: 'post',
      data: logData
    })
  },

  /**
   * 删除系统日志
   */
  deleteSystemLog(logId: string): Promise<{ message: string }> {
    return request({
      url: `/system/logs/${logId}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除系统日志
   */
  batchDeleteSystemLogs(logIds: string[]): Promise<{
    message: string
    deleted_count: number
  }> {
    return request({
      url: '/system/logs/batch-delete',
      method: 'post',
      data: logIds
    })
  },

  /**
   * 清理过期日志
   */
  cleanupSystemLogs(days: number = 30, level?: string): Promise<{
    message: string
    deleted_count: number
    cutoff_date: string
  }> {
    return request({
      url: '/system/logs/cleanup',
      method: 'post',
      data: { days, level }
    })
  },

  /**
   * 获取日志模块列表
   */
  getLogModules(): Promise<{ modules: string[] }> {
    return request({
      url: '/system/logs/modules',
      method: 'get'
    })
  },

  /**
   * 获取日志统计信息
   */
  getLogStatistics(): Promise<LogStatistics> {
    return request({
      url: '/system/logs/statistics',
      method: 'get'
    })
  },

  /**
   * 获取实时日志
   */
  getRealtimeLogs(params: {
    level?: string
    module?: string
    limit?: number
  } = {}): Promise<{
    logs: RealtimeLog[]
    count: number
    timestamp: string
  }> {
    return request({
      url: '/system/logs/realtime',
      method: 'get',
      params
    })
  },

  /**
   * 获取日志配置
   */
  getLogConfig(): Promise<LogConfig> {
    return request({
      url: '/system/logs/config',
      method: 'get'
    })
  },

  /**
   * 更新日志配置
   */
  updateLogConfig(config: Partial<LogConfig>): Promise<{
    message: string
    config: LogConfig
  }> {
    return request({
      url: '/system/logs/config',
      method: 'put',
      data: config
    })
  }
}

// WebSocket连接管理
export class LogWebSocketManager {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private listeners: Map<string, Function[]> = new Map()

  constructor(private token: string) {}

  connect(filters: { level?: string; module?: string } = {}) {
    const wsUrl = `${import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000'}/api/ws/logs`
    const params = new URLSearchParams({
      token: this.token,
      ...filters
    })

    this.ws = new WebSocket(`${wsUrl}?${params}`)

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.reconnectAttempts = 0
      this.emit('connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.emit('message', data)
        
        if (data.type === 'log_message') {
          this.emit('log', data.data)
        } else if (data.type === 'heartbeat') {
          this.emit('heartbeat', data)
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      this.emit('disconnected')
      this.attemptReconnect()
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      this.emit('error', error)
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  updateFilters(filters: { level?: string; module?: string }) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'update_filters',
        filters
      }))
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      setTimeout(() => {
        console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
        this.connect()
      }, this.reconnectInterval)
    }
  }

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  off(event: string, callback: Function) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  private emit(event: string, data?: any) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(data))
    }
  }
}
