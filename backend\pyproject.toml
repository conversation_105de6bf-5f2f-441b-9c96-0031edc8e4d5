[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "wiscude-admin"
version = "1.0.0"
description = "WisCude 后台管理系统"
authors = [
    {name = "WisCude Team", email = "<EMAIL>"}
]
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    # FastAPI 框架
    "fastapi==0.109.0",
    "uvicorn[standard]==0.25.0",
    "pydantic==2.5.3",
    "pydantic-settings==2.1.0",
    "python-multipart==0.0.6",
    "email-validator==2.1.0",

    # 数据库
    "sqlalchemy==2.0.25",
    "psycopg2-binary==2.9.9",
    "alembic==1.13.1",
    "asyncpg==0.29.0",
    "aiosqlite==0.19.0",

    # 身份认证
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",

    # 数据处理
    "pandas==2.1.4",
    "numpy==1.26.2",
    "openpyxl==3.1.2",
    "python-dateutil==2.8.2",

    # 异步任务
    "celery==5.3.6",
    "redis==5.0.1",
    "flower==2.0.1",

    # 日志和监控
    "loguru==0.7.2",
    "prometheus-fastapi-instrumentator==6.1.0",

    # 工具
    "python-dotenv==1.0.0",
    "tenacity==8.2.3",
    "orjson==3.9.10",
    "ujson==5.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest==7.4.3",
    "pytest-asyncio==0.23.2",
    "pytest-cov>=4.1.0",
    "httpx==0.26.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.4.0",
]
test = [
    "pytest==7.4.3",
    "pytest-asyncio==0.23.2",
    "pytest-cov>=4.1.0",
    "httpx==0.26.0",
]
production = [
    # 生产环境额外依赖
    "gunicorn>=21.2.0",
    "psutil>=5.9.0",
]

[project.urls]
Homepage = "https://github.com/wiscude/admin"
Repository = "https://github.com/wiscude/admin.git"
Issues = "https://github.com/wiscude/admin/issues"

# Setuptools 配置
[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]
exclude = ["logs*", "alembic*", "uploads*", "tests*", "migrations*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.sql"]

# Black 配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

# isort 配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app", "tests"]
known_third_party = ["fastapi", "sqlalchemy", "pydantic", "pytest"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy 配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "passlib.*",
    "jose.*",
    "sqlalchemy.*",
    "alembic.*",
    "psycopg2.*",
]
ignore_missing_imports = true

# Pytest 配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage 配置
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "*/__pycache__/*",
    "*/site-packages/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# Bandit 安全检查配置
[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]

# Flake8 配置（在 setup.cfg 或 .flake8 文件中）
# 这里提供参考配置
# [flake8]
# max-line-length = 88
# extend-ignore = E203, E266, E501, W503, F403, F401
# max-complexity = 18
# select = B,C,E,F,W,T4,B9
# exclude = 
#     .git,
#     __pycache__,
#     .venv,
#     .eggs,
#     *.egg,
#     build,
#     dist,
#     migrations

# Pre-commit hooks 配置
# 在 .pre-commit-config.yaml 文件中配置
