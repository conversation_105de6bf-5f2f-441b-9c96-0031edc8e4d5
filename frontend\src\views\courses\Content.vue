<template>
  <div class="course-content-management">
    <div class="page-header">
      <h1>课程内容管理</h1>
      <p>管理课程视频、章节内容和学习资料</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增课程
        </el-button>
        <el-button 
          type="success" 
          :disabled="selectedCourses.length === 0"
          @click="batchPublish"
        >
          批量发布
        </el-button>
        <el-button 
          type="warning" 
          :disabled="selectedCourses.length === 0"
          @click="batchArchive"
        >
          批量归档
        </el-button>
        <el-button @click="exportCourses">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索课程名称"
          style="width: 200px"
          clearable
          @change="loadCourses"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="categoryFilter" placeholder="分类筛选" style="width: 120px" @change="loadCourses">
          <el-option label="全部分类" value="" />
          <el-option label="前端开发" value="frontend" />
          <el-option label="后端开发" value="backend" />
          <el-option label="移动开发" value="mobile" />
          <el-option label="数据科学" value="data" />
          <el-option label="人工智能" value="ai" />
        </el-select>
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 100px" @change="loadCourses">
          <el-option label="全部状态" value="" />
          <el-option label="草稿" value="draft" />
          <el-option label="已发布" value="published" />
          <el-option label="已归档" value="archived" />
        </el-select>
      </div>
    </div>

    <!-- 课程列表 -->
    <el-table
      :data="courses"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="课程信息" min-width="350">
        <template #default="{ row }">
          <div class="course-info">
            <div class="course-cover">
              <img v-if="row.cover" :src="row.cover" :alt="row.title" />
              <div v-else class="cover-placeholder">
                <el-icon><VideoPlay /></el-icon>
              </div>
            </div>
            <div class="course-details">
              <div class="course-title">{{ row.title }}</div>
              <div class="course-description">{{ row.description }}</div>
              <div class="course-meta">
                <el-tag size="small" :type="getCategoryTagType(row.category)">
                  {{ getCategoryName(row.category) }}
                </el-tag>
                <span class="course-duration">{{ row.duration }}小时</span>
                <span class="course-level">{{ getLevelName(row.level) }}</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="讲师" width="120">
        <template #default="{ row }">
          <div class="instructor-info">
            <div class="instructor-avatar">
              <img v-if="row.instructor_avatar" :src="row.instructor_avatar" :alt="row.instructor" />
              <div v-else class="avatar-placeholder">{{ row.instructor.charAt(0) }}</div>
            </div>
            <div class="instructor-name">{{ row.instructor }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="学习统计" width="150">
        <template #default="{ row }">
          <div class="learning-stats">
            <div class="stat-item">
              <span class="stat-label">学生:</span>
              <span class="stat-value">{{ row.students }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">完成:</span>
              <span class="stat-value completed">{{ row.completed }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">评分:</span>
              <span class="stat-value rating">{{ row.rating }}分</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="章节" width="100">
        <template #default="{ row }">
          <div class="chapter-info">
            <div class="chapter-count">{{ row.chapters }}章节</div>
            <div class="video-count">{{ row.videos }}视频</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusName(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="120">
        <template #default="{ row }">
          <span class="create-time">{{ formatTime(row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="viewCourse(row)">
            <el-icon><View /></el-icon>
            查看
          </el-button>
          <el-button type="text" size="small" @click="editCourse(row)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button type="text" size="small" @click="manageChapters(row)">
            <el-icon><Menu /></el-icon>
            章节
          </el-button>
          <el-button type="text" size="small" @click="viewAnalytics(row)">
            <el-icon><DataAnalysis /></el-icon>
            分析
          </el-button>
          <el-button 
            type="text" 
            size="small" 
            :class="row.status === 'published' ? 'warning' : 'success'"
            @click="toggleStatus(row)"
          >
            <el-icon><Switch /></el-icon>
            {{ row.status === 'published' ? '下架' : '发布' }}
          </el-button>
          <el-button type="text" size="small" class="danger" @click="deleteCourse(row)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadCourses"
        @current-change="loadCourses"
      />
    </div>

    <!-- 创建/编辑课程对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingCourse ? '编辑课程' : '新增课程'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="courseForm" :rules="courseRules" ref="courseFormRef" label-width="100px">
        <el-form-item label="课程标题" prop="title">
          <el-input v-model="courseForm.title" placeholder="请输入课程标题" />
        </el-form-item>
        <el-form-item label="课程描述" prop="description">
          <el-input v-model="courseForm.description" type="textarea" rows="3" placeholder="请输入课程描述" />
        </el-form-item>
        <el-form-item label="课程分类" prop="category">
          <el-select v-model="courseForm.category" placeholder="请选择课程分类">
            <el-option label="前端开发" value="frontend" />
            <el-option label="后端开发" value="backend" />
            <el-option label="移动开发" value="mobile" />
            <el-option label="数据科学" value="data" />
            <el-option label="人工智能" value="ai" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级" prop="level">
          <el-select v-model="courseForm.level" placeholder="请选择难度等级">
            <el-option label="初级" value="beginner" />
            <el-option label="中级" value="intermediate" />
            <el-option label="高级" value="advanced" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程时长" prop="duration">
          <el-input-number v-model="courseForm.duration" :min="1" :max="200" placeholder="小时" />
        </el-form-item>
        <el-form-item label="授课讲师" prop="instructor">
          <el-select v-model="courseForm.instructor" placeholder="请选择讲师">
            <el-option label="张老师" value="张老师" />
            <el-option label="李老师" value="李老师" />
            <el-option label="王老师" value="王老师" />
            <el-option label="赵老师" value="赵老师" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程价格" prop="price">
          <el-input-number v-model="courseForm.price" :min="0" :precision="2" placeholder="元" />
        </el-form-item>
        <el-form-item label="课程封面">
          <el-upload
            class="cover-uploader"
            :show-file-list="false"
            :before-upload="beforeCoverUpload"
            :http-request="uploadCover"
            accept="image/*"
          >
            <img v-if="courseForm.cover" :src="courseForm.cover" class="cover-preview" />
            <div v-else class="cover-upload-placeholder">
              <el-icon><Plus /></el-icon>
              <div class="upload-text">上传封面</div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="课程标签">
          <el-select v-model="courseForm.tags" multiple placeholder="请选择课程标签">
            <el-option label="热门" value="hot" />
            <el-option label="推荐" value="recommended" />
            <el-option label="新课" value="new" />
            <el-option label="实战" value="practical" />
            <el-option label="基础" value="basic" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程介绍" prop="content">
          <el-input v-model="courseForm.content" type="textarea" rows="5" placeholder="请输入详细的课程介绍" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
        <el-button type="primary" @click="saveCourse" :loading="saving">保存并发布</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Download, VideoPlay, View, Edit, Menu, DataAnalysis, Switch, Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const selectedCourses = ref([])
const showCreateDialog = ref(false)
const editingCourse = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const categoryFilter = ref('')
const statusFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 课程列表
const courses = ref([
  {
    id: '1',
    title: 'Vue.js 3.0 完整教程',
    description: '从零开始学习Vue.js 3.0，包含组合式API、响应式系统等核心概念',
    category: 'frontend',
    level: 'intermediate',
    duration: 25,
    instructor: '张老师',
    instructor_avatar: '',
    students: 234,
    completed: 189,
    rating: 4.8,
    chapters: 12,
    videos: 45,
    status: 'published',
    cover: '',
    created_at: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    title: 'Python 数据分析实战',
    description: '使用Python进行数据分析，包含pandas、numpy、matplotlib等库的使用',
    category: 'data',
    level: 'beginner',
    duration: 18,
    instructor: '李老师',
    instructor_avatar: '',
    students: 189,
    completed: 156,
    rating: 4.6,
    chapters: 8,
    videos: 32,
    status: 'published',
    cover: '',
    created_at: '2024-01-12 14:20:00'
  }
])

// 表单数据
const courseForm = reactive({
  title: '',
  description: '',
  category: '',
  level: 'beginner',
  duration: 10,
  instructor: '',
  price: 0,
  cover: '',
  tags: [],
  content: ''
})

// 表单验证规则
const courseRules = {
  title: [
    { required: true, message: '请输入课程标题', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择课程分类', trigger: 'change' }
  ],
  instructor: [
    { required: true, message: '请选择授课讲师', trigger: 'change' }
  ]
}

// 方法
const loadCourses = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取课程列表
    console.log('Loading courses...')
  } catch (error) {
    ElMessage.error('加载课程列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedCourses.value = selection
}

const getCategoryName = (category) => {
  const categories = {
    frontend: '前端开发',
    backend: '后端开发',
    mobile: '移动开发',
    data: '数据科学',
    ai: '人工智能'
  }
  return categories[category] || category
}

const getCategoryTagType = (category) => {
  const types = {
    frontend: 'primary',
    backend: 'success',
    mobile: 'warning',
    data: 'info',
    ai: 'danger'
  }
  return types[category] || ''
}

const getLevelName = (level) => {
  const levels = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级'
  }
  return levels[level] || level
}

const getStatusName = (status) => {
  const statuses = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return types[status] || ''
}

const formatTime = (time) => {
  return new Date(time).toLocaleDateString()
}

const viewCourse = (course) => {
  // TODO: 查看课程详情
  console.log('Viewing course:', course)
}

const editCourse = (course) => {
  editingCourse.value = course
  Object.assign(courseForm, course)
  showCreateDialog.value = true
}

const manageChapters = (course) => {
  // TODO: 管理课程章节
  console.log('Managing chapters for course:', course)
}

const viewAnalytics = (course) => {
  // TODO: 查看课程分析
  console.log('Viewing analytics for course:', course)
}

const toggleStatus = async (course) => {
  // TODO: 切换课程状态
  console.log('Toggling status for course:', course)
}

const deleteCourse = async (course) => {
  try {
    await ElMessageBox.confirm('确定要删除这个课程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 实现删除逻辑
    console.log('Deleting course:', course)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const resetForm = () => {
  editingCourse.value = null
  Object.assign(courseForm, {
    title: '',
    description: '',
    category: '',
    level: 'beginner',
    duration: 10,
    instructor: '',
    price: 0,
    cover: '',
    tags: [],
    content: ''
  })
}

const beforeCoverUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const uploadCover = async (options) => {
  // TODO: 实现封面上传逻辑
  console.log('Uploading cover...', options.file)
  courseForm.cover = URL.createObjectURL(options.file)
  ElMessage.success('封面上传成功')
}

const saveAsDraft = async () => {
  saving.value = true
  try {
    // TODO: 实现保存为草稿逻辑
    console.log('Saving as draft...', courseForm)
    showCreateDialog.value = false
    ElMessage.success('课程已保存为草稿')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveCourse = async () => {
  saving.value = true
  try {
    // TODO: 实现保存并发布逻辑
    console.log('Saving and publishing course...', courseForm)
    showCreateDialog.value = false
    ElMessage.success(editingCourse.value ? '课程更新成功' : '课程创建并发布成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const batchPublish = async () => {
  // TODO: 实现批量发布逻辑
  console.log('Batch publishing courses...', selectedCourses.value)
}

const batchArchive = async () => {
  // TODO: 实现批量归档逻辑
  console.log('Batch archiving courses...', selectedCourses.value)
}

const exportCourses = async () => {
  // TODO: 实现导出逻辑
  console.log('Exporting courses...')
}

onMounted(() => {
  loadCourses()
})
</script>

<style scoped>
.course-content-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.course-info {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  padding: 8px 0;
}

.course-cover {
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.course-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 20px;
}

.course-details {
  flex: 1;
  min-width: 0;
}

.course-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.course-duration,
.course-level {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.instructor-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.instructor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

.instructor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
}

.instructor-name {
  font-size: 12px;
  color: #303133;
  text-align: center;
}

.learning-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #606266;
  font-weight: 500;
}

.stat-value.completed {
  color: #67c23a !important;
}

.stat-value.rating {
  color: #e6a23c !important;
}

.chapter-info {
  text-align: center;
}

.chapter-count,
.video-count {
  font-size: 12px;
  color: #606266;
  margin-bottom: 2px;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.cover-uploader {
  display: inline-block;
}

.cover-preview {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.cover-upload-placeholder {
  width: 120px;
  height: 80px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cover-upload-placeholder:hover {
  border-color: #409eff;
  color: #409eff;
}

.upload-text {
  font-size: 12px;
  margin-top: 4px;
}

.danger {
  color: #f56c6c;
}

.warning {
  color: #e6a23c;
}

.success {
  color: #67c23a;
}
</style>
