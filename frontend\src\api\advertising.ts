/**
 * 广告管理API接口
 */
import request from './request'

// ==================== 轮播图管理 ====================

/**
 * 获取轮播图列表
 */
export const getBanners = (params: {
  page?: number
  size?: number
  keyword?: string
  status?: string
  position?: string
}) => {
  return request({
    url: '/v1/advertising/banners',
    method: 'get',
    params
  })
}

/**
 * 获取轮播图详情
 */
export const getBanner = (bannerId: string) => {
  return request({
    url: `/api/v1/advertising/banners/${bannerId}`,
    method: 'get'
  })
}

/**
 * 创建轮播图
 */
export const createBanner = (data: {
  title: string
  description?: string
  image_url: string
  link_url?: string
  position: string
  sort_order?: number
  status?: string
  publish_start_time?: string
  publish_end_time?: string
}) => {
  return request({
    url: '/api/v1/advertising/banners',
    method: 'post',
    data
  })
}

/**
 * 更新轮播图
 */
export const updateBanner = (bannerId: string, data: any) => {
  return request({
    url: `/api/v1/advertising/banners/${bannerId}`,
    method: 'put',
    data
  })
}

/**
 * 删除轮播图
 */
export const deleteBanner = (bannerId: string) => {
  return request({
    url: `/api/v1/advertising/banners/${bannerId}`,
    method: 'delete'
  })
}

/**
 * 上传轮播图图片
 */
export const uploadBannerImage = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/v1/advertising/banners/upload-image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 激活轮播图
 */
export const activateBanner = (bannerId: string) => {
  return request({
    url: `/api/v1/advertising/banners/${bannerId}/activate`,
    method: 'post'
  })
}

/**
 * 停用轮播图
 */
export const deactivateBanner = (bannerId: string) => {
  return request({
    url: `/api/v1/advertising/banners/${bannerId}/deactivate`,
    method: 'post'
  })
}

// ==================== 弹窗管理 ====================

/**
 * 获取弹窗列表
 */
export const getPopups = (params: {
  page?: number
  size?: number
  keyword?: string
  status?: string
  popup_type?: string
}) => {
  return request({
    url: '/v1/advertising/popups',
    method: 'get',
    params
  })
}

/**
 * 获取弹窗详情
 */
export const getPopup = (popupId: string) => {
  return request({
    url: `/api/v1/advertising/popups/${popupId}`,
    method: 'get'
  })
}

/**
 * 创建弹窗
 */
export const createPopup = (data: {
  title: string
  content: string
  content_type: string
  popup_type: string
  size?: string
  priority?: number
  trigger_type: string
  trigger_value?: string
  display_frequency: string
  max_displays_per_user?: number
  target_user_types?: string[]
  target_new_users?: boolean
  target_active_users?: boolean
  target_user_levels?: string[]
  start_time?: string
  end_time?: string
  daily_start_hour?: number
  daily_end_hour?: number
  action_type?: string
  action_text?: string
  action_url?: string
  status?: string
}) => {
  return request({
    url: '/api/v1/advertising/popups',
    method: 'post',
    data
  })
}

/**
 * 更新弹窗
 */
export const updatePopup = (popupId: string, data: any) => {
  return request({
    url: `/api/v1/advertising/popups/${popupId}`,
    method: 'put',
    data
  })
}

/**
 * 删除弹窗
 */
export const deletePopup = (popupId: string) => {
  return request({
    url: `/api/v1/advertising/popups/${popupId}`,
    method: 'delete'
  })
}

// ==================== 统计分析 ====================

/**
 * 获取广告管理概览统计
 */
export const getAdvertisingOverview = () => {
  return request({
    url: '/v1/advertising/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取轮播图统计
 */
export const getBannerStatistics = () => {
  return request({
    url: '/v1/advertising/statistics/banners',
    method: 'get'
  })
}

/**
 * 获取弹窗统计
 */
export const getPopupStatistics = () => {
  return request({
    url: '/v1/advertising/statistics/popups',
    method: 'get'
  })
}

// ==================== 数据类型定义 ====================

export interface Banner {
  id: string
  title: string
  description?: string
  image_url: string
  link_url?: string
  position: string
  sort_order: number
  status: string
  publish_start_time?: string
  publish_end_time?: string
  click_count: number
  view_count: number
  created_at: string
  updated_at: string
}

export interface Popup {
  id: string
  title: string
  content: string
  content_type: string
  popup_type: string
  size: string
  priority: number
  trigger_type: string
  trigger_value?: string
  display_frequency: string
  max_displays_per_user: number
  target_user_types: string[]
  target_new_users: boolean
  target_active_users: boolean
  target_user_levels: string[]
  start_time?: string
  end_time?: string
  daily_start_hour?: number
  daily_end_hour?: number
  action_type: string
  action_text?: string
  action_url?: string
  status: string
  display_count: number
  click_count: number
  conversion_count: number
  created_at: string
  updated_at: string
}

export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}

export interface AdvertisingOverviewStats {
  total_banners: number
  active_banners: number
  total_popups: number
  active_popups: number
  total_views: number
  total_clicks: number
  click_rate: number
  conversion_rate: number
}

export interface BannerStats {
  total_count: number
  active_count: number
  total_views: number
  total_clicks: number
  click_rate: number
  top_performing: Banner[]
}

export interface PopupStats {
  total_count: number
  active_count: number
  total_displays: number
  total_clicks: number
  total_conversions: number
  click_rate: number
  conversion_rate: number
  top_performing: Popup[]
}