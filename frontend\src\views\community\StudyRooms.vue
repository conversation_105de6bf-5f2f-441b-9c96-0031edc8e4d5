<template>
  <div class="study-rooms-management">
    <div class="page-header">
      <h1>自习室管理</h1>
      <p>管理虚拟自习室的状态监控、违规处理、配置模板和数据分析</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="自习室状态监控" name="monitor">
        <!-- 实时监控统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><School /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ monitorStats.totalRooms }}</div>
                  <div class="stat-label">总自习室数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ monitorStats.activeRooms }}</div>
                  <div class="stat-label">活跃自习室</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ monitorStats.onlineUsers }}</div>
                  <div class="stat-label">在线用户数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ monitorStats.abnormalRooms }}</div>
                  <div class="stat-label">异常自习室</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 实时自习室列表 -->
        <el-card class="monitor-table-card">
          <template #header>
            <div class="card-header">
              <span>实时自习室监控</span>
              <div class="header-actions">
                <el-button type="primary" @click="refreshMonitor" :loading="monitorLoading">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
                <el-button type="danger" @click="handleBatchClose" :disabled="!selectedRooms.length">
                  <el-icon><Close /></el-icon>
                  批量关闭
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="studyRooms"
            v-loading="monitorLoading"
            @selection-change="handleRoomSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="自习室名称" min-width="200">
              <template #default="{ row }">
                <div class="room-info">
                  <div class="room-name">{{ row.name }}</div>
                  <div class="room-meta">
                    <el-tag :type="getRoomTypeTagType(row.type)" size="small">{{ getRoomTypeName(row.type) }}</el-tag>
                    <span class="creator">创建者：{{ row.creatorName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="currentMembers" label="当前人数" width="120">
              <template #default="{ row }">
                <span :class="getMemberCountClass(row.currentMembers, row.maxMembers)">
                  {{ row.currentMembers }}/{{ row.maxMembers }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="totalStudyTime" label="总专注时长" width="120">
              <template #default="{ row }">
                {{ formatStudyTime(row.totalStudyTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="reportCount" label="举报次数" width="100">
              <template #default="{ row }">
                <el-tag :type="row.reportCount > 0 ? 'danger' : 'success'">
                  {{ row.reportCount }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getRoomStatusTagType(row.status)">
                  {{ getRoomStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewRoomDetails(row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button type="warning" size="small" @click="manageRoom(row)">
                  <el-icon><Setting /></el-icon>
                  管理
                </el-button>
                <el-button type="danger" size="small" @click="closeRoom(row)" v-if="row.status === 'active'">
                  <el-icon><Close /></el-icon>
                  关闭
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="monitorPagination.page"
              v-model:page-size="monitorPagination.size"
              :total="monitorPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleMonitorSizeChange"
              @current-change="handleMonitorPageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="违规行为处理" name="violation">
        <!-- 违规统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.totalReports }}</div>
                  <div class="stat-label">总举报数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><UserFilled /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.bannedUsers }}</div>
                  <div class="stat-label">被处罚用户</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><ChatDotRound /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.mutedUsers }}</div>
                  <div class="stat-label">禁言用户</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ violationStats.resolvedReports }}</div>
                  <div class="stat-label">已处理举报</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 违规举报列表 -->
        <el-card class="violation-table-card">
          <template #header>
            <div class="card-header">
              <span>违规举报处理</span>
              <div class="header-actions">
                <el-select v-model="violationFilter" placeholder="筛选举报类型" clearable>
                  <el-option label="刷屏干扰" value="spam" />
                  <el-option label="发布广告" value="advertisement" />
                  <el-option label="恶意骚扰" value="harassment" />
                  <el-option label="不当言论" value="inappropriate" />
                  <el-option label="其他" value="other" />
                </el-select>
              </div>
            </div>
          </template>

          <el-table
            :data="violationReports"
            v-loading="violationLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="roomName" label="自习室" min-width="150" />
            <el-table-column prop="reportedUser" label="被举报用户" width="120" />
            <el-table-column prop="reporterUser" label="举报人" width="120" />
            <el-table-column prop="violationType" label="违规类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getViolationTypeTagType(row.violationType)">
                  {{ getViolationTypeText(row.violationType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="举报描述" min-width="200">
              <template #default="{ row }">
                <el-tooltip :content="row.description" placement="top">
                  <span class="description-text">{{ row.description }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="reportTime" label="举报时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.reportTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="处理状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getReportStatusTagType(row.status)">
                  {{ getReportStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewReportDetails(row)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
                <el-button type="warning" size="small" @click="muteUser(row)">
                  <el-icon><Mute /></el-icon>
                  禁言
                </el-button>
                <el-button type="danger" size="small" @click="banUser(row)">
                  <el-icon><CircleClose /></el-icon>
                  拉黑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="配置模板管理" name="template">
        <!-- 模板统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ templateStats.totalTemplates }}</div>
                  <div class="stat-label">总模板数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ templateStats.popularTemplates }}</div>
                  <div class="stat-label">热门模板</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ templateStats.usageCount }}</div>
                  <div class="stat-label">总使用次数</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 模板管理 -->
        <el-card class="template-card">
          <template #header>
            <div class="card-header">
              <span>自习室配置模板</span>
              <el-button type="primary" @click="showCreateTemplateDialog = true">
                <el-icon><Plus /></el-icon>
                创建模板
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="template-grid">
            <el-col :span="8" v-for="template in roomTemplates" :key="template.id">
              <el-card class="template-item" :class="{ 'template-popular': template.isPopular }">
                <div class="template-header">
                  <h3>{{ template.name }}</h3>
                  <el-tag v-if="template.isPopular" type="success" size="small">热门</el-tag>
                </div>
                <div class="template-description">{{ template.description }}</div>
                <div class="template-config">
                  <div class="config-item">
                    <span class="config-label">类型：</span>
                    <span class="config-value">{{ getRoomTypeName(template.type) }}</span>
                  </div>
                  <div class="config-item">
                    <span class="config-label">最大人数：</span>
                    <span class="config-value">{{ template.maxMembers }}人</span>
                  </div>
                  <div class="config-item">
                    <span class="config-label">使用次数：</span>
                    <span class="config-value">{{ template.usageCount }}次</span>
                  </div>
                </div>
                <div class="template-actions">
                  <el-button type="primary" size="small" @click="useTemplate(template)">
                    <el-icon><Check /></el-icon>
                    使用模板
                  </el-button>
                  <el-button type="warning" size="small" @click="editTemplate(template)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button type="danger" size="small" @click="deleteTemplate(template)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="数据看板" name="dashboard">
        <!-- 数据概览 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardStats.todayStudyTime }}</div>
                  <div class="stat-label">今日专注时长(小时)</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardStats.weeklyStudyTime }}</div>
                  <div class="stat-label">本周专注时长(小时)</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardStats.avgStudyTime }}</div>
                  <div class="stat-label">用户平均专注时长(分钟)</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardStats.peakHour }}</div>
                  <div class="stat-label">高峰时段</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 图表展示 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>专注时长趋势</span>
              </template>
              <div class="chart-container" ref="studyTrendChartRef"></div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>时段分布</span>
              </template>
              <div class="chart-container" ref="timeDistributionChartRef"></div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 活跃自习室TOP5 -->
        <el-card class="top-rooms-card">
          <template #header>
            <div class="card-header">
              <span>活跃自习室 TOP5</span>
              <el-radio-group v-model="topRoomsFilter" size="small">
                <el-radio-button label="today">今日</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
              </el-radio-group>
            </div>
          </template>

          <el-table
            :data="topActiveRooms"
            stripe
            style="width: 100%"
          >
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <el-tag :type="getRankTagType($index + 1)" size="small">
                  第{{ $index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="自习室名称" min-width="200" />
            <el-table-column prop="totalMembers" label="总成员数" width="120" />
            <el-table-column prop="totalStudyTime" label="总专注时长" width="150">
              <template #default="{ row }">
                {{ formatStudyTime(row.totalStudyTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="avgStudyTime" label="平均专注时长" width="150">
              <template #default="{ row }">
                {{ formatStudyTime(row.avgStudyTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="activeRate" label="活跃度" width="120">
              <template #default="{ row }">
                <el-progress :percentage="row.activeRate" :color="getProgressColor(row.activeRate)" />
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 数据对比分析 -->
        <el-card class="comparison-card">
          <template #header>
            <span>数据对比分析</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="comparison-item">
                <h4>工作日 vs 周末</h4>
                <div class="comparison-data">
                  <div class="data-item">
                    <span class="data-label">工作日平均专注时长：</span>
                    <span class="data-value">{{ comparisonData.weekdayAvg }}分钟</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">周末平均专注时长：</span>
                    <span class="data-value">{{ comparisonData.weekendAvg }}分钟</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">差异：</span>
                    <span class="data-value" :class="comparisonData.weekendAvg > comparisonData.weekdayAvg ? 'text-success' : 'text-danger'">
                      {{ comparisonData.weekendAvg > comparisonData.weekdayAvg ? '+' : '' }}{{ comparisonData.weekendAvg - comparisonData.weekdayAvg }}分钟
                    </span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="comparison-item">
                <h4>上午 vs 下午 vs 晚上</h4>
                <div class="comparison-data">
                  <div class="data-item">
                    <span class="data-label">上午(6-12点)：</span>
                    <span class="data-value">{{ comparisonData.morningUsers }}人</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">下午(12-18点)：</span>
                    <span class="data-value">{{ comparisonData.afternoonUsers }}人</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">晚上(18-24点)：</span>
                    <span class="data-value">{{ comparisonData.eveningUsers }}人</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  School, VideoPlay, User, Timer, Warning, Refresh, Close, View, Setting,
  UserFilled, ChatDotRound, CircleCheck, Document, Star, DataLine, Plus,
  Check, Edit, Delete, TrendCharts, Mute, CircleClose, ArrowUp
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('monitor')
const monitorLoading = ref(false)
const violationLoading = ref(false)
const selectedRooms = ref([])
const showCreateTemplateDialog = ref(false)

// 监控统计数据
const monitorStats = reactive({
  totalRooms: 156,
  activeRooms: 89,
  onlineUsers: 1234,
  abnormalRooms: 3
})

// 违规统计数据
const violationStats = reactive({
  totalReports: 45,
  bannedUsers: 12,
  mutedUsers: 28,
  resolvedReports: 156
})

// 模板统计数据
const templateStats = reactive({
  totalTemplates: 8,
  popularTemplates: 3,
  usageCount: 456
})

// 数据看板统计
const dashboardStats = reactive({
  todayStudyTime: 2456,
  weeklyStudyTime: 18234,
  avgStudyTime: 45,
  peakHour: '20:00-22:00'
})

// 自习室列表
const studyRooms = ref([
  {
    id: 1,
    name: '高考冲刺自习室',
    type: 'public',
    creatorName: '张同学',
    currentMembers: 45,
    maxMembers: 50,
    totalStudyTime: 12345,
    reportCount: 0,
    status: 'active',
    createTime: '2024-01-15 09:00:00'
  },
  {
    id: 2,
    name: '考研数学专题',
    type: 'private',
    creatorName: '李老师',
    currentMembers: 52,
    maxMembers: 50,
    totalStudyTime: 8765,
    reportCount: 2,
    status: 'active',
    createTime: '2024-01-14 14:30:00'
  }
])

// 违规举报列表
const violationReports = ref([
  {
    id: 1,
    roomName: '高考冲刺自习室',
    reportedUser: '违规用户A',
    reporterUser: '举报人B',
    violationType: 'spam',
    description: '频繁发送无关信息，干扰其他用户学习',
    reportTime: '2024-01-15 16:30:00',
    status: 'pending'
  }
])

// 自习室模板
const roomTemplates = ref([
  {
    id: 1,
    name: '公开自习室',
    description: '适合大众学习的开放式自习室',
    type: 'public',
    maxMembers: 100,
    usageCount: 156,
    isPopular: true
  },
  {
    id: 2,
    name: '私密自习室',
    description: '小组学习专用的私密空间',
    type: 'private',
    maxMembers: 20,
    usageCount: 89,
    isPopular: false
  },
  {
    id: 3,
    name: '限时自习室',
    description: '设定学习时间的专注自习室',
    type: 'timed',
    maxMembers: 50,
    usageCount: 234,
    isPopular: true
  }
])

// TOP5活跃自习室
const topActiveRooms = ref([
  {
    name: '高考冲刺自习室',
    totalMembers: 156,
    totalStudyTime: 45678,
    avgStudyTime: 120,
    activeRate: 95
  },
  {
    name: '考研数学专题',
    totalMembers: 89,
    totalStudyTime: 34567,
    avgStudyTime: 105,
    activeRate: 88
  }
])

// 对比数据
const comparisonData = reactive({
  weekdayAvg: 85,
  weekendAvg: 120,
  morningUsers: 456,
  afternoonUsers: 789,
  eveningUsers: 1234
})

// 分页数据
const monitorPagination = reactive({
  page: 1,
  size: 20,
  total: 156
})

// 筛选条件
const violationFilter = ref('')
const topRoomsFilter = ref('today')

// 工具函数
const getRoomTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    public: 'success',
    private: 'warning',
    timed: 'info'
  }
  return typeMap[type] || 'default'
}

const getRoomTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    public: '公开',
    private: '私密',
    timed: '限时'
  }
  return typeMap[type] || type
}

const getMemberCountClass = (current: number, max: number) => {
  const ratio = current / max
  if (ratio >= 1) return 'text-danger'
  if (ratio >= 0.8) return 'text-warning'
  return 'text-success'
}

const formatStudyTime = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins}分钟`
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getRoomStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    closed: 'danger'
  }
  return statusMap[status] || 'default'
}

const getRoomStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    closed: '已关闭'
  }
  return statusMap[status] || status
}

const getViolationTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    spam: 'danger',
    advertisement: 'warning',
    harassment: 'danger',
    inappropriate: 'warning',
    other: 'info'
  }
  return typeMap[type] || 'default'
}

const getViolationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    spam: '刷屏干扰',
    advertisement: '发布广告',
    harassment: '恶意骚扰',
    inappropriate: '不当言论',
    other: '其他'
  }
  return typeMap[type] || type
}

const getReportStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    resolved: 'success',
    rejected: 'info'
  }
  return statusMap[status] || 'default'
}

const getReportStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    resolved: '已处理',
    rejected: '已驳回'
  }
  return statusMap[status] || status
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 事件处理函数
const handleRoomSelectionChange = (selection: any[]) => {
  selectedRooms.value = selection
}

const refreshMonitor = () => {
  monitorLoading.value = true
  setTimeout(() => {
    monitorLoading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const handleBatchClose = () => {
  ElMessageBox.confirm('确定要批量关闭选中的自习室吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量关闭成功')
    // TODO: 调用API
  })
}

const viewRoomDetails = (room: any) => {
  console.log('查看自习室详情:', room)
  // TODO: 实现自习室详情查看
}

const manageRoom = (room: any) => {
  console.log('管理自习室:', room)
  // TODO: 实现自习室管理
}

const closeRoom = (room: any) => {
  ElMessageBox.confirm(`确定要关闭自习室"${room.name}"吗？`, '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('自习室已关闭')
    // TODO: 调用API
  })
}

const viewReportDetails = (report: any) => {
  console.log('查看举报详情:', report)
  // TODO: 实现举报详情查看
}

const muteUser = (report: any) => {
  ElMessageBox.confirm(`确定要禁言用户"${report.reportedUser}"吗？`, '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('用户已被禁言7天')
    // TODO: 调用API
  })
}

const banUser = (report: any) => {
  ElMessageBox.confirm(`确定要永久拉黑用户"${report.reportedUser}"吗？`, '危险操作', {
    type: 'error'
  }).then(() => {
    ElMessage.success('用户已被永久拉黑')
    // TODO: 调用API
  })
}

const useTemplate = (template: any) => {
  ElMessage.success(`已使用模板"${template.name}"创建自习室`)
  // TODO: 调用API
}

const editTemplate = (template: any) => {
  console.log('编辑模板:', template)
  // TODO: 实现模板编辑
}

const deleteTemplate = (template: any) => {
  ElMessageBox.confirm(`确定要删除模板"${template.name}"吗？`, '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('模板已删除')
    // TODO: 调用API
  })
}

const handleMonitorSizeChange = (size: number) => {
  monitorPagination.size = size
  // TODO: 重新加载数据
}

const handleMonitorPageChange = (page: number) => {
  monitorPagination.page = page
  // TODO: 重新加载数据
}

// 工具函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.study-rooms-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.info {
  background-color: #409eff;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.danger {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-info {
  display: flex;
  flex-direction: column;
}

.room-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.room-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.creator {
  color: #909399;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}

.text-warning {
  color: #e6a23c;
  font-weight: 600;
}

.text-success {
  color: #67c23a;
  font-weight: 600;
}

.description-text {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-grid {
  margin-top: 16px;
}

.template-item {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.template-item:hover {
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.template-popular {
  border: 2px solid #67c23a;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.template-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.template-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 16px;
  line-height: 1.4;
}

.template-config {
  margin-bottom: 16px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.config-label {
  color: #909399;
}

.config-value {
  color: #303133;
  font-weight: 500;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.comparison-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.comparison-item h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.comparison-data {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.data-label {
  color: #606266;
}

.data-value {
  font-weight: 500;
  color: #303133;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .study-rooms-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .template-grid .el-col {
    margin-bottom: 10px;
  }
}
</style>
