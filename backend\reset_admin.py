#!/usr/bin/env python3
"""
重置管理员用户脚本
删除现有管理员并重新创建
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def reset_admin():
    """重置管理员用户"""
    try:
        from app.models.admin import AdminUser, UserRole
        from app.core.database import SessionLocal
        
        print("开始重置管理员用户...")
        
        db = SessionLocal()
        try:
            # 删除所有现有管理员用户
            deleted_count = db.query(AdminUser).delete()
            print(f"删除了 {deleted_count} 个现有管理员用户")
            
            # 创建新的管理员用户
            admin = AdminUser(
                username="admin",
                email="<EMAIL>",
                full_name="系统管理员",
                role=UserRole.SUPERADMIN,
                is_superuser=True,
                is_active=True
            )
            admin.set_password("admin123")
            
            db.add(admin)
            db.commit()
            
            print("✅ 新管理员用户创建成功:")
            print(f"  用户名: {admin.username}")
            print(f"  邮箱: {admin.email}")
            print(f"  角色: {admin.role}")
            print(f"  密码: admin123")
            print("\n请在首次登录后修改默认密码!")
            
        except Exception as e:
            print(f"❌ 重置过程中出现错误: {e}")
            db.rollback()
            raise
        finally:
            db.close()
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保在正确的Python环境中运行此脚本")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 重置失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    reset_admin()
