<template>
  <div class="game-circle-management">
    <div class="page-header">
      <h1>游戏圈管理</h1>
      <p>管理学习游戏化的设计、运营、激励和数据分析</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="游戏设计" name="design">
        <!-- 设计统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><GamePad /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ designStats.totalGames }}</div>
                  <div class="stat-label">游戏总数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ designStats.activeGames }}</div>
                  <div class="stat-label">活跃游戏</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Edit /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ designStats.draftGames }}</div>
                  <div class="stat-label">设计中游戏</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ designStats.popularGames }}</div>
                  <div class="stat-label">热门游戏</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 游戏类型管理 -->
        <el-card class="game-types-card">
          <template #header>
            <div class="card-header">
              <span>游戏类型管理</span>
              <el-button type="primary" @click="showGameTypeDialog = true">
                <el-icon><Plus /></el-icon>
                添加类型
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="game-types-grid">
            <el-col :span="8" v-for="gameType in gameTypes" :key="gameType.id">
              <el-card class="game-type-item" :class="{ 'type-active': gameType.isActive }">
                <div class="type-header">
                  <div class="type-icon" :style="{ backgroundColor: gameType.color }">
                    <el-icon><component :is="getTypeIcon(gameType.type)" /></el-icon>
                  </div>
                  <div class="type-info">
                    <h3>{{ gameType.name }}</h3>
                    <div class="type-meta">
                      <span class="game-count">{{ gameType.gameCount }}个游戏</span>
                      <span class="avg-rating">评分{{ gameType.avgRating }}</span>
                    </div>
                  </div>
                </div>
                <div class="type-description">{{ gameType.description }}</div>
                <div class="type-features">
                  <div class="feature-item" v-for="feature in gameType.features" :key="feature">
                    <el-icon><Check /></el-icon>
                    <span>{{ feature }}</span>
                  </div>
                </div>
                <div class="type-stats">
                  <div class="stat-item">
                    <span class="stat-label">参与人数：</span>
                    <span class="stat-value">{{ formatNumber(gameType.totalPlayers) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">平均时长：</span>
                    <span class="stat-value">{{ gameType.avgDuration }}分钟</span>
                  </div>
                </div>
                <div class="type-actions">
                  <el-button type="primary" size="small" @click="viewTypeGames(gameType)">
                    <el-icon><View /></el-icon>
                    查看游戏
                  </el-button>
                  <el-button 
                    :type="gameType.isActive ? 'warning' : 'success'" 
                    size="small" 
                    @click="toggleGameType(gameType)"
                  >
                    <el-icon><Switch /></el-icon>
                    {{ gameType.isActive ? '禁用' : '启用' }}
                  </el-button>
                  <el-button type="info" size="small" @click="editGameType(gameType)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>

        <!-- 游戏设计工作台 -->
        <el-card class="game-design-workspace-card">
          <template #header>
            <div class="card-header">
              <span>游戏设计工作台</span>
              <div class="header-actions">
                <el-select v-model="designFilter" placeholder="筛选状态" clearable>
                  <el-option label="设计中" value="draft" />
                  <el-option label="测试中" value="testing" />
                  <el-option label="已发布" value="published" />
                  <el-option label="已下线" value="offline" />
                </el-select>
                <el-button type="primary" @click="showCreateGameDialog = true">
                  <el-icon><Plus /></el-icon>
                  创建游戏
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="designGames"
            v-loading="designLoading"
            @selection-change="handleGameSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="游戏名称" min-width="200">
              <template #default="{ row }">
                <div class="game-info">
                  <div class="game-name">{{ row.name }}</div>
                  <div class="game-meta">
                    <el-tag :type="getGameTypeTagType(row.typeId)" size="small">
                      {{ getGameTypeName(row.typeId) }}
                    </el-tag>
                    <span class="designer">设计师：{{ row.designerName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="difficulty" label="难度" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.difficulty" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="estimatedDuration" label="预计时长" width="120">
              <template #default="{ row }">
                <span>{{ row.estimatedDuration }}分钟</span>
              </template>
            </el-table-column>
            <el-table-column prop="maxPlayers" label="最大人数" width="100">
              <template #default="{ row }">
                <span>{{ row.maxPlayers }}人</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getGameStatusTagType(row.status)">
                  {{ getGameStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewGameDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="testGame(row)" v-if="row.status === 'draft'">
                  <el-icon><VideoPlay /></el-icon>
                  测试
                </el-button>
                <el-button type="warning" size="small" @click="editGame(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="info" size="small" @click="publishGame(row)" v-if="row.status === 'testing'">
                  <el-icon><Upload /></el-icon>
                  发布
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="designPagination.page"
              v-model:page-size="designPagination.size"
              :total="designPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleDesignSizeChange"
              @current-change="handleDesignPageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="运营管理" name="operation">
        <!-- 运营统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ formatNumber(operationStats.totalPlayers) }}</div>
                  <div class="stat-label">总玩家数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ operationStats.activePlayers }}</div>
                  <div class="stat-label">活跃玩家</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ operationStats.avgPlayTime }}</div>
                  <div class="stat-label">平均游戏时长(分钟)</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ operationStats.retentionRate }}%</div>
                  <div class="stat-label">留存率</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 游戏运营监控 -->
        <el-card class="game-monitoring-card">
          <template #header>
            <div class="card-header">
              <span>游戏运营监控</span>
              <el-radio-group v-model="monitoringView" size="small">
                <el-radio-button label="realtime">实时数据</el-radio-button>
                <el-radio-button label="daily">日报</el-radio-button>
                <el-radio-button label="weekly">周报</el-radio-button>
              </el-radio-group>
            </div>
          </template>

          <el-table :data="gameMonitoring" v-loading="monitoringLoading" stripe style="width: 100%">
            <el-table-column prop="name" label="游戏名称" min-width="150">
              <template #default="{ row }">
                <div class="game-monitoring-info">
                  <div class="game-name">{{ row.name }}</div>
                  <el-tag :type="getGameStatusTagType(row.status)" size="small">
                    {{ getGameStatusText(row.status) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="currentPlayers" label="当前玩家" width="120">
              <template #default="{ row }">
                <span class="current-players">{{ row.currentPlayers }}人</span>
              </template>
            </el-table-column>
            <el-table-column prop="todayPlayers" label="今日玩家" width="120">
              <template #default="{ row }">
                <span class="today-players">{{ row.todayPlayers }}人</span>
              </template>
            </el-table-column>
            <el-table-column prop="avgRating" label="平均评分" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.avgRating" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="completionRate" label="完成率" width="100">
              <template #default="{ row }">
                <span class="completion-rate">{{ row.completionRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="avgDuration" label="平均时长" width="120">
              <template #default="{ row }">
                <span>{{ row.avgDuration }}分钟</span>
              </template>
            </el-table-column>
            <el-table-column prop="issueCount" label="问题反馈" width="100">
              <template #default="{ row }">
                <span :class="getIssueClass(row.issueCount)">{{ row.issueCount }}个</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewGameAnalytics(row)">
                  <el-icon><DataAnalysis /></el-icon>
                  分析
                </el-button>
                <el-button type="info" size="small" @click="viewGameFeedback(row)">
                  <el-icon><ChatDotRound /></el-icon>
                  反馈
                </el-button>
                <el-button
                  :type="row.status === 'published' ? 'warning' : 'success'"
                  size="small"
                  @click="toggleGameStatus(row)"
                >
                  <el-icon><Switch /></el-icon>
                  {{ row.status === 'published' ? '下线' : '上线' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 玩家行为分析 -->
        <el-card class="player-behavior-card">
          <template #header>
            <span>玩家行为分析</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="behavior-chart">
                <h4>游戏时长分布</h4>
                <div class="chart-container" ref="playTimeDistributionChartRef">
                  <div class="chart-mock">游戏时长分布图表</div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="behavior-chart">
                <h4>玩家活跃度趋势</h4>
                <div class="chart-container" ref="playerActivityTrendChartRef">
                  <div class="chart-mock">玩家活跃度趋势图表</div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="8">
              <div class="behavior-metrics">
                <h4>参与度指标</h4>
                <div class="metrics-list">
                  <div class="metric-item">
                    <span class="metric-label">日活跃率：</span>
                    <span class="metric-value">{{ playerBehavior.dailyActiveRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">周活跃率：</span>
                    <span class="metric-value">{{ playerBehavior.weeklyActiveRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">月活跃率：</span>
                    <span class="metric-value">{{ playerBehavior.monthlyActiveRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">平均会话时长：</span>
                    <span class="metric-value">{{ playerBehavior.avgSessionDuration }}分钟</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="behavior-metrics">
                <h4>学习效果指标</h4>
                <div class="metrics-list">
                  <div class="metric-item">
                    <span class="metric-label">知识点掌握率：</span>
                    <span class="metric-value">{{ playerBehavior.knowledgeMastery }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">技能提升率：</span>
                    <span class="metric-value">{{ playerBehavior.skillImprovement }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">学习目标达成率：</span>
                    <span class="metric-value">{{ playerBehavior.goalAchievement }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">错误率下降：</span>
                    <span class="metric-value">{{ playerBehavior.errorReduction }}%</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="behavior-metrics">
                <h4>社交互动指标</h4>
                <div class="metrics-list">
                  <div class="metric-item">
                    <span class="metric-label">多人游戏参与率：</span>
                    <span class="metric-value">{{ playerBehavior.multiplayerRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">好友互动频率：</span>
                    <span class="metric-value">{{ playerBehavior.friendInteraction }}次/周</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">排行榜参与率：</span>
                    <span class="metric-value">{{ playerBehavior.leaderboardRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">成就分享率：</span>
                    <span class="metric-value">{{ playerBehavior.achievementShare }}%</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="数据分析" name="analytics">
        <!-- 分析统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ formatNumber(analyticsStats.totalSessions) }}</div>
                  <div class="stat-label">总游戏会话</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ analyticsStats.learningEfficiency }}%</div>
                  <div class="stat-label">学习效率提升</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Trophy /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ analyticsStats.achievementRate }}%</div>
                  <div class="stat-label">成就达成率</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ analyticsStats.avgSatisfaction }}</div>
                  <div class="stat-label">平均满意度</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 学习效果分析 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>学习效果趋势</span>
              </template>
              <div class="chart-container" ref="learningEffectTrendChartRef">
                <div class="chart-mock">学习效果趋势图表</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>游戏类型效果对比</span>
              </template>
              <div class="chart-container" ref="gameTypeEffectComparisonChartRef">
                <div class="chart-mock">游戏类型效果对比图表</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 热门游戏排行 -->
        <el-card class="popular-games-card">
          <template #header>
            <div class="card-header">
              <span>热门游戏排行</span>
              <el-radio-group v-model="popularGamesPeriod" size="small">
                <el-radio-button label="today">今日</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
              </el-radio-group>
            </div>
          </template>

          <el-table :data="popularGames" stripe style="width: 100%">
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <el-tag :type="getRankTagType($index + 1)" size="small">
                  第{{ $index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="游戏名称" min-width="200">
              <template #default="{ row }">
                <div class="popular-game-info">
                  <div class="game-name">{{ row.name }}</div>
                  <div class="game-meta">
                    <el-tag :type="getGameTypeTagType(row.typeId)" size="small">
                      {{ getGameTypeName(row.typeId) }}
                    </el-tag>
                    <span class="designer">{{ row.designerName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="playerCount" label="玩家数" width="120">
              <template #default="{ row }">
                <span class="player-count">{{ formatNumber(row.playerCount) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="sessionCount" label="游戏次数" width="120">
              <template #default="{ row }">
                <span class="session-count">{{ formatNumber(row.sessionCount) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="avgRating" label="平均评分" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.avgRating" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="learningEffect" label="学习效果" width="120">
              <template #default="{ row }">
                <span class="learning-effect">{{ row.learningEffect }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="retentionRate" label="留存率" width="100">
              <template #default="{ row }">
                <span class="retention-rate">{{ row.retentionRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewGameReport(row)">
                  <el-icon><Document /></el-icon>
                  报告
                </el-button>
                <el-button type="success" size="small" @click="promoteGame(row)">
                  <el-icon><Promotion /></el-icon>
                  推广
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="激励系统" name="incentive">
        <!-- 激励统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Trophy /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ incentiveStats.totalAchievements }}</div>
                  <div class="stat-label">总成就数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Medal /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ incentiveStats.activeAchievers }}</div>
                  <div class="stat-label">活跃成就者</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Coin /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ formatNumber(incentiveStats.totalPoints) }}</div>
                  <div class="stat-label">总积分发放</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Gift /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ incentiveStats.totalRewards }}</div>
                  <div class="stat-label">总奖励发放</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 成就系统管理 -->
        <el-card class="achievement-system-card">
          <template #header>
            <div class="card-header">
              <span>成就系统管理</span>
              <el-button type="primary" @click="showCreateAchievementDialog = true">
                <el-icon><Plus /></el-icon>
                创建成就
              </el-button>
            </div>
          </template>

          <el-table :data="achievements" v-loading="achievementsLoading" stripe style="width: 100%">
            <el-table-column prop="name" label="成就名称" min-width="200">
              <template #default="{ row }">
                <div class="achievement-info">
                  <div class="achievement-icon" :style="{ backgroundColor: row.color }">
                    <el-icon><component :is="getAchievementIcon(row.type)" /></el-icon>
                  </div>
                  <div class="achievement-details">
                    <div class="achievement-name">{{ row.name }}</div>
                    <div class="achievement-description">{{ row.description }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="category" label="分类" width="120">
              <template #default="{ row }">
                <el-tag :type="getCategoryTagType(row.category)" size="small">
                  {{ getCategoryName(row.category) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="difficulty" label="难度" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.difficulty" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="points" label="奖励积分" width="120">
              <template #default="{ row }">
                <span class="points-reward">{{ row.points }}积分</span>
              </template>
            </el-table-column>
            <el-table-column prop="achieverCount" label="获得人数" width="120">
              <template #default="{ row }">
                <span class="achiever-count">{{ row.achieverCount }}人</span>
              </template>
            </el-table-column>
            <el-table-column prop="achievementRate" label="达成率" width="100">
              <template #default="{ row }">
                <span class="achievement-rate">{{ row.achievementRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="isActive" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.isActive ? 'success' : 'info'">
                  {{ row.isActive ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewAchievementDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="warning" size="small" @click="editAchievement(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  :type="row.isActive ? 'danger' : 'success'"
                  size="small"
                  @click="toggleAchievement(row)"
                >
                  <el-icon><Switch /></el-icon>
                  {{ row.isActive ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 排行榜管理 -->
        <el-card class="leaderboard-card">
          <template #header>
            <div class="card-header">
              <span>排行榜</span>
              <el-radio-group v-model="leaderboardType" size="small">
                <el-radio-button label="points">积分榜</el-radio-button>
                <el-radio-button label="achievements">成就榜</el-radio-button>
                <el-radio-button label="learning">学习榜</el-radio-button>
              </el-radio-group>
            </div>
          </template>

          <el-table :data="leaderboard" stripe style="width: 100%">
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <el-tag :type="getRankTagType($index + 1)" size="small">
                  第{{ $index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="玩家信息" min-width="200">
              <template #default="{ row }">
                <div class="player-info">
                  <el-avatar :src="row.avatar" :size="40">{{ row.name.charAt(0) }}</el-avatar>
                  <div class="player-details">
                    <div class="player-name">{{ row.name }}</div>
                    <div class="player-level">
                      <el-tag :type="getLevelTagType(row.level)" size="small">{{ getLevelName(row.level) }}</el-tag>
                      <span class="join-time">{{ formatRelativeTime(row.joinTime) }}加入</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalPoints" label="总积分" width="120">
              <template #default="{ row }">
                <span class="total-points">{{ formatNumber(row.totalPoints) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="achievementCount" label="成就数" width="100">
              <template #default="{ row }">
                <span class="achievement-count">{{ row.achievementCount }}个</span>
              </template>
            </el-table-column>
            <el-table-column prop="gameCount" label="游戏次数" width="120">
              <template #default="{ row }">
                <span class="game-count">{{ formatNumber(row.gameCount) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="learningHours" label="学习时长" width="120">
              <template #default="{ row }">
                <span class="learning-hours">{{ row.learningHours }}小时</span>
              </template>
            </el-table-column>
            <el-table-column prop="favoriteGame" label="最爱游戏" min-width="150" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewPlayerProfile(row)">
                  <el-icon><User /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="rewardPlayer(row)">
                  <el-icon><Gift /></el-icon>
                  奖励
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  GamePad, VideoPlay, Edit, Star, Plus, Check, View, Switch, Upload, User, Timer,
  DataLine, DataAnalysis, ChatDotRound, TrendCharts, Trophy, Medal, Coin, Gift,
  Document, Promotion
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('design')
const designLoading = ref(false)
const monitoringLoading = ref(false)
const achievementsLoading = ref(false)
const selectedGames = ref([])
const showGameTypeDialog = ref(false)
const showCreateGameDialog = ref(false)
const showCreateAchievementDialog = ref(false)

// 设计统计数据
const designStats = reactive({
  totalGames: 45,
  activeGames: 32,
  draftGames: 8,
  popularGames: 12
})

// 运营统计数据
const operationStats = reactive({
  totalPlayers: 15600,
  activePlayers: 3240,
  avgPlayTime: 25,
  retentionRate: 78
})

// 分析统计数据
const analyticsStats = reactive({
  totalSessions: 125000,
  learningEfficiency: 35,
  achievementRate: 68,
  avgSatisfaction: 4.6
})

// 激励统计数据
const incentiveStats = reactive({
  totalAchievements: 156,
  activeAchievers: 890,
  totalPoints: 2340000,
  totalRewards: 1250
})

// 游戏类型
const gameTypes = ref([
  {
    id: 1,
    name: '知识竞答',
    type: 'quiz',
    color: '#409eff',
    description: '通过问答形式巩固学习知识，支持单人和多人模式',
    features: ['实时排行', '题库管理', '难度分级', '成就系统'],
    gameCount: 15,
    avgRating: 4.6,
    totalPlayers: 12500,
    avgDuration: 15,
    isActive: true
  },
  {
    id: 2,
    name: '角色扮演',
    type: 'rpg',
    color: '#67c23a',
    description: '通过角色扮演学习历史、文学等人文知识',
    features: ['剧情模式', '角色成长', '装备系统', '团队协作'],
    gameCount: 8,
    avgRating: 4.8,
    totalPlayers: 8900,
    avgDuration: 45,
    isActive: true
  }
])

// 设计中的游戏
const designGames = ref([
  {
    id: 1,
    name: '数学大冒险',
    typeId: 1,
    designerName: '张老师',
    difficulty: 3,
    estimatedDuration: 20,
    maxPlayers: 30,
    status: 'draft',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '历史穿越记',
    typeId: 2,
    designerName: '李教授',
    difficulty: 4,
    estimatedDuration: 60,
    maxPlayers: 20,
    status: 'testing',
    createTime: '2024-01-10 14:20:00'
  }
])

// 游戏监控数据
const gameMonitoring = ref([
  {
    id: 1,
    name: '数学大冒险',
    status: 'published',
    currentPlayers: 45,
    todayPlayers: 234,
    avgRating: 4.5,
    completionRate: 78,
    avgDuration: 18,
    issueCount: 2
  },
  {
    id: 2,
    name: '英语单词王',
    status: 'published',
    currentPlayers: 67,
    todayPlayers: 189,
    avgRating: 4.7,
    completionRate: 85,
    avgDuration: 12,
    issueCount: 0
  }
])

// 玩家行为数据
const playerBehavior = reactive({
  dailyActiveRate: 25,
  weeklyActiveRate: 45,
  monthlyActiveRate: 68,
  avgSessionDuration: 22,
  knowledgeMastery: 72,
  skillImprovement: 35,
  goalAchievement: 68,
  errorReduction: 28,
  multiplayerRate: 42,
  friendInteraction: 3.5,
  leaderboardRate: 58,
  achievementShare: 35
})

// 热门游戏
const popularGames = ref([
  {
    id: 1,
    name: '数学大冒险',
    typeId: 1,
    designerName: '张老师',
    playerCount: 2340,
    sessionCount: 15600,
    avgRating: 4.8,
    learningEffect: 85,
    retentionRate: 78
  },
  {
    id: 2,
    name: '英语单词王',
    typeId: 1,
    designerName: '李老师',
    playerCount: 1890,
    sessionCount: 12300,
    avgRating: 4.6,
    learningEffect: 82,
    retentionRate: 75
  }
])

// 成就列表
const achievements = ref([
  {
    id: 1,
    name: '数学新手',
    description: '完成第一个数学游戏',
    category: 'beginner',
    type: 'trophy',
    color: '#67c23a',
    difficulty: 1,
    points: 100,
    achieverCount: 1250,
    achievementRate: 85,
    isActive: true
  },
  {
    id: 2,
    name: '学习达人',
    description: '连续7天完成学习任务',
    category: 'persistence',
    type: 'medal',
    color: '#e6a23c',
    difficulty: 3,
    points: 500,
    achieverCount: 340,
    achievementRate: 25,
    isActive: true
  }
])

// 排行榜
const leaderboard = ref([
  {
    id: 1,
    name: '学霸小王',
    avatar: '',
    level: 'gold',
    joinTime: '2023-09-01',
    totalPoints: 15600,
    achievementCount: 25,
    gameCount: 156,
    learningHours: 89,
    favoriteGame: '数学大冒险'
  },
  {
    id: 2,
    name: '学习小能手',
    avatar: '',
    level: 'silver',
    joinTime: '2023-10-15',
    totalPoints: 12300,
    achievementCount: 18,
    gameCount: 134,
    learningHours: 67,
    favoriteGame: '英语单词王'
  }
])

// 分页数据
const designPagination = reactive({
  page: 1,
  size: 20,
  total: 50
})

// 筛选条件
const designFilter = ref('')
const monitoringView = ref('realtime')
const popularGamesPeriod = ref('week')
const leaderboardType = ref('points')

// 工具函数
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatRelativeTime = (date: string) => {
  const now = new Date()
  const target = new Date(date)
  const diffMonths = Math.floor((now.getTime() - target.getTime()) / (1000 * 60 * 60 * 24 * 30))

  if (diffMonths === 0) return '本月'
  if (diffMonths < 12) return `${diffMonths}个月前`
  return `${Math.floor(diffMonths / 12)}年前`
}

const getTypeIcon = (type: string) => {
  return GamePad // 简化处理
}

const getGameTypeTagType = (typeId: number) => {
  return 'primary'
}

const getGameTypeName = (typeId: number) => {
  const gameType = gameTypes.value.find(t => t.id === typeId)
  return gameType?.name || '未知类型'
}

const getGameStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    testing: 'warning',
    published: 'success',
    offline: 'danger'
  }
  return statusMap[status] || 'default'
}

const getGameStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '设计中',
    testing: '测试中',
    published: '已发布',
    offline: '已下线'
  }
  return statusMap[status] || status
}

const getIssueClass = (count: number) => {
  if (count === 0) return 'text-success'
  if (count <= 2) return 'text-warning'
  return 'text-danger'
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

const getAchievementIcon = (type: string) => {
  return Trophy // 简化处理
}

const getCategoryTagType = (category: string) => {
  const categoryMap: Record<string, string> = {
    beginner: 'success',
    persistence: 'warning',
    mastery: 'danger',
    social: 'info'
  }
  return categoryMap[category] || 'default'
}

const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    beginner: '新手',
    persistence: '坚持',
    mastery: '精通',
    social: '社交'
  }
  return categoryMap[category] || category
}

const getLevelTagType = (level: string) => {
  const levelMap: Record<string, string> = {
    gold: 'danger',
    silver: 'warning',
    bronze: 'success'
  }
  return levelMap[level] || 'info'
}

const getLevelName = (level: string) => {
  const levelMap: Record<string, string> = {
    gold: '金牌玩家',
    silver: '银牌玩家',
    bronze: '铜牌玩家'
  }
  return levelMap[level] || level
}

// 事件处理函数
const handleGameSelectionChange = (selection: any[]) => {
  selectedGames.value = selection
}

const viewTypeGames = (gameType: any) => {
  console.log('查看类型游戏:', gameType)
  // TODO: 实现类型游戏查看
}

const toggleGameType = (gameType: any) => {
  gameType.isActive = !gameType.isActive
  ElMessage.success(`游戏类型已${gameType.isActive ? '启用' : '禁用'}`)
  // TODO: 调用API
}

const editGameType = (gameType: any) => {
  console.log('编辑游戏类型:', gameType)
  // TODO: 实现游戏类型编辑
}

const viewGameDetails = (game: any) => {
  console.log('查看游戏详情:', game)
  // TODO: 实现游戏详情查看
}

const testGame = (game: any) => {
  ElMessage.success(`游戏"${game.name}"已进入测试阶段`)
  game.status = 'testing'
  // TODO: 调用API
}

const editGame = (game: any) => {
  console.log('编辑游戏:', game)
  // TODO: 实现游戏编辑
}

const publishGame = (game: any) => {
  ElMessage.success(`游戏"${game.name}"已发布`)
  game.status = 'published'
  // TODO: 调用API
}

const handleDesignSizeChange = (size: number) => {
  designPagination.size = size
  // TODO: 重新加载数据
}

const handleDesignPageChange = (page: number) => {
  designPagination.page = page
  // TODO: 重新加载数据
}

const viewGameAnalytics = (game: any) => {
  console.log('查看游戏分析:', game)
  // TODO: 实现游戏分析查看
}

const viewGameFeedback = (game: any) => {
  console.log('查看游戏反馈:', game)
  // TODO: 实现游戏反馈查看
}

const toggleGameStatus = (game: any) => {
  const newStatus = game.status === 'published' ? 'offline' : 'published'
  game.status = newStatus
  ElMessage.success(`游戏"${game.name}"已${newStatus === 'published' ? '上线' : '下线'}`)
  // TODO: 调用API
}

const viewGameReport = (game: any) => {
  console.log('查看游戏报告:', game)
  // TODO: 实现游戏报告查看
}

const promoteGame = (game: any) => {
  ElMessage.success(`游戏"${game.name}"已加入推广计划`)
  // TODO: 调用API推广游戏
}

const viewAchievementDetails = (achievement: any) => {
  console.log('查看成就详情:', achievement)
  // TODO: 实现成就详情查看
}

const editAchievement = (achievement: any) => {
  console.log('编辑成就:', achievement)
  // TODO: 实现成就编辑
}

const toggleAchievement = (achievement: any) => {
  achievement.isActive = !achievement.isActive
  ElMessage.success(`成就"${achievement.name}"已${achievement.isActive ? '启用' : '禁用'}`)
  // TODO: 调用API
}

const viewPlayerProfile = (player: any) => {
  console.log('查看玩家详情:', player)
  // TODO: 实现玩家详情查看
}

const rewardPlayer = (player: any) => {
  console.log('奖励玩家:', player)
  // TODO: 实现玩家奖励
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.game-circle-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.info {
  background-color: #409eff;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.danger {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.game-types-grid {
  margin-top: 16px;
}

.game-type-item {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.game-type-item:hover {
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.15);
}

.game-type-item.type-active {
  border-left: 4px solid #67c23a;
}

.type-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.type-info {
  flex: 1;
}

.type-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.type-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.game-count {
  color: #409eff;
}

.avg-rating {
  color: #67c23a;
}

.type-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.type-features {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
}

.feature-item .el-icon {
  color: #67c23a;
  font-size: 14px;
}

.type-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

.type-actions {
  display: flex;
  gap: 8px;
}

.game-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.game-name {
  font-weight: 500;
  color: #303133;
}

.game-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.designer {
  color: #606266;
}

.game-monitoring-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.current-players {
  color: #67c23a;
  font-weight: 600;
}

.today-players {
  color: #409eff;
  font-weight: 600;
}

.completion-rate {
  color: #67c23a;
  font-weight: 600;
}

.text-success {
  color: #67c23a;
  font-weight: 600;
}

.text-warning {
  color: #e6a23c;
  font-weight: 600;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}

.behavior-chart {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.behavior-chart h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  background: white;
  border-radius: 4px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-mock {
  color: #909399;
  font-size: 14px;
}

.behavior-metrics {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.behavior-metrics h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.metric-label {
  color: #606266;
}

.metric-value {
  color: #409eff;
  font-weight: 600;
}

.popular-game-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.player-count {
  color: #409eff;
  font-weight: 600;
}

.session-count {
  color: #67c23a;
  font-weight: 600;
}

.learning-effect {
  color: #e6a23c;
  font-weight: 600;
}

.retention-rate {
  color: #f56c6c;
  font-weight: 600;
}

.achievement-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.achievement-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.achievement-details {
  flex: 1;
}

.achievement-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.achievement-description {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.points-reward {
  color: #e6a23c;
  font-weight: 600;
}

.achiever-count {
  color: #409eff;
  font-weight: 600;
}

.achievement-rate {
  color: #67c23a;
  font-weight: 600;
}

.player-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.player-details {
  flex: 1;
}

.player-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.player-level {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.join-time {
  color: #909399;
}

.total-points {
  color: #e6a23c;
  font-weight: 600;
}

.achievement-count {
  color: #67c23a;
  font-weight: 600;
}

.game-count {
  color: #409eff;
  font-weight: 600;
}

.learning-hours {
  color: #f56c6c;
  font-weight: 600;
}

@media (max-width: 768px) {
  .game-circle-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .game-types-grid .el-col {
    margin-bottom: 10px;
  }

  .type-actions {
    flex-direction: column;
    gap: 4px;
  }

  .behavior-chart {
    margin-bottom: 10px;
  }

  .behavior-metrics {
    margin-bottom: 10px;
  }
}
</style>
