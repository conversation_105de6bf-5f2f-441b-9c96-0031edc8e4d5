"""
广告管理模块数据模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from app.core.database import Base

class Banner(Base):
    """轮播图模型"""
    __tablename__ = "banners"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="轮播图标题")
    description = Column(Text, comment="轮播图描述")
    image_url = Column(String(500), nullable=False, comment="图片链接")
    link_url = Column(String(500), comment="跳转链接")
    link_type = Column(String(20), default="external", comment="链接类型: external, internal, none")
    position = Column(String(50), default="home", comment="展示位置: home, course, community")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    status = Column(String(20), default="draft", comment="状态: draft, active, inactive, expired")
    
    # 定时发布
    publish_start_time = Column(DateTime, comment="开始展示时间")
    publish_end_time = Column(DateTime, comment="结束展示时间")
    
    # 统计数据
    view_count = Column(Integer, default=0, comment="展示次数")
    click_count = Column(Integer, default=0, comment="点击次数")
    click_rate = Column(Float, default=0.0, comment="点击率")
    
    # 目标用户
    target_users = Column(JSON, comment="目标用户群体配置")
    target_regions = Column(JSON, comment="目标地区")
    target_devices = Column(JSON, comment="目标设备类型")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class Popup(Base):
    """弹窗广告模型"""
    __tablename__ = "popups"
    
    id = Column(String(36), primary_key=True)
    title = Column(String(200), nullable=False, comment="弹窗标题")
    content = Column(Text, nullable=False, comment="弹窗内容")
    content_type = Column(String(20), default="text", comment="内容类型: text, html, image, video")
    image_url = Column(String(500), comment="弹窗图片")
    video_url = Column(String(500), comment="弹窗视频")
    
    # 跳转设置
    action_type = Column(String(20), default="none", comment="操作类型: none, link, close, download")
    action_url = Column(String(500), comment="操作链接")
    action_text = Column(String(100), comment="操作按钮文本")
    
    # 显示设置
    popup_type = Column(String(20), default="modal", comment="弹窗类型: modal, toast, banner")
    size = Column(String(20), default="medium", comment="弹窗大小: small, medium, large, fullscreen")
    position = Column(String(20), default="center", comment="弹窗位置: center, top, bottom")
    
    # 显示条件
    trigger_type = Column(String(20), default="immediate", comment="触发类型: immediate, delay, scroll, exit")
    trigger_value = Column(String(100), comment="触发条件值")
    display_frequency = Column(String(20), default="always", comment="显示频率: always, once, daily, weekly")
    
    # 用户群体定向
    target_user_types = Column(JSON, comment="目标用户类型")
    target_user_levels = Column(JSON, comment="目标用户等级")
    target_new_users = Column(Boolean, default=False, comment="是否针对新用户")
    target_active_users = Column(Boolean, default=False, comment="是否针对活跃用户")
    
    # 时间控制
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    daily_start_hour = Column(Integer, comment="每日开始小时")
    daily_end_hour = Column(Integer, comment="每日结束小时")
    
    # 状态和统计
    status = Column(String(20), default="draft", comment="状态: draft, active, paused, expired")
    priority = Column(Integer, default=0, comment="优先级")
    max_displays_per_user = Column(Integer, default=0, comment="每用户最大展示次数，0为无限制")
    
    # 统计数据
    total_displays = Column(Integer, default=0, comment="总展示次数")
    total_clicks = Column(Integer, default=0, comment="总点击次数")
    total_closes = Column(Integer, default=0, comment="总关闭次数")
    click_rate = Column(Float, default=0.0, comment="点击率")
    close_rate = Column(Float, default=0.0, comment="关闭率")
    
    # 创建信息
    creator_id = Column(String(36), nullable=False, comment="创建者ID")
    creator_name = Column(String(100), nullable=False, comment="创建者姓名")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


class AdvertisingStatistics(Base):
    """广告统计数据模型"""
    __tablename__ = "advertising_statistics"
    
    id = Column(String(36), primary_key=True)
    ad_id = Column(String(36), nullable=False, comment="广告ID")
    ad_type = Column(String(20), nullable=False, comment="广告类型: banner, popup")
    user_id = Column(String(36), comment="用户ID")
    
    # 行为数据
    action_type = Column(String(20), nullable=False, comment="行为类型: view, click, close, skip")
    action_time = Column(DateTime, default=datetime.utcnow, comment="行为时间")
    
    # 设备和环境信息
    device_type = Column(String(20), comment="设备类型")
    os_version = Column(String(50), comment="操作系统版本")
    app_version = Column(String(50), comment="应用版本")
    screen_resolution = Column(String(20), comment="屏幕分辨率")
    
    # 位置信息
    region = Column(String(100), comment="地区")
    city = Column(String(100), comment="城市")
    
    # 用户画像
    user_type = Column(String(20), comment="用户类型")
    user_level = Column(Integer, comment="用户等级")
    registration_days = Column(Integer, comment="注册天数")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
