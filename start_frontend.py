#!/usr/bin/env python3
"""
WisCude 前端启动脚本
简洁的前端开发服务器启动器
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 50)
    print("🎨 WisCude 前端开发服务器启动器")
    print("=" * 50)
    print("📍 项目路径:", Path.cwd())
    print("🌐 前端服务: http://localhost:5173")
    print("🔧 开发模式: Vite + Vue3")
    print("=" * 50)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查frontend目录
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    print("✓ frontend目录存在")
    
    # 检查package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ frontend/package.json不存在")
        return False
    print("✓ package.json文件存在")
    
    # 检查Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ Node.js版本: {result.stdout.strip()}")
        else:
            print("❌ Node.js未安装或不在PATH中")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False
    
    # 检查npm
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ npm版本: {result.stdout.strip()}")
        else:
            print("❌ npm未安装或不在PATH中")
            return False
    except FileNotFoundError:
        print("❌ npm未安装")
        return False
    
    return True

def install_dependencies():
    """安装前端依赖"""
    print("\n📦 检查前端依赖...")
    
    frontend_dir = Path("frontend")
    node_modules = frontend_dir / "node_modules"
    
    if node_modules.exists():
        print("✓ node_modules已存在")
        return True
    
    print("📦 安装前端依赖...")
    try:
        os.chdir(frontend_dir)
        result = subprocess.run(["npm", "install"], capture_output=True, text=True, shell=True)
        os.chdir("..")
        
        if result.returncode == 0:
            print("✓ 前端依赖安装完成")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 依赖安装过程中出错: {e}")
        os.chdir("..")
        return False

def start_frontend():
    """启动前端开发服务器"""
    print("\n🚀 启动前端开发服务器...")
    
    try:
        # 切换到frontend目录
        os.chdir("frontend")
        
        # 启动开发服务器
        cmd = ["npm", "run", "dev"]
        print(f"执行命令: {' '.join(cmd)}")
        print("\n🌟 前端开发服务器启动中...")
        print("💡 按 Ctrl+C 停止服务")
        print("🔥 支持热重载，修改代码自动刷新")
        print("\n" + "=" * 50)
        
        # 启动服务
        subprocess.run(cmd, shell=True)
        
    except KeyboardInterrupt:
        print("\n\n🛑 开发服务器已停止")
    except FileNotFoundError:
        print("❌ 未找到npm命令")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    finally:
        # 返回项目根目录
        os.chdir("..")

def main():
    """主函数"""
    print_banner()
    
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        print("\n💡 提示:")
        print("  - 确保已安装Node.js: https://nodejs.org/")
        print("  - 确保npm可用: npm --version")
        sys.exit(1)
    
    if not install_dependencies():
        print("\n❌ 依赖安装失败，请手动执行: cd frontend && npm install")
        sys.exit(1)
    
    start_frontend()

if __name__ == "__main__":
    main()