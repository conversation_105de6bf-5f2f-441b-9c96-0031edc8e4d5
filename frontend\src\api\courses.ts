/**
 * 课程管理API接口
 */
import request from './request'

// ==================== 讲师管理 ====================

/**
 * 获取讲师列表
 */
export const getInstructors = (params: {
  page?: number
  size?: number
  keyword?: string
  status?: string
}) => {
  return request({
    url: '/v1/courses/instructors',
    method: 'get',
    params
  })
}

/**
 * 获取讲师详情
 */
export const getInstructorDetail = (instructorId: string) => {
  return request({
    url: `/api/v1/courses/instructors/${instructorId}`,
    method: 'get'
  })
}

/**
 * 创建讲师
 */
export const createInstructor = (data: {
  user_id: string
  name: string
  avatar?: string
  title: string
  bio?: string
  specialties: string[]
  experience_years: number
  education_background: any[]
  certifications: any[]
  contact_info: any
  status?: string
}) => {
  return request({
    url: '/api/v1/courses/instructors',
    method: 'post',
    data
  })
}

/**
 * 更新讲师
 */
export const updateInstructor = (instructorId: string, data: any) => {
  return request({
    url: `/api/v1/courses/instructors/${instructorId}`,
    method: 'put',
    data
  })
}

/**
 * 删除讲师
 */
export const deleteInstructor = (instructorId: string) => {
  return request({
    url: `/api/v1/courses/instructors/${instructorId}`,
    method: 'delete'
  })
}

/**
 * 上传讲师头像
 */
export const uploadInstructorAvatar = (instructorId: string, file: File) => {
  const formData = new FormData()
  formData.append('avatar', file)
  return request({
    url: `/api/v1/courses/instructors/${instructorId}/avatar`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 课程管理 ====================

/**
 * 获取课程列表
 */
export const getCourses = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  instructor_id?: string
  status?: string
  difficulty_level?: string
}) => {
  return request({
    url: '/v1/courses',
    method: 'get',
    params
  })
}

/**
 * 获取课程详情
 */
export const getCourseDetail = (courseId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}`,
    method: 'get'
  })
}

/**
 * 创建课程
 */
export const createCourse = (data: {
  title: string
  description?: string
  cover_image?: string
  category: string
  instructor_id: string
  difficulty_level: string
  duration_hours: number
  price: number
  original_price?: number
  tags?: string[]
  learning_objectives?: string[]
  prerequisites?: string[]
  target_audience?: string
  status?: string
}) => {
  return request({
    url: '/api/v1/courses',
    method: 'post',
    data
  })
}

/**
 * 更新课程
 */
export const updateCourse = (courseId: string, data: any) => {
  return request({
    url: `/api/v1/courses/${courseId}`,
    method: 'put',
    data
  })
}

/**
 * 删除课程
 */
export const deleteCourse = (courseId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}`,
    method: 'delete'
  })
}

/**
 * 上传课程封面
 */
export const uploadCourseCover = (courseId: string, file: File) => {
  const formData = new FormData()
  formData.append('cover', file)
  return request({
    url: `/api/v1/courses/${courseId}/cover`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 发布课程
 */
export const publishCourse = (courseId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/publish`,
    method: 'post'
  })
}

/**
 * 下架课程
 */
export const unpublishCourse = (courseId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/unpublish`,
    method: 'post'
  })
}

// ==================== 章节管理 ====================

/**
 * 获取课程章节列表
 */
export const getCourseChapters = (courseId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters`,
    method: 'get'
  })
}

/**
 * 创建课程章节
 */
export const createCourseChapter = (courseId: string, data: {
  title: string
  description?: string
  sort_order: number
  is_free?: boolean
}) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters`,
    method: 'post',
    data
  })
}

/**
 * 更新课程章节
 */
export const updateCourseChapter = (courseId: string, chapterId: string, data: any) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters/${chapterId}`,
    method: 'put',
    data
  })
}

/**
 * 删除课程章节
 */
export const deleteCourseChapter = (courseId: string, chapterId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters/${chapterId}`,
    method: 'delete'
  })
}

// ==================== 课时管理 ====================

/**
 * 获取章节课时列表
 */
export const getChapterLessons = (courseId: string, chapterId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters/${chapterId}/lessons`,
    method: 'get'
  })
}

/**
 * 创建课时
 */
export const createLesson = (courseId: string, chapterId: string, data: {
  title: string
  description?: string
  lesson_type: string
  content_url?: string
  duration_minutes: number
  sort_order: number
  is_free?: boolean
  resources?: any[]
}) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters/${chapterId}/lessons`,
    method: 'post',
    data
  })
}

/**
 * 更新课时
 */
export const updateLesson = (courseId: string, chapterId: string, lessonId: string, data: any) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters/${chapterId}/lessons/${lessonId}`,
    method: 'put',
    data
  })
}

/**
 * 删除课时
 */
export const deleteLesson = (courseId: string, chapterId: string, lessonId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/chapters/${chapterId}/lessons/${lessonId}`,
    method: 'delete'
  })
}

/**
 * 上传课时视频
 */
export const uploadLessonVideo = (courseId: string, chapterId: string, lessonId: string, file: File) => {
  const formData = new FormData()
  formData.append('video', file)
  return request({
    url: `/api/v1/courses/${courseId}/chapters/${chapterId}/lessons/${lessonId}/video`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 学习进度管理 ====================

/**
 * 获取用户学习进度
 */
export const getUserProgress = (params: {
  course_id?: string
  status?: string
}) => {
  return request({
    url: '/v1/courses/progress',
    method: 'get',
    params
  })
}

/**
 * 获取课程学习进度详情
 */
export const getCourseProgress = (courseId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/progress`,
    method: 'get'
  })
}

/**
 * 更新学习进度
 */
export const updateProgress = (courseId: string, lessonId: string, data: {
  progress_percentage: number
  watch_duration?: number
  is_completed?: boolean
}) => {
  return request({
    url: `/api/v1/courses/${courseId}/lessons/${lessonId}/progress`,
    method: 'post',
    data
  })
}

// ==================== 课程评价管理 ====================

/**
 * 获取课程评价列表
 */
export const getCourseEvaluations = (courseId: string, params: {
  page?: number
  size?: number
  rating?: number
}) => {
  return request({
    url: `/api/v1/courses/${courseId}/evaluations`,
    method: 'get',
    params
  })
}

/**
 * 创建课程评价
 */
export const createCourseEvaluation = (courseId: string, data: {
  rating: number
  comment?: string
  recommend_score: number
  tags?: string[]
}) => {
  return request({
    url: `/api/v1/courses/${courseId}/evaluations`,
    method: 'post',
    data
  })
}

/**
 * 更新课程评价
 */
export const updateCourseEvaluation = (courseId: string, evaluationId: string, data: any) => {
  return request({
    url: `/api/v1/courses/${courseId}/evaluations/${evaluationId}`,
    method: 'put',
    data
  })
}

/**
 * 删除课程评价
 */
export const deleteCourseEvaluation = (courseId: string, evaluationId: string) => {
  return request({
    url: `/api/v1/courses/${courseId}/evaluations/${evaluationId}`,
    method: 'delete'
  })
}

// ==================== 统计分析 ====================

/**
 * 获取课程管理概览统计
 */
export const getCoursesOverview = () => {
  return request({
    url: '/v1/courses/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取讲师统计
 */
export const getInstructorStatistics = () => {
  return request({
    url: '/v1/courses/statistics/instructors',
    method: 'get'
  })
}

/**
 * 获取课程统计
 */
export const getCourseStatistics = () => {
  return request({
    url: '/v1/courses/statistics/courses',
    method: 'get'
  })
}

/**
 * 获取学习进度统计
 */
export const getProgressStatistics = () => {
  return request({
    url: '/v1/courses/statistics/progress',
    method: 'get'
  })
}

/**
 * 获取评价统计
 */
export const getEvaluationStatistics = () => {
  return request({
    url: '/v1/courses/statistics/evaluations',
    method: 'get'
  })
}

// ==================== 数据类型定义 ====================

export interface Instructor {
  id: string
  user_id: string
  name: string
  avatar?: string
  title: string
  bio?: string
  specialties: string[]
  experience_years: number
  education_background: any[]
  certifications: any[]
  contact_info: any
  status: string
  rating: number
  rating_count: number
  course_count: number
  student_count: number
  created_at: string
  updated_at: string
}

export interface Course {
  id: string
  title: string
  description?: string
  cover_image?: string
  category: string
  instructor_id: string
  difficulty_level: string
  duration_hours: number
  price: number
  original_price?: number
  tags: string[]
  learning_objectives: string[]
  prerequisites: string[]
  target_audience?: string
  status: string
  enrollment_count: number
  completion_count: number
  rating: number
  rating_count: number
  view_count: number
  created_at: string
  updated_at: string
}

export interface CourseChapter {
  id: string
  course_id: string
  title: string
  description?: string
  sort_order: number
  is_free: boolean
  lesson_count: number
  total_duration: number
  created_at: string
  updated_at: string
}

export interface CourseLesson {
  id: string
  chapter_id: string
  title: string
  description?: string
  lesson_type: string
  content_url?: string
  duration_minutes: number
  sort_order: number
  is_free: boolean
  resources: any[]
  view_count: number
  completion_count: number
  created_at: string
  updated_at: string
}

export interface CourseEnrollment {
  id: string
  user_id: string
  course_id: string
  enrolled_at: string
  payment_status: string
  payment_amount: number
  progress_percentage: number
  completed_lessons: number
  total_study_time: number
  learning_status: string
  certificate_issued: boolean
  certificate_url?: string
  last_accessed_at?: string
  created_at: string
  updated_at: string
}

export interface CourseEvaluation {
  id: string
  user_id: string
  course_id: string
  rating: number
  comment?: string
  recommend_score: number
  tags: string[]
  like_count: number
  dislike_count: number
  reply_count: number
  status: string
  created_at: string
  updated_at: string
}

export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}