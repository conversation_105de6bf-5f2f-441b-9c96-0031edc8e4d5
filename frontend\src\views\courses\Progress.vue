<template>
  <div class="course-progress-management">
    <div class="page-header">
      <h1>学习进度管理</h1>
      <p>跟踪用户学习进度和完成度统计</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-learners">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalLearners }}</div>
              <div class="stat-label">学习用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active-learners">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.activeLearners }}</div>
              <div class="stat-label">活跃学习</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed-courses">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.completedCourses }}</div>
              <div class="stat-label">完成课程</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon avg-progress">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.avgProgress }}%</div>
              <div class="stat-label">平均进度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="学习进度" name="progress">
        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="generateReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
            <el-button type="success" @click="exportProgress">
              <el-icon><Download /></el-icon>
              导出进度
            </el-button>
            <el-button type="warning" @click="sendReminders">
              <el-icon><Bell /></el-icon>
              发送提醒
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户姓名"
              style="width: 200px"
              clearable
              @change="loadProgress"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="courseFilter" placeholder="课程筛选" style="width: 150px" @change="loadProgress">
              <el-option label="全部课程" value="" />
              <el-option label="Vue.js 教程" value="vue" />
              <el-option label="Python 数据分析" value="python" />
              <el-option label="React 开发" value="react" />
            </el-select>
            <el-select v-model="progressFilter" placeholder="进度筛选" style="width: 120px" @change="loadProgress">
              <el-option label="全部进度" value="" />
              <el-option label="未开始" value="not-started" />
              <el-option label="进行中" value="in-progress" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </div>
        </div>

        <!-- 学习进度列表 -->
        <el-table
          :data="progressList"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column label="用户信息" min-width="200">
            <template #default="{ row }">
              <div class="user-info">
                <div class="user-avatar">
                  <img v-if="row.user_avatar" :src="row.user_avatar" :alt="row.user_name" />
                  <div v-else class="avatar-placeholder">{{ row.user_name.charAt(0) }}</div>
                </div>
                <div class="user-details">
                  <div class="user-name">{{ row.user_name }}</div>
                  <div class="user-id">ID: {{ row.user_id }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="课程信息" min-width="250">
            <template #default="{ row }">
              <div class="course-info">
                <div class="course-title">{{ row.course_title }}</div>
                <div class="course-instructor">讲师: {{ row.instructor }}</div>
                <div class="course-meta">
                  <span class="course-duration">{{ row.total_duration }}小时</span>
                  <span class="course-chapters">{{ row.total_chapters }}章节</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习进度" width="200">
            <template #default="{ row }">
              <div class="progress-info">
                <div class="progress-header">
                  <span class="progress-text">{{ row.progress }}%</span>
                  <span class="progress-status" :class="getProgressClass(row.progress)">
                    {{ getProgressStatus(row.progress) }}
                  </span>
                </div>
                <el-progress 
                  :percentage="row.progress" 
                  :status="getProgressBarStatus(row.progress)"
                  :stroke-width="8"
                />
                <div class="progress-details">
                  <span>{{ row.completed_chapters }}/{{ row.total_chapters }}章节</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习统计" width="150">
            <template #default="{ row }">
              <div class="learning-stats">
                <div class="stat-item">
                  <span class="stat-label">学习时长:</span>
                  <span class="stat-value">{{ formatDuration(row.study_time) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">最后学习:</span>
                  <span class="stat-value">{{ formatTime(row.last_study) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">连续天数:</span>
                  <span class="stat-value streak">{{ row.streak_days }}天</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="注册时间" width="120">
            <template #default="{ row }">
              <span class="enroll-time">{{ formatTime(row.enrolled_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="link" size="small" @click="viewDetails(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button type="link" size="small" @click="viewAnalytics(row)">
                <el-icon><DataAnalysis /></el-icon>
                分析
              </el-button>
              <el-button type="link" size="small" @click="sendMessage(row)">
                <el-icon><ChatDotRound /></el-icon>
                消息
              </el-button>
              <el-button type="link" size="small" @click="resetProgress(row)">
                <el-icon><RefreshRight /></el-icon>
                重置
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadProgress"
            @current-change="loadProgress"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="analytics">
        <!-- 统计图表区域 -->
        <div class="analytics-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>学习进度分布</span>
                    <el-select v-model="timeRange" size="small" style="width: 120px;">
                      <el-option label="最近7天" value="7d" />
                      <el-option label="最近30天" value="30d" />
                      <el-option label="最近90天" value="90d" />
                    </el-select>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加学习进度分布图表 -->
                  <div class="chart-placeholder">学习进度分布图表</div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>学习活跃度</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加学习活跃度图表 -->
                  <div class="chart-placeholder">学习活跃度图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>课程完成趋势</span>
                  </div>
                </template>
                <div class="chart-container">
                  <!-- TODO: 添加课程完成趋势图表 -->
                  <div class="chart-placeholder">课程完成趋势图表</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User, VideoPlay, CircleCheck, TrendCharts, Document, Download, Bell, Search,
  View, DataAnalysis, ChatDotRound, RefreshRight
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('progress')
const loading = ref(false)
const timeRange = ref('30d')
const searchKeyword = ref('')
const courseFilter = ref('')
const progressFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = reactive({
  totalLearners: 2345,
  activeLearners: 1567,
  completedCourses: 456,
  avgProgress: 68.5
})

// 学习进度列表
const progressList = ref([
  {
    id: '1',
    user_name: '张小明',
    user_id: 'U001',
    user_avatar: '',
    course_title: 'Vue.js 3.0 完整教程',
    instructor: '张老师',
    total_duration: 25,
    total_chapters: 12,
    progress: 75,
    completed_chapters: 9,
    study_time: 18000, // 秒
    last_study: '2024-01-20 14:30:00',
    streak_days: 7,
    status: 'active',
    enrolled_at: '2024-01-10 09:00:00'
  },
  {
    id: '2',
    user_name: '李小红',
    user_id: 'U002',
    user_avatar: '',
    course_title: 'Python 数据分析实战',
    instructor: '李老师',
    total_duration: 18,
    total_chapters: 8,
    progress: 100,
    completed_chapters: 8,
    study_time: 21600, // 秒
    last_study: '2024-01-19 16:45:00',
    streak_days: 12,
    status: 'completed',
    enrolled_at: '2024-01-05 10:15:00'
  }
])

// 方法
const loadProgress = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取学习进度数据
    console.log('Loading progress data...')
  } catch (error) {
    ElMessage.error('加载学习进度失败')
  } finally {
    loading.value = false
  }
}

const getProgressClass = (progress) => {
  if (progress >= 90) return 'excellent'
  if (progress >= 70) return 'good'
  if (progress >= 40) return 'average'
  return 'poor'
}

const getProgressStatus = (progress) => {
  if (progress >= 90) return '优秀'
  if (progress >= 70) return '良好'
  if (progress >= 40) return '一般'
  return '需努力'
}

const getProgressBarStatus = (progress) => {
  if (progress >= 90) return 'success'
  if (progress >= 70) return ''
  if (progress >= 40) return 'warning'
  return 'exception'
}

const getStatusName = (status) => {
  const statuses = {
    active: '学习中',
    completed: '已完成',
    paused: '暂停',
    dropped: '退课'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'primary',
    completed: 'success',
    paused: 'warning',
    dropped: 'danger'
  }
  return types[status] || ''
}

const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}小时${minutes}分钟`
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const viewDetails = (progress) => {
  // TODO: 查看学习详情
  console.log('Viewing progress details:', progress)
}

const viewAnalytics = (progress) => {
  // TODO: 查看学习分析
  console.log('Viewing progress analytics:', progress)
}

const sendMessage = (progress) => {
  // TODO: 发送消息
  console.log('Sending message to user:', progress)
}

const resetProgress = (progress) => {
  // TODO: 重置学习进度
  console.log('Resetting progress:', progress)
}

const generateReport = () => {
  // TODO: 生成学习报告
  console.log('Generating learning report...')
}

const exportProgress = () => {
  // TODO: 导出进度数据
  console.log('Exporting progress data...')
}

const sendReminders = () => {
  // TODO: 发送学习提醒
  console.log('Sending learning reminders...')
}

onMounted(() => {
  loadProgress()
})
</script>

<style scoped>
.course-progress-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.total-learners {
  background-color: #409eff;
}

.stat-icon.active-learners {
  background-color: #67c23a;
}

.stat-icon.completed-courses {
  background-color: #e6a23c;
}

.stat-icon.avg-progress {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.management-tabs {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.user-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.user-id {
  font-size: 12px;
  color: #909399;
}

.course-info {
  padding: 4px 0;
}

.course-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.course-instructor {
  font-size: 12px;
  color: #606266;
  margin-bottom: 6px;
}

.course-meta {
  display: flex;
  gap: 8px;
}

.course-duration,
.course-chapters {
  font-size: 11px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.progress-info {
  text-align: center;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.progress-status {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
}

.progress-status.excellent {
  background-color: #67c23a;
}

.progress-status.good {
  background-color: #409eff;
}

.progress-status.average {
  background-color: #e6a23c;
}

.progress-status.poor {
  background-color: #f56c6c;
}

.progress-details {
  font-size: 11px;
  color: #909399;
  margin-top: 8px;
}

.learning-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-label {
  color: #909399;
  font-size: 12px;
}

.stat-value {
  color: #606266;
  font-weight: 600;
  font-size: 14px;
}

.stat-value.streak {
  color: #67c23a !important;
}

.enroll-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.analytics-section {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 16px;
}
</style>
