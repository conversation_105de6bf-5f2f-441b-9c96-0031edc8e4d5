<template>
  <div class="games-management">
    <div class="page-header">
      <h1>游戏圈</h1>
      <p>管理学习游戏化功能</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Present /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.total }}</div>
              <div class="stat-label">游戏总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.active }}</div>
              <div class="stat-label">活跃游戏</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalParticipants }}</div>
              <div class="stat-label">总参与人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon info">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(statistics.totalPlays) }}</div>
              <div class="stat-label">总游戏次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" :inline="true" class="filter-form">
        <el-form-item label="关键词">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索游戏名称、描述"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="游戏类型">
          <el-select v-model="queryParams.type" placeholder="全部类型" clearable>
            <el-option label="答题游戏" value="quiz" />
            <el-option label="挑战游戏" value="challenge" />
            <el-option label="排行榜" value="ranking" />
            <el-option label="成就系统" value="achievement" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.isActive" placeholder="全部状态" clearable>
            <el-option label="活跃" :value="true" />
            <el-option label="停用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 游戏列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>游戏列表</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            创建游戏
          </el-button>
        </div>
      </template>

      <el-table :data="games" v-loading="loading" stripe style="width: 100%">
        <el-table-column label="游戏信息" min-width="250">
          <template #default="{ row }">
            <div class="game-info">
              <div class="game-name">{{ row.name }}</div>
              <div class="game-meta">
                <el-tag :type="getTypeTagType(row.type)">{{ getTypeName(row.type) }}</el-tag>
                <el-tag :type="row.isActive ? 'success' : 'info'" size="small">
                  {{ row.isActive ? '活跃' : '停用' }}
                </el-tag>
              </div>
              <div class="game-description">{{ row.description }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="参与数据" width="150">
          <template #default="{ row }">
            <div class="participation-stats">
              <div>参与人数: {{ row.participantCount }}</div>
              <div>游戏次数: {{ formatNumber(row.totalPlays || 0) }}</div>
              <div>平均分: {{ row.averageScore || 0 }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="时间范围" width="200">
          <template #default="{ row }">
            <div class="time-range">
              <div>开始: {{ formatDate(row.startDate) }}</div>
              <div v-if="row.endDate">结束: {{ formatDate(row.endDate) }}</div>
              <div v-else>无限期</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="奖励设置" width="150">
          <template #default="{ row }">
            <div class="rewards">
              <el-tag v-for="(reward, index) in row.rewards.slice(0, 2)" :key="index" size="small">
                {{ reward }}
              </el-tag>
              <span v-if="row.rewards.length > 2">...</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleParticipants(row)">参与者</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="toggle">{{ row.isActive ? '停用' : '启用' }}</el-dropdown-item>
                  <el-dropdown-item command="stats">统计数据</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 游戏详情对话框 -->
    <el-dialog v-model="detailDialog.visible" title="游戏详情" width="800px">
      <div v-if="detailDialog.game" class="game-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="游戏名称" :span="2">{{ detailDialog.game.name }}</el-descriptions-item>
          <el-descriptions-item label="游戏类型">{{ getTypeName(detailDialog.game.type) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="detailDialog.game.isActive ? 'success' : 'info'">
              {{ detailDialog.game.isActive ? '活跃' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="参与人数">{{ detailDialog.game.participantCount }}</el-descriptions-item>
          <el-descriptions-item label="游戏次数">{{ detailDialog.game.totalPlays || 0 }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(detailDialog.game.startDate) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ detailDialog.game.endDate ? formatDateTime(detailDialog.game.endDate) : '无限期' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(detailDialog.game.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="游戏描述" :span="2">{{ detailDialog.game.description }}</el-descriptions-item>
          <el-descriptions-item label="游戏规则" :span="2">{{ detailDialog.game.rules }}</el-descriptions-item>
          <el-descriptions-item label="奖励设置" :span="2">
            <div v-for="(reward, index) in detailDialog.game.rewards" :key="index">
              {{ index + 1 }}. {{ reward }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="handleParticipants(detailDialog.game)">查看参与者</el-button>
      </template>
    </el-dialog>

    <!-- 参与者管理对话框 -->
    <el-dialog v-model="participantsDialog.visible" title="参与者管理" width="1000px">
      <el-table :data="participantsDialog.participants" v-loading="participantsDialog.loading">
        <el-table-column label="用户信息" width="200">
          <template #default="{ row }">
            <div class="participant-info">
              <el-avatar :src="row.userAvatar" :size="32">{{ row.userName.charAt(0) }}</el-avatar>
              <div class="user-details">
                <div class="name">{{ row.userName }}</div>
                <div class="rank">排名: {{ row.rank }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="得分" width="100">
          <template #default="{ row }">
            <el-tag :type="getScoreTagType(row.score)">{{ row.score }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="成就" width="200">
          <template #default="{ row }">
            <el-tag v-for="achievement in row.achievements.slice(0, 3)" :key="achievement" size="small" style="margin-right: 4px;">
              {{ achievement }}
            </el-tag>
            <span v-if="row.achievements.length > 3">...</span>
          </template>
        </el-table-column>
        <el-table-column prop="participatedAt" label="参与时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.participatedAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastPlayedAt" label="最后游戏" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.lastPlayedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewParticipant(row)">详情</el-button>
            <el-button type="danger" size="small" @click="handleRemoveParticipant(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="participantsDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="handleExportParticipants">导出数据</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Present, VideoPlay, User, DataLine, Search, Plus, ArrowDown
} from '@element-plus/icons-vue'
import type { GameActivity, GameParticipant, QueryParams } from '@/types/community'

// 响应式数据
const loading = ref(false)
const games = ref<GameActivity[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive<QueryParams & { type?: string; isActive?: boolean }>({
  page: 1,
  size: 20,
  keyword: '',
  type: undefined,
  isActive: undefined,
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// 统计数据
const statistics = reactive({
  total: 0,
  active: 0,
  totalParticipants: 0,
  totalPlays: 0
})

// 对话框状态
const detailDialog = reactive({
  visible: false,
  game: null as GameActivity | null
})

const participantsDialog = reactive({
  visible: false,
  loading: false,
  participants: [] as GameParticipant[]
})

// 工具函数
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const getTypeName = (type: string) => {
  const names = {
    quiz: '答题游戏',
    challenge: '挑战游戏',
    ranking: '排行榜',
    achievement: '成就系统'
  }
  return names[type] || type
}

const getTypeTagType = (type: string) => {
  const types = {
    quiz: 'primary',
    challenge: 'warning',
    ranking: 'success',
    achievement: 'danger'
  }
  return types[type] || ''
}

const getScoreTagType = (score: number) => {
  if (score >= 90) return 'danger'
  if (score >= 80) return 'warning'
  if (score >= 70) return 'success'
  return 'info'
}

// 事件处理
const handleSearch = () => {
  queryParams.page = 1
  loadGames()
}

const handleReset = () => {
  Object.assign(queryParams, {
    page: 1,
    size: 20,
    keyword: '',
    type: undefined,
    isActive: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
  loadGames()
}

const handleSizeChange = (size: number) => {
  queryParams.size = size
  queryParams.page = 1
  loadGames()
}

const handleCurrentChange = (page: number) => {
  queryParams.page = page
  loadGames()
}

const handleAdd = () => {
  ElMessage.info('创建游戏功能开发中')
}

const handleView = (game: GameActivity) => {
  detailDialog.game = game
  detailDialog.visible = true
}

const handleParticipants = async (game: GameActivity) => {
  participantsDialog.visible = true
  participantsDialog.loading = true
  
  try {
    // TODO: 调用API获取参与者列表
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    participantsDialog.participants = [
      {
        id: '1',
        gameId: game.id,
        userId: '1',
        userName: '张三',
        userAvatar: '',
        score: 95,
        rank: 1,
        achievements: ['答题达人', '连胜王者', '速度之星'],
        participatedAt: '2024-01-10T10:00:00Z',
        lastPlayedAt: '2024-01-15T14:30:00Z'
      }
    ]
  } catch (error) {
    ElMessage.error('加载参与者列表失败')
  } finally {
    participantsDialog.loading = false
  }
}

const handleAction = async (command: string, game: GameActivity) => {
  switch (command) {
    case 'edit':
      ElMessage.info(`编辑游戏: ${game.name}`)
      break
    case 'toggle':
      await handleToggle(game)
      break
    case 'stats':
      ElMessage.info(`查看统计: ${game.name}`)
      break
    case 'delete':
      await handleDelete(game)
      break
  }
}

const handleToggle = async (game: GameActivity) => {
  try {
    const action = game.isActive ? '停用' : '启用'
    await ElMessageBox.confirm(`确定要${action}游戏 "${game.name}" 吗？`, `确认${action}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 调用API
    ElMessage.success(`${action}成功`)
    loadGames()
  } catch {
    // 用户取消
  }
}

const handleDelete = async (game: GameActivity) => {
  try {
    await ElMessageBox.confirm(`确定要删除游戏 "${game.name}" 吗？此操作不可恢复！`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'danger'
    })
    // TODO: 调用API
    ElMessage.success('删除成功')
    loadGames()
  } catch {
    // 用户取消
  }
}

const handleViewParticipant = (participant: GameParticipant) => {
  ElMessage.info(`查看参与者详情: ${participant.userName}`)
}

const handleRemoveParticipant = async (participant: GameParticipant) => {
  try {
    await ElMessageBox.confirm(`确定要移除参与者 "${participant.userName}" 吗？`, '确认移除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 调用API
    ElMessage.success('移除成功')
  } catch {
    // 用户取消
  }
}

const handleExportParticipants = () => {
  ElMessage.info('导出参与者数据功能开发中')
}

// 数据加载
const loadGames = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取游戏列表
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    games.value = [
      {
        id: '1',
        name: '数学知识竞答',
        description: '通过答题形式检验数学知识掌握程度',
        type: 'quiz',
        rules: '每题限时30秒，答对得分，答错不扣分',
        rewards: ['第一名：学习积分100', '第二名：学习积分80', '第三名：学习积分60'],
        participantCount: 156,
        totalPlays: 2340,
        averageScore: 78.5,
        isActive: true,
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z',
        createdAt: '2024-01-01T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z'
      }
    ]
    
    total.value = 15
    
    // 更新统计数据
    statistics.total = 15
    statistics.active = 12
    statistics.totalParticipants = 800
    statistics.totalPlays = 15000
    
  } catch (error) {
    ElMessage.error('加载游戏列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadGames()
})
</script>

<style scoped>
.games-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background-color: #409eff;
  color: white;
  font-size: 20px;
}

.stat-icon.active {
  background-color: #67c23a;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.info {
  background-color: #909399;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin-bottom: 0;
}

.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.game-name {
  font-weight: 500;
  color: #303133;
}

.game-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.game-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.participation-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  color: #606266;
}

.time-range {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  color: #606266;
}

.rewards {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.game-detail {
  margin-bottom: 20px;
}

.participant-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-details {
  flex: 1;
}

.user-details .name {
  font-weight: 500;
  color: #303133;
}

.user-details .rank {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .games-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .filter-form .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
