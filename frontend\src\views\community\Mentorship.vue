<template>
  <div class="mentorship-management">
    <div class="page-header">
      <h1>师徒结对管理</h1>
      <p>管理师徒关系的匹配算法、资质认证、进度跟踪和效果评估</p>
    </div>

    <!-- 功能导航标签 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <el-tab-pane label="导师资质认证" name="certification">
        <!-- 认证统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ certificationStats.pendingApplications }}</div>
                  <div class="stat-label">待审核申请</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Medal /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ certificationStats.certifiedMentors }}</div>
                  <div class="stat-label">认证导师</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ certificationStats.goldMentors }}</div>
                  <div class="stat-label">金牌导师</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ certificationStats.expiringSoon }}</div>
                  <div class="stat-label">即将过期认证</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 导师认证标准 -->
        <el-card class="certification-standards-card">
          <template #header>
            <div class="card-header">
              <span>导师认证标准</span>
              <el-button type="primary" @click="showStandardsDialog = true">
                <el-icon><Setting /></el-icon>
                修改标准
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="standards-content">
            <el-col :span="8">
              <div class="standard-level">
                <h4>普通导师</h4>
                <div class="requirements">
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>学历要求：{{ certificationStandards.basic.education }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>经验要求：{{ certificationStandards.basic.experience }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>评分要求：≥ {{ certificationStandards.basic.minRating }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>学生数量：≤ {{ certificationStandards.basic.maxStudents }}人</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="standard-level">
                <h4>高级导师</h4>
                <div class="requirements">
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>学历要求：{{ certificationStandards.advanced.education }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>经验要求：{{ certificationStandards.advanced.experience }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>评分要求：≥ {{ certificationStandards.advanced.minRating }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>学生数量：≤ {{ certificationStandards.advanced.maxStudents }}人</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="standard-level gold">
                <h4>金牌导师</h4>
                <div class="requirements">
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>学历要求：{{ certificationStandards.gold.education }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>经验要求：{{ certificationStandards.gold.experience }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>评分要求：≥ {{ certificationStandards.gold.minRating }}</span>
                  </div>
                  <div class="requirement-item">
                    <el-icon><Check /></el-icon>
                    <span>学生数量：≤ {{ certificationStandards.gold.maxStudents }}人</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 待审核申请列表 -->
        <el-card class="applications-table-card">
          <template #header>
            <div class="card-header">
              <span>导师认证申请</span>
              <div class="header-actions">
                <el-button type="success" @click="handleBatchApprove" :disabled="!selectedApplications.length">
                  <el-icon><Check /></el-icon>
                  批量通过
                </el-button>
                <el-button type="danger" @click="handleBatchReject" :disabled="!selectedApplications.length">
                  <el-icon><Close /></el-icon>
                  批量拒绝
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="mentorApplications"
            v-loading="applicationLoading"
            @selection-change="handleApplicationSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="applicantName" label="申请人" width="120">
              <template #default="{ row }">
                <div class="applicant-info">
                  <el-avatar :src="row.applicantAvatar" :size="32">{{ row.applicantName.charAt(0) }}</el-avatar>
                  <span class="applicant-name">{{ row.applicantName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="targetLevel" label="申请等级" width="120">
              <template #default="{ row }">
                <el-tag :type="getLevelTagType(row.targetLevel)">
                  {{ getLevelName(row.targetLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="specialties" label="专业领域" min-width="200">
              <template #default="{ row }">
                <div class="specialties">
                  <el-tag
                    v-for="specialty in row.specialties"
                    :key="specialty"
                    size="small"
                    class="specialty-tag"
                  >
                    {{ getSpecialtyName(specialty) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="education" label="学历" width="120" />
            <el-table-column prop="experience" label="经验" width="100">
              <template #default="{ row }">
                <span>{{ row.experience }}年</span>
              </template>
            </el-table-column>
            <el-table-column prop="currentRating" label="当前评分" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.currentRating" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="applicationTime" label="申请时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.applicationTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewApplicationDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="approveApplication(row)">
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button type="danger" size="small" @click="rejectApplication(row)">
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="applicationPagination.page"
              v-model:page-size="applicationPagination.size"
              :total="applicationPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleApplicationSizeChange"
              @current-change="handleApplicationPageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="匹配算法管理" name="matching">
        <!-- 匹配统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.totalMatches }}</div>
                  <div class="stat-label">总匹配数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.successfulMatches }}</div>
                  <div class="stat-label">成功匹配</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.avgMatchTime }}</div>
                  <div class="stat-label">平均匹配时长(天)</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ matchingStats.matchSuccessRate }}%</div>
                  <div class="stat-label">匹配成功率</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 匹配算法配置 -->
        <el-card class="algorithm-config-card">
          <template #header>
            <div class="card-header">
              <span>匹配算法配置</span>
              <el-button type="primary" @click="showAlgorithmDialog = true">
                <el-icon><Setting /></el-icon>
                调整算法
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="algorithm-content">
            <el-col :span="12">
              <div class="algorithm-section">
                <h4>匹配权重设置</h4>
                <div class="weight-settings">
                  <div class="weight-item">
                    <span class="weight-label">专业匹配度：</span>
                    <el-slider v-model="algorithmConfig.specialtyWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">学习风格匹配：</span>
                    <el-slider v-model="algorithmConfig.styleWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">时间安排匹配：</span>
                    <el-slider v-model="algorithmConfig.timeWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">地理位置匹配：</span>
                    <el-slider v-model="algorithmConfig.locationWeight" :max="100" show-input />
                  </div>
                  <div class="weight-item">
                    <span class="weight-label">性格匹配度：</span>
                    <el-slider v-model="algorithmConfig.personalityWeight" :max="100" show-input />
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="algorithm-section">
                <h4>匹配规则设置</h4>
                <div class="rule-settings">
                  <div class="rule-item">
                    <span class="rule-label">最低匹配分数：</span>
                    <el-input-number v-model="algorithmConfig.minMatchScore" :min="0" :max="100" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">最大推荐数量：</span>
                    <el-input-number v-model="algorithmConfig.maxRecommendations" :min="1" :max="20" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">自动匹配：</span>
                    <el-switch v-model="algorithmConfig.autoMatch" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">跨地区匹配：</span>
                    <el-switch v-model="algorithmConfig.crossRegionMatch" />
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">匹配冷却期：</span>
                    <el-input-number v-model="algorithmConfig.cooldownDays" :min="0" :max="30" />
                    <span class="unit">天</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div class="algorithm-actions">
            <el-button type="primary" @click="saveAlgorithmConfig">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button type="info" @click="testAlgorithm">
              <el-icon><DataAnalysis /></el-icon>
              测试算法
            </el-button>
            <el-button @click="resetAlgorithmConfig">
              <el-icon><Refresh /></el-icon>
              重置默认
            </el-button>
          </div>
        </el-card>

        <!-- 待匹配队列 -->
        <el-card class="matching-queue-card">
          <template #header>
            <div class="card-header">
              <span>待匹配队列</span>
              <div class="header-actions">
                <el-button type="primary" @click="runMatching">
                  <el-icon><Connection /></el-icon>
                  执行匹配
                </el-button>
                <el-button type="info" @click="refreshQueue">
                  <el-icon><Refresh /></el-icon>
                  刷新队列
                </el-button>
              </div>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="queue-section">
                <h4>等待导师的学生 ({{ waitingStudents.length }})</h4>
                <div class="queue-list">
                  <div
                    v-for="student in waitingStudents"
                    :key="student.id"
                    class="queue-item"
                  >
                    <div class="user-info">
                      <el-avatar :src="student.avatar" :size="32">{{ student.name.charAt(0) }}</el-avatar>
                      <div class="user-details">
                        <div class="user-name">{{ student.name }}</div>
                        <div class="user-meta">
                          <el-tag size="small">{{ getSpecialtyName(student.targetSpecialty) }}</el-tag>
                          <span class="waiting-time">等待{{ formatWaitingTime(student.waitingTime) }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="queue-actions">
                      <el-button type="primary" size="small" @click="manualMatch(student)">
                        <el-icon><Connection /></el-icon>
                        手动匹配
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="queue-section">
                <h4>等待学生的导师 ({{ waitingMentors.length }})</h4>
                <div class="queue-list">
                  <div
                    v-for="mentor in waitingMentors"
                    :key="mentor.id"
                    class="queue-item"
                  >
                    <div class="user-info">
                      <el-avatar :src="mentor.avatar" :size="32">{{ mentor.name.charAt(0) }}</el-avatar>
                      <div class="user-details">
                        <div class="user-name">{{ mentor.name }}</div>
                        <div class="user-meta">
                          <el-tag :type="getLevelTagType(mentor.level)" size="small">{{ getLevelName(mentor.level) }}</el-tag>
                          <span class="capacity">{{ mentor.currentStudents }}/{{ mentor.maxStudents }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="queue-actions">
                      <el-button type="success" size="small" @click="viewMentorProfile(mentor)">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="进度跟踪" name="progress">
        <!-- 进度统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ progressStats.activePairs }}</div>
                  <div class="stat-label">活跃师徒对</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ progressStats.avgProgress }}%</div>
                  <div class="stat-label">平均学习进度</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ progressStats.atRiskPairs }}</div>
                  <div class="stat-label">风险师徒对</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ progressStats.completedPairs }}</div>
                  <div class="stat-label">已完成师徒对</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 进度监控设置 -->
        <el-card class="monitoring-settings-card">
          <template #header>
            <div class="card-header">
              <span>进度监控设置</span>
              <el-button type="primary" @click="showMonitoringDialog = true">
                <el-icon><Setting /></el-icon>
                配置监控
              </el-button>
            </div>
          </template>

          <el-row :gutter="20" class="monitoring-content">
            <el-col :span="8">
              <div class="monitoring-section">
                <h4>预警规则</h4>
                <div class="alert-rules">
                  <div class="rule-item">
                    <span class="rule-label">无互动天数：</span>
                    <el-input-number v-model="monitoringConfig.noInteractionDays" :min="1" :max="30" />
                    <span class="unit">天</span>
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">进度停滞天数：</span>
                    <el-input-number v-model="monitoringConfig.noProgressDays" :min="1" :max="60" />
                    <span class="unit">天</span>
                  </div>
                  <div class="rule-item">
                    <span class="rule-label">低评分阈值：</span>
                    <el-input-number v-model="monitoringConfig.lowRatingThreshold" :min="1" :max="5" :step="0.1" />
                    <span class="unit">分</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="monitoring-section">
                <h4>检查频率</h4>
                <div class="check-frequency">
                  <div class="frequency-item">
                    <span class="frequency-label">进度检查：</span>
                    <el-select v-model="monitoringConfig.progressCheckFrequency">
                      <el-option label="每日" value="daily" />
                      <el-option label="每周" value="weekly" />
                      <el-option label="每月" value="monthly" />
                    </el-select>
                  </div>
                  <div class="frequency-item">
                    <span class="frequency-label">满意度调查：</span>
                    <el-select v-model="monitoringConfig.satisfactionSurveyFrequency">
                      <el-option label="每周" value="weekly" />
                      <el-option label="每月" value="monthly" />
                      <el-option label="每季度" value="quarterly" />
                    </el-select>
                  </div>
                  <div class="frequency-item">
                    <span class="frequency-label">自动提醒：</span>
                    <el-switch v-model="monitoringConfig.autoReminder" />
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="monitoring-section">
                <h4>干预措施</h4>
                <div class="intervention-measures">
                  <div class="measure-item">
                    <el-checkbox v-model="monitoringConfig.autoWarning">自动发送预警</el-checkbox>
                  </div>
                  <div class="measure-item">
                    <el-checkbox v-model="monitoringConfig.mentorNotification">通知导师</el-checkbox>
                  </div>
                  <div class="measure-item">
                    <el-checkbox v-model="monitoringConfig.adminIntervention">管理员介入</el-checkbox>
                  </div>
                  <div class="measure-item">
                    <el-checkbox v-model="monitoringConfig.suggestReMatch">建议重新匹配</el-checkbox>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 师徒对进度列表 -->
        <el-card class="progress-table-card">
          <template #header>
            <div class="card-header">
              <span>师徒对进度监控</span>
              <div class="header-actions">
                <el-select v-model="progressFilter" placeholder="筛选状态" clearable>
                  <el-option label="正常" value="normal" />
                  <el-option label="风险" value="at_risk" />
                  <el-option label="停滞" value="stagnant" />
                  <el-option label="优秀" value="excellent" />
                </el-select>
                <el-button type="warning" @click="handleBatchIntervention" :disabled="!selectedPairs.length">
                  <el-icon><Warning /></el-icon>
                  批量干预
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="mentorshipPairs"
            v-loading="progressLoading"
            @selection-change="handlePairSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="师徒信息" min-width="200">
              <template #default="{ row }">
                <div class="pair-info">
                  <div class="mentor-info">
                    <el-avatar :src="row.mentorAvatar" :size="24">{{ row.mentorName.charAt(0) }}</el-avatar>
                    <span class="mentor-name">{{ row.mentorName }}</span>
                    <el-tag :type="getLevelTagType(row.mentorLevel)" size="small">{{ getLevelName(row.mentorLevel) }}</el-tag>
                  </div>
                  <div class="student-info">
                    <el-avatar :src="row.studentAvatar" :size="24">{{ row.studentName.charAt(0) }}</el-avatar>
                    <span class="student-name">{{ row.studentName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="specialty" label="专业领域" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getSpecialtyName(row.specialty) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="学习进度" width="150">
              <template #default="{ row }">
                <el-progress :percentage="row.progress" :color="getProgressColor(row.progress)" />
              </template>
            </el-table-column>
            <el-table-column prop="lastInteraction" label="最后互动" width="120">
              <template #default="{ row }">
                <span :class="getInteractionTimeClass(row.lastInteraction)">
                  {{ formatRelativeTime(row.lastInteraction) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="satisfaction" label="满意度" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.satisfaction" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="startDate" label="开始时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.startDate) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewPairDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="warning" size="small" @click="interventePair(row)" v-if="row.status === 'at_risk'">
                  <el-icon><Warning /></el-icon>
                  干预
                </el-button>
                <el-button type="info" size="small" @click="sendReminder(row)">
                  <el-icon><Bell /></el-icon>
                  提醒
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="效果评估" name="evaluation">
        <!-- 评估统计 -->
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><Trophy /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ evaluationStats.overallSatisfaction }}</div>
                  <div class="stat-label">整体满意度</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><DataLine /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ evaluationStats.avgImprovementRate }}%</div>
                  <div class="stat-label">平均提升率</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ evaluationStats.retentionRate }}%</div>
                  <div class="stat-label">师徒关系保持率</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon danger">
                  <el-icon><Medal /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ evaluationStats.excellentPairs }}</div>
                  <div class="stat-label">优秀师徒对</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 效果分析图表 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>满意度趋势</span>
              </template>
              <div class="chart-container" ref="satisfactionTrendChartRef">
                <div class="chart-mock">满意度趋势图表</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>学习效果分布</span>
              </template>
              <div class="chart-container" ref="effectDistributionChartRef">
                <div class="chart-mock">学习效果分布图表</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 优秀师徒对展示 -->
        <el-card class="excellent-pairs-card">
          <template #header>
            <div class="card-header">
              <span>优秀师徒对展示</span>
              <el-radio-group v-model="excellentPairsFilter" size="small">
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="quarter">本季度</el-radio-button>
                <el-radio-button label="year">本年度</el-radio-button>
              </el-radio-group>
            </div>
          </template>

          <el-table :data="excellentPairs" stripe style="width: 100%">
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <el-tag :type="getRankTagType($index + 1)" size="small">
                  第{{ $index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="师徒信息" min-width="200">
              <template #default="{ row }">
                <div class="pair-info">
                  <div class="mentor-info">
                    <el-avatar :src="row.mentorAvatar" :size="32">{{ row.mentorName.charAt(0) }}</el-avatar>
                    <span class="mentor-name">{{ row.mentorName }}</span>
                    <el-tag :type="getLevelTagType(row.mentorLevel)" size="small">{{ getLevelName(row.mentorLevel) }}</el-tag>
                  </div>
                  <div class="student-info">
                    <el-avatar :src="row.studentAvatar" :size="32">{{ row.studentName.charAt(0) }}</el-avatar>
                    <span class="student-name">{{ row.studentName }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="specialty" label="专业领域" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getSpecialtyName(row.specialty) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="improvementRate" label="提升率" width="120">
              <template #default="{ row }">
                <span class="improvement-rate">+{{ row.improvementRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="satisfaction" label="满意度" width="120">
              <template #default="{ row }">
                <el-rate v-model="row.satisfaction" disabled show-score />
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="持续时长" width="120">
              <template #default="{ row }">
                <span>{{ row.duration }}个月</span>
              </template>
            </el-table-column>
            <el-table-column prop="achievements" label="主要成就" min-width="200">
              <template #default="{ row }">
                <div class="achievements">
                  <el-tag
                    v-for="achievement in row.achievements"
                    :key="achievement"
                    size="small"
                    type="success"
                    class="achievement-tag"
                  >
                    {{ achievement }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 问题分析与改进建议 -->
        <el-card class="analysis-card">
          <template #header>
            <span>问题分析与改进建议</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="analysis-section">
                <h4>常见问题分析</h4>
                <div class="problem-list">
                  <div class="problem-item" v-for="problem in commonProblems" :key="problem.id">
                    <div class="problem-header">
                      <span class="problem-title">{{ problem.title }}</span>
                      <el-tag :type="getProblemSeverityType(problem.severity)" size="small">
                        {{ getProblemSeverityText(problem.severity) }}
                      </el-tag>
                    </div>
                    <div class="problem-description">{{ problem.description }}</div>
                    <div class="problem-stats">
                      <span class="occurrence-rate">发生率：{{ problem.occurrenceRate }}%</span>
                      <span class="impact-score">影响度：{{ problem.impactScore }}/10</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="analysis-section">
                <h4>改进建议</h4>
                <div class="suggestion-list">
                  <div class="suggestion-item" v-for="suggestion in improvementSuggestions" :key="suggestion.id">
                    <div class="suggestion-header">
                      <el-icon><Star /></el-icon>
                      <span class="suggestion-title">{{ suggestion.title }}</span>
                      <el-tag :type="getPriorityType(suggestion.priority)" size="small">
                        {{ getPriorityText(suggestion.priority) }}
                      </el-tag>
                    </div>
                    <div class="suggestion-description">{{ suggestion.description }}</div>
                    <div class="suggestion-actions">
                      <el-button type="primary" size="small" @click="implementSuggestion(suggestion)">
                        <el-icon><Check /></el-icon>
                        采纳建议
                      </el-button>
                      <el-button size="small" @click="viewSuggestionDetails(suggestion)">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 数据洞察 -->
        <el-card class="insights-card">
          <template #header>
            <span>数据洞察</span>
          </template>

          <div class="insights-content">
            <div class="insight-section">
              <h4>成功因素分析</h4>
              <div class="insights-list">
                <div class="insight-item">
                  <el-icon><TrendCharts /></el-icon>
                  <span>专业匹配度高的师徒对成功率提升45%，建议优化匹配算法权重</span>
                </div>
                <div class="insight-item">
                  <el-icon><ChatDotRound /></el-icon>
                  <span>定期互动的师徒对满意度平均高出2.3分，建议加强互动提醒机制</span>
                </div>
                <div class="insight-item">
                  <el-icon><Star /></el-icon>
                  <span>金牌导师的学生提升率比普通导师高出60%，建议扩大金牌导师队伍</span>
                </div>
              </div>
            </div>

            <div class="insight-section">
              <h4>风险预警</h4>
              <div class="insights-list">
                <div class="insight-item warning">
                  <el-icon><Warning /></el-icon>
                  <span>近期师徒关系解除率上升15%，需要关注匹配质量和支持服务</span>
                </div>
                <div class="insight-item warning">
                  <el-icon><Timer /></el-icon>
                  <span>部分导师响应时间过长，建议建立导师活跃度监控机制</span>
                </div>
              </div>
            </div>

            <div class="insight-section">
              <h4>发展机会</h4>
              <div class="insights-list">
                <div class="insight-item success">
                  <el-icon><Trophy /></el-icon>
                  <span>可考虑推出师徒成就系统，激励长期稳定的师徒关系</span>
                </div>
                <div class="insight-item success">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>建议开发AI辅助学习计划制定功能，提升指导效果</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock, Medal, Star, Warning, Setting, Check, Close, View, Connection, CircleCheck,
  Timer, DataLine, DataAnalysis, Refresh, User, TrendCharts, Trophy, Bell,
  ChatDotRound
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('certification')
const applicationLoading = ref(false)
const progressLoading = ref(false)
const selectedApplications = ref([])
const selectedPairs = ref([])
const showStandardsDialog = ref(false)
const showAlgorithmDialog = ref(false)
const showMonitoringDialog = ref(false)

// 认证统计数据
const certificationStats = reactive({
  pendingApplications: 23,
  certifiedMentors: 156,
  goldMentors: 12,
  expiringSoon: 8
})

// 匹配统计数据
const matchingStats = reactive({
  totalMatches: 456,
  successfulMatches: 389,
  avgMatchTime: 3.5,
  matchSuccessRate: 85
})

// 进度统计数据
const progressStats = reactive({
  activePairs: 234,
  avgProgress: 75,
  atRiskPairs: 18,
  completedPairs: 89
})

// 评估统计数据
const evaluationStats = reactive({
  overallSatisfaction: 4.6,
  avgImprovementRate: 68,
  retentionRate: 82,
  excellentPairs: 45
})

// 认证标准
const certificationStandards = reactive({
  basic: {
    education: '本科及以上',
    experience: '1年以上',
    minRating: 4.0,
    maxStudents: 5
  },
  advanced: {
    education: '硕士及以上',
    experience: '3年以上',
    minRating: 4.5,
    maxStudents: 8
  },
  gold: {
    education: '硕士及以上',
    experience: '5年以上',
    minRating: 4.8,
    maxStudents: 12
  }
})

// 算法配置
const algorithmConfig = reactive({
  specialtyWeight: 40,
  styleWeight: 25,
  timeWeight: 15,
  locationWeight: 10,
  personalityWeight: 10,
  minMatchScore: 70,
  maxRecommendations: 5,
  autoMatch: false,
  crossRegionMatch: true,
  cooldownDays: 7
})

// 监控配置
const monitoringConfig = reactive({
  noInteractionDays: 7,
  noProgressDays: 14,
  lowRatingThreshold: 3.0,
  progressCheckFrequency: 'weekly',
  satisfactionSurveyFrequency: 'monthly',
  autoReminder: true,
  autoWarning: true,
  mentorNotification: true,
  adminIntervention: false,
  suggestReMatch: true
})

// 导师申请列表
const mentorApplications = ref([
  {
    id: 1,
    applicantName: '张老师',
    applicantAvatar: '',
    targetLevel: 'advanced',
    specialties: ['math', 'physics'],
    education: '硕士',
    experience: 5,
    currentRating: 4.7,
    applicationTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    applicantName: '李教授',
    applicantAvatar: '',
    targetLevel: 'gold',
    specialties: ['english', 'literature'],
    education: '博士',
    experience: 8,
    currentRating: 4.9,
    applicationTime: '2024-01-14 14:20:00'
  }
])

// 等待匹配的学生
const waitingStudents = ref([
  {
    id: 1,
    name: '学生A',
    avatar: '',
    targetSpecialty: 'math',
    waitingTime: 180
  },
  {
    id: 2,
    name: '学生B',
    avatar: '',
    targetSpecialty: 'english',
    waitingTime: 120
  }
])

// 等待匹配的导师
const waitingMentors = ref([
  {
    id: 1,
    name: '导师C',
    avatar: '',
    level: 'advanced',
    currentStudents: 3,
    maxStudents: 8
  },
  {
    id: 2,
    name: '导师D',
    avatar: '',
    level: 'gold',
    currentStudents: 8,
    maxStudents: 12
  }
])

// 师徒对列表
const mentorshipPairs = ref([
  {
    id: 1,
    mentorName: '张老师',
    mentorAvatar: '',
    mentorLevel: 'advanced',
    studentName: '小明',
    studentAvatar: '',
    specialty: 'math',
    progress: 75,
    lastInteraction: '2024-01-14',
    satisfaction: 4.5,
    status: 'normal',
    startDate: '2023-12-01'
  },
  {
    id: 2,
    mentorName: '李教授',
    mentorAvatar: '',
    mentorLevel: 'gold',
    studentName: '小红',
    studentAvatar: '',
    specialty: 'english',
    progress: 45,
    lastInteraction: '2024-01-05',
    satisfaction: 3.8,
    status: 'at_risk',
    startDate: '2023-11-15'
  }
])

// 优秀师徒对
const excellentPairs = ref([
  {
    mentorName: '王教授',
    mentorAvatar: '',
    mentorLevel: 'gold',
    studentName: '优秀学生A',
    studentAvatar: '',
    specialty: 'math',
    improvementRate: 85,
    satisfaction: 4.9,
    duration: 6,
    achievements: ['竞赛一等奖', '成绩提升显著', '学习习惯改善']
  }
])

// 常见问题
const commonProblems = ref([
  {
    id: 1,
    title: '师徒沟通不畅',
    description: '师徒双方缺乏有效沟通，导致指导效果不佳',
    severity: 'high',
    occurrenceRate: 25,
    impactScore: 8
  },
  {
    id: 2,
    title: '学习进度缓慢',
    description: '学生学习进度未达到预期，需要调整学习计划',
    severity: 'medium',
    occurrenceRate: 35,
    impactScore: 6
  }
])

// 改进建议
const improvementSuggestions = ref([
  {
    id: 1,
    title: '优化匹配算法',
    description: '基于历史数据调整匹配权重，提高匹配成功率',
    priority: 'high'
  },
  {
    id: 2,
    title: '加强导师培训',
    description: '定期组织导师培训，提升指导技能和沟通能力',
    priority: 'medium'
  }
])

// 分页数据
const applicationPagination = reactive({
  page: 1,
  size: 20,
  total: 100
})

// 筛选条件
const progressFilter = ref('')
const excellentPairsFilter = ref('month')

// 工具函数
const getLevelName = (level: string) => {
  const levelMap: Record<string, string> = {
    basic: '普通导师',
    advanced: '高级导师',
    gold: '金牌导师'
  }
  return levelMap[level] || level
}

const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    basic: 'info',
    advanced: 'warning',
    gold: 'danger'
  }
  return typeMap[level] || 'default'
}

const getSpecialtyName = (specialty: string) => {
  const specialtyMap: Record<string, string> = {
    math: '数学',
    physics: '物理',
    chemistry: '化学',
    biology: '生物',
    english: '英语',
    chinese: '语文',
    literature: '文学'
  }
  return specialtyMap[specialty] || specialty
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatWaitingTime = (minutes: number) => {
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins}分钟`
}

const formatRelativeTime = (date: string) => {
  const now = new Date()
  const target = new Date(date)
  const diffDays = Math.floor((now.getTime() - target.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  return `${Math.floor(diffDays / 30)}月前`
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getInteractionTimeClass = (date: string) => {
  const now = new Date()
  const target = new Date(date)
  const diffDays = Math.floor((now.getTime() - target.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays > 7) return 'text-danger'
  if (diffDays > 3) return 'text-warning'
  return 'text-success'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: 'success',
    at_risk: 'warning',
    stagnant: 'danger',
    excellent: 'primary'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: '正常',
    at_risk: '风险',
    stagnant: '停滞',
    excellent: '优秀'
  }
  return statusMap[status] || status
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

const getProblemSeverityType = (severity: string) => {
  const severityMap: Record<string, string> = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return severityMap[severity] || 'default'
}

const getProblemSeverityText = (severity: string) => {
  const severityMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高'
  }
  return severityMap[severity] || severity
}

const getPriorityType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger'
  }
  return priorityMap[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    low: '低优先级',
    medium: '中优先级',
    high: '高优先级'
  }
  return priorityMap[priority] || priority
}

// 事件处理函数
const handleApplicationSelectionChange = (selection: any[]) => {
  selectedApplications.value = selection
}

const handlePairSelectionChange = (selection: any[]) => {
  selectedPairs.value = selection
}

const handleBatchApprove = () => {
  ElMessageBox.confirm('确定要批量通过选中的申请吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量审核通过成功')
    // TODO: 调用API
  })
}

const handleBatchReject = () => {
  ElMessageBox.confirm('确定要批量拒绝选中的申请吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量拒绝成功')
    // TODO: 调用API
  })
}

const viewApplicationDetails = (application: any) => {
  console.log('查看申请详情:', application)
  // TODO: 实现申请详情查看
}

const approveApplication = (application: any) => {
  ElMessage.success(`申请人"${application.applicantName}"的认证申请已通过`)
  // TODO: 调用API
}

const rejectApplication = (application: any) => {
  ElMessage.warning(`申请人"${application.applicantName}"的认证申请已拒绝`)
  // TODO: 调用API
}

const saveAlgorithmConfig = () => {
  ElMessage.success('算法配置已保存')
  // TODO: 调用API保存配置
}

const testAlgorithm = () => {
  ElMessage.info('正在测试算法效果...')
  // TODO: 实现算法测试
}

const resetAlgorithmConfig = () => {
  ElMessageBox.confirm('确定要重置为默认配置吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    // 重置配置
    Object.assign(algorithmConfig, {
      specialtyWeight: 40,
      styleWeight: 25,
      timeWeight: 15,
      locationWeight: 10,
      personalityWeight: 10,
      minMatchScore: 70,
      maxRecommendations: 5,
      autoMatch: false,
      crossRegionMatch: true,
      cooldownDays: 7
    })
    ElMessage.success('配置已重置为默认值')
  })
}

const runMatching = () => {
  ElMessage.info('正在执行匹配算法...')
  // TODO: 调用匹配API
}

const refreshQueue = () => {
  ElMessage.success('队列已刷新')
  // TODO: 刷新队列数据
}

const manualMatch = (student: any) => {
  console.log('手动匹配学生:', student)
  // TODO: 实现手动匹配
}

const viewMentorProfile = (mentor: any) => {
  console.log('查看导师详情:', mentor)
  // TODO: 实现导师详情查看
}

const handleBatchIntervention = () => {
  ElMessageBox.confirm('确定要对选中的师徒对进行批量干预吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量干预操作已执行')
    // TODO: 调用API
  })
}

const viewPairDetails = (pair: any) => {
  console.log('查看师徒对详情:', pair)
  // TODO: 实现师徒对详情查看
}

const interventePair = (pair: any) => {
  console.log('干预师徒对:', pair)
  // TODO: 实现师徒对干预
}

const sendReminder = (pair: any) => {
  ElMessage.success(`已向师徒对"${pair.mentorName}-${pair.studentName}"发送提醒`)
  // TODO: 调用API发送提醒
}

const implementSuggestion = (suggestion: any) => {
  ElMessage.success(`建议"${suggestion.title}"已采纳，将安排实施`)
  // TODO: 调用API实施建议
}

const viewSuggestionDetails = (suggestion: any) => {
  console.log('查看建议详情:', suggestion)
  // TODO: 实现建议详情查看
}

const handleApplicationSizeChange = (size: number) => {
  applicationPagination.size = size
  // TODO: 重新加载数据
}

const handleApplicationPageChange = (page: number) => {
  applicationPagination.page = page
  // TODO: 重新加载数据
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.mentorship-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.management-tabs {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.stat-icon.info {
  background-color: #409eff;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.danger {
  background-color: #f56c6c;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.standards-content {
  margin-top: 16px;
}

.standard-level {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
  transition: all 0.3s;
}

.standard-level:hover {
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.1);
}

.standard-level.gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #8b4513;
}

.standard-level h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.standard-level.gold h4 {
  color: #8b4513;
}

.requirements {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.standard-level.gold .requirement-item {
  color: #8b4513;
}

.requirement-item .el-icon {
  color: #67c23a;
  font-size: 16px;
}

.applicant-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.applicant-name {
  font-weight: 500;
  color: #303133;
}

.specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.specialty-tag {
  margin-bottom: 4px;
}

.algorithm-content {
  margin-top: 16px;
}

.algorithm-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.algorithm-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.weight-settings, .rule-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.weight-item, .rule-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.weight-label, .rule-label {
  min-width: 120px;
  font-size: 14px;
  color: #606266;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 8px;
}

.algorithm-actions {
  margin-top: 20px;
  text-align: center;
}

.queue-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 400px;
}

.queue-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.queue-list {
  max-height: 320px;
  overflow-y: auto;
}

.queue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.waiting-time {
  color: #e6a23c;
}

.capacity {
  color: #606266;
}

.monitoring-content {
  margin-top: 16px;
}

.monitoring-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.monitoring-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.alert-rules, .check-frequency, .intervention-measures {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.frequency-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.frequency-label {
  min-width: 100px;
  font-size: 14px;
  color: #606266;
}

.measure-item {
  font-size: 14px;
}

.pair-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mentor-info, .student-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mentor-name, .student-name {
  font-weight: 500;
  color: #303133;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}

.text-warning {
  color: #e6a23c;
  font-weight: 600;
}

.text-success {
  color: #67c23a;
  font-weight: 600;
}

.chart-container {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-mock {
  color: #909399;
  font-size: 14px;
}

.improvement-rate {
  color: #67c23a;
  font-weight: 600;
}

.achievements {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.achievement-tag {
  margin-bottom: 4px;
}

.analysis-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.analysis-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.problem-list, .suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.problem-item, .suggestion-item {
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.problem-header, .suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.problem-title, .suggestion-title {
  font-weight: 500;
  color: #303133;
}

.problem-description, .suggestion-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.problem-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.occurrence-rate, .impact-score {
  font-weight: 500;
}

.suggestion-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.insights-content {
  padding: 16px;
}

.insight-section {
  margin-bottom: 24px;
}

.insight-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.insight-item.warning {
  background: #fef0e6;
  border-left: 4px solid #e6a23c;
}

.insight-item.success {
  background: #f0f9ff;
  border-left: 4px solid #67c23a;
}

.insight-item .el-icon {
  color: #409eff;
  font-size: 16px;
}

.insight-item.warning .el-icon {
  color: #e6a23c;
}

.insight-item.success .el-icon {
  color: #67c23a;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .mentorship-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .standards-content .el-col,
  .algorithm-content .el-col,
  .monitoring-content .el-col {
    margin-bottom: 10px;
  }

  .queue-section {
    height: auto;
    min-height: 300px;
  }
}
</style>

