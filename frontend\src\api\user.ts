import request from '@/api/request'

// 用户相关API接口
export const userApi = {
  // 获取用户列表
  getUsers: (params: {
    page?: number
    page_size?: number
    search?: string
    status?: string
    membership?: string
    gender?: string
    level?: string
    start_date?: string
    end_date?: string
    sort_by?: string
    sort_order?: 'asc' | 'desc'
  }) => {
    return request({
      url: '/users/',
      method: 'get',
      params
    })
  },

  // 获取用户详情
  getUserById: (id: number) => {
    return request({
      url: `/users/${id}`,
      method: 'get'
    })
  },

  // 创建用户
  createUser: (data: {
    nickname: string
    email: string
    phone?: string
    full_name?: string
    gender?: 'MALE' | 'FEMALE' | 'OTHER'
    password: string
    status?: 'ACTIVE' | 'INACTIVE'
    membership?: 'REGULAR' | 'VIP'
    birth_date?: string
    bio?: string
    location?: string
  }) => {
    return request({
      url: '/users/',
      method: 'post',
      data
    })
  },

  // 更新用户信息
  updateUser: (id: number, data: {
    nickname?: string
    email?: string
    phone?: string
    full_name?: string
    gender?: 'MALE' | 'FEMALE' | 'OTHER'
    status?: 'ACTIVE' | 'INACTIVE'
    membership?: 'REGULAR' | 'VIP'
    birth_date?: string
    bio?: string
    location?: string
  }) => {
    return request({
      url: `/users/${id}`,
      method: 'put',
      data
    })
  },

  // 删除用户
  deleteUser: (id: number) => {
    return request({
      url: `/users/${id}`,
      method: 'delete'
    })
  },

  // 修改用户密码
  changeUserPassword: (id: number, data: {
    new_password: string
  }) => {
    return request({
      url: `/users/${id}/change-password`,
      method: 'post',
      data
    })
  },

  // 切换用户状态
  toggleUserStatus: (id: number) => {
    return request({
      url: `/users/${id}/toggle-status`,
      method: 'post'
    })
  },

  // 切换用户会员状态
  toggleUserPremium: (id: number) => {
    return request({
      url: `/users/${id}/toggle-premium`,
      method: 'post'
    })
  },

  // 获取用户统计信息
  getUserStats: (id: number) => {
    return request({
      url: `/users/${id}/stats`,
      method: 'get'
    })
  },

  // 获取用户概览统计
  getUsersOverview: () => {
    return request({
      url: '/users/stats/overview',
      method: 'get'
    })
  }
}

// 用户类型定义
export interface User {
  id: number
  nickname: string
  email: string
  phone?: string
  full_name?: string
  gender?: 'MALE' | 'FEMALE' | 'OTHER'
  status: 'ACTIVE' | 'INACTIVE' | 'DELETED'
  membership: 'REGULAR' | 'VIP'
  level: 'LEVEL_ONE' | 'LEVEL_TWO' | 'LEVEL_THREE' | 'LEVEL_FOUR' | 'LEVEL_FIVE' | 'LEVEL_SIX'
  birth_date?: string
  bio?: string
  location?: string
  avatar_url?: string
  is_premium: boolean
  premium_expires_at?: string
  registration_time: string
  last_login?: string
  total_study_time: number
  total_focus_sessions: number
  total_check_ins: number
  experience_points: number
  settings?: Record<string, any>
  preferences?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface UserListResponse {
  users: User[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface UserStatsResponse {
  user_id: number
  study_stats: {
    total_study_time: number
    total_focus_sessions: number
    average_session_duration: number
    longest_session: number
    study_streak: number
  }
  check_in_stats: {
    total_check_ins: number
    current_streak: number
    longest_streak: number
    last_check_in: string
  }
  learning_stats: {
    courses_enrolled: number
    courses_completed: number
    completion_rate: number
    certificates_earned: number
  }
  community_stats: {
    posts_created: number
    comments_made: number
    likes_received: number
    reputation_score: number
  }
  user_info: {
    level: string
    experience_points: number
    registration_days: number
  }
}

export interface UserOverviewResponse {
  total_users: number
  active_users: number
  premium_users: number
  free_users: number
  new_users_today?: number
  gender_distribution: Array<{
    gender: string
    count: number
  }>
  level_distribution: Array<{
    level: string
    count: number
  }>
}