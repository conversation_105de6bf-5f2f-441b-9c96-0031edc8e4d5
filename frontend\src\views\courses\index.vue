<template>
  <div class="courses-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>课程学习中心</h1>
          <p>管理课程进度，跟踪学习成果，制定学习计划</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="continueLearning">
            <el-icon><VideoPlay /></el-icon>
            继续学习
          </el-button>
          <el-button @click="viewSchedule">
            <el-icon><Calendar /></el-icon>
            学习计划
          </el-button>
          <el-button @click="viewGrades">
            <el-icon><TrendCharts /></el-icon>
            成绩分析
          </el-button>
        </div>
      </div>
    </div>

    <!-- 学习进度概览 -->
    <div class="progress-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card enrolled">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <div class="progress-info">
                <div class="progress-value">{{ courseStats.enrolled }}</div>
                <div class="progress-label">已选课程</div>
                <div class="progress-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  +{{ courseStats.newEnrolled }}门新课程
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card completed">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="progress-info">
                <div class="progress-value">{{ courseStats.completed }}</div>
                <div class="progress-label">已完成课程</div>
                <div class="progress-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  +{{ courseStats.newCompleted }}门本月完成
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card study-time">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="progress-info">
                <div class="progress-value">{{ courseStats.studyTime }}h</div>
                <div class="progress-label">总学习时长</div>
                <div class="progress-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  +{{ courseStats.weeklyTime }}h本周
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="progress-card average-score">
            <div class="progress-content">
              <div class="progress-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="progress-info">
                <div class="progress-value">{{ courseStats.averageScore }}</div>
                <div class="progress-label">平均成绩</div>
                <div class="progress-change positive">
                  <el-icon><ArrowUp /></el-icon>
                  +{{ courseStats.scoreImprovement }}分提升
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 当前课程 -->
    <div class="current-courses">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>正在学习的课程</span>
            <el-button text @click="viewAllCourses">查看全部</el-button>
          </div>
        </template>
        <div class="courses-grid">
          <div
            v-for="course in currentCourses"
            :key="course.id"
            class="course-card"
            @click="enterCourse(course)"
          >
            <div class="course-cover">
              <img :src="course.cover" :alt="course.title" />
              <div class="course-overlay">
                <div class="course-category">
                  <el-tag size="small">{{ course.category }}</el-tag>
                </div>
                <div class="course-difficulty">
                  <el-rate
                    v-model="course.difficulty"
                    disabled
                    :max="5"
                    size="small"
                  />
                </div>
              </div>
            </div>
            <div class="course-info">
              <h4 class="course-title">{{ course.title }}</h4>
              <p class="course-instructor">{{ course.instructor }}</p>
              <div class="course-progress">
                <div class="progress-info">
                  <span>学习进度</span>
                  <span>{{ course.progress }}%</span>
                </div>
                <el-progress
                  :percentage="course.progress"
                  :stroke-width="6"
                  :show-text="false"
                />
              </div>
              <div class="course-meta">
                <span class="next-lesson">
                  <el-icon><VideoPlay /></el-icon>
                  下节课：{{ course.nextLesson }}
                </span>
                <span class="due-date">
                  <el-icon><Clock /></el-icon>
                  {{ formatDueDate(course.dueDate) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  VideoPlay, Calendar, TrendCharts, Reading, CircleCheck, Clock, Star,
  ArrowUp, Document, Refresh
} from '@element-plus/icons-vue'
import { LineChart, PieChart, BarChart } from '@/components/charts'

// 路由
const router = useRouter()

// 响应式数据
const analyticsPeriod = ref('30d')

// 课程统计数据
const courseStats = reactive({
  enrolled: 8,
  completed: 3,
  studyTime: 156.5,
  averageScore: 87.2,
  newEnrolled: 2,
  newCompleted: 1,
  weeklyTime: 12.5,
  scoreImprovement: 3.8
})

// 当前课程数据
const currentCourses = ref([
  {
    id: '1',
    title: '高等数学进阶',
    instructor: '张教授',
    category: '数学',
    difficulty: 4,
    progress: 68,
    nextLesson: '第8章 微分方程',
    dueDate: '2024-02-15',
    cover: '/images/math-course.jpg'
  },
  {
    id: '2',
    title: '英语口语提升',
    instructor: 'Smith老师',
    category: '英语',
    difficulty: 3,
    progress: 45,
    nextLesson: '商务对话练习',
    dueDate: '2024-02-20',
    cover: '/images/english-course.jpg'
  },
  {
    id: '3',
    title: '计算机程序设计',
    instructor: '李老师',
    category: '计算机',
    difficulty: 5,
    progress: 82,
    nextLesson: '数据结构与算法',
    dueDate: '2024-02-10',
    cover: '/images/programming-course.jpg'
  },
  {
    id: '4',
    title: '物理实验',
    instructor: '王教授',
    category: '物理',
    difficulty: 3,
    progress: 23,
    nextLesson: '光学实验',
    dueDate: '2024-02-25',
    cover: '/images/physics-course.jpg'
  }
])

// 学习时长趋势数据
const studyTrendData = ref([
  {
    name: '每日学习时长',
    data: [] as number[],
    color: '#4f46e5',
    smooth: true,
    area: true
  }
])

const studyTrendXAxisData = ref([] as string[])

// 课程分类分布数据
const categoryDistributionData = ref([
  { name: '数学', value: 25 },
  { name: '英语', value: 20 },
  { name: '计算机', value: 18 },
  { name: '物理', value: 15 },
  { name: '化学', value: 12 },
  { name: '其他', value: 10 }
])

// 成绩分析数据
const gradeAnalysisData = ref([
  {
    name: '平均成绩',
    data: [85, 88, 92, 87, 90, 89, 91],
    color: '#10b981'
  }
])

const gradeAnalysisXAxisData = ref(['数学', '英语', '计算机', '物理', '化学', '生物', '历史'])

// 学习活跃度热力图数据
const activityHeatmapData = ref([])

// 作业提醒数据
const upcomingAssignments = ref([
  {
    id: '1',
    title: '高等数学作业第8章',
    course: '高等数学进阶',
    dueDate: '2024-01-25 23:59',
    status: 'pending',
    urgent: true
  },
  {
    id: '2',
    title: '英语口语录音作业',
    course: '英语口语提升',
    dueDate: '2024-01-26 18:00',
    status: 'pending',
    urgent: false
  },
  {
    id: '3',
    title: '程序设计项目报告',
    course: '计算机程序设计',
    dueDate: '2024-01-28 12:00',
    status: 'submitted',
    urgent: false
  }
])

// 学习建议数据
const learningSuggestions = ref([
  {
    id: '1',
    type: 'time',
    icon: 'Clock',
    title: '学习时间建议',
    description: '建议每天保持2-3小时的学习时间，当前平均学习时间偏低'
  },
  {
    id: '2',
    type: 'course',
    icon: 'Reading',
    title: '课程进度提醒',
    description: '物理实验课程进度较慢，建议加快学习节奏以跟上计划'
  },
  {
    id: '3',
    type: 'review',
    icon: 'Refresh',
    title: '复习建议',
    description: '数学课程已学习较多内容，建议安排时间进行系统复习'
  }
])

// 方法
const continueLearning = () => {
  // 找到进度最高的未完成课程
  const activeCourse = currentCourses.value.find(course => course.progress < 100)
  if (activeCourse) {
    router.push(`/courses/${activeCourse.id}/learn`)
  } else {
    ElMessage.info('所有课程都已完成！')
  }
}

const viewSchedule = () => {
  router.push('/courses/schedule')
}

const viewGrades = () => {
  router.push('/courses/grades')
}

const viewAllCourses = () => {
  router.push('/courses/all')
}

const enterCourse = (course: any) => {
  router.push(`/courses/${course.id}`)
}

const formatDueDate = (date: string): string => {
  const dueDate = new Date(date)
  const now = new Date()
  const diffTime = dueDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return '已过期'
  } else if (diffDays === 0) {
    return '今天截止'
  } else if (diffDays === 1) {
    return '明天截止'
  } else {
    return `${diffDays}天后截止`
  }
}

const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString()
}

const updateAnalytics = () => {
  generateStudyTrendData()
}

const generateStudyTrendData = () => {
  const days = analyticsPeriod.value === '7d' ? 7 : analyticsPeriod.value === '30d' ? 30 : 90
  const labels = []
  const studyData = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString())
    studyData.push(Math.floor(Math.random() * 5) + 1) // 1-6小时随机学习时长
  }

  studyTrendXAxisData.value = labels
  studyTrendData.value[0].data = studyData
}

const generateActivityHeatmap = () => {
  const data = []
  for (let i = 0; i < 365; i++) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.floor(Math.random() * 8) // 0-7小时学习时长
    })
  }
  activityHeatmapData.value = data.reverse()
}

const getActivityLevel = (value: number): string => {
  if (value === 0) return 'none'
  if (value <= 2) return 'low'
  if (value <= 4) return 'medium'
  return 'high'
}

const doAssignment = (assignment: any) => {
  router.push(`/assignments/${assignment.id}`)
}

const refreshSuggestions = () => {
  ElMessage.success('学习建议已更新')
}

const applySuggestion = (suggestion: any) => {
  ElMessage.success(`已采纳建议：${suggestion.title}`)
}

const dismissSuggestion = (suggestion: any) => {
  const index = learningSuggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    learningSuggestions.value.splice(index, 1)
    ElMessage.info('建议已忽略')
  }
}

// 生命周期
onMounted(() => {
  generateStudyTrendData()
  generateActivityHeatmap()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.courses-overview {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 学习进度概览 */
  .progress-overview {
    margin-bottom: var(--spacing-8);

    .progress-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-fast);
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.enrolled {
        border-left: 4px solid #4f46e5;
      }

      &.completed {
        border-left: 4px solid #10b981;
      }

      &.study-time {
        border-left: 4px solid #f59e0b;
      }

      &.average-score {
        border-left: 4px solid #ef4444;
      }

      .progress-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .progress-icon {
          width: 60px;
          height: 60px;
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-xl);
        }

        .enrolled .progress-icon {
          background: linear-gradient(135deg, #4f46e5, #6366f1);
        }

        .completed .progress-icon {
          background: linear-gradient(135deg, #10b981, #34d399);
        }

        .study-time .progress-icon {
          background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }

        .average-score .progress-icon {
          background: linear-gradient(135deg, #ef4444, #f87171);
        }

        .progress-info {
          flex: 1;

          .progress-value {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .progress-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .progress-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }
          }
        }
      }
    }
  }

  /* 当前课程 */
  .current-courses {
    margin-bottom: var(--spacing-8);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: var(--spacing-6);
      margin-top: var(--spacing-4);

      .course-card {
        background: white;
        border-radius: var(--radius-xl);
        border: 1px solid var(--border-light);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-fast);
        cursor: pointer;
        overflow: hidden;

        &:hover {
          transform: translateY(-4px);
          box-shadow: var(--shadow-lg);
          border-color: var(--primary-light);
        }

        .course-cover {
          position: relative;
          height: 180px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
          }

          .course-overlay {
            position: absolute;
            top: var(--spacing-3);
            left: var(--spacing-3);
            right: var(--spacing-3);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            .course-category {
              .el-tag {
                background: rgba(255, 255, 255, 0.9);
                border: none;
                font-weight: var(--font-weight-medium);
              }
            }

            .course-difficulty {
              background: rgba(0, 0, 0, 0.6);
              border-radius: var(--radius-md);
              padding: var(--spacing-1) var(--spacing-2);
            }
          }
        }

        .course-info {
          padding: var(--spacing-5);

          .course-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-1) 0;
            line-height: 1.4;
          }

          .course-instructor {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin: 0 0 var(--spacing-4) 0;
          }

          .course-progress {
            margin-bottom: var(--spacing-4);

            .progress-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: var(--spacing-2);
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
            }
          }

          .course-meta {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-2);

            .next-lesson, .due-date {
              display: flex;
              align-items: center;
              gap: var(--spacing-2);
              font-size: var(--font-size-xs);
              color: var(--text-tertiary);

              .el-icon {
                font-size: var(--font-size-sm);
              }
            }

            .due-date {
              color: var(--warning-color);
              font-weight: var(--font-weight-medium);
            }
          }
        }
      }
    }
  }

  /* 学习统计 */
  .learning-analytics {
    margin-bottom: var(--spacing-8);

    .chart-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);
        padding: var(--spacing-5) var(--spacing-6);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-container {
        padding: var(--spacing-4);
        min-height: 300px;
      }
    }

    /* 学习活跃度热力图 */
    .activity-heatmap {
      padding: var(--spacing-4);

      .heatmap-legend {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        margin-bottom: var(--spacing-4);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);

        .legend-items {
          display: flex;
          gap: var(--spacing-2);

          .legend-item {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            font-size: var(--font-size-xs);
            text-align: center;
            line-height: 12px;

            &.low {
              background: #ebedf0;
            }

            &.medium {
              background: #9be9a8;
            }

            &.high {
              background: #40c463;
            }
          }
        }
      }

      .heatmap-grid {
        display: grid;
        grid-template-columns: repeat(53, 1fr);
        gap: 2px;
        max-width: 100%;
        overflow-x: auto;

        .heatmap-cell {
          width: 10px;
          height: 10px;
          border-radius: 2px;
          cursor: pointer;
          transition: var(--transition-fast);

          &:hover {
            transform: scale(1.2);
          }

          &.none {
            background: #ebedf0;
          }

          &.low {
            background: #c6e48b;
          }

          &.medium {
            background: #7bc96f;
          }

          &.high {
            background: #239a3b;
          }
        }
      }
    }
  }

  /* 作业提醒 */
  .assignments-reminder {
    margin-bottom: var(--spacing-8);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .assignments-list {
      .assignment-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: var(--transition-fast);

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        &.urgent {
          border-left: 3px solid var(--error-color);
          background: var(--error-light);
        }

        .assignment-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--radius-lg);
          background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-base);
        }

        .assignment-info {
          flex: 1;

          .assignment-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .assignment-course {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-1);
          }

          .assignment-due {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }

        .assignment-status {
          margin-right: var(--spacing-3);
        }

        .assignment-actions {
          .el-button {
            border-radius: var(--radius-md);
          }
        }
      }
    }
  }

  /* 学习建议 */
  .learning-suggestions {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .suggestions-list {
      .suggestion-item {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-4);
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: var(--transition-fast);

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        &.time {
          border-left: 3px solid var(--warning-color);
        }

        &.course {
          border-left: 3px solid var(--info-color);
        }

        &.review {
          border-left: 3px solid var(--success-color);
        }

        .suggestion-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--radius-lg);
          background: linear-gradient(135deg, var(--info-light), var(--info-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-base);
        }

        .suggestion-content {
          flex: 1;

          .suggestion-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-2);
          }

          .suggestion-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-3);
          }

          .suggestion-actions {
            display: flex;
            gap: var(--spacing-2);

            .el-button {
              border-radius: var(--radius-md);
            }
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .courses-overview {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .progress-overview {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }

    .courses-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: var(--spacing-4);
    }

    .learning-analytics {
      .el-col {
        margin-bottom: var(--spacing-6);
      }
    }
  }
}

@include respond-to('md') {
  .courses-overview {
    padding: var(--spacing-4);

    .progress-overview {
      .progress-card .progress-content {
        padding: var(--spacing-4);

        .progress-icon {
          width: 50px;
          height: 50px;
        }

        .progress-info .progress-value {
          font-size: var(--font-size-xl);
        }
      }
    }

    .courses-grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);

      .course-card {
        .course-cover {
          height: 150px;
        }

        .course-info {
          padding: var(--spacing-4);

          .course-title {
            font-size: var(--font-size-base);
          }
        }
      }
    }

    .learning-analytics {
      .activity-heatmap {
        .heatmap-grid {
          grid-template-columns: repeat(26, 1fr);

          .heatmap-cell {
            width: 8px;
            height: 8px;
          }
        }
      }
    }

    .assignments-reminder {
      .assignment-item {
        padding: var(--spacing-3);

        .assignment-icon {
          width: 32px;
          height: 32px;
        }

        .assignment-info .assignment-title {
          font-size: var(--font-size-sm);
        }
      }
    }

    .learning-suggestions {
      .suggestion-item {
        padding: var(--spacing-3);

        .suggestion-icon {
          width: 32px;
          height: 32px;
        }

        .suggestion-content .suggestion-title {
          font-size: var(--font-size-sm);
        }
      }
    }
  }
}
</style>