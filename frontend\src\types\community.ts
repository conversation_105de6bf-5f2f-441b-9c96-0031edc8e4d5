/**
 * 社区模块相关类型定义
 */

// 基础枚举类型
export enum ResourceType {
  DOCUMENT = 'document',
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
  LINK = 'link',
  COURSE = 'course'
}

export enum ResourceStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  DELETED = 'deleted'
}

export enum StudyRoomStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  FULL = 'full',
  PRIVATE = 'private'
}

export enum TopicStatus {
  ACTIVE = 'active',
  LOCKED = 'locked',
  DELETED = 'deleted',
  PINNED = 'pinned'
}

export enum TribeRole {
  FOUNDER = 'founder',
  ADMIN = 'admin',
  ELDER = 'elder',
  MEMBER = 'member'
}

export enum MentorshipStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum ActivityStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ONGOING = 'ongoing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum WishStatus {
  ACTIVE = 'active',
  ACHIEVED = 'achieved',
  DELETED = 'deleted',
  REPORTED = 'reported'
}

// 学习资源相关类型
export interface LearningResource {
  id: string
  title: string
  description: string
  type: ResourceType
  url: string
  thumbnailUrl?: string
  fileSize?: number
  duration?: number
  subject: string
  gradeLevel: string
  tags: string[]
  authorId: string
  authorName: string
  status: ResourceStatus
  downloadCount: number
  viewCount: number
  likeCount: number
  rating: number
  ratingCount: number
  isApproved: boolean
  createdAt: string
  updatedAt: string
  approvedAt?: string
  approvedBy?: string
}

export interface ResourceCategory {
  id: string
  name: string
  description: string
  parentId?: string
  icon: string
  resourceCount: number
  order: number
}

// 自习室相关类型
export interface StudyRoom {
  id: string
  name: string
  description: string
  ownerId: string
  ownerName: string
  maxMembers: number
  currentMembers: number
  isPublic: boolean
  password?: string
  tags: string[]
  rules: string
  status: StudyRoomStatus
  totalStudyTime: number
  averageStudyTime: number
  createdAt: string
  updatedAt: string
}

export interface StudyRoomMember {
  id: string
  roomId: string
  userId: string
  userName: string
  userAvatar: string
  joinedAt: string
  totalStudyTime: number
  weeklyStudyTime: number
  isOnline: boolean
  lastActiveAt: string
}

export interface StudySession {
  id: string
  roomId: string
  userId: string
  startTime: string
  endTime?: string
  duration: number
  subject?: string
  notes?: string
}

// 话题相关类型
export interface Topic {
  id: string
  title: string
  content: string
  authorId: string
  authorName: string
  authorAvatar: string
  categoryId: string
  categoryName: string
  tags: string[]
  status: TopicStatus
  viewCount: number
  likeCount: number
  replyCount: number
  lastReplyAt?: string
  lastReplyBy?: string
  isPinned: boolean
  isHot: boolean
  createdAt: string
  updatedAt: string
}

export interface TopicCategory {
  id: string
  name: string
  description: string
  icon: string
  parentId?: string
  topicCount: number
  order: number
}

export interface TopicReply {
  id: string
  topicId: string
  content: string
  authorId: string
  authorName: string
  authorAvatar: string
  likeCount: number
  isLiked: boolean
  createdAt: string
  updatedAt: string
}

// 部落相关类型
export interface Tribe {
  id: string
  name: string
  description: string
  avatar: string
  banner: string
  founderId: string
  founderName: string
  category: string
  level: number
  experience: number
  memberCount: number
  maxMembers: number
  isPublic: boolean
  joinCondition: string
  tags: string[]
  announcement: string
  rules: string
  totalPoints: number
  weeklyPoints: number
  rank: number
  createdAt: string
  updatedAt: string
}

export interface TribeMember {
  id: string
  tribeId: string
  userId: string
  userName: string
  userAvatar: string
  role: TribeRole
  contribution: number
  weeklyContribution: number
  joinTime: string
  lastActiveTime: string
  title: string
}

// 师徒结对相关类型
export interface MentorProfile {
  id: string
  userId: string
  userName: string
  userAvatar: string
  subjects: string[]
  experience: string
  achievements: string[]
  rating: number
  ratingCount: number
  studentCount: number
  maxStudents: number
  isAvailable: boolean
  introduction: string
  teachingStyle: string
  createdAt: string
  updatedAt: string
}

export interface MentorshipRelation {
  id: string
  mentorId: string
  mentorName: string
  studentId: string
  studentName: string
  subject: string
  goals: string
  status: MentorshipStatus
  progress: number
  startDate: string
  endDate?: string
  notes: string
  rating?: number
  feedback?: string
}

// 学霸经验相关类型
export interface Experience {
  id: string
  title: string
  content: string
  authorId: string
  authorName: string
  authorAvatar: string
  authorLevel: string
  authorAchievements: string[]
  subject: string
  tags: string[]
  images: string[]
  videoUrl?: string
  viewCount: number
  likeCount: number
  commentCount: number
  shareCount: number
  isRecommended: boolean
  createdAt: string
  updatedAt: string
}

// 活动竞赛相关类型
export interface Activity {
  id: string
  title: string
  description: string
  type: 'competition' | 'challenge' | 'event'
  organizerId: string
  organizerName: string
  subject?: string
  startDate: string
  endDate: string
  registrationDeadline: string
  maxParticipants?: number
  currentParticipants: number
  rules: string
  prizes: string[]
  status: ActivityStatus
  isPublic: boolean
  tags: string[]
  createdAt: string
  updatedAt: string
}

export interface ActivityParticipant {
  id: string
  activityId: string
  userId: string
  userName: string
  userAvatar: string
  registeredAt: string
  score?: number
  rank?: number
  status: 'registered' | 'participating' | 'completed' | 'withdrawn'
}

// 心愿相关类型
export interface Wish {
  id: string
  userId: string
  userName: string
  userAvatar: string
  content: string
  tags: string[]
  status: WishStatus
  isAchieved: boolean
  supportCount: number
  commentCount: number
  viewCount: number
  hotScore: number
  createdAt: string
  updatedAt: string
  achievedAt?: string
}

export interface WishComment {
  id: string
  wishId: string
  userId: string
  userName: string
  userAvatar: string
  content: string
  likeCount: number
  isLiked: boolean
  createdAt: string
}

// 游戏圈相关类型
export interface GameActivity {
  id: string
  name: string
  description: string
  type: 'quiz' | 'challenge' | 'ranking' | 'achievement'
  rules: string
  rewards: string[]
  participantCount: number
  isActive: boolean
  startDate: string
  endDate?: string
  createdAt: string
  updatedAt: string
}

export interface GameParticipant {
  id: string
  gameId: string
  userId: string
  userName: string
  userAvatar: string
  score: number
  rank: number
  achievements: string[]
  participatedAt: string
  lastPlayedAt: string
}

// 分页和查询相关类型
export interface PagedResult<T> {
  items: T[]
  total: number
  page: number
  size: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface QueryParams {
  page?: number
  size?: number
  keyword?: string
  status?: string
  category?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  startDate?: string
  endDate?: string
}

// 统计相关类型
export interface CommunityStatistics {
  totalResources: number
  totalStudyRooms: number
  totalTopics: number
  totalTribes: number
  totalMentorships: number
  totalExperiences: number
  totalActivities: number
  totalWishes: number
  totalGames: number
  activeUsers: number
  dailyActiveUsers: number
  weeklyActiveUsers: number
  monthlyActiveUsers: number
}

export interface ModuleStatistics {
  resourceStats: {
    total: number
    pending: number
    approved: number
    rejected: number
    downloads: number
    views: number
  }
  studyRoomStats: {
    total: number
    active: number
    totalMembers: number
    totalStudyTime: number
    averageSessionTime: number
  }
  topicStats: {
    total: number
    active: number
    replies: number
    views: number
    hotTopics: number
  }
  tribeStats: {
    total: number
    totalMembers: number
    activeTribes: number
    totalPoints: number
  }
  mentorshipStats: {
    totalMentors: number
    totalStudents: number
    activeRelations: number
    completedRelations: number
  }
  experienceStats: {
    total: number
    views: number
    likes: number
    shares: number
    recommended: number
  }
  activityStats: {
    total: number
    ongoing: number
    completed: number
    totalParticipants: number
  }
  wishStats: {
    total: number
    active: number
    achieved: number
    totalSupports: number
  }
  gameStats: {
    total: number
    active: number
    totalParticipants: number
    totalPlays: number
  }
}
